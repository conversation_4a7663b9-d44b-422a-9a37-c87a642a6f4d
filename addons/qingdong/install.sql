CREATE TABLE IF NOT EXISTS `__PREFIX__qingdong_achievement` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `obj_id` int(11) NOT NULL DEFAULT '0' COMMENT '对象ID',
  `type` tinyint(2) DEFAULT '0' COMMENT '1公司2团队3员工',
  `year` int(8) DEFAULT NULL COMMENT '年',
  `january` decimal(18,2) DEFAULT '0.00' COMMENT '一月',
  `february` decimal(18,2) DEFAULT '0.00' COMMENT '二月',
  `march` decimal(18,2) DEFAULT '0.00' COMMENT '三月',
  `april` decimal(18,2) DEFAULT '0.00' COMMENT '四月',
  `may` decimal(18,2) DEFAULT '0.00' COMMENT '五月',
  `june` decimal(18,2) DEFAULT '0.00' COMMENT '六月',
  `july` decimal(18,2) DEFAULT '0.00' COMMENT '七月',
  `august` decimal(18,2) DEFAULT '0.00' COMMENT '八月',
  `september` decimal(18,2) DEFAULT '0.00' COMMENT '九月',
  `october` decimal(18,2) DEFAULT '0.00' COMMENT '十月',
  `november` decimal(18,2) DEFAULT '0.00' COMMENT '十一月',
  `december` decimal(18,2) DEFAULT '0.00' COMMENT '十二月',
  `status` enum('1','2') CHARACTER SET utf8 DEFAULT '1' COMMENT '1销售（目标）2回款（目标）',
  `yeartarget` decimal(18,2) DEFAULT '0.00' COMMENT '年目标',
  `createtime` int(11) DEFAULT NULL,
  `updatetime` int(11) DEFAULT NULL,
  `deletetime` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=44 DEFAULT CHARSET=utf8mb4 COMMENT='业绩目标表';

CREATE TABLE IF NOT EXISTS `__PREFIX__qingdong_achievement_records` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '名字',
  `obj_id` int(11) NOT NULL DEFAULT '0' COMMENT '对象ID',
  `type` tinyint(2) NOT NULL DEFAULT '0' COMMENT '1公司2部门3员工',
  `year` int(8) NOT NULL COMMENT '年',
  `january` decimal(18,2) DEFAULT NULL COMMENT '一月',
  `february` decimal(18,2) DEFAULT NULL COMMENT '二月',
  `march` decimal(18,2) DEFAULT NULL COMMENT '三月',
  `april` decimal(18,2) DEFAULT NULL COMMENT '四月',
  `may` decimal(18,2) DEFAULT NULL COMMENT '五月',
  `june` decimal(18,2) DEFAULT NULL COMMENT '六月',
  `july` decimal(18,2) DEFAULT NULL COMMENT '七月',
  `august` decimal(18,2) DEFAULT NULL COMMENT '八月',
  `september` decimal(18,2) DEFAULT NULL COMMENT '九月',
  `october` decimal(18,2) DEFAULT NULL COMMENT '十月',
  `november` decimal(18,2) DEFAULT NULL COMMENT '十一月',
  `december` decimal(18,2) DEFAULT NULL COMMENT '十二月',
  `status` tinyint(2) NOT NULL DEFAULT '0' COMMENT '1销售（目标）2回款（目标）',
  `yeartarget` decimal(18,2) DEFAULT NULL COMMENT '年目标',
  `check_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0待审核、1审核中、2审核通过、3审核未通过、4撤销、',
  `flow_staff_ids` varchar(255) CHARACTER SET utf8 DEFAULT '' COMMENT '流程审批人ID',
  `flow_id` int(11) DEFAULT NULL COMMENT '审核流程ID',
  `order_id` int(11) DEFAULT NULL COMMENT '审批流程步骤id',
  `check_staff_ids` varchar(255) CHARACTER SET utf8 DEFAULT '' COMMENT '已审批人IDs',
  `owner_staff_id` int(11) DEFAULT NULL COMMENT '负责人id',
  `createtime` int(11) DEFAULT NULL,
  `updatetime` int(11) DEFAULT NULL,
  `deletetime` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=42 DEFAULT CHARSET=utf8mb4 COMMENT='业绩目标修改日志表';

CREATE TABLE IF NOT EXISTS `__PREFIX__qingdong_admin_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` varchar(50) CHARACTER SET utf8 DEFAULT NULL COMMENT '类型 seas公海回收',
  `field` varchar(50) CHARACTER SET utf8 DEFAULT NULL COMMENT '字段名',
  `value` varchar(200) CHARACTER SET utf8 DEFAULT NULL COMMENT '字段值',
  `createtime` int(11) DEFAULT NULL,
  `updatetime` int(11) DEFAULT NULL,
  `deletetime` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COMMENT='管理员配置表';

CREATE TABLE IF NOT EXISTS `__PREFIX__qingdong_approval` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `formapproval_id` int(11) DEFAULT NULL COMMENT '审批id',
  `number` varchar(255) DEFAULT NULL COMMENT '编号',
  `relation_id` int(11) DEFAULT NULL COMMENT '关联id',
  `content` text COMMENT '内容',
  `file_ids` varchar(255) DEFAULT NULL COMMENT '附件',
  `create_staff_id` int(11) DEFAULT NULL COMMENT '创建用户id',
  `check_status` tinyint(4) DEFAULT '0' COMMENT '0待审核、1审核中、2审核通过、3审核未通过、4撤销、',
  `flow_id` int(11) DEFAULT NULL COMMENT '审核流程ID',
  `order_id` int(11) DEFAULT NULL COMMENT '审批流程步骤id',
  `flow_staff_ids` varchar(255) DEFAULT '' COMMENT '流程审批人ID',
  `check_staff_ids` varchar(255) DEFAULT '' COMMENT '已审批人IDs',
  `next_staff_id` int(4) DEFAULT NULL COMMENT '下个审核人Id',
  `show_staff_id` varchar(128) DEFAULT NULL COMMENT '显示人',
  `createtime` int(11) DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(11) DEFAULT NULL COMMENT '修改时间',
  `deletetime` int(11) DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=39 DEFAULT CHARSET=utf8 COMMENT='审批详情';

CREATE TABLE IF NOT EXISTS `__PREFIX__qingdong_comment` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `staff_id` int(11) DEFAULT NULL COMMENT '员工ID',
  `relation_type` tinyint(1) DEFAULT '0' COMMENT '1客户 2联系人 3合同4 线索  5工作报告',
  `relation_id` int(11) DEFAULT NULL COMMENT '关联id',
  `content` varchar(255) CHARACTER SET utf8 DEFAULT NULL COMMENT '评论内容',
  `praise` int(11) DEFAULT '0' COMMENT '点赞数',
  `status` tinyint(1) DEFAULT '0' COMMENT '0 待审核 1已审核',
  `ip` varchar(100) CHARACTER SET utf8 DEFAULT NULL COMMENT 'ip地址',
  `operator` int(11) DEFAULT NULL COMMENT '审核人',
  `checktime` datetime DEFAULT NULL COMMENT '审核时间',
  `createtime` int(11) DEFAULT NULL,
  `updatetime` int(11) DEFAULT NULL,
  `deletetime` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=83 DEFAULT CHARSET=utf8mb4 COMMENT='评论表';

CREATE TABLE IF NOT EXISTS `__PREFIX__qingdong_consume` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `staff_id` int(10) NOT NULL COMMENT '创建人ID',
  `customer_id` int(11) NOT NULL COMMENT '客户ID',
  `consume_time` date DEFAULT NULL COMMENT '消费日期',
  `consume_type` varchar(64) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '消费方式',
  `money` decimal(18,2) NOT NULL DEFAULT '0.00' COMMENT '消费金额',
  `check_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0待审核、1审核中、2审核通过、3审核未通过、4撤销、5草稿(未提交)',
  `flow_staff_ids` varchar(128) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '流程审批人ID',
  `flow_id` int(11) DEFAULT NULL COMMENT '审核流程ID',
  `order_id` int(11) DEFAULT NULL COMMENT '审批流程步骤id',
  `check_staff_ids` varchar(128) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '已审核审批人IDs',
  `remark` varchar(255) CHARACTER SET utf8 DEFAULT NULL COMMENT '备注',
  `file_ids` varchar(128) CHARACTER SET utf8 DEFAULT NULL COMMENT '附件ID',
  `createtime` int(11) DEFAULT NULL,
  `updatetime` int(11) DEFAULT NULL,
  `deletetime` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `staff_id` (`staff_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=22 DEFAULT CHARSET=utf8mb4 COMMENT='费用表';

CREATE TABLE IF NOT EXISTS `__PREFIX__qingdong_contacts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `customer_id` int(11) NOT NULL COMMENT '客户ID',
  `is_major` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否主要联系人 0 否 1是',
  `name` varchar(64) CHARACTER SET utf8 DEFAULT '' COMMENT '姓名',
  `subname` varchar(64) CHARACTER SET utf8 DEFAULT NULL COMMENT '简称或尊称',
  `role` varchar(32) CHARACTER SET utf8 DEFAULT NULL COMMENT '角色',
  `post` varchar(32) CHARACTER SET utf8 DEFAULT NULL COMMENT '职务',
  `email` varchar(32) CHARACTER SET utf8 DEFAULT NULL COMMENT '邮箱',
  `mobile` varchar(32) CHARACTER SET utf8 DEFAULT NULL COMMENT '手机号',
  `remarks` varchar(255) CHARACTER SET utf8 DEFAULT NULL COMMENT '备注信息',
  `next_time` datetime DEFAULT NULL COMMENT '下次跟进时间',
  `create_staff_id` int(10) DEFAULT NULL COMMENT '创建人ID',
  `owner_staff_id` int(10) DEFAULT NULL COMMENT '负责人ID',
  `createtime` int(11) DEFAULT NULL,
  `updatetime` int(11) DEFAULT NULL,
  `deletetime` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `customer_id_index` (`customer_id`) USING BTREE,
  KEY `mobile` (`mobile`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='联系人表';

CREATE TABLE IF NOT EXISTS `__PREFIX__qingdong_contacts_file` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `contacts_id` int(11) NOT NULL,
  `file_id` int(11) NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COMMENT='联系人附件';

CREATE TABLE IF NOT EXISTS `__PREFIX__qingdong_contacts_other` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '客户id',
  `otherdata` text CHARACTER SET utf8 COMMENT '自定义信息 json',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='其他联系人信息';

CREATE TABLE IF NOT EXISTS `__PREFIX__qingdong_contract` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `customer_id` int(11) DEFAULT NULL COMMENT '客户ID',
  `contacts_id` int(11) DEFAULT NULL COMMENT '客户签约人（联系人ID）',
  `order_staff_id` varchar(255) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '公司签约人',
  `business_id` int(11) NULL COMMENT '商机ID',
  `name` varchar(64) CHARACTER SET utf8 DEFAULT '' COMMENT '合同名称',
  `num` varchar(64) CHARACTER SET utf8 DEFAULT '' COMMENT '合同编号',
  `money` decimal(18,2) NOT NULL DEFAULT '0.00' COMMENT '合同金额',
  `order_date` date DEFAULT NULL COMMENT '签约时间',
  `start_time` date DEFAULT NULL COMMENT '生效时间',
  `end_time` date DEFAULT NULL COMMENT '到期时间',
  `total_price` decimal(18,2) NOT NULL DEFAULT '0.00' COMMENT '产品总金额',
  `discount_rate` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '整单折扣',
  `contract_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '0 进行中 1 已完成  2 已作废',
  `check_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0待审核、1审核中、2审核通过、3审核未通过、4撤销、',
  `remarks` varchar(255) CHARACTER SET utf8 DEFAULT NULL COMMENT '备注',
  `flow_id` int(11) DEFAULT NULL COMMENT '审核流程ID',
  `order_id` int(11) DEFAULT NULL COMMENT '审批流程步骤id',
  `ratio_id` int(11) NULL COMMENT '绩效ID',
  `ratios` varchar(255) NULL DEFAULT NULL COMMENT '业绩比例',
  `flow_staff_ids` varchar(255) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '流程审批人ID',
  `check_staff_ids` varchar(255) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '已审批人IDs',
  `create_staff_id` int(10) DEFAULT NULL COMMENT '创建人ID',
  `owner_staff_id` int(10) DEFAULT NULL COMMENT '负责人ID',
  `createtime` int(11) DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(11) DEFAULT NULL COMMENT '更新时间',
  `deletetime` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `customer_id_index` (`customer_id`) USING BTREE,
  KEY `contacts_id_index` (`contacts_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='合同表';


CREATE TABLE IF NOT EXISTS `__PREFIX__qingdong_contract_file` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `contract_id` int(11) NOT NULL,
  `file_id` int(11) NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COMMENT='合同附件';

CREATE TABLE IF NOT EXISTS `__PREFIX__qingdong_contract_other` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '客户id',
  `otherdata` text CHARACTER SET utf8 COMMENT '自定义信息 json',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='其他合同信息';

CREATE TABLE IF NOT EXISTS `__PREFIX__qingdong_contract_product` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `contract_id` int(11) NOT NULL COMMENT '合同id',
  `product_id` int(11) DEFAULT NULL COMMENT '产品id',
  `number` tinyint(5) DEFAULT NULL,
  `price` double(10,2) DEFAULT NULL,
  `parts` varchar(1000) DEFAULT NULL COMMENT '产品配置',
  `ship_number` tinyint(5) DEFAULT '0' COMMENT '发货数量',
  `ship_status` tinyint(1) DEFAULT '0' COMMENT '0 待发货 1发货 2已收货',
  `freight` double(10,2) DEFAULT '0.00' COMMENT '运费',
  `wholesale` decimal(10,2) DEFAULT '0.00' COMMENT '批发价',
  `retail` decimal(10,2) DEFAULT '0.00' COMMENT '零售价',
  `createtime` int(11) DEFAULT NULL,
  `updatetime` int(11) DEFAULT NULL,
  `deletetime` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='产品';

CREATE TABLE IF NOT EXISTS `__PREFIX__qingdong_contract_ratio` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `contract_id` int(11) NOT NULL COMMENT '合同id',
  `ratio` decimal(10,2) DEFAULT NULL COMMENT '比例',
  `staff_id` int(11) DEFAULT NULL,
  `ratio_money` double(18,2) DEFAULT NULL COMMENT '分成金额',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='业绩分成';

CREATE TABLE IF NOT EXISTS `__PREFIX__qingdong_customer` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `parent_id` int(11) DEFAULT NULL COMMENT '上级（客户/公司）ID',
  `seas_id` varchar(128) DEFAULT NULL COMMENT '所属公海',
  `contract_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '成交状态 0未成交 1已成交',
  `name` varchar(128) CHARACTER SET utf8 DEFAULT NULL COMMENT '客户名称',
  `subname` varchar(128) CHARACTER SET utf8 DEFAULT NULL COMMENT '助记名称',
  `mobile` varchar(15) NULL DEFAULT '' COMMENT '手机号',
  `level` tinyint(1) DEFAULT NULL COMMENT '1-5 客户星级',
  `industry` varchar(155) CHARACTER SET utf8 DEFAULT NULL COMMENT '客户行业',
  `source` varchar(64) CHARACTER SET utf8 DEFAULT NULL COMMENT '客户来源',
  `follow` varchar(32) CHARACTER SET utf8 DEFAULT NULL COMMENT '跟进状态',
  `next_time` datetime DEFAULT NULL COMMENT '下次联系时间',
  `last_time` datetime DEFAULT NULL COMMENT '最后一次联系时间',
  `receivetime` int(11) DEFAULT NULL COMMENT '领取时间',
  `address` varchar(100) CHARACTER SET utf8 DEFAULT NULL COMMENT '省市区ID',
  `address_detail` varchar(255) DEFAULT NULL COMMENT '详细地址',
  `location` varchar(128) CHARACTER SET utf8 DEFAULT NULL COMMENT '定位信息',
  `lng` double(16,12) DEFAULT NULL COMMENT '地理位置经度',
  `lat` double(16,12) DEFAULT NULL COMMENT '地理位置维度',
  `remarks` text COMMENT '备注信息',
  `create_staff_id` int(11) DEFAULT NULL COMMENT '创建人ID',
  `owner_staff_id` int(11) DEFAULT NULL COMMENT '负责人ID',
  `ro_staff_id` varchar(255) DEFAULT '' COMMENT '只读权限',
  `rw_staff_id` varchar(255) DEFAULT '' COMMENT '读写权限',
  `sea_time` int(11) DEFAULT NULL COMMENT '放入公海时间',
  `createtime` int(11) DEFAULT NULL,
  `updatetime` int(11) DEFAULT NULL,
  `deletetime` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `name` (`name`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='客户表';

CREATE TABLE IF NOT EXISTS `__PREFIX__qingdong_customer_file` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `customer_id` int(11) NOT NULL,
  `file_id` int(11) NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='客户附件表';

CREATE TABLE IF NOT EXISTS `__PREFIX__qingdong_customer_other` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '客户id',
  `otherdata` text CHARACTER SET utf8 COMMENT '自定义信息 json',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='客户其他信息';

CREATE TABLE IF NOT EXISTS `__PREFIX__qingdong_daily` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` varchar(50) CHARACTER SET utf8 DEFAULT NULL COMMENT ' 日 周 月 季 年',
  `other` text CHARACTER SET utf8 COMMENT '其他',
  `reminds_id` varchar(255) CHARACTER SET utf8 DEFAULT NULL COMMENT '提醒人员列表',
  `create_staff_id` int(11) DEFAULT NULL COMMENT '创建用户id',
  `createtime` int(11) DEFAULT NULL,
  `updatetime` int(11) DEFAULT NULL,
  `deletetime` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='工作报告';

CREATE TABLE IF NOT EXISTS `__PREFIX__qingdong_daily_draft` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` varchar(20) DEFAULT NULL COMMENT ' 日 周 月 季 年',
  `other` text COMMENT '其他',
  `reminds_id` varchar(255) NOT NULL COMMENT '提醒人员列表',
  `create_staff_id` int(11) NOT NULL COMMENT '创建用户id',
  `createtime` int(11) DEFAULT NULL,
  `updatetime` int(11) DEFAULT NULL,
  `deletetime` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT='工作报告草稿';

CREATE TABLE IF NOT EXISTS `__PREFIX__qingdong_daily_read` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `staff_id` int(11) DEFAULT NULL,
  `daily_id` int(11) DEFAULT NULL,
  `createtime` int(11) DEFAULT NULL,
  `updatetime` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `staff_id` (`staff_id`) USING BTREE,
  KEY `record_id` (`daily_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='跟进记录已读记录';

CREATE TABLE IF NOT EXISTS `__PREFIX__qingdong_ding_contacts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` int(1) DEFAULT '0' COMMENT '0:钉钉  1:微信',
  `instance_id` varchar(64) DEFAULT NULL COMMENT '数据ID',
  `customer_id` int(11) NOT NULL COMMENT '客户ID',
  `name` varchar(64) DEFAULT '' COMMENT '姓名',
  `post` varchar(32) DEFAULT NULL COMMENT '职务',
  `email` varchar(32) DEFAULT NULL COMMENT '邮箱',
  `mobile` varchar(32) DEFAULT NULL COMMENT '手机号',
  `remarks` varchar(255) DEFAULT NULL COMMENT '备注信息',
  `create_staff_id` varchar(100) DEFAULT NULL COMMENT '创建人ID',
  `owner_staff_id` varchar(100) DEFAULT NULL COMMENT '负责人ID',
  `status` int(1) DEFAULT '0' COMMENT '0:未同步  1:已同步',
  `createtime` int(11) DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(11) DEFAULT NULL COMMENT '修改时间',
  `deletetime` int(11) DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `customer_id_index` (`customer_id`) USING BTREE,
  KEY `mobile` (`mobile`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT='钉钉联系人表';

CREATE TABLE IF NOT EXISTS `__PREFIX__qingdong_ding_customer` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `type` int(1) DEFAULT '0' COMMENT '0:钉钉  1:微信',
  `customer_id` int(11) DEFAULT NULL COMMENT 'CRM客户ID',
  `instance_id` varchar(64) DEFAULT NULL COMMENT '数据ID',
  `user_id` varchar(100) DEFAULT NULL COMMENT '用户ID',
  `name` varchar(100) DEFAULT NULL COMMENT '客户名称',
  `address` varchar(100) DEFAULT NULL COMMENT '地址',
  `address_detail` varchar(100) DEFAULT NULL COMMENT '详细地址',
  `source` varchar(50) DEFAULT NULL COMMENT '来源',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `owner_staff_id` varchar(100) DEFAULT NULL COMMENT '负责人',
  `create_staff_id` varchar(100) DEFAULT NULL COMMENT '创建人',
  `status` int(1) DEFAULT '0' COMMENT '0:未同步  1：已同步',
  `createtime` int(11) DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(11) DEFAULT NULL COMMENT '更新时间',
  `deletetime` int(11) DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT='钉钉、企业微信客户表';

CREATE TABLE IF NOT EXISTS `__PREFIX__qingdong_ding_record` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` int(1) DEFAULT '0' COMMENT '0:钉钉  1:微信',
  `instance_id` varchar(64) CHARACTER SET utf8 DEFAULT NULL COMMENT '数据ID',
  `customer_id` int(11) DEFAULT NULL COMMENT '客户ID',
  `content` varchar(255) CHARACTER SET utf8 DEFAULT NULL COMMENT '跟进内容',
  `create_staff_id` varchar(64) DEFAULT NULL COMMENT '创建用户id',
  `status` int(1) DEFAULT '0' COMMENT '0:未同步 1：已同步',
  `createtime` int(11) DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(11) DEFAULT NULL COMMENT '更新时间',
  `deletetime` int(11) DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `instance_index` (`instance_id`) USING BTREE,
  KEY `customer_index` (`customer_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='钉钉跟进记录';

CREATE TABLE IF NOT EXISTS `__PREFIX__qingdong_ding_staff` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `type` int(1) DEFAULT '0' COMMENT '0:钉钉 1：企业微信 ',
  `staff_id` int(11) DEFAULT NULL COMMENT '员工ID',
  `dept_id` int(11) DEFAULT NULL COMMENT '部门ID',
  `user_id` varchar(100) DEFAULT NULL COMMENT '钉钉用户ID',
  `name` varchar(30) DEFAULT NULL COMMENT '姓名',
  `mobile` varchar(12) DEFAULT NULL,
  `status` tinyint(1) DEFAULT '0' COMMENT '0:未同步  1：已同步',
  `createtime` int(11) DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(11) DEFAULT NULL COMMENT '更新时间',
  `deletetime` int(11) DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  KEY `dept_index` (`dept_id`) USING BTREE,
  KEY `user_id` (`user_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT='钉钉、企业微信用户';

CREATE TABLE IF NOT EXISTS `__PREFIX__qingdong_event` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `staff_id` int(10) NOT NULL COMMENT '创建人ID',
  `type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '1 日程 2任务',
  `title` varchar(255) CHARACTER SET utf8 DEFAULT NULL COMMENT '日程标题',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `status` tinyint(2) NOT NULL DEFAULT '0' COMMENT '日程状态 0未开始 1执行中2 已结束 3 已取消',
  `auto_end` tinyint(1) NOT NULL DEFAULT '1' COMMENT '自动结束 0否 1是',
  `level` tinyint(2) DEFAULT NULL COMMENT '紧要程度 1重要 2紧急 3普通 4 重要且紧急',
  `remindtype` tinyint(4) NOT NULL DEFAULT '0' COMMENT '提醒时间0准时提醒 1：5分钟前 2：15分钟前 3：30分钟前 4：一个小时前 5：两个小时前 6：一天前 7：两天前 8：一周前',
  `remind_time` datetime DEFAULT NULL COMMENT '提醒时间',
  `owner_staff_id` int(11) NOT NULL DEFAULT '0' COMMENT '指派人',
  `remark` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT '' COMMENT '备注',
  `relation_type` tinyint(1) DEFAULT NULL COMMENT '关联类型  1客户 2联系人 3合同 4线索',
  `relation_id` int(11) DEFAULT NULL COMMENT '关联ID',
  `createtime` int(11) DEFAULT NULL,
  `updatetime` int(11) DEFAULT NULL,
  `deletetime` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `title` (`title`) USING BTREE,
  KEY `staff_id` (`staff_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='日程提醒';

CREATE TABLE IF NOT EXISTS `__PREFIX__qingdong_examine_record` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `relation_id` int(11) NOT NULL DEFAULT '0' COMMENT '关联ID',
  `relation_type` varchar(32) CHARACTER SET utf8 NOT NULL DEFAULT 'consume' COMMENT 'consume 费用 ',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0待审批 1审核通过2审核拒绝 3撤销',
  `content` varchar(255) CHARACTER SET utf8 DEFAULT NULL COMMENT '审核意见',
  `is_end` tinyint(1) NOT NULL DEFAULT '0' COMMENT '审批失效（1标记为无效）',
  `check_staff_id` int(11) NOT NULL DEFAULT '0' COMMENT '审批人ID',
  `check_time` int(11) NOT NULL DEFAULT '0' COMMENT '审批时间',
  `createtime` int(11) DEFAULT NULL,
  `updatetime` int(11) DEFAULT NULL,
  `deletetime` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='审批记录';

CREATE TABLE IF NOT EXISTS `__PREFIX__qingdong_fauser` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT 'fast用户ID',
  `staff_id` int(11) DEFAULT NULL COMMENT '员工ID',
  `relation_id` int(11) DEFAULT NULL COMMENT '关联ID',
  `type` tinyint(1) DEFAULT '0' COMMENT '0:客户  1：线索',
  `status` tinyint(1) DEFAULT '0' COMMENT '0:未同步 1同步',
  `createtime` int(11) DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(11) DEFAULT NULL COMMENT '更新时间',
  `deletetime` int(11) DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  KEY `user_index` (`user_id`) USING BTREE,
  KEY `status_index` (`status`) USING BTREE,
  KEY `type_index` (`type`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT='同步fast用户表';

CREATE TABLE IF NOT EXISTS `__PREFIX__qingdong_feedback` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `staff_id` int(11) NOT NULL,
  `content` text CHARACTER SET utf8,
  `file_ids` varchar(128) CHARACTER SET utf8 DEFAULT NULL,
  `createtime` int(10) DEFAULT NULL,
  `updatetime` int(10) DEFAULT NULL,
  `deletetime` int(10) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='意见反馈';

CREATE TABLE IF NOT EXISTS `__PREFIX__qingdong_field` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` varchar(32) DEFAULT '',
  `name` varchar(32) DEFAULT NULL,
  `data` text,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='字段管理';

CREATE TABLE IF NOT EXISTS `__PREFIX__qingdong_file` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `types` varchar(100) CHARACTER SET utf8 DEFAULT NULL COMMENT '类型（file、image）',
  `name` varchar(100) CHARACTER SET utf8 DEFAULT NULL COMMENT '附件名称',
  `save_name` varchar(500) CHARACTER SET utf8 DEFAULT NULL COMMENT '保存路径名称',
  `size` int(10) DEFAULT NULL COMMENT '附件大小（字节）',
  `file_path` varchar(500) CHARACTER SET utf8 DEFAULT NULL COMMENT '文件路径',
  `file_path_thumb` varchar(500) CHARACTER SET utf8 DEFAULT '' COMMENT '文件路径(图片缩略图)',
  `create_staff_id` int(10) DEFAULT NULL COMMENT '创建人ID',
  `createtime` int(11) DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(11) DEFAULT NULL,
  `deletetime` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='小程序 附件表';


CREATE TABLE IF NOT EXISTS `__PREFIX__qingdong_flow` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) CHARACTER SET utf8 DEFAULT NULL,
  `status` tinyint(1) DEFAULT NULL COMMENT '1 固定审批 0 授权审批',
  `examine_ids` text CHARACTER SET utf8 COMMENT '默认审核人id',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `relation_type` varchar(255) DEFAULT NULL COMMENT '关联类型',
  `group_ids` varchar(255) DEFAULT NULL COMMENT '关联角色组',
  `weight` tinyint(4) DEFAULT NULL COMMENT '权重',
  `last_modified` int(11) DEFAULT NULL COMMENT '最后修改时间',
  `last_admin_id` int(11) DEFAULT NULL COMMENT '最后修改人',
  `createtime` int(11) DEFAULT NULL,
  `updatetime` int(11) DEFAULT NULL,
  `deletetime` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='流程审核配置';

CREATE TABLE IF NOT EXISTS `__PREFIX__qingdong_form` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `name` varchar(64) CHARACTER SET utf8 DEFAULT NULL COMMENT '姓名',
  `type` varchar(32) CHARACTER SET utf8 DEFAULT NULL COMMENT 'type类型',
  `data` text CHARACTER SET utf8,
  `createtime` int(10) DEFAULT NULL,
  `updatetime` int(10) DEFAULT NULL,
  `deletetime` int(10) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='表单配置';

CREATE TABLE IF NOT EXISTS `__PREFIX__qingdong_form_approval` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `form_id` int(11) DEFAULT NULL COMMENT '表单ID',
  `examine_ids` text COMMENT '默认审核人id',
  `status` tinyint(1) DEFAULT NULL COMMENT '1 固定审批 0 授权审批',
  `name` varchar(64) DEFAULT NULL COMMENT '名称',
  `img` varchar(255) DEFAULT NULL COMMENT '图片',
  `desc` text COMMENT '描述',
  `flow_staff_ids` varchar(255) DEFAULT NULL COMMENT '审批人列表',
  `createtime` int(10) DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) DEFAULT NULL COMMENT '修改时间',
  `deletetime` int(10) DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `form_index` (`form_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT='审批表单配置';

CREATE TABLE IF NOT EXISTS `__PREFIX__qingdong_leads` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `customer_id` int(11) NOT NULL DEFAULT '0' COMMENT '线索转化为客户ID',
  `is_transform` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1已转化',
  `name` varchar(64) CHARACTER SET utf8 DEFAULT '' COMMENT '线索名称',
  `source` varchar(64) CHARACTER SET utf8 DEFAULT '' COMMENT '线索来源',
  `sex` tinyint(1) DEFAULT NULL COMMENT '1男 2女',
  `telephone` varchar(64) CHARACTER SET utf8 DEFAULT '' COMMENT '电话',
  `mobile` varchar(64) CHARACTER SET utf8 DEFAULT '' COMMENT '手机',
  `industry` varchar(64) CHARACTER SET utf8 DEFAULT '' COMMENT '客户行业',
  `level` varchar(64) CHARACTER SET utf8 DEFAULT '' COMMENT '客户级别',
  `address` varchar(128) CHARACTER SET utf8 DEFAULT NULL COMMENT '省市区',
  `address_detail` varchar(128) CHARACTER SET utf8 DEFAULT '' COMMENT '地址',
  `remarks` varchar(255) CHARACTER SET utf8 DEFAULT NULL COMMENT '备注',
  `create_staff_id` int(10) DEFAULT NULL COMMENT '创建人ID',
  `owner_staff_id` int(10) DEFAULT NULL COMMENT '负责人ID',
  `tranfer_stff_id` int(10) DEFAULT NULL COMMENT '转派人ID',
  `next_time` datetime DEFAULT NULL COMMENT '下次联系时间',
  `follow` varchar(20) CHARACTER SET utf8 DEFAULT NULL COMMENT '跟进时间',
  `receive_time` int(11) DEFAULT NULL COMMENT '分配时间',
  `createtime` int(11) DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(11) DEFAULT NULL COMMENT '更新时间',
  `deletetime` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `name` (`name`) USING BTREE,
  KEY `mobile` (`mobile`) USING BTREE,
  KEY `staff_id` (`owner_staff_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='线索表';

CREATE TABLE IF NOT EXISTS `__PREFIX__qingdong_leads_file` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `leads_id` int(11) NOT NULL,
  `file_id` int(11) NOT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='客户附件表';

CREATE TABLE IF NOT EXISTS `__PREFIX__qingdong_leads_other` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '客户id',
  `otherdata` text CHARACTER SET utf8 COMMENT '自定义信息 json',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='线索其他信息';

CREATE TABLE IF NOT EXISTS `__PREFIX__qingdong_login_token` (
  `token` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'Token',
  `login_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '0' COMMENT '登录ID',
  `createtime` int(10) DEFAULT NULL COMMENT '创建时间',
  `expiretime` int(10) DEFAULT NULL COMMENT '过期时间',
  PRIMARY KEY (`token`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='小程序用户登录Token表';

CREATE TABLE IF NOT EXISTS `__PREFIX__qingdong_message` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `relation_type` varchar(32) CHARACTER SET utf8 NOT NULL DEFAULT '0' COMMENT '关联类型 consume 费用审批',
  `relation_id` int(10) DEFAULT NULL COMMENT '关联ID',
  `to_staff_id` int(10) DEFAULT NULL COMMENT '接收人ID',
  `from_staff_id` int(10) DEFAULT NULL COMMENT '发送人ID',
  `content` varchar(500) CHARACTER SET utf8 DEFAULT NULL COMMENT '发送内容',
  `send_time` int(11) DEFAULT NULL COMMENT '发送时间',
  `read_time` int(11) DEFAULT NULL COMMENT '阅读时间',
  `status` tinyint(1) DEFAULT NULL COMMENT '0 未操作 1已操作 ',
  `action_id` int(11) DEFAULT NULL COMMENT '操作ID',
  `createtime` int(11) DEFAULT NULL,
  `updatetime` int(11) DEFAULT NULL,
  `deletetime` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='提醒消息';

CREATE TABLE IF NOT EXISTS `__PREFIX__qingdong_notice` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` varchar(50) CHARACTER SET utf8 DEFAULT NULL COMMENT '标题',
  `content` text CHARACTER SET utf8 COMMENT '内容',
  `start_time` int(11) DEFAULT NULL COMMENT '开始时间',
  `end_time` int(11) DEFAULT NULL COMMENT '结束时间',
  `owner_staff_ids` varchar(100) CHARACTER SET utf8 DEFAULT '' COMMENT '通知人',
  `read_staff_ids` text CHARACTER SET utf8 COMMENT '阅读人',
  `create_staff_id` int(10) DEFAULT NULL COMMENT '创建人ID',
  `createtime` int(11) DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(11) DEFAULT NULL COMMENT '更新时间',
  `deletetime` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COMMENT='公告表';

CREATE TABLE IF NOT EXISTS `__PREFIX__qingdong_notice_template` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(64) DEFAULT NULL COMMENT '名称',
  `type` varchar(32) DEFAULT NULL COMMENT '类型',
  `data` text COMMENT '微信公众号',
  `enterprise_data` text COMMENT '企业微信',
  `createtime` int(11) DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(11) DEFAULT NULL COMMENT '修改时间',
  `deletetime` int(11) DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 COMMENT='通知模板设置';

CREATE TABLE IF NOT EXISTS `__PREFIX__qingdong_operation_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `content` varchar(255) CHARACTER SET utf8 DEFAULT NULL COMMENT '操作内容',
  `operation_type` tinyint(1) DEFAULT NULL COMMENT '操作人类型 1 员工 2管理员',
  `operation_id` int(11) DEFAULT NULL COMMENT '操作人id',
  `relation_type` tinyint(1) DEFAULT NULL COMMENT '关联类型  1客户 2联系人 3合同 5商机',
  `relation_id` int(11) DEFAULT NULL COMMENT '被操作的客户id',
  `createtime` int(11) DEFAULT NULL,
  `updatetime` int(11) DEFAULT NULL,
  `deletetime` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='操作日志';

CREATE TABLE IF NOT EXISTS `__PREFIX__qingdong_product` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `goods_id` int(11) DEFAULT NULL COMMENT '商品ID',
  `type_id` int(11) DEFAULT NULL COMMENT '分类ID',
  `name` varchar(128) NOT NULL DEFAULT '' COMMENT '产品名称',
   `type` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '规格',
  `img` varchar(200) DEFAULT NULL COMMENT '产品图片',
  `num` varchar(128) DEFAULT '' COMMENT '产品编码',
  `unit` varchar(128) DEFAULT '箱' COMMENT '单位',
  `price` decimal(18,2) DEFAULT '0.00' COMMENT '价格',
  `min_price` decimal(10,2) DEFAULT '0.00' COMMENT '最低销售价',
  `cost_price` decimal(10,2) DEFAULT NULL COMMENT '成本价',
  `wholesale` decimal(10,2) DEFAULT '0.00' COMMENT '批发价',
  `status` varchar(32) NOT NULL DEFAULT '上架' COMMENT '是否上架',
  `description` text COMMENT '产品描述',
  `stock` int(11) DEFAULT '0' COMMENT '总库存',
  `instock` int(11) DEFAULT '0' COMMENT '入库数',
  `outstock` int(11) DEFAULT '0' COMMENT '出库数',
  `createtime` int(11) DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(11) DEFAULT NULL COMMENT '更新时间',
  `deletetime` int(10) DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8 COMMENT='产品管理';

CREATE TABLE IF NOT EXISTS `__PREFIX__qingdong_product_part` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` int(11) DEFAULT NULL,
  `name` varchar(128) NOT NULL DEFAULT '' COMMENT '产品名称',
  `img` varchar(200) DEFAULT NULL COMMENT '产品图片',
  `description` text COMMENT '产品描述',
  `createtime` int(11) DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(11) DEFAULT NULL COMMENT '更新时间',
  `deletetime` int(10) DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8 COMMENT='产品配件';

CREATE TABLE IF NOT EXISTS `__PREFIX__qingdong_product_type` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(128) NOT NULL DEFAULT '' COMMENT '分类名称',
  `image` varchar(200) DEFAULT NULL COMMENT '分类图片',
  `content` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '内容',
  `status` tinyint(1) NULL DEFAULT NULL COMMENT '状态',
  `createtime` int(11) DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(11) DEFAULT NULL COMMENT '更新时间',
  `deletetime` int(10) DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8 COMMENT='产品分类';

CREATE TABLE IF NOT EXISTS `__PREFIX__qingdong_push_reload` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `openid` varchar(255) CHARACTER SET utf8 DEFAULT NULL,
  `template_id` varchar(255) CHARACTER SET utf8 DEFAULT NULL,
  `data` text  DEFAULT NULL COMMENT '数据',
  `pagepath` varchar(255) CHARACTER SET utf8 DEFAULT NULL,
  `errcode` varchar(50) CHARACTER SET utf8 DEFAULT NULL,
  `result` varchar(255) CHARACTER SET utf8 DEFAULT NULL,
  `createtime` int(11) DEFAULT NULL,
  `updatetime` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='微信推送记录';

CREATE TABLE IF NOT EXISTS `__PREFIX__qingdong_receivables` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `plan_id` int(11) DEFAULT NULL COMMENT '回款计划ID',
  `number` varchar(100) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '回款编号',
  `customer_id` int(11) DEFAULT NULL COMMENT '客户ID',
  `contract_id` int(11) DEFAULT NULL COMMENT '合同ID',
  `return_time` date DEFAULT NULL COMMENT '回款日期',
  `return_type` varchar(64) CHARACTER SET utf8 DEFAULT '' COMMENT '回款方式',
  `money` decimal(18,2) DEFAULT NULL COMMENT '回款金额',
  `check_status` tinyint(4) DEFAULT '0' COMMENT '0待审核、1审核中、2审核通过、3审核未通过、4撤销、5草稿(未提交)',
  `flow_id` int(11) DEFAULT NULL COMMENT '审核流程ID',
  `order_id` int(11) DEFAULT NULL COMMENT '审批流程步骤id',
  `flow_staff_ids` varchar(255) CHARACTER SET utf8 DEFAULT '' COMMENT '流程审批人ID',
  `check_staff_ids` varchar(255) CHARACTER SET utf8 DEFAULT '' COMMENT '已审批人IDs',
  `remarks` text CHARACTER SET utf8 COMMENT '备注',
  `create_staff_id` int(10) DEFAULT NULL COMMENT '创建人ID',
  `owner_staff_id` int(10) DEFAULT NULL COMMENT '负责人ID',
  `createtime` int(11) DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(11) DEFAULT NULL COMMENT '更新时间',
  `deletetime` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=60 DEFAULT CHARSET=utf8mb4 COMMENT='回款表';

CREATE TABLE IF NOT EXISTS `__PREFIX__qingdong_receivables_other` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '客户id',
  `otherdata` text CHARACTER SET utf8 COMMENT '自定义信息 json',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=60 DEFAULT CHARSET=utf8mb4 COMMENT='回款其他信息';

CREATE TABLE IF NOT EXISTS `__PREFIX__qingdong_receivables_plan` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `num` varchar(100) CHARACTER SET utf8 DEFAULT '' COMMENT '期数',
  `receivables_id` int(11) NOT NULL DEFAULT '0' COMMENT '回款ID',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0进行中 1完成 2逾期',
  `contract_id` int(11) NOT NULL DEFAULT '0' COMMENT '合同ID',
  `customer_id` int(11) NOT NULL DEFAULT '0' COMMENT '客户ID',
  `money` decimal(18,2) NOT NULL DEFAULT '0.00' COMMENT '计划回款金额',
  `return_date` date DEFAULT NULL COMMENT '计划回款日期',
  `return_type` varchar(64) CHARACTER SET utf8 DEFAULT '' COMMENT '计划回款方式',
  `remind` tinyint(4) DEFAULT '0' COMMENT '提前几天提醒',
  `remind_date` date DEFAULT NULL COMMENT '提醒日期',
  `remarks` varchar(255) CHARACTER SET utf8 DEFAULT '' COMMENT '备注',
  `create_staff_id` int(11) DEFAULT NULL COMMENT '创建人ID',
  `owner_staff_id` int(11) DEFAULT NULL COMMENT '负责人ID',
  `createtime` int(11) DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(11) DEFAULT NULL COMMENT '更新时间',
  `deletetime` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=28 DEFAULT CHARSET=utf8mb4 COMMENT='回款计划表';

CREATE TABLE IF NOT EXISTS `__PREFIX__qingdong_record` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `follow_type` varchar(32) CHARACTER SET utf8 DEFAULT NULL COMMENT '跟进类型',
  `follow_time` datetime DEFAULT NULL,
  `follow` varchar(32) CHARACTER SET utf8 DEFAULT NULL COMMENT '跟进状态',
  `content` varchar(255) CHARACTER SET utf8 DEFAULT NULL COMMENT '跟进内容',
  `reminds_id` varchar(255) CHARACTER SET utf8 DEFAULT NULL COMMENT '提醒人员列表',
  `next_time` datetime DEFAULT NULL COMMENT '下次跟进时间',
  `relation_type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '关联类型  1客户 2联系人 3合同 4线索 5商机',
  `relation_id` int(11) NOT NULL DEFAULT '0' COMMENT '关联ID',
  `create_staff_id` int(11) DEFAULT '0' COMMENT '负责人',
  `status` int(1) DEFAULT '0' COMMENT '跟进状态 0：未跟进 1：已跟进',
  `createtime` int(11) DEFAULT NULL,
  `updatetime` int(11) DEFAULT NULL,
  `deletetime` int(11) DEFAULT NULL,
  `location` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '定位信息',
  `lng` double(16, 12) NULL DEFAULT NULL COMMENT '地理位置经度',
  `lat` double(16, 12) NULL DEFAULT NULL COMMENT '地理位置维度',
  `staff_id` int(11) NULL COMMENT '创建人',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='跟进记录';

CREATE TABLE IF NOT EXISTS `__PREFIX__qingdong_record_file` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `record_id` int(11) DEFAULT NULL COMMENT '日志ID',
  `file_id` int(11) DEFAULT NULL COMMENT '附件ID',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='跟进记录附件';

CREATE TABLE IF NOT EXISTS `__PREFIX__qingdong_record_read` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `admin_id` int(11) DEFAULT NULL,
  `staff_id` int(11) DEFAULT NULL,
  `record_id` int(11) DEFAULT NULL,
  `createtime` int(11) DEFAULT NULL,
  `updatetime` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `staff_id` (`staff_id`) USING BTREE,
  KEY `record_id` (`record_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='跟进记录已读记录';

CREATE TABLE IF NOT EXISTS `__PREFIX__qingdong_send_template` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` enum('sms','email') DEFAULT NULL COMMENT 'sms 短信 email邮箱',
  `number` varchar(64) DEFAULT NULL COMMENT '短信模板编号',
  `name` varchar(128) DEFAULT NULL COMMENT '模板名称',
  `content` text,
  `preview` varchar(255) DEFAULT NULL COMMENT '预览内容',
  `createtime` int(11) DEFAULT NULL,
  `updatetime` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `__PREFIX__qingdong_staff` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `admin_id` int(11) DEFAULT NULL COMMENT '关联后台账号id',
  `group_ids` varchar(100) CHARACTER SET utf8 DEFAULT NULL COMMENT '角色组列表',
  `role` tinyint(2) DEFAULT NULL COMMENT '权限列表',
  `rules` varchar(255) CHARACTER SET utf8 DEFAULT NULL COMMENT '权限列表',
  `mobile` varchar(32) CHARACTER SET utf8 DEFAULT NULL COMMENT '手机号码',
  `password` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '密码',
  `salt` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '密码盐',
  `name` varchar(64) CHARACTER SET utf8 DEFAULT NULL COMMENT '姓名',
  `nickname` varchar(64) CHARACTER SET utf8 DEFAULT NULL COMMENT '昵称',
  `img` varchar(255) CHARACTER SET utf8 DEFAULT NULL COMMENT '头像',
  `num` varchar(64) CHARACTER SET utf8 DEFAULT NULL COMMENT '员工编号',
  `email` varchar(64) CHARACTER SET utf8 DEFAULT NULL COMMENT '邮箱',
  `sex` tinyint(1) DEFAULT NULL COMMENT '1男 2女',
  `department_id` int(11) DEFAULT NULL COMMENT '部门id',
  `post` varchar(32) CHARACTER SET utf8 DEFAULT NULL COMMENT '岗位',
  `parent_id` int(11) DEFAULT NULL COMMENT '直属上级ID',
  `openid` varchar(64) CHARACTER SET utf8 DEFAULT NULL COMMENT '小程序openid',
  `unionid` varchar(64) CHARACTER SET utf8 DEFAULT NULL COMMENT '微信unionid',
  `wx_openid` varchar(64) CHARACTER SET utf8 DEFAULT NULL COMMENT '微信公众号openid',
  `touser` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '企业微信touser',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '用户状态 0待审核 1启用 2禁用',
  `createtime` int(11) DEFAULT NULL,
  `updatetime` int(11) DEFAULT NULL,
  `deletetime` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='员工列表';

CREATE TABLE IF NOT EXISTS `__PREFIX__qingdong_staff_collect` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `staff_id` int(11) NOT NULL DEFAULT '0' COMMENT '销售ID',
  `relation_type` tinyint(2) NOT NULL DEFAULT '1' COMMENT '关联类型 1 客户 2 联系人 3合同',
  `relation_id` int(11) NOT NULL DEFAULT '0' COMMENT '关联ID',
  `createtime` int(11) DEFAULT NULL,
  `updatetime` int(11) DEFAULT NULL,
  `deletetime` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=17 DEFAULT CHARSET=utf8mb4 COMMENT='重点关注表';

CREATE TABLE IF NOT EXISTS `__PREFIX__qingdong_staff_department` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(128) CHARACTER SET utf8 DEFAULT NULL COMMENT '部门名称',
  `pid` int(11) DEFAULT '0' COMMENT '上级ID',
  `createtime` int(11) DEFAULT NULL,
  `updatetime` int(11) DEFAULT NULL,
  `deletetime` int(11) DEFAULT NULL,
  `type` int(1) DEFAULT '0' COMMENT '0:钉钉  1：微信',
  `number` varchar(32) DEFAULT NULL COMMENT '微信唯一编号',
  `status` int(1) DEFAULT '0' COMMENT '0:未同步 1已同步',
  `role_id` int(11) DEFAULT NULL COMMENT '同步ID',
  `parentid` int(11) DEFAULT '0' COMMENT '子ID',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='员工部门表';

CREATE TABLE IF NOT EXISTS `__PREFIX__qingdong_staff_sign_in` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `staff_id` int(11) NOT NULL DEFAULT '0',
  `customer_id` int(11) NOT NULL DEFAULT '0' COMMENT '拜访客户',
  `location` varchar(255) CHARACTER SET utf8 DEFAULT NULL COMMENT '地理位置',
  `lng` varchar(32) CHARACTER SET utf8 DEFAULT NULL COMMENT '经度',
  `lat` varchar(32) CHARACTER SET utf8 DEFAULT NULL COMMENT '维度',
  `file_ids` varchar(128) CHARACTER SET utf8 DEFAULT NULL,
  `content` varchar(255) CHARACTER SET utf8 DEFAULT NULL,
  `createtime` int(11) DEFAULT NULL,
  `updatetime` int(11) DEFAULT NULL,
  `deletetime` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='员工签到';


CREATE TABLE IF NOT EXISTS `__PREFIX__qingdong_ku_customer` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `customer_id` int(11) DEFAULT NULL COMMENT '客户ID',
  `contacts_id` int(11) DEFAULT NULL COMMENT '联系人ID',
  `ku_id` int(11) DEFAULT NULL COMMENT 'ku客户ID',
  `code` varchar(64) CHARACTER SET utf8 DEFAULT NULL COMMENT '客户编号',
  `createtime` int(11) DEFAULT NULL,
  `updatetime` int(11) DEFAULT NULL,
  `deletetime` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='酷柚客户表';

CREATE TABLE IF NOT EXISTS `__PREFIX__qingdong_remind`
(
    `id`         int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
    `type`       varchar(32) NULL DEFAULT 'customer' COMMENT 'customer:客户 record:跟进记录 approval：工作报告',
    `staff_ids`  varchar(255) NULL DEFAULT NULL COMMENT '员工ID',
    `remark`     varchar(255) NULL DEFAULT NULL COMMENT '备注',
    `createtime` int(11) NULL DEFAULT NULL COMMENT '创建时间',
    `updatetime` int(11) NULL DEFAULT NULL COMMENT '更新时间',
    `deletetime` int(11) NULL DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`) USING BTREE
)ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='提醒表';

CREATE TABLE IF NOT EXISTS `__PREFIX__qingdong_staff_ratio`  (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `name` varchar(255) NULL DEFAULT NULL COMMENT '名称',
    `ratio` text NULL COMMENT '比例',
    `status` tinyint(1) NULL DEFAULT 0,
    `createtime` int(11) NULL DEFAULT NULL,
    `updatetime` int(11) NULL DEFAULT NULL,
    `deletetime` int(11) NULL DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE
)ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COMMENT='业绩比例分割';

CREATE TABLE IF NOT EXISTS `__PREFIX__qingdong_business` (
   `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `customer_id` int(11) NOT NULL COMMENT '客户ID',
  `contract_status` int(1) DEFAULT '0' COMMENT '成交状态 0：未成交 1：成交',
  `name` varchar(80) DEFAULT NULL COMMENT '商机名称',
  `money` varchar(10) DEFAULT NULL COMMENT '商机金额',
  `expect_time` datetime DEFAULT NULL COMMENT '预计成交时间',
  `next_time` datetime DEFAULT NULL COMMENT '下次联系时间',
  `status` varchar(30) DEFAULT NULL COMMENT '状态 验证客户、需求分析 、方案/报价 、赢单 、输单',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `create_staff_id` int(11) DEFAULT NULL COMMENT '创建人',
  `owner_staff_id` int(11) DEFAULT NULL COMMENT '负责人',
  `createtime` int(11) DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(11) DEFAULT NULL COMMENT '更新时间',
  `deletetime` int(11) DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  KEY `customer_index` (`customer_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT='商机';

CREATE TABLE IF NOT EXISTS `__PREFIX__qingdong_business_other` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '商机id',
  `otherdata` text CHARACTER SET utf8 COMMENT '自定义信息 json',
  `createtime` int(11) DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(11) DEFAULT NULL COMMENT '更新时间',
  `deletetime` int(11) DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='商机其他信息';

CREATE TABLE IF NOT EXISTS `__PREFIX__qingdong_business_product` (
   `id` int(11) NOT NULL AUTO_INCREMENT,
  `business_id` int(11) NOT NULL COMMENT '商机id',
  `product_id` int(11) DEFAULT NULL COMMENT '产品id',
  `number` tinyint(5) DEFAULT NULL COMMENT '数量',
  `price` double(10,2) DEFAULT NULL COMMENT '价格',
  `wholesale` decimal(10, 2) NULL COMMENT '批发价',
  `parts` varchar(1000) DEFAULT NULL COMMENT '产品配置',
  `ship_status` tinyint(1) DEFAULT '0' COMMENT '0 待发货 1发货 2已收货',
  `createtime` int(11) DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(11) DEFAULT NULL COMMENT '更新时间',
  `deletetime` int(11) DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT='商机产品';

CREATE TABLE IF NOT EXISTS `__PREFIX__qingdong_business_status` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `business_id` int(11) DEFAULT NULL COMMENT '商机ID',
  `type` int(1) DEFAULT '0' COMMENT '类型0:初期沟通 1：立项跟踪 2：方案/报价 3:谈判审核4:赢单5：输单 6：无效',
  `file` varchar(255) DEFAULT NULL COMMENT '图片',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `createtime` int(11) DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(11) DEFAULT NULL COMMENT '更新时间',
  `deletetime` int(11) DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  KEY `business_id` (`business_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=17 DEFAULT CHARSET=utf8 COMMENT='商机阶段表';

CREATE TABLE IF NOT EXISTS  `__PREFIX__qingdong_seas_type`
(
    `id`         int(11) NOT NULL AUTO_INCREMENT,
    `name`       varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '部门名称',
    `remarks` varchar(255) DEFAULT NULL COMMENT '备注',
    `rules` text COMMENT '权限',
    `createtime` int(11) NULL DEFAULT NULL,
    `updatetime` int(11) NULL DEFAULT NULL,
    `deletetime` int(11) NULL DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT = '公海类型';

CREATE TABLE IF NOT EXISTS  `__PREFIX__qingdong_staff_role`
(
    `id`         int(11) NOT NULL AUTO_INCREMENT,
    `name`       varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '角色名称',
    `role_type`  tinyint(1) NULL DEFAULT NULL COMMENT '角色权限',
    `rules`      text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
    `createtime` int(11) NULL DEFAULT NULL,
    `updatetime` int(11) NULL DEFAULT NULL,
    `deletetime` int(11) NULL DEFAULT NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT = '员工角色';

CREATE TABLE IF NOT EXISTS  `__PREFIX__qingdong_staff_rule`
(
    `id`     int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
    `type`   enum('menus','operation','fields','data') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'menus' COMMENT 'menu为菜单,file为权限节点',
    `pid`    int(10) UNSIGNED NULL DEFAULT 0 COMMENT '父ID',
    `name`   varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '规则名称',
    `title`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '规则名称',
    `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '备注',
    `weigh`  int(10) NULL DEFAULT 0 COMMENT '权重',
    `status` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '状态',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX    `pid`(`pid`) USING BTREE,
    INDEX    `weigh`(`weigh`) USING BTREE,
    INDEX    `name`(`name`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT = '节点表';

CREATE TABLE IF NOT EXISTS `__PREFIX__qingdong_attendance` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `staff_id` int(11) DEFAULT NULL COMMENT '员工ID',
  `statisc_id` int(11) DEFAULT NULL COMMENT '统计id',
  `type` int(1) DEFAULT '0' COMMENT '0:上下班打卡 1：外勤',
  `time` varchar(30) DEFAULT NULL COMMENT '打卡时间',
  `address` varchar(150) DEFAULT NULL COMMENT '打卡位置',
  `type_name` int(1) DEFAULT '0' COMMENT '0上班，1下班 2 外勤',
  `late` int(1) DEFAULT '0' COMMENT '迟到0：否 1：是',
  `late_time` int(11) DEFAULT NULL COMMENT '迟到时间(分)',
  `leaver` int(1) DEFAULT '0' COMMENT '早退 0：否 1：是',
  `leaver_time` int(11) DEFAULT NULL COMMENT '早退时间(分)',
  `lng` varchar(50) DEFAULT NULL COMMENT '经度',
  `lat` varchar(50) DEFAULT NULL COMMENT '纬度',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `status` int(1) DEFAULT '0' COMMENT '是否打卡 0：否 1是',
  `remarks` varchar(255) DEFAULT NULL COMMENT '备注',
  `file_ids` varchar(128) DEFAULT NULL COMMENT '附件',
  `createtime` int(11) DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(11) DEFAULT NULL COMMENT '修改时间',
  `deletetime` int(11) DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT='考勤打卡表';
CREATE TABLE IF NOT EXISTS `__PREFIX__qingdong_attendance_address` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `address` varchar(200) DEFAULT NULL COMMENT '地点',
  `address_detail` varchar(255) DEFAULT NULL COMMENT '地址',
  `distance` int(10) DEFAULT '0' COMMENT '范围(米)',
  `lng` varchar(50) DEFAULT NULL COMMENT '经度',
  `lat` varchar(50) DEFAULT NULL COMMENT '纬度',
  `createtime` int(11) DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(11) DEFAULT NULL COMMENT '更新时间',
  `deletetime` int(11) DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT='考勤位置';
CREATE TABLE IF NOT EXISTS `__PREFIX__qingdong_attendance_card` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `statisc_id` int(11) DEFAULT NULL COMMENT '考勤ID',
  `type` int(1) DEFAULT '0' COMMENT '0:补卡 1：早退 2：迟到',
  `time` datetime DEFAULT NULL COMMENT '补卡时间',
  `title` varchar(255) DEFAULT NULL COMMENT '补卡原因',
  `remark` varchar(255) DEFAULT NULL COMMENT '补卡事由',
  `file_ids` varchar(255) DEFAULT NULL COMMENT '附件',
  `check_status` int(1) DEFAULT '0' COMMENT '0待审核、1审核中、2审核通过、3审核未通过、4撤销',
  `flow_id` int(11) DEFAULT NULL COMMENT '审核流程ID',
  `order_id` int(11) DEFAULT NULL COMMENT '审批流程步骤id',
  `flow_staff_ids` varchar(255) DEFAULT NULL COMMENT '流程审批人ID',
  `check_staff_ids` varchar(255) DEFAULT NULL COMMENT '已审批人IDs',
  `create_staff_id` int(11) DEFAULT NULL COMMENT '创建人ID',
  `owner_staff_id` int(11) DEFAULT NULL COMMENT '负责人ID',
  `createtime` int(11) DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(11) DEFAULT NULL COMMENT '更新时间',
  `deletetime` int(11) DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  KEY `attendance_index` (`statisc_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT='补卡表';
CREATE TABLE IF NOT EXISTS `__PREFIX__qingdong_attendance_leave` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `type` varchar(255) DEFAULT NULL COMMENT '请假类型',
  `start_time` varchar(64) DEFAULT NULL COMMENT '开始时间',
  `end_time` varchar(64) DEFAULT NULL COMMENT '结束时间',
  `hour` varchar(32) DEFAULT NULL COMMENT '时长',
  `remarks` varchar(255) DEFAULT NULL COMMENT '备注',
  `file_ids` varchar(128) DEFAULT NULL COMMENT '附件',
  `create_staff_id` int(11) DEFAULT NULL,
  `createtime` int(11) DEFAULT NULL,
  `updatetime` int(11) DEFAULT NULL,
  `deletetime` int(11) DEFAULT NULL,
  `flow_id` int(11) DEFAULT NULL COMMENT '审核流程ID',
  `order_id` int(11) DEFAULT NULL COMMENT '审批流程步骤id',
  `flow_staff_ids` varchar(255) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '流程审批人ID',
  `check_staff_ids` varchar(255) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '已审批人IDs',
  `check_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0待审核、1审核中、2审核通过、3审核未通过、4撤销、',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='考勤请假';
CREATE TABLE IF NOT EXISTS `__PREFIX__qingdong_attendance_rule` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(50) DEFAULT NULL COMMENT '规则名称',
  `staff_id` text COMMENT '员工ID',
  `type` tinyint(1) DEFAULT '0' COMMENT '0:上班 1：外出',
  `createtime` int(11) DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(11) DEFAULT NULL COMMENT '更新时间',
  `deletetime` int(11) DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT='考勤规则';
CREATE TABLE IF NOT EXISTS `__PREFIX__qingdong_attendance_statisc` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `staff_id` int(11) DEFAULT NULL COMMENT '员工ID',
  `number` varchar(32) DEFAULT NULL COMMENT '打卡编码',
  `time` date DEFAULT NULL COMMENT '统计时间',
  `clock_in` datetime DEFAULT NULL COMMENT '上班打卡',
  `clock_in_status` tinyint(2) DEFAULT '0' COMMENT '0 正常 1 缺卡 2 旷工 3 迟到/早退打卡 5 补卡 6请假',
  `late_time` int(11) DEFAULT '0' COMMENT '迟到时间(分)',
  `clock_out` datetime DEFAULT NULL COMMENT '下班打卡',
  `clock_out_status` tinyint(2) DEFAULT '0' COMMENT '0 正常 1 缺卡 2 旷工  3 迟到/早退打卡  5 补卡 6请假',
  `leaver_time` int(11) DEFAULT '0' COMMENT '早退时间(分)',
  `createtime` int(11) DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(11) DEFAULT NULL COMMENT '修改时间',
  `deletetime` int(11) DEFAULT NULL COMMENT '删除时间',
  `start_time` varchar(20) DEFAULT NULL COMMENT '开始时间',
  `end_time` varchar(20) DEFAULT NULL COMMENT '结束时间',
  `ustart_time` varchar(20) DEFAULT NULL COMMENT '可打卡开始时间',
  `uend_time` varchar(20) DEFAULT NULL COMMENT '可打卡结束时间',
  `dstart_time` varchar(20) DEFAULT NULL COMMENT '下班可打卡开始时间',
  `dend_time` varchar(20) DEFAULT NULL COMMENT '下班可打卡结束时间',
  `start_status` tinyint(1) DEFAULT '0' COMMENT '上班打卡0:开启 1：关闭',
  `end_status` tinyint(1) DEFAULT '0' COMMENT '下班打卡0:开启 1：关闭',
  PRIMARY KEY (`id`),
  KEY `time_index` (`time`) USING BTREE,
  KEY `staff_index` (`staff_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT='考勤统计表';
CREATE TABLE IF NOT EXISTS `__PREFIX__qingdong_attendance_time` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `number` varchar(32) DEFAULT NULL COMMENT '编码',
  `start_time` varchar(20) DEFAULT NULL COMMENT '开始时间',
  `end_time` varchar(20) DEFAULT NULL COMMENT '结束时间',
  `ustart_time` varchar(20) DEFAULT NULL COMMENT '可打卡开始时间',
  `uend_time` varchar(20) DEFAULT NULL COMMENT '可打卡结束时间',
  `dstart_time` varchar(20) DEFAULT NULL COMMENT '下班可打卡开始时间',
  `dend_time` varchar(20) DEFAULT NULL COMMENT '下班可打卡结束时间',
  `start_next` int(11) DEFAULT '0' COMMENT '0:非次日   1：次日',
  `end_next` int(11) DEFAULT NULL COMMENT '0:非次日   1：次日',
  `start_status` int(1) DEFAULT '0' COMMENT '上班打卡0:开启 1：关闭',
  `end_status` int(1) DEFAULT '0' COMMENT '下班打卡0:开启 1：关闭',
  `monday` int(1) DEFAULT '0' COMMENT '周一  0未选择 1：已选择',
  `tuesday` int(1) DEFAULT '0' COMMENT '周二  0未选择 1：已选择',
  `wednesday` int(1) DEFAULT '0' COMMENT '周三  0未选择 1：已选择',
  `thursday` int(1) DEFAULT '0' COMMENT '周四  0未选择 1：已选择',
  `friday` int(1) DEFAULT '0' COMMENT '周五  0未选择 1：已选择',
  `saturday` int(1) DEFAULT '0' COMMENT '周六  0未选择 1：已选择',
  `weekday` int(1) DEFAULT '0' COMMENT '周日  0未选择 1：已选择',
  `createtime` int(11) DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(11) DEFAULT NULL COMMENT '更新时间',
  `deletetime` int(11) DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  KEY `number_index` (`number`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT='考勤时间';
CREATE TABLE IF NOT EXISTS `__PREFIX__qingdong_form_field`  (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `types` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '分类',
  `types_id` int(11) NOT NULL DEFAULT 0 COMMENT '分类ID(审批等)',
  `field` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '字段名',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '标识名',
  `form_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '字段类型',
  `info_type` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'main' COMMENT '信息类型',
  `default_value` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '默认值',
  `max_length` int(4) NULL DEFAULT 0 COMMENT '字数上限',
  `is_unique` tinyint(1) NULL DEFAULT 0 COMMENT '是否唯一(1是，0否)',
  `is_null` tinyint(1) NULL DEFAULT 0 COMMENT '是否必填(1是，0否)',
  `list_show` tinyint(1) NULL DEFAULT 1 COMMENT '列表显示(1是，0否)',
  `add_show` tinyint(1) NULL DEFAULT 1 COMMENT '添加显示(1是，0否)',
  `input_tips` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '输入提示',
  `setting` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '设置',
  `order_id` int(4) NOT NULL DEFAULT 0 COMMENT '排序ID',
  `operating` tinyint(1) NOT NULL DEFAULT 0 COMMENT '0改删，1改，2删，3无',
  `createtime` int(11) NOT NULL COMMENT '创建时间',
  `updatetime` int(11) NULL DEFAULT NULL COMMENT '更新时间',
  `deletetime` int(11) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT='表单字段';
CREATE TABLE IF NOT EXISTS `__PREFIX__qingdong_goods`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `num` varchar(64) DEFAULT NULL COMMENT '编号',
  `type_id` int(11) NULL DEFAULT NULL COMMENT '分类ID',
  `specs_id` int(11) DEFAULT NULL COMMENT '规格',
  `is_specs` tinyint(1) NULL DEFAULT NULL COMMENT '是否有规格 0 1:启用',
  `img` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `name` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '产品名称',
  `unit` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '箱' COMMENT '单位',
  `price` decimal(18, 2) NULL DEFAULT 0.00 COMMENT '价格',
  `min_price` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '最低销售价',
  `cost_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '成本价',
  `wholesale` decimal(10,2) DEFAULT '0.00' COMMENT '批发价',
  `status` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '上架' COMMENT '是否上架',
  `description` text COMMENT '产品描述',
  `createtime` int(11) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(11) NULL DEFAULT NULL COMMENT '更新时间',
  `deletetime` int(10) NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT='商品管理';
CREATE TABLE IF NOT EXISTS `__PREFIX__qingdong_word_template` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `type` varchar(32) DEFAULT NULL COMMENT '类型',
    `name` varchar(128) DEFAULT NULL COMMENT '模板名称',
    `url` varchar(255) DEFAULT NULL COMMENT '模板地址',
    `createtime` int(11) DEFAULT NULL,
    `updatetime` int(11) DEFAULT NULL,
    `deletetime` int(10) NULL DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT='word模板';
CREATE TABLE IF NOT EXISTS `__PREFIX__qingdong_goods_unit` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(128) NOT NULL DEFAULT '' COMMENT '名称',
  `sort` int(10) DEFAULT '0' COMMENT '权重',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态 0：隐藏  1：显示',
  `createtime` int(11) DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(11) DEFAULT NULL COMMENT '更新时间',
  `deletetime` int(10) DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8 COMMENT='商品单位';
CREATE TABLE IF NOT EXISTS `__PREFIX__qingdong_specs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `pid` int(11) DEFAULT NULL COMMENT '上级',
  `name` varchar(128) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '规格名称',
  `content` text CHARACTER SET utf8 COMMENT '详情',
  `status` tinyint(1) DEFAULT NULL COMMENT '状态 1启用 0',
  `createtime` int(11) DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(11) DEFAULT NULL COMMENT '更新时间',
  `deletetime` int(10) DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4  COMMENT='规格';




INSERT INTO `__PREFIX__qingdong_admin_config`
VALUES (1, 'seas', 'auto', '1', 1631875558, 1631875558, NULL);
INSERT INTO `__PREFIX__qingdong_admin_config`
VALUES (2, 'seas', 'genjing', '1', 1631875558, 1631875558, NULL);
INSERT INTO `__PREFIX__qingdong_admin_config`
VALUES (3, 'seas', 'genjing_success', '1', 1631875558, 1631875558, NULL);
INSERT INTO `__PREFIX__qingdong_admin_config`
VALUES (4, 'seas', 'genjing_day', '0', 1631875558, 1631875558, NULL);
INSERT INTO `__PREFIX__qingdong_admin_config`
VALUES (5, 'seas', 'chengjiao', '0', 1631875558, 1631875558, NULL);
INSERT INTO `__PREFIX__qingdong_admin_config`
VALUES (6, 'seas', 'chengjiao_day', '0', 1631875558, 1631875558, NULL);
INSERT INTO `__PREFIX__qingdong_admin_config`
VALUES (7, 'wechat', 'mini_appid', '', 1631875559, 1631875559, NULL);
INSERT INTO `__PREFIX__qingdong_admin_config`
VALUES (8, 'wechat', 'mini_secret', '', 1631875559, 1631875559, NULL);
INSERT INTO `__PREFIX__qingdong_admin_config`
VALUES (9, 'wechat', 'appid', '', 1631875559, 1631875559, NULL);
INSERT INTO `__PREFIX__qingdong_admin_config`
VALUES (10, 'wechat', 'secret', '', 1631875559, 1631875559, NULL);
INSERT INTO `__PREFIX__qingdong_admin_config`
VALUES (11, 'wechat', 'examine_template', '0', 1631875559, 1631875559, NULL);
INSERT INTO `__PREFIX__qingdong_admin_config`
VALUES (12, 'wechat', 'record_template', '0', 1631875559, 1631875559, NULL);
INSERT INTO `__PREFIX__qingdong_admin_config`
VALUES (13, 'wechat', 'map_key', '', 1631875559, 1631875559, NULL);
INSERT INTO `__PREFIX__qingdong_admin_config`
VALUES (14, 'wechat', 'web_url', '', 1631875559, 1631875559, NULL);
INSERT INTO `__PREFIX__qingdong_admin_config`
VALUES (15, 'dingding', 'ding_key', '', 1631875559, 1631875559, NULL);
INSERT INTO `__PREFIX__qingdong_admin_config`
VALUES (16, 'dingding', 'ding_secret', '', 1631875559, 1631875559, NULL);
INSERT INTO `__PREFIX__qingdong_field`
VALUES (138, 'leads', '线索来源', '[\"电话营销\",\"拜访\"]');
INSERT INTO `__PREFIX__qingdong_field`
VALUES (139, 'leads', '客户行业', '[\"互联网\",\"信息化\"]');
INSERT INTO `__PREFIX__qingdong_field`
VALUES (141, 'examine', '回款方式', '[\"微信\"]');
INSERT INTO `__PREFIX__qingdong_field`
VALUES (147, 'contacts', '角色',
        '[\"普通员工\",\"决策人\",\"分项决策人\",\"商务决策\",\"财务决策\",\"使用人\",\"意见影响人\",\"采购\",\"老板\",\"股东\",\"职业经理人\"]');
INSERT INTO `__PREFIX__qingdong_field`
VALUES (148, 'other', '跟进类型', '[\"到访\",\"电话\"]');
INSERT INTO `__PREFIX__qingdong_field`
VALUES (149, 'other', '消费方式', '[\"吃饭\",\"住酒店\"]');
INSERT INTO `__PREFIX__qingdong_field`
VALUES (151, 'customer', '客户所属', '[\"重点客户\",\"普通客户\",\"非优先客户\"]');
INSERT INTO `__PREFIX__qingdong_field`
VALUES (152, 'customer', '客户状态', '[\"初次接触\",\"正在跟进\",\"准备购买\",\"准备付款\",\"已经购买\",\"暂时搁置\"]');
INSERT INTO `__PREFIX__qingdong_field`
VALUES (153, 'customer', '客户来源', '[\"行业活动\",\"搜索引擎\",\"客户转介绍\",\"展会\",\"朋友介绍\",\"市场推广\"]');
INSERT INTO `__PREFIX__qingdong_field`
VALUES (158, 'daily', '报告类型', '[\"日报\",\"周报\",\"月报\",\"季报\",\"年报\"]');
INSERT INTO `__PREFIX__qingdong_form` VALUES (11, '线索管理', 'leads', '{\"data\":[{\"id\":\"name\",\"name\":\"单行输入框\",\"type\":1,\"component\":\"input\",\"config\":{\"type\":\"text\",\"label\":\"线索名称\",\"readonly\":false,\"placeholder\":\"线索名称\",\"required\":true,\"is_delete\":true}},{\"id\":\"source\",\"name\":\"下拉选择框\",\"type\":1,\"component\":\"select\",\"config\":{\"label\":\"线索来源\",\"type\":null,\"content\":[{\"key\":\"\",\"value\":\"电话营销\",\"nodeKey\":1,\"isEdit\":false,\"label\":\"电话营销\",\"children\":[],\"expand\":true,\"__label\":\"电话营销\",\"__value\":[\"电话营销\"]},{\"key\":\"\",\"value\":\"主动来电\",\"__label\":\"拜访\",\"__value\":[\"主动来电\"],\"nodeKey\":2,\"isEdit\":false,\"label\":\"拜访\"}],\"required\":true,\"multiple\":false,\"is_delete\":true,\"placeholder\":\"请选择线索来源\"}},{\"id\":\"industry\",\"name\":\"下拉选择框\",\"type\":1,\"component\":\"select\",\"config\":{\"label\":\"客户行业\",\"type\":null,\"content\":[{\"key\":\"\",\"value\":\"行业1\",\"__label\":\"互联网\",\"__value\":[\"行业1\"],\"nodeKey\":1,\"isEdit\":false,\"label\":\"互联网\"},{\"key\":\"\",\"value\":\"行业2\",\"__value\":[\"行业2\"],\"nodeKey\":2,\"isEdit\":false,\"label\":\"信息化\",\"__label\":\"信息化\"}],\"required\":true,\"multiple\":false,\"is_delete\":true,\"placeholder\":\"请选择客户行业\"}},{\"id\":\"mobile\",\"name\":\"单行输入框\",\"type\":1,\"component\":\"input\",\"config\":{\"type\":\"text\",\"label\":\"手机号\",\"readonly\":false,\"placeholder\":\"手机号\",\"required\":true,\"is_delete\":true}},{\"id\":\"level\",\"name\":\"评分\",\"type\":1,\"component\":\"Rate\",\"config\":{\"label\":\"客户级别\",\"allowHalf\":true,\"disabled\":false,\"type\":\"default\",\"is_delete\":true}},{\"id\":\"telephone\",\"name\":\"单行输入框\",\"type\":1,\"component\":\"input\",\"config\":{\"type\":\"text\",\"label\":\"电话\",\"readonly\":false,\"placeholder\":\"电话\",\"required\":false,\"is_delete\":true}},{\"id\":\"address_detail\",\"name\":\"文本框\",\"type\":1,\"component\":\"textarea\",\"config\":{\"label\":\"地址\",\"readonly\":false,\"row\":2,\"placeholder\":\"请输入地址\",\"required\":false,\"is_delete\":true}},{\"id\":\"next_time\",\"name\":\"时间选择框\",\"type\":1,\"component\":\"TimePicker\",\"config\":{\"label\":\"下次联系时间\",\"placeholder\":\"点击选择\",\"type\":\"time\",\"format\":\"YYYY-MM-DD HH:mm:ss\",\"confirm\":true,\"required\":false,\"readonly\":false,\"is_delete\":true}},{\"id\":\"remarks\",\"name\":\"文本框\",\"type\":1,\"component\":\"textarea\",\"config\":{\"label\":\"备注信息\",\"readonly\":false,\"row\":2,\"placeholder\":\"备注信息\",\"required\":false,\"is_delete\":true}}]}', 1619592288, 1655433791, NULL);


INSERT INTO `__PREFIX__qingdong_form`
VALUES (12, '客户管理', 'customer', '{\"data\":[{\"id\":\"name\",\"name\":\"单行输入框\",\"type\":1,\"component\":\"input\",\"config\":{\"type\":\"text\",\"label\":\"客户名称\",\"readonly\":false,\"placeholder\":\"请输入客户名称\",\"required\":true,\"is_delete\":false}},{\"id\":\"subname\",\"name\":\"单行输入框\",\"type\":1,\"component\":\"input\",\"config\":{\"type\":\"text\",\"label\":\"助记名称\",\"readonly\":false,\"placeholder\":\"请输入助记名称\",\"required\":false,\"is_delete\":true}},{\"id\":\"mobile\",\"name\":\"数字框\",\"type\":1,\"value\":\"\",\"component\":\"input\",\"config\":{\"label\":\"手机号\",\"placeholder\":\"请输入手机号\",\"required\":true,\"is_delete\":true,\"value\":\"\",\"infoType\":\"main\",\"only\":false,\"listShow\":false,\"addShow\":false}},{\"id\":\"industry\",\"name\":\"下拉选择框\",\"type\":1,\"component\":\"select\",\"config\":{\"label\":\"客户所属\",\"type\":null,\"content\":[{\"key\":\"\",\"value\":\"终端客户\",\"__label\":\"重点客户\",\"__value\":[\"终端客户\"],\"nodeKey\":1,\"isEdit\":false,\"label\":\"重点客户\"},{\"key\":\"\",\"value\":\"代理商\",\"__label\":\"普通客户\",\"__value\":[\"代理商\"],\"nodeKey\":2,\"isEdit\":false,\"label\":\"普通客户\"},{\"key\":\"\",\"value\":\"同行\",\"__value\":[\"同行\"],\"nodeKey\":3,\"isEdit\":false,\"label\":\"非优先客户\",\"__label\":\"非优先客户\"}],\"required\":true,\"multiple\":false,\"is_delete\":true,\"placeholder\":\"请输入客户所属\"}},{\"id\":\"follow\",\"name\":\"下拉选择框\",\"type\":1,\"component\":\"select\",\"config\":{\"label\":\"客户状态\",\"type\":null,\"content\":[{\"key\":\"\",\"value\":\"初次接触\",\"__label\":\"初次接触\",\"__value\":[\"初次接触\"],\"nodeKey\":1,\"isEdit\":false,\"label\":\"初次接触\"},{\"key\":\"\",\"value\":\"正在跟进\",\"__label\":\"正在跟进\",\"__value\":[\"正在跟进\"],\"nodeKey\":2,\"isEdit\":false,\"label\":\"正在跟进\"},{\"key\":\"\",\"value\":\"准备购买\",\"__label\":\"准备购买\",\"__value\":[\"准备购买\"],\"nodeKey\":3,\"isEdit\":false,\"label\":\"准备购买\"},{\"key\":\"\",\"value\":\"准备付款\",\"__label\":\"准备付款\",\"__value\":[\"准备付款\"],\"nodeKey\":4,\"isEdit\":false,\"label\":\"准备付款\"},{\"key\":\"\",\"value\":\"已经购买\",\"__label\":\"已经购买\",\"__value\":[\"已经购买\"],\"nodeKey\":5,\"isEdit\":false,\"label\":\"已经购买\"},{\"key\":\"\",\"value\":\"暂时搁置\",\"__label\":\"暂时搁置\",\"__value\":[\"暂时搁置\"],\"nodeKey\":6,\"isEdit\":false,\"label\":\"暂时搁置\"}],\"required\":true,\"multiple\":false,\"is_delete\":true,\"placeholder\":\"请输入客户状态\"}},{\"id\":\"level\",\"name\":\"评分\",\"type\":1,\"component\":\"Rate\",\"config\":{\"label\":\"客户星级\",\"allowHalf\":true,\"disabled\":false,\"type\":\"default\",\"is_delete\":true,\"placeholder\":\"客户星级\"}},{\"id\":\"source\",\"name\":\"下拉选择框\",\"type\":1,\"component\":\"select\",\"config\":{\"label\":\"客户来源\",\"type\":null,\"content\":[{\"key\":\"\",\"value\":\"行业活动\",\"__label\":\"行业活动\",\"__value\":[\"行业活动\"],\"nodeKey\":1,\"isEdit\":false,\"label\":\"行业活动\"},{\"key\":\"\",\"value\":\"搜索引擎\",\"__label\":\"搜索引擎\",\"__value\":[\"搜索引擎\"],\"nodeKey\":2,\"isEdit\":false,\"label\":\"搜索引擎\"},{\"key\":\"\",\"value\":\"客户转介绍\",\"__label\":\"客户转介绍\",\"__value\":[\"客户转介绍\"],\"nodeKey\":3,\"isEdit\":false,\"label\":\"客户转介绍\"},{\"key\":\"\",\"value\":\"展会\",\"__label\":\"展会\",\"__value\":[\"展会\"],\"nodeKey\":4,\"isEdit\":false,\"label\":\"展会\"},{\"key\":\"\",\"value\":\"朋友介绍\",\"__label\":\"朋友介绍\",\"__value\":[\"朋友介绍\"],\"nodeKey\":5,\"isEdit\":false,\"label\":\"朋友介绍\"},{\"key\":\"\",\"value\":\"市场推广\",\"__value\":[\"市场推广\"],\"nodeKey\":6,\"isEdit\":false,\"label\":\"市场推广\",\"__label\":\"市场推广\"}],\"required\":true,\"multiple\":false,\"is_delete\":true,\"placeholder\":\"客户来源\"}},{\"id\":\"other_12771\",\"name\":\"文本框\",\"type\":1,\"component\":\"textarea\",\"config\":{\"label\":\"备注信息\",\"row\":2,\"placeholder\":\"请输入备注信息\",\"required\":false,\"is_delete\":true}}],\"isAddContact\":false}', 1619592291, 1663896541, NULL);
INSERT INTO `__PREFIX__qingdong_form`
VALUES (13, '联系人管理', 'contacts',
        '{\"data\":[{\"id\":\"name\",\"name\":\"单行输入框\",\"type\":1,\"component\":\"input\",\"config\":{\"type\":\"text\",\"label\":\"联系人姓名\",\"readonly\":false,\"placeholder\":\"请输入联系人姓名\",\"required\":true,\"is_delete\":false}},{\"id\":\"subname\",\"name\":\"单行输入框\",\"type\":1,\"component\":\"input\",\"config\":{\"type\":\"text\",\"label\":\"尊称\",\"readonly\":false,\"placeholder\":\"请输入尊称\",\"required\":false,\"is_delete\":true}},{\"id\":\"other_21517\",\"name\":\"单行输入框\",\"type\":1,\"component\":\"input\",\"config\":{\"type\":\"text\",\"label\":\"电话\",\"placeholder\":\"请输入联系人电话\",\"required\":false,\"is_delete\":true}},{\"id\":\"mobile\",\"name\":\"单行输入框\",\"type\":1,\"component\":\"input\",\"config\":{\"type\":\"text\",\"label\":\"手机号\",\"placeholder\":\"请输入联系人手机号\",\"required\":true,\"is_delete\":false}},{\"id\":\"role\",\"name\":\"下拉选择框\",\"type\":1,\"component\":\"select\",\"config\":{\"label\":\"角色\",\"type\":null,\"content\":[{\"key\":\"\",\"value\":\"普通员工\",\"__label\":\"普通员工\",\"__value\":[\"普通员工\"],\"nodeKey\":1,\"isEdit\":false,\"label\":\"普通员工\"},{\"key\":\"\",\"value\":\"决策人\",\"__label\":\"决策人\",\"__value\":[\"决策人\"],\"nodeKey\":2,\"isEdit\":false,\"label\":\"决策人\"},{\"key\":\"\",\"value\":\"分项决策人\",\"__label\":\"分项决策人\",\"__value\":[\"分项决策人\"],\"nodeKey\":3,\"isEdit\":false,\"label\":\"分项决策人\"},{\"key\":\"\",\"value\":\"商务决策\",\"__label\":\"商务决策\",\"__value\":[\"商务决策\"],\"nodeKey\":4,\"isEdit\":false,\"label\":\"商务决策\"},{\"key\":\"\",\"value\":\"财务决策\",\"__label\":\"财务决策\",\"__value\":[\"财务决策\"],\"nodeKey\":5,\"isEdit\":false,\"label\":\"财务决策\"},{\"key\":\"\",\"value\":\"使用人\",\"__label\":\"使用人\",\"__value\":[\"使用人\"],\"nodeKey\":6,\"isEdit\":false,\"label\":\"使用人\"},{\"key\":\"\",\"value\":\"意见影响人\",\"__label\":\"意见影响人\",\"__value\":[\"意见影响人\"],\"nodeKey\":7,\"isEdit\":false,\"label\":\"意见影响人\"},{\"key\":\"\",\"value\":\"采购\",\"__label\":\"采购\",\"__value\":[\"采购\"],\"nodeKey\":8,\"isEdit\":false,\"label\":\"采购\"},{\"key\":\"\",\"value\":\"老板\",\"__label\":\"老板\",\"__value\":[\"老板\"],\"nodeKey\":9,\"isEdit\":false,\"label\":\"老板\"},{\"key\":\"\",\"value\":\"股东\",\"__label\":\"股东\",\"__value\":[\"股东\"],\"nodeKey\":10,\"isEdit\":false,\"label\":\"股东\"},{\"key\":\"\",\"value\":\"职业经理人\",\"__value\":[\"职业经理人\"],\"nodeKey\":11,\"isEdit\":false,\"label\":\"职业经理人\",\"__label\":\"职业经理人\"}],\"required\":true,\"multiple\":false,\"is_delete\":true,\"placeholder\":\"请选择角色\"}},{\"id\":\"post\",\"name\":\"单行输入框\",\"type\":1,\"component\":\"input\",\"config\":{\"type\":\"text\",\"label\":\"部门职务\",\"placeholder\":\"请输入部门职务\",\"required\":true,\"is_delete\":true}},{\"id\":\"other_4367\",\"name\":\"日期选择框\",\"type\":1,\"component\":\"DatePicker\",\"config\":{\"label\":\"生日\",\"placeholder\":\"请输入联系人生日\",\"type\":\"date\",\"value\":\"\",\"required\":false,\"is_delete\":true}},{\"id\":\"email\",\"name\":\"单行输入框\",\"type\":1,\"component\":\"input\",\"config\":{\"type\":\"text\",\"label\":\"邮箱\",\"placeholder\":\"请输入邮箱\",\"required\":false,\"is_delete\":true}},{\"id\":\"other_17344\",\"name\":\"单行输入框\",\"type\":1,\"component\":\"input\",\"config\":{\"type\":\"text\",\"label\":\"微信\",\"placeholder\":\"请输入联系人微信\",\"required\":false,\"is_delete\":true}},{\"id\":\"other_23536\",\"name\":\"文本框\",\"type\":1,\"component\":\"textarea\",\"config\":{\"label\":\"备注信息\",\"row\":2,\"placeholder\":\"请输入备注信息\",\"required\":false,\"is_delete\":true}}]}',
        1619592291, 1643096926, NULL);
INSERT INTO `__PREFIX__qingdong_form`
VALUES (14, '合同管理', 'contract',
        '{\"data\":[{\"id\":\"num\",\"name\":\"单行输入框\",\"type\":1,\"component\":\"input\",\"config\":{\"type\":\"text\",\"label\":\"合同编号\",\"readonly\":false,\"placeholder\":\"输入合同编号\",\"required\":true,\"is_delete\":false}},{\"id\":\"name\",\"name\":\"单行输入框\",\"type\":1,\"component\":\"input\",\"config\":{\"type\":\"text\",\"label\":\"合同名称\",\"readonly\":false,\"placeholder\":\"请输入合同名称\",\"required\":true,\"is_delete\":false}},{\"id\":\"money\",\"name\":\"单行输入框\",\"type\":1,\"component\":\"input\",\"config\":{\"type\":\"text\",\"label\":\"合同金额\",\"readonly\":false,\"placeholder\":\"请输入合同金额\",\"required\":true,\"is_delete\":false}},{\"id\":\"order_date\",\"name\":\"时间选择框\",\"type\":1,\"component\":\"TimePicker\",\"config\":{\"label\":\"签约时间\",\"placeholder\":\"点击选择\",\"type\":\"time\",\"format\":\"HH:mm:ss\",\"confirm\":true,\"required\":true,\"readonly\":false,\"is_delete\":false}},{\"id\":\"start_time\",\"name\":\"日期选择框\",\"type\":1,\"component\":\"DatePicker\",\"config\":{\"label\":\"合同生效时间\",\"placeholder\":\"点击选择\",\"type\":\"date\",\"format\":\"\",\"value\":\"\",\"required\":true,\"readonly\":false,\"is_delete\":false}},{\"id\":\"end_time\",\"name\":\"日期选择框\",\"type\":1,\"component\":\"DatePicker\",\"config\":{\"label\":\"合同到期时间\",\"placeholder\":\"点击选择\",\"type\":\"date\",\"format\":\"\",\"value\":\"\",\"required\":true,\"readonly\":false,\"is_delete\":false}},{\"id\":\"other_47006\",\"name\":\"单选框\",\"type\":1,\"component\":\"radio\",\"config\":{\"label\":\"是否含税\",\"content\":[{\"key\":\"\",\"value\":\"含税\"},{\"key\":\"\",\"value\":\"不含税\"}],\"placeholder\":\"请输入\",\"required\":false,\"is_delete\":true}}]}',
        1619592291, 1643020154, NULL);
INSERT INTO `__PREFIX__qingdong_form`
VALUES (15, '回款管理', 'examine',
        '{\"data\":[{\"id\":\"number\",\"name\":\"单行输入框\",\"type\":1,\"component\":\"input\",\"config\":{\"type\":\"text\",\"label\":\"回款编号\",\"placeholder\":\"请输入回款编号\",\"required\":true,\"is_delete\":false}},{\"id\":\"return_time\",\"name\":\"日期选择框\",\"type\":1,\"component\":\"DatePicker\",\"config\":{\"label\":\"回款日期\",\"placeholder\":\"点击选择\",\"type\":\"date\",\"value\":\"\",\"required\":true,\"is_delete\":false}},{\"id\":\"money\",\"name\":\"单行输入框\",\"type\":1,\"component\":\"input\",\"config\":{\"type\":\"text\",\"label\":\"回款金额\",\"placeholder\":\"请输入回款金额\",\"required\":true,\"is_delete\":false}},{\"id\":\"return_type\",\"name\":\"下拉选择框\",\"type\":1,\"component\":\"select\",\"config\":{\"label\":\"回款方式\",\"type\":null,\"content\":[{\"key\":\"\",\"value\":\"微信\",\"__label\":\"微信\",\"__value\":[\"微信\"],\"nodeKey\":1,\"isEdit\":false,\"label\":\"微信\"}],\"required\":true,\"is_delete\":true}}]}',
        1619592291, 1643020163, NULL);
INSERT INTO `__PREFIX__qingdong_form` VALUES (16, '日报管理', 'daily', '{\"data\":[{\"id\":\"other_43472\",\"name\":\"文本框\",\"type\":1,\"component\":\"textarea\",\"config\":{\"label\":\"1.1.完成了哪些工作\",\"row\":2,\"placeholder\":\"请输入内容\",\"required\":true,\"is_delete\":true}},{\"id\":\"other_48871\",\"name\":\"文本框\",\"type\":1,\"component\":\"textarea\",\"config\":{\"label\":\"1.2.取得了哪些成绩\",\"row\":2,\"placeholder\":\"请输入内容\",\"required\":true,\"is_delete\":true}},{\"id\":\"other_33048\",\"name\":\"文本框\",\"type\":1,\"component\":\"textarea\",\"config\":{\"label\":\"1.3.当前进展如何\",\"row\":2,\"placeholder\":\"请输入内容\",\"required\":true,\"is_delete\":true}},{\"id\":\"other_82389\",\"name\":\"文本框\",\"type\":1,\"component\":\"textarea\",\"config\":{\"label\":\"1.4.计划实施情况\",\"row\":2,\"placeholder\":\"请输入内容\",\"required\":true,\"is_delete\":true}}]}', 1619491859, 1640141699, NULL);
INSERT INTO `__PREFIX__qingdong_form` VALUES (17, '周报管理', 'weekly', '{\"data\":[{\"id\":\"other_72263\",\"name\":\"文本框\",\"type\":1,\"component\":\"textarea\",\"config\":{\"label\":\"1.1.完成了哪些工作\",\"row\":2,\"placeholder\":\"请输入内容\",\"required\":false,\"is_delete\":true}},{\"id\":\"other_22415\",\"name\":\"文本框\",\"type\":1,\"component\":\"textarea\",\"config\":{\"label\":\"1.2.取得了哪些成绩\",\"row\":2,\"placeholder\":\"请输入内容\",\"required\":false,\"is_delete\":true}},{\"id\":\"other_41856\",\"name\":\"文本框\",\"type\":1,\"component\":\"textarea\",\"config\":{\"label\":\"1.3.当前进展如何\",\"row\":2,\"placeholder\":\"请输入内容\",\"required\":false,\"is_delete\":true}}]}', 1643020163, 1643020163, NULL);
INSERT INTO `__PREFIX__qingdong_form` VALUES (18, '月报管理', 'monthly', '{\"data\":[{\"id\":\"other_79661\",\"name\":\"文本框\",\"type\":1,\"component\":\"textarea\",\"config\":{\"label\":\"1.1.完成了哪些工作\",\"row\":2,\"placeholder\":\"请输入内容\",\"required\":false,\"is_delete\":true}},{\"id\":\"other_52344\",\"name\":\"文本框\",\"type\":1,\"component\":\"textarea\",\"config\":{\"label\":\"1.2.取得了哪些成绩\",\"row\":2,\"placeholder\":\"请输入内容\",\"required\":false,\"is_delete\":true}},{\"id\":\"other_15457\",\"name\":\"文本框\",\"type\":1,\"component\":\"textarea\",\"config\":{\"label\":\"1.3.当前进展如何\",\"row\":2,\"placeholder\":\"请输入内容\",\"required\":false,\"is_delete\":true}}]}', 1643020163, 1643020163, NULL);
INSERT INTO `__PREFIX__qingdong_form` VALUES (19, '季报管理', 'quarterly', '{\"data\":[{\"id\":\"other_43472\",\"name\":\"文本框\",\"type\":1,\"component\":\"textarea\",\"config\":{\"label\":\"1.1.完成了哪些工作\",\"row\":2,\"placeholder\":\"请输入内容\",\"required\":true,\"is_delete\":true}},{\"id\":\"other_48871\",\"name\":\"文本框\",\"type\":1,\"component\":\"textarea\",\"config\":{\"label\":\"1.2.取得了哪些成绩\",\"row\":2,\"placeholder\":\"请输入内容\",\"required\":true,\"is_delete\":true}},{\"id\":\"other_33048\",\"name\":\"文本框\",\"type\":1,\"component\":\"textarea\",\"config\":{\"label\":\"1.3.当前进展如何\",\"row\":2,\"placeholder\":\"请输入内容\",\"required\":true,\"is_delete\":true}},{\"id\":\"other_82389\",\"name\":\"文本框\",\"type\":1,\"component\":\"textarea\",\"config\":{\"label\":\"1.4.计划实施情况\",\"row\":2,\"placeholder\":\"请输入内容\",\"required\":true,\"is_delete\":true}}]}', 1619491859, 1640141727, NULL);
INSERT INTO `__PREFIX__qingdong_form` VALUES (20, '年报管理', 'yearly', '{\"data\":[{\"id\":\"other_43472\",\"name\":\"文本框\",\"type\":1,\"component\":\"textarea\",\"config\":{\"label\":\"1.1.完成了哪些工作\",\"row\":2,\"placeholder\":\"请输入内容\",\"required\":true,\"is_delete\":true}},{\"id\":\"other_48871\",\"name\":\"文本框\",\"type\":1,\"component\":\"textarea\",\"config\":{\"label\":\"1.2.取得了哪些成绩\",\"row\":2,\"placeholder\":\"请输入内容\",\"required\":true,\"is_delete\":true}},{\"id\":\"other_33048\",\"name\":\"文本框\",\"type\":1,\"component\":\"textarea\",\"config\":{\"label\":\"1.3.当前进展如何\",\"row\":2,\"placeholder\":\"请输入内容\",\"required\":true,\"is_delete\":true}},{\"id\":\"other_82389\",\"name\":\"文本框\",\"type\":1,\"component\":\"textarea\",\"config\":{\"label\":\"1.4.计划实施情况\",\"row\":2,\"placeholder\":\"请输入内容\",\"required\":true,\"is_delete\":true}},{\"id\":\"other_90392\",\"name\":\"文本框\",\"type\":1,\"component\":\"textarea\",\"config\":{\"label\":\"2.1 面临哪些挑战\",\"row\":2,\"placeholder\":\"请输入内容\",\"required\":false,\"is_delete\":true}},{\"id\":\"other_72126\",\"name\":\"文本框\",\"type\":1,\"component\":\"textarea\",\"config\":{\"label\":\"2.2 什么原因导致的\",\"row\":2,\"placeholder\":\"请输入内容\",\"required\":false,\"is_delete\":true}},{\"id\":\"other_39787\",\"name\":\"文本框\",\"type\":1,\"component\":\"textarea\",\"config\":{\"label\":\"2.3 有何改进措施\",\"row\":2,\"placeholder\":\"请输入内容\",\"required\":true,\"is_delete\":true}},{\"id\":\"other_66752\",\"name\":\"文本框\",\"type\":1,\"component\":\"textarea\",\"config\":{\"label\":\"3.1 节约了哪些成本\",\"row\":2,\"placeholder\":\"请输入内容\",\"required\":false,\"is_delete\":true}},{\"id\":\"other_62312\",\"name\":\"文本框\",\"type\":1,\"component\":\"textarea\",\"config\":{\"label\":\"3.2 提升了哪些效率\",\"row\":2,\"placeholder\":\"请输入内容\",\"required\":false,\"is_delete\":true}},{\"id\":\"other_62889\",\"name\":\"文本框\",\"type\":1,\"component\":\"textarea\",\"config\":{\"label\":\"3.3 优化了哪些步骤\",\"row\":2,\"placeholder\":\"请输入内容\",\"required\":false,\"is_delete\":true}},{\"id\":\"other_98365\",\"name\":\"文本框\",\"type\":1,\"component\":\"textarea\",\"config\":{\"label\":\"3.4 拓宽了哪些渠道\",\"row\":2,\"placeholder\":\"请输入内容\",\"required\":false,\"is_delete\":true}},{\"id\":\"other_80726\",\"name\":\"文本框\",\"type\":1,\"component\":\"textarea\",\"config\":{\"label\":\"4.1 下一步的安排\",\"row\":2,\"placeholder\":\"请输入下一步的安排\",\"required\":false,\"is_delete\":true}},{\"id\":\"other_15038\",\"name\":\"文本框\",\"type\":1,\"component\":\"textarea\",\"config\":{\"label\":\"4.2 需要的资源支持\",\"row\":2,\"placeholder\":\"请输入需要的资源支持\",\"required\":false,\"is_delete\":true}},{\"id\":\"other_970\",\"name\":\"文本框\",\"type\":1,\"component\":\"textarea\",\"config\":{\"label\":\"4.3 预计达到的结果\",\"row\":2,\"placeholder\":\"请输入预计达到的结果\",\"required\":false,\"is_delete\":true}}]}', 1643020163, 1643020163, NULL);
INSERT INTO `__PREFIX__qingdong_form` VALUES (21, '外勤签到', 'signin', '{\"data\":[{\"id\":\"other_35260\",\"name\":\"文本框\",\"type\":1,\"component\":\"textarea\",\"config\":{\"label\":\"备注\",\"row\":2,\"placeholder\":\"请输入备注\",\"required\":false,\"is_delete\":true}},{\"id\":\"other_65453\",\"name\":\"图片上传\",\"type\":1,\"component\":\"uploadImage\",\"config\":{\"label\":\"图片上传\",\"value\":[],\"required\":false,\"placeholder\":\"请上传图片\",\"maxnum\":1,\"is_delete\":true,\"format\":[\"jpg\",\"jpeg\",\"png\"],\"maxSize\":5000}}],\"isAddContact\":true}', 1643020163, 1657163099, NULL);
INSERT INTO `__PREFIX__qingdong_form` VALUES (22, '商机管理', 'business', '{\"data\":[{\"id\":\"name\",\"name\":\"单行输入框\",\"type\":1,\"component\":\"input\",\"config\":{\"type\":\"text\",\"label\":\"商机名称\",\"readonly\":false,\"placeholder\":\"商机名称\",\"required\":true,\"is_delete\":false}},{\"id\":\"money\",\"name\":\"数字框\",\"type\":1,\"component\":\"input-number\",\"config\":{\"label\":\"商机金额\",\"placeholder\":\"请输入商机金额\",\"required\":true,\"is_delete\":false}},{\"id\":\"status\",\"name\":\"下拉选择框\",\"type\":1,\"component\":\"select\",\"config\":{\"label\":\"商机状态\",\"type\":null,\"content\":[{\"key\":\"\",\"value\":\"行业1\",\"__label\":\"验证客户\",\"__value\":[\"行业1\"],\"nodeKey\":1,\"isEdit\":false,\"label\":\"验证客户\"},{\"key\":\"\",\"value\":\"行业2\",\"__value\":[\"行业2\"],\"nodeKey\":2,\"isEdit\":false,\"label\":\"需求分析\",\"__label\":\"需求分析\"},{\"label\":\"方案/报价\",\"expand\":true,\"value\":\"0-0\",\"isEdit\":false,\"__label\":\"方案/报价\",\"__value\":[\"0-0\"],\"nodeKey\":3},{\"label\":\"赢单\",\"expand\":true,\"value\":\"0-0\",\"isEdit\":false,\"__label\":\"赢单\",\"__value\":[\"0-0\"],\"nodeKey\":4},{\"label\":\"输单\",\"expand\":true,\"value\":\"0-0\",\"isEdit\":false,\"__label\":\"输单\",\"__value\":[\"0-0\"],\"nodeKey\":5}],\"required\":true,\"multiple\":false,\"is_delete\":false,\"placeholder\":\"请选择商机状态\"}},{\"id\":\"expect_time\",\"name\":\"日期选择框\",\"type\":1,\"component\":\"DatePicker\",\"config\":{\"label\":\"预计成交时间\",\"placeholder\":\"请输入预计成交时间\",\"type\":\"date\",\"value\":\"\",\"required\":true,\"is_delete\":true}},{\"id\":\"next_time\",\"name\":\"时间选择框\",\"type\":1,\"component\":\"TimePicker\",\"config\":{\"label\":\"下次跟进时间\",\"placeholder\":\"点击选择\",\"type\":\"time\",\"format\":\"YYYY-MM-DD HH:mm:ss\",\"confirm\":true,\"required\":false,\"readonly\":false,\"is_delete\":true}},{\"id\":\"other_48629\",\"name\":\"图片上传\",\"type\":1,\"component\":\"uploadImage\",\"config\":{\"label\":\"图片上传\",\"value\":[],\"required\":false,\"placeholder\":\"请上传图片\",\"maxnum\":1,\"is_delete\":true,\"format\":[\"jpg\",\"jpeg\",\"png\"],\"maxSize\":5000}},{\"id\":\"remark\",\"name\":\"文本框\",\"type\":1,\"component\":\"textarea\",\"config\":{\"label\":\"备注信息\",\"readonly\":false,\"row\":2,\"placeholder\":\"备注信息\",\"required\":false,\"is_delete\":true}}],\"isAddContact\":true}', 1619592288, 1659082152, NULL);


INSERT INTO `__PREFIX__qingdong_notice_template` (id,name,type,data)
VALUES (1, '审批通知', 'examine',
        '{\"template_id\":\"\",\"first\":\"{{staff_name}}\\u53d1\\u8d77\\u4e00\\u4e2a\\u300a\\u5ba1\\u6279\\u300b\\u6307\\u5b9a\\u7531\\u60a8\\u5904\\u7406\",\"keyword1\":\"{{examine_type}}\",\"keyword1_color\":\"#666\",\"keyword2\":\"{{date}}\",\"keyword2_color\":\"#666\",\"keyword3\":\"\",\"keyword3_color\":\"#666\",\"keyword4\":\"\",\"keyword4_color\":\"#666\",\"keyword5\":\"\",\"keyword5_color\":\"#666\",\"remark\":\"\\u8bf7\\u53ca\\u65f6\\u5904\\u7406\\uff01\",\"remark_color\":\"#666\"}');
INSERT INTO `__PREFIX__qingdong_notice_template` (id,name,type,data)
VALUES (3, '新建跟进记录通知', 'record',
        '{\"template_id\":\"\",\"first\":\"{{staff_name}}\\u521a\\u521a\\u65b0\\u5efa\\u4e86\\u4e00\\u6761\\u300a\\u8ddf\\u8fdb\\u8bb0\\u5f55\\u300b\\u6307\\u5b9a\\u7531\\u60a8\\u5ba1\\u9605\",\"keyword1\":\"{{customer_name}}{{staff_department}}\",\"keyword1_color\":\"#666\",\"keyword2\":\"{{customer_follow}}\",\"keyword2_color\":\"#666\",\"keyword3\":\"{{date}}\",\"keyword3_color\":\"#666\",\"keyword4\":\"\",\"keyword4_color\":\"#666\",\"keyword5\":\"\",\"keyword5_color\":\"#666\",\"remark\":\"\\u8bf7\\u53ca\\u65f6\\u5904\\u7406\\uff01\",\"remark_color\":\"#666\"}');
INSERT INTO `__PREFIX__qingdong_notice_template` (id,name,type,data)
VALUES (4, '新建工作报告通知', 'daily',
        '{\"template_id\":\"\",\"first\":\"{{staff_name}}\\u521a\\u521a\\u65b0\\u5efa\\u4e86\\u4e00\\u6761\\u300a\\u5de5\\u4f5c\\u62a5\\u544a\\u300b\\u6307\\u5b9a\\u7531\\u60a8\\u5ba1\\u9605\",\"keyword1\":\"{staff_name}\",\"keyword1_color\":\"#666\",\"keyword2\":\"{daily_type}\",\"keyword2_color\":\"#666\",\"keyword3\":\"{date}\",\"keyword3_color\":\"#666\",\"keyword4\":\"\",\"keyword4_color\":\"#666\",\"keyword5\":\"\",\"keyword5_color\":\"#666\",\"remark\":\"\",\"remark_color\":\"#666\"}');
INSERT INTO `__PREFIX__qingdong_notice_template` (id,name,type,data)
VALUES (5, '回收客户到公海通知', 'seas',
        '{\"template_id\":\"\",\"first\":\"\\u60a8\\u7684\\u5ba2\\u6237\\u957f\\u65f6\\u95f4\\u672a\\u8ddf\\u8fdb\\uff0c\\u5df2\\u88ab\\u7cfb\\u7edf\\u81ea\\u52a8\\u56de\\u6536\\u5230\\u516c\\u6d77\\u6c60\\u5185\\u3002\",\"keyword1\":\"{{customer_name}}\",\"keyword1_color\":\"#666\",\"keyword2\":\"{{day}}\",\"keyword2_color\":\"#666\",\"keyword3\":\"{{date}}\",\"keyword3_color\":\"#666\",\"keyword4\":\"\",\"keyword4_color\":\"#666\",\"keyword5\":\"\",\"keyword5_color\":\"#666\",\"remark\":\"\\u5efa\\u8bae\\u5ba2\\u6237\\u53ca\\u65f6\\u8ddf\\u8fdb\\uff01\",\"remark_color\":\"#666\"}');
INSERT INTO `__PREFIX__qingdong_notice_template` (id,name,type,data)
VALUES (6, '出差签到通知', 'sign',
        '{\"template_id\":\"\",\"first\":\"{{staff_name}}\\u63d0\\u4ea4\\u4e86\\u4e00\\u4e2a\\u51fa\\u5dee\\u7b7e\\u5230\\uff0c\\u7531\\u60a8\\u5ba1\\u9605\\uff01\",\"keyword1\":\"{{address}}\",\"keyword1_color\":\"#666\",\"keyword2\":\"{{date}}\",\"keyword2_color\":\"#666\",\"keyword3\":\"{{customer_name}}\",\"keyword3_color\":\"#666\",\"keyword4\":\"\",\"keyword4_color\":\"#666\",\"keyword5\":\"\",\"keyword5_color\":\"#666\",\"remark\":\"\\u8bf7\\u53ca\\u65f6\\u5ba1\\u9605\\uff01\",\"remark_color\":\"#666\"}');
INSERT INTO `__PREFIX__qingdong_notice_template` (id,name,type,data)
VALUES (7, '审批通过通知', 'examine_adopt',
        '{\"template_id\":\"\",\"first\":\"\\u60a8\\u63d0\\u4ea4\\u7684\\u5ba1\\u6279\\u5df2\\u5ba1\\u6838\\u901a\\u8fc7\\uff01\",\"keyword1\":\"{{check_name}}\",\"keyword1_color\":\"#666\",\"keyword2\":\"{examine_type}\",\"keyword2_color\":\"#666\",\"keyword3\":\"{submit_time}\",\"keyword3_color\":\"#666\",\"keyword4\":\"\",\"keyword4_color\":\"#666\",\"keyword5\":\"\",\"keyword5_color\":\"#666\",\"remark\":\"\\u5ba1\\u6838\\u901a\\u8fc7\\uff01\",\"remark_color\":\"#666\"}');
INSERT INTO `__PREFIX__qingdong_notice_template` (id,name,type,data)
VALUES (8, '审批拒绝通知', 'examine_refuse',
        '{\"template_id\":\"\",\"first\":\"\\u60a8\\u63d0\\u4ea4\\u7684\\u5ba1\\u6279\\u88ab\\u62d2\\u7edd\\uff01\",\"keyword1\":\"{{staff_name}}\",\"keyword1_color\":\"#666\",\"keyword2\":\"{check_name}\",\"keyword2_color\":\"#666\",\"keyword3\":\"{examine_type}\",\"keyword3_color\":\"#666\",\"keyword4\":\"{{examine_desc}}\",\"keyword4_color\":\"#666\",\"keyword5\":\"\",\"keyword5_color\":\"#666\",\"remark\":\"\\u5ba1\\u6838\\u62d2\\u7edd\\uff01\",\"remark_color\":\"#666\"}');
INSERT INTO `__PREFIX__qingdong_notice_template` (id,name,type,data)
VALUES (9, '合同到期通知', 'contract_expire',
        '{\"template_id\":\"\",\"first\":\"\\u5408\\u540c\\u5373\\u5c06\\u5230\\u671f\\uff0c\\u8bf7\\u5c3d\\u5feb\\u8ddf\\u8fdb\\u56de\\u6b3e\",\"keyword1\":\"{customer_name}\",\"keyword1_color\":\"#666\",\"keyword2\":\"{money}\",\"keyword2_color\":\"#666\",\"keyword3\":\"\",\"keyword3_color\":\"#666\",\"keyword4\":\"\",\"keyword4_color\":\"#666\",\"keyword5\":\"\",\"keyword5_color\":\"#666\",\"remark\":\"\\u8bf7\\u53ca\\u65f6\\u8ddf\\u8fdb\\uff01\",\"remark_color\":\"#666\"}');
INSERT INTO `__PREFIX__qingdong_notice_template` (id,name,type,data)
VALUES (10, '回款计划到期通知', 'plan_expire',
        '{\"template_id\":\"\",\"first\":\"\\u5df2\\u5230\\u8ba1\\u5212\\u56de\\u6b3e\\u7ea6\\u5b9a\\u65f6\\u95f4\\uff0c\\u8bf7\\u5c3d\\u5feb\\u8ddf\\u8fdb\\u56de\\u6b3e\",\"keyword1\":\"{{customer_name}}\",\"keyword1_color\":\"#666\",\"keyword2\":\"{{plan_money}}\",\"keyword2_color\":\"#666\",\"keyword3\":\"{{plan_time}}\",\"keyword3_color\":\"#666\",\"keyword4\":\"\",\"keyword4_color\":\"#666\",\"keyword5\":\"\",\"keyword5_color\":\"#666\",\"remark\":\"\\u8bf7\\u53ca\\u65f6\\u8ddf\\u8fdb\\uff01\",\"remark_color\":\"#666\"}');
INSERT INTO `__PREFIX__qingdong_notice_template` (id,name,type,data)
VALUES (11, '评论通知', 'comment',
        '{\"template_id\":\"\",\"first\":\"\\u8bc4\\u8bba\\u4e86\\u60a8\\u63d0\\u4ea4\\u7684\\u8ddf\\u8fdb\\u8bb0\\u5f55\\uff01\",\"keyword1\":\"{{staff_name}}\",\"keyword1_color\":\"#666\",\"keyword2\":\"{{content}}\",\"keyword2_color\":\"#666\",\"keyword3\":\"{{date}}\",\"keyword3_color\":\"#666\",\"keyword4\":\"\",\"keyword4_color\":\"#666\",\"keyword5\":\"\",\"keyword5_color\":\"#666\",\"remark\":\"\\u8bf7\\u53ca\\u65f6\\u5904\\u7406\\uff01\",\"remark_color\":\"#666\"}');
INSERT INTO `__PREFIX__qingdong_notice_template` (id,name,type,data)
VALUES (12, '日程通知', 'event',
        '{"template_id":"","first":"{{event_title}}日程即将开始","keyword1":"","keyword1_color":"#666","keyword2":"","keyword2_color":"#666","keyword3":"","keyword3_color":"#666","keyword4":"","keyword4_color":"#666","keyword5":"","keyword5_color":"#666","remark":"请及时处理！","remark_color":"#666"}');

INSERT INTO `__PREFIX__qingdong_flow` (id,name,status,examine_ids,relation_type,last_modified,last_admin_id,createtime,updatetime)
VALUES (1, '费用', 0, 0,'consume', 1657014906, 1, 1657014906, 1657014906);
INSERT INTO `__PREFIX__qingdong_flow`  (id,name,status,examine_ids,relation_type,last_modified,last_admin_id,createtime,updatetime)
VALUES (2, '业绩', 0, 0,'achievement',1657767346, 1, 1657767346, 1657767346);
INSERT INTO `__PREFIX__qingdong_flow`  (id,name,status,examine_ids,relation_type,last_modified,last_admin_id,createtime,updatetime)
VALUES (3, '回款', 0, 0,'receivables',1658388920, 1, 1658388920, 1658388920);
INSERT INTO `__PREFIX__qingdong_flow`  (id,name,status,examine_ids,relation_type,last_modified,last_admin_id,createtime,updatetime)
VALUES (4, '合同', 0, 0,'contract', 1658388928, 1, 1658388928, 1658388928);
INSERT INTO `__PREFIX__qingdong_flow`  (id,name,status,examine_ids,relation_type,last_modified,last_admin_id,createtime,updatetime)
VALUES (5, '补卡', 0, 0,'card',1658388928, 1, 1658388928, 1658388928);
INSERT INTO `__PREFIX__qingdong_flow`  (id,name,status,examine_ids,relation_type,last_modified,last_admin_id,createtime,updatetime)
VALUES (6, '请假', 0, 0, 'leave',1658388928, 1, 1658388928, 1658388928);

ALTER TABLE `__PREFIX__qingdong_achievement_records`
ADD COLUMN `flow_id` int(11) NULL COMMENT '审核流程ID' AFTER `flow_staff_ids`,
ADD COLUMN `order_id` int(11) NULL COMMENT '审批流程步骤id' AFTER `flow_id`;
ALTER TABLE `__PREFIX__qingdong_approval`
ADD COLUMN `flow_id` int(11) NULL COMMENT '审核流程ID' AFTER `check_status`,
ADD COLUMN `order_id` int(11) NULL COMMENT '审批流程步骤id' AFTER `flow_id`;
ALTER TABLE `__PREFIX__qingdong_consume`
ADD COLUMN `flow_id` int(11) NULL COMMENT '审核流程ID' AFTER `flow_staff_ids`,
ADD COLUMN `order_id` int(11) NULL COMMENT '审批流程步骤id' AFTER `flow_id`;
ALTER TABLE `__PREFIX__qingdong_contract`
ADD COLUMN `flow_id` int(11) NULL COMMENT '审核流程ID' AFTER `remarks`,
ADD COLUMN `order_id` int(11) NULL COMMENT '审批流程步骤id' AFTER `flow_id`;
ALTER TABLE `__PREFIX__qingdong_contract`
ADD COLUMN `ratios` varchar(255) NULL DEFAULT NULL COMMENT '业绩比例' AFTER `order_id`;
ALTER TABLE `__PREFIX__qingdong_contract` ADD COLUMN `business_id` int(11) NULL COMMENT '商机ID' AFTER `order_staff_id`;

ALTER TABLE `__PREFIX__qingdong_contract`
ADD COLUMN `ratio_id` int(11) NULL COMMENT '绩效ID' AFTER `order_id`;
ALTER TABLE `__PREFIX__qingdong_customer`
    ADD COLUMN `ro_staff_id` varchar(255) NULL DEFAULT '' COMMENT '只读权限' AFTER `owner_staff_id`,
    ADD COLUMN `rw_staff_id` varchar(255) NULL DEFAULT '' COMMENT '读写权限' AFTER `ro_staff_id`,
    ADD COLUMN `mobile` varchar(15) NULL DEFAULT '' COMMENT '手机号' AFTER `subname`;
ALTER TABLE `__PREFIX__qingdong_customer` MODIFY COLUMN `address_detail` varchar(255) NULL DEFAULT NULL COMMENT '详细地址' AFTER `address`;
ALTER TABLE `__PREFIX__qingdong_customer` MODIFY COLUMN `remarks` text NULL COMMENT '备注信息' AFTER `lat`;
ALTER TABLE `__PREFIX__qingdong_file`
MODIFY COLUMN `types` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '类型（file、image）' AFTER `id`;
ALTER TABLE `__PREFIX__qingdong_flow`
ADD COLUMN `status` tinyint(1) DEFAULT NULL COMMENT '1 固定审批 0 授权审批' AFTER `examine_ids`,
ADD COLUMN `remark` varchar(255) DEFAULT NULL COMMENT '备注' AFTER `status`,
ADD COLUMN `relation_type` varchar(255) DEFAULT NULL COMMENT '关联类型' AFTER `remark`,
ADD COLUMN `group_ids` varchar(255) DEFAULT NULL COMMENT '关联角色组' AFTER `relation_type`,
ADD COLUMN `weight` tinyint(4) DEFAULT NULL COMMENT '权重' AFTER `group_ids`,
ADD COLUMN `last_modified` int(11) DEFAULT NULL COMMENT '最后修改时间' AFTER `weight`,
ADD COLUMN `last_admin_id` int(11) DEFAULT NULL COMMENT '最后修改人' AFTER `last_modified`,
ADD COLUMN `deletetime` int(11) DEFAULT NULL AFTER `updatetime`;
ALTER TABLE `__PREFIX__qingdong_form_approval`
ADD COLUMN `examine_ids` text COMMENT '默认审核人id' AFTER `form_id`,
ADD COLUMN `status` tinyint(1) DEFAULT NULL COMMENT '1 固定审批 0 授权审批' AFTER `examine_ids`;
ALTER TABLE `__PREFIX__qingdong_product`
ADD COLUMN `cost_price` decimal(10,2) DEFAULT NULL COMMENT '成本价' AFTER `min_price`,
MODIFY COLUMN `description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '产品描述' AFTER `status`;
ALTER TABLE `__PREFIX__qingdong_product_part`
MODIFY COLUMN `description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '产品描述' AFTER `img`;

ALTER TABLE `__PREFIX__qingdong_receivables`
ADD COLUMN `flow_id` int(11) NULL COMMENT '审核流程ID' AFTER `check_status`,
ADD COLUMN `order_id` int(11) NULL COMMENT '审批流程步骤id' AFTER `flow_id`;
ALTER TABLE `__PREFIX__qingdong_staff_sign_in` ADD COLUMN `other` text NULL COMMENT '表单信息' AFTER `file_ids`;

ALTER TABLE `__PREFIX__qingdong_customer` ADD COLUMN `seas_id` varchar(128) NULL DEFAULT NULL COMMENT '所属公海' AFTER `parent_id`;
ALTER TABLE `__PREFIX__qingdong_staff` MODIFY COLUMN `role` int(11) NULL DEFAULT NULL COMMENT '角色id' AFTER `group_ids`;
ALTER TABLE `__PREFIX__qingdong_customer` ADD COLUMN `sea_time` int(11) NULL COMMENT '放入公海时间' AFTER `rw_staff_id`;
INSERT INTO `__PREFIX__qingdong_seas_type` VALUES (1, '默认公海', '客户不满足其他公海规则时，会移入当前公海', NULL, NULL, NULL, NULL);

INSERT INTO `__PREFIX__qingdong_staff_rule` VALUES (1, 'menus',  0, 'seas', '公海客户', '', 1, '');
INSERT INTO `__PREFIX__qingdong_staff_rule` VALUES (2, 'data',  1, 1, '默认公海', '', 0, '');

ALTER TABLE `__PREFIX__qingdong_notice_template`
MODIFY COLUMN `data` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '微信公众号' AFTER `type`,
ADD COLUMN `wechat` text NULL COMMENT '企业微信' AFTER `data`,
ADD COLUMN `createtime` int(11) NULL COMMENT '创建时间' AFTER `wechat`,
ADD COLUMN `updatetime` int(11) NULL COMMENT '修改时间' AFTER `createtime`,
ADD COLUMN `deletetime` int(11) NULL COMMENT '删除时间' AFTER `updatetime`;
ALTER TABLE `__PREFIX__qingdong_staff`
ADD COLUMN `touser` varchar(50) NULL COMMENT '企业微信userid' AFTER `wx_openid`;

ALTER TABLE `__PREFIX__qingdong_leads`
ADD COLUMN `receive_time` int(11) NULL COMMENT '分配时间' AFTER `follow`,
ADD COLUMN `tranfer_stff_id` int(10) NULL COMMENT '转派人ID' AFTER `owner_staff_id`;

ALTER TABLE `__PREFIX__qingdong_notice_template`
    ADD COLUMN `enterprise_data` text NULL COMMENT '企业微信通知' AFTER `data`;
ALTER TABLE `__PREFIX__qingdong_product`
    ADD COLUMN `goods_id` int(11) NULL DEFAULT NULL COMMENT '商品ID' AFTER `id`,
    ADD COLUMN  `type` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '规格' AFTER `name`;
ALTER TABLE `__PREFIX__qingdong_product_type`
    ADD COLUMN `content` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '内容' AFTER `image`,
    ADD COLUMN  `status` tinyint(1) NULL DEFAULT NULL COMMENT '状态' AFTER `content`;
ALTER TABLE `__PREFIX__qingdong_record`
    ADD COLUMN `status` int(1) DEFAULT '0' COMMENT '跟进状态 0：未跟进 1：已跟进' AFTER `create_staff_id`,
    ADD COLUMN `location` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '定位信息' AFTER `deletetime`,
    ADD COLUMN `lng` double(16, 12) NULL DEFAULT NULL COMMENT '地理位置经度' AFTER `location`,
    ADD COLUMN  `lat` double(16, 12) NULL DEFAULT NULL COMMENT '地理位置维度' AFTER `lng`,
    ADD COLUMN  `staff_id` int(11) NULL COMMENT '创建人' AFTER `lat`;

ALTER TABLE `__PREFIX__qingdong_contract_product`
    ADD COLUMN `ship_number` tinyint(5) DEFAULT '0' COMMENT '发货数量' AFTER `parts`,
    ADD COLUMN `freight` double(10,2) DEFAULT '0.00' COMMENT '运费' AFTER `ship_status`,
    ADD COLUMN `wholesale` decimal(10,2) DEFAULT '0.00' COMMENT '批发价' AFTER `freight`,
    ADD COLUMN  `createtime` int(11) DEFAULT NULL AFTER `wholesale`,
    ADD COLUMN  `updatetime` int(11) DEFAULT NULL AFTER `createtime`,
    ADD COLUMN  `deletetime` int(11) DEFAULT NULL AFTER `updatetime`;
ALTER TABLE `__PREFIX__qingdong_product`
    ADD COLUMN `wholesale` decimal(10,2) DEFAULT '0.00' COMMENT '批发价' AFTER `price`,
    ADD COLUMN `stock` int(11) DEFAULT '0' COMMENT '总库存' AFTER `description`,
    ADD COLUMN `instock` int(11) DEFAULT '0' COMMENT '入库数' AFTER `stock`,
    ADD COLUMN  `outstock` int(11) DEFAULT '0' COMMENT '出库数' AFTER `instock`;
ALTER TABLE `__PREFIX__qingdong_goods`
    ADD COLUMN `num` varchar(64) DEFAULT NULL COMMENT '编号' AFTER `id`,
    ADD COLUMN `specs_id` int(11) DEFAULT NULL COMMENT '规格' AFTER `type_id`,
    ADD COLUMN  `wholesale` decimal(10,2) DEFAULT '0.00' COMMENT '批发价' AFTER `cost_price`;

ALTER TABLE `__PREFIX__qingdong_business_product`
ADD COLUMN `wholesale` decimal(10, 2) NULL COMMENT '批发价' AFTER `price`;
ALTER TABLE `__PREFIX__qingdong_ding_staff`
MODIFY COLUMN `user_id` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '钉钉用户ID' AFTER `dept_id`;
ALTER TABLE `__PREFIX__qingdong_ding_customer`
MODIFY COLUMN `user_id` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用户ID' AFTER `instance_id`,
MODIFY COLUMN `owner_staff_id` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '负责人' AFTER `remark`,
MODIFY COLUMN `create_staff_id` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人' AFTER `owner_staff_id`;
ALTER TABLE `__PREFIX__qingdong_ding_contacts`
MODIFY COLUMN `create_staff_id` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人ID' AFTER `remarks`,
MODIFY COLUMN `owner_staff_id` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '负责人ID' AFTER `create_staff_id`;
ALTER TABLE `__PREFIX__qingdong_record`
    ADD COLUMN  `staff_id` int(11) NULL COMMENT '创建人' AFTER `id`;
ALTER TABLE `__PREFIX__qingdong_push_reload`
MODIFY COLUMN `data` text  DEFAULT NULL COMMENT '数据' AFTER `template_id`;

ALTER TABLE `__PREFIX__qingdong_staff_department`
    ADD COLUMN `parentid` int(11) NULL COMMENT '子ID' AFTER `role_id`;

CREATE TABLE IF NOT EXISTS `__PREFIX__qingdong_wechat_openid`
(
    `id`              int(11) NOT NULL AUTO_INCREMENT,
    `openid`     varchar(128) DEFAULT NULL COMMENT 'openid',
    `unionid`     varchar(128)  DEFAULT NULL COMMENT 'unionid',
    `createtime` int(11) NULL,
    `updatetime` int(11) NULL,
    `deletetime` int(11) NULL,
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='openid和unionid';