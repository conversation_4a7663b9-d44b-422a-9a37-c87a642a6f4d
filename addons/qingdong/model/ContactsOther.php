<?php

namespace addons\qingdong\model;

use think\Model;

/**
 *联系人其他信息表
 */
class ContactsOther Extends Model {
    // 表名,不含前缀
    protected $name = 'qingdong_contacts_other';
	// 开启自动写入时间戳字段
	protected $autoWriteTimestamp = false;

    public static function getOther($params){
        $other = self::where(['id' => $params['id']])->value('otherdata');
        if($other){
            $other=json_decode($other,true);
        }else{
            $other=[];
        }

        $params = array_merge($params, $other);
        $params = Form::getDataDetail(Form::CONTACTS_TYPE, $params);

        return $params;
    }
}
