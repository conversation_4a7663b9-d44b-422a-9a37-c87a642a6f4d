<?php

namespace addons\qingdong\model;

use think\Model;
use traits\model\SoftDelete;

/**
 * 业绩目标
 */
class Achievement Extends Model {
	use SoftDelete;
    // 表名,不含前缀
    protected $name = 'qingdong_achievement';
	// 开启自动写入时间戳字段
	protected $autoWriteTimestamp = 'int';
	// 定义时间戳字段名
	protected $createTime = 'createtime';
	protected $updateTime = 'updatetime';
	protected $deleteTime = 'deletetime';


	/**
	 * 获取员工年目标
	 * @param $year
	 * @return false
	 */
	public static function getStaffYearAchievement($year) {
		$staff  = Staff::info();
		$result = self::where(['year' => $year, 'type' => 3, 'obj_id' => $staff->id, 'status' => 1])->find();
		if (empty($result)) {
			$model = new self();
			$model->save(['year' => $year, 'type' => 3, 'obj_id' => $staff->id, 'status' => 1]);
			$result = self::where(['year' => $year, 'type' => 3, 'obj_id' => $staff->id, 'status' => 1])->find();
		}
		return $result->toArray();
	}
	/**
	 * 获取团队年目标
	 * @param $year
	 * @return false
	 */
	public static function getTeamYearAchievement($year) {
		$staff  = Staff::info();
		$result = self::where(['year' => $year, 'type' => 4, 'obj_id' => $staff->id, 'status' => 1])->find();
		if (empty($result)) {
			$model = new self();
			$model->save(['year' => $year, 'type' => 4, 'obj_id' => $staff->id, 'status' => 1]);
			$result = self::where(['year' => $year, 'type' => 4, 'obj_id' => $staff->id, 'status' => 1])->find();
		}

		return $result->toArray();
	}
	/**
	 * 获取团队年目标
	 * @param $year
	 * @return false
	 */
	public static function getCompanyYearAchievement($year) {
		$staff  = Staff::info();
		$result = self::where(['year' => $year, 'type' => 1, 'status' => 1])->find();
		if (empty($result)) {
			$model = new self();
			$model->save(['year' => $year, 'type' => 1,  'status' => 1]);
			$result = self::where(['year' => $year, 'type' => 1,  'status' => 1])->find();
		}

		return $result->toArray();
	}


    /**
         * 获取员工当月业绩目标
         * @desc 备注
         * @update_date 2021/4/17 更新时间
     */
    public static function getStaffAchievement($startDate,$endDate,$status) {
        $staff = Staff::info();
        $field='';
        $startDate=strtotime($startDate);
        $endDate=strtotime($endDate);
        for ($i = $startDate; $i <= $endDate;) {
            $field .= ($field ? '+' : '') . self::getMonthField(date('Y-m-d',$i));
            $i = strtotime('+1 month',$i);
        }

        return self::where([
            'year'   => date('Y', $startDate),
            'status'   => $status,
            'type'   => 3,
            'obj_id' => $staff->id,
        ])->field($field.' as achievement')->find();
    }
	/**
	 * 获取团队业绩
	 * @desc 备注
	 * @update_date 2021/4/17 更新时间
	 * <AUTHOR>
	 */
	public static function getTeamAchievement($date) {
		$staff = Staff::info();
		$field = self::getMonthField($date);

		return self::where([
				'year'     => date('Y', strtotime($date)),
				'type'     => 4,
				'obj_id' => $staff->id
			])->value($field) ?? 0;
	}
	/**
	 * 获取公司业绩
	 * @desc 备注
	 * @update_date 2021/4/17 更新时间
	 * <AUTHOR>
	 */
    public static function getCompanyAchievement($startDate, $endDate,$status)
    {
        $staff = Staff::info();
        $field = '';
        $startDate=strtotime($startDate);
        $endDate=strtotime($endDate);
        for ($i = $startDate; $i <= $endDate;) {
            $field .= ($field ? '+' : '') . self::getMonthField(date('Y-m-d',$i));
            $i = strtotime('+1 month',$i);
        }

        return self::where([
            'year'   => date('Y', $startDate),
            'type' => 1,
            'status'   => $status,
        ])->field($field . ' as achievement')->find();
    }

    /**
     * 员工
     * @return \think\model\relation\HasOne
     */
    public function staff() {
        return $this->hasOne(Staff::class, 'id', 'obj_id')->field('id,name,img');
    }
	/**
	 * 获取月字段
	 */
	public static function getMonthField($date) {
		$m = date('m', strtotime($date));
		switch ($m) {
			case 1:
				$field = 'january';
			break;
			case 2:
				$field = 'february';
			break;
			case 3:
				$field = 'march';
			break;
			case 4:
				$field = 'april';
			break;
			case 5:
				$field = 'may';
			break;
			case 6:
				$field = 'june';
			break;
			case 7:
				$field = 'july';
			break;
			case 8:
				$field = 'august';
			break;
			case 9:
				$field = 'september';
			break;
			case 10:
				$field = 'october';
			break;
			case 11:
				$field = 'november';
			break;
			case 12:
				$field = 'december';
			break;
		}

		return $field;
	}


	/**
	 * 根据字段获取月数字
	 */
	public static function getFieldMonth($field) {
		switch ($field) {
			case 'january':
				$date = '01';
			break;
			case 'february':
				$date = '02';
			break;
			case 'march':
				$date = '03';
			break;
			case 'april':
				$date = '04';
			break;
			case 'may':
				$date = '05';
			break;
			case 'june':
				$date = '06';
			break;
			case 'july':
				$date = '07';
			break;
			case 'august':
				$date = '08';
			break;
			case 'september':
				$date = '09';
			break;
			case 'october':
				$date = '10';
			break;
			case 'november':
				$date = '11';
			break;
			case 'december':
				$date = '12';
			break;
			default:
				return false;
		}

		return $date;
	}
}
