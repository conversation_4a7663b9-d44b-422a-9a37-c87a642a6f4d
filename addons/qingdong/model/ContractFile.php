<?php

namespace addons\qingdong\model;

use think\Model;
use traits\model\SoftDelete;

/**
 *联系人表
 */
class ContractFile Extends Model {
	use SoftDelete;
    // 表名,不含前缀
    protected $name = 'qingdong_contract_file';
	// 开启自动写入时间戳字段
	protected $autoWriteTimestamp = 'int';
	// 定义时间戳字段名
	protected $createTime = false;
	protected $updateTime = false;
	protected $deleteTime = false;

	//附件表
	public function file() {
		return $this->hasOne(File::class, 'id', 'file_id')->with('staff')->bind('size,create_staff_id,types,name,file_path,staff');
	}

	public static function addFiles($files, $contract_id) {
		$files    = explode(',', $files);
		$addFiles = [];
		foreach ($files as $v) {
			if(!empty($v)){
				// 如果是文件路径，转换为文件ID
				if(is_numeric($v)){
					$file_id = $v;
				} else {
					$file_id = File::where(['file_path' => $v])->value('id');
				}

				if($file_id){
					$addFiles[] = [
						'contract_id' => $contract_id,
						'file_id'     => $file_id
					];
				}
			}
		}
		if(!empty($addFiles)){
			$model=new self();
			if($model->insertAll($addFiles)){
				return true;
			}
		}
		return false;
	}
}
