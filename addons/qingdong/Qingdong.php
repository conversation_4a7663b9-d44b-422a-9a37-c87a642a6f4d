<?php

namespace addons\qingdong;

use app\admin\model\AuthRule;
use app\common\library\Menu;
use think\Addons;
use think\Exception;
use think\exception\PDOException;


/**
 * 插件
 */
class Qingdong extends Addons
{

    /**
     * 插件安装方法
     * @return bool
     */
    public function install()
    {
        $menu = self::getMenu();
        Menu::create($menu['new']);
        return true;
    }

    /**
     * 插件卸载方法
     * @return bool
     */
    public function uninstall()
    {
        Menu::delete('qingdong');
        return true;
    }

    /**
     * 插件启用方法
     * @return bool
     */
    public function enable()
    {
        Menu::enable('qingdong');
        return true;
    }

    /**
     * 插件禁用方法
     * @return bool
     */
    public function disable()
    {
        Menu::disable('qingdong');
        return true;
    }

    /**
     * 插件更新方法
     */
    public function upgrade()
    {
        $menu = self::getMenu();
        if (method_exists(Menu::class, 'upgrade')) {
            Menu::upgrade('qingdong', $menu['new']);
        } else {
            self::menuCreateOrUpdate($menu['new'], $menu['old']);
        }

        return true;
    }

    private static function getMenu()
    {
        $newMenu = [];
        $config_file = ADDON_PATH . "qingdong" . DS . 'config' . DS . "menu.php";
        if (is_file($config_file)) {
            $newMenu = include $config_file;
        }
        $oldMenu = AuthRule::where('name', 'like', "qingdong%")->select();
        $oldMenu = array_column($oldMenu, null, 'name');
        return ['new' => $newMenu, 'old' => $oldMenu];
    }

    private static function menuCreateOrUpdate($newMenu, $oldMenu, $parent = 0)
    {
        if (!is_numeric($parent)) {
            $parentRule = AuthRule::getByName($parent);
            $pid = $parentRule ? $parentRule['id'] : 0;
        } else {
            $pid = $parent;
        }
        $allow = array_flip(['file', 'name', 'title', 'icon', 'condition', 'remark', 'ismenu', 'weigh']);
        foreach ($newMenu as $k => $v) {
            $hasChild = isset($v['sublist']) && $v['sublist'] ? true : false;
            $data = array_intersect_key($v, $allow);
            $data['ismenu'] = isset($data['ismenu']) ? $data['ismenu'] : ($hasChild ? 1 : 0);
            $data['icon'] = isset($data['icon']) ? $data['icon'] : ($hasChild ? 'fa fa-list' : 'fa fa-circle-o');
            $data['pid'] = $pid;
            $data['status'] = 'normal';
            try {
                if (!isset($oldMenu[$data['name']])) {
                    $menu = AuthRule::create($data);
                } else {
                    $menu = $oldMenu[$data['name']];
                }
                if ($hasChild) {
                    self::menuCreateOrUpdate($v['sublist'], $oldMenu, $menu['id']);
                }
            } catch (PDOException $e) {
                throw new Exception($e->getMessage());
            }
        }
    }

    /**
     * 应用初始化
     */
    public function appInit()
    {
        // 公共方法
        require_once __DIR__ . '/helper.php';
        if(!class_exists("\dingding\TopSdk")){
            \think\Loader::addNamespace('dingding', ADDON_PATH . 'qingdong' . DS . 'library' . DS . 'dingding' . DS);
        }
        if(!class_exists("\WeWork\App")){
            \think\Loader::addNamespace('WeWork', ADDON_PATH . 'qingdong' . DS . 'library' . DS . 'wework' . DS);
        }
        if(!class_exists("\PhpOffice\PhpWord\PhpWord")){
            \think\Loader::addNamespace('PhpOffice\PhpWord', ADDON_PATH . 'qingdong' . DS . 'library' . DS . 'PhpWord' . DS);
        }
    }
}
