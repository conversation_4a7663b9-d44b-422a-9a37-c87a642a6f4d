<?php

namespace addons\qingdong\controller;

use addons\qingdong\model\OperationLog;
/**
 * 操作记录
 */
class Operation extends StaffApi {
	protected $noNeedLogin = [];
	protected $noNeedRight = [];


	//
	public function getList() {
		$relation_type = input('relation_type', '');//1客户 2联系人 3合同
		$relation_id   = input('relation_id', '');
        if(empty($relation_type) || empty($relation_id)){
            $this->error('参数不能为空');
        }
		//操作记录
		$logs=OperationLog::where([
			'relation_type'  => $relation_type,
			'relation_id'    => $relation_id,
			'operation_type' => 1
		])->field('id,content,operation_id,createtime')->with(['staff'])->select();
		$this->success('请求成功',$logs);
	}
}
