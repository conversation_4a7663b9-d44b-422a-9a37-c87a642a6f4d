<?php

namespace addons\qingdong\controller;

use addons\qingdong\model\Event;
use addons\qingdong\model\Form;
use addons\qingdong\model\FormField;
use addons\qingdong\model\Leads as LeadsModel;
use addons\qingdong\model\LeadsFile;
use addons\qingdong\model\LeadsOther;
use addons\qingdong\model\Staff;
use addons\qingdong\model\Record;
use think\Db;
use think\Exception;

/**
 * 线索接口
 */
class Leads extends StaffApi {
    protected $noNeedLogin = [];
    protected $noNeedRight = [];
    public function _initialize()
    {
        parent::_initialize();
        try{
            \think\Db::execute("SET @@sql_mode='';");
        }catch (Exception $e){

        }
    }

    //添加线索
    public function addLeads() {
        $params = $this->request->post();
        if (empty($params['leads'])) {
            $this->error('线索信息不能为空');
        }
        // 表单验证
        if (($result = $this->qingdongValidate($params['leads'], get_class(), 'create')) !== true) {
            $this->error($result);
        }
        $result = FormField::checkFields(FormField::LEADS_TYPE, $params['leads']);
        if ($result !== true) {
            $this->error($result);
        }
        try {
            $params['leads'] = Form::updateFormParams(Form::LEADS_TYPE, $params['leads']);
            $leadsId                     = LeadsModel::createLeads($params['leads']);
        } catch (Exception $e) {
            $this->error($e->getMessage());
        }
        if ($result) {
            $this->success('添加线索成功');
        }
    }


    //获取线索列表
    public function getList() {
        $name   = input('name', '', 'trim');
        $mobile = input('mobile', '', 'trim');
        $limit  = input("limit/d", 10);
        $params = $this->request->post();

        $where= FormField::updateWhereField(FormField::LEADS_TYPE,$params);

        if (isset($params['createtime']) && $params['createtime']) {//跟进状态
            $createtime          = $params['createtime'];
            $createtime          = explode(',', $createtime);
            $where['createtime'] = ['between', [strtotime($createtime[0]), strtotime($createtime[1])+86400-1]];
        }
        $wheres=[];
        //0:全部 1：我负责的 2：下属负责的 3：今日待跟进 4:今日已跟进 5:从未跟进的
        $team = input('team',0);
        switch($team){
            case 1:
                $staff = Staff::info();
                $wheres['owner_staff_id'] =  $staff->id;
                break;
            case 2:
                $wheres['owner_staff_id'] =  array('in',Staff::getLowerStaffId());
                break;
            case 3:
                $start = date('Y-m-d 00:00:00');
                $end = date('Y-m-d 23:59:59');
                $record = collection(Record::where(array('status'=>0,'relation_type'=>4,'next_time'=>array(array('egt',$start),array('elt',$end))))->field("id,relation_id")->select())->toArray();
                $relationId = [];
                foreach($record as $k=>$v){
                    $whereRe['id'] = array('gt',$v['id']);
                    $whereRe['relation_id'] = $v['relation_id'];
                    $recordData = Record::where($whereRe)->count();
                    if($recordData == 0){
                        $relationId[] = $v['relation_id'];
                    }

                }
                $wheres['id'] =  array('in',$relationId);
                $staff = Staff::info();
                $wheres['owner_staff_id'] =  $staff->id;
                break;
            case 4:
                $start = date('Y-m-d 00:00:00');
                $end = date('Y-m-d 23:59:59');
                $relationId = Record::where(array('status'=>1,'relation_type'=>4,'next_time'=>array(array('egt',$start),array('elt',$end))))->field("id,relation_id")->column('relation_id');
                $wheres['id'] =  array('in',$relationId);
                $staff = Staff::info();
                $wheres['owner_staff_id'] =  $staff->id;
                break;
            case 5:
                $record = collection(Record::where(array('relation_type'=>4))->column('relation_id'))->toArray();
                $wheres['id'] =  array('not in',$record);
                $staff = Staff::info();
                $wheres['owner_staff_id'] =  $staff->id;
                break;
            default:
                $wheres['owner_staff_id'] =  array('in',Staff::getMyStaffIds());
                break;

        }
        if (isset($params['staff_id']) && $params['staff_id']) {//下级员工筛选
            $wheres['owner_staff_id'] = $params['staff_id'];
        }

        if ($name) {
            $where['name'] = ['like', "%{$name}%"];
        }
        if ($mobile) {
            $where['mobile'] = ['like', "%{$mobile}%"];
        }
        $where['is_transform']=0;
        $records           = LeadsModel::where($where)->where($wheres)->with(['ownerStaff'])->field('id,owner_staff_id,name,follow,mobile,level,next_time,source')->order('id desc')->paginate($limit);
        $this->success('请求成功', $records);
    }


    //获取线索详情
    public function getDetail() {
        $id    = input('id', '', 'intval');
        $leads = LeadsModel::where(['id' => $id])->with([
            'createStaff',
            'ownerStaff',
            'tranferStaff'
        ])->find();
        if(empty($leads)){
            $this->error('信息不存在');
        }
        $leads=$leads->toArray();
        $leads=LeadsOther::getOther($leads);
        $this->success('请求成功', $leads);
    }


    //获取选择列表
    public function getSelectList() {
        $name = input('name','');

        $where   = ['owner_staff_id' => $this->auth->id,'is_transform'=>0];
        if ($name) {
            $where['name'] = ['like',"%$name%"];
        }

        $records = LeadsModel::where($where)->field('id,owner_staff_id,name,follow,mobile')->order('id desc')->select();
        $this->success('请求成功', $records);
    }


    //转移线索
    public function transfer()
    {
        $id = input('id');
        $staff_id = input('staff_id');
        if (!$staff_id || !$id) {
            $this->error('参数错误');
        }
        $staff = Staff::get($staff_id);
        if (empty($staff)) {
            $this->error('接收对象不存在');
        }
        $row = LeadsModel::where(['id' => $id])->find();
        if (empty($row)) {
            $this->error('线索不存在');
        }
        try {
            LeadsModel::transfer($id, $staff_id);
        } catch (Exception $e) {
            $this->error($e->getMessage());
        }
        $this->success('转移线索成功');
    }


    //修改线索
    public function editLeads() {

        $id     = input('id');
        $params = $this->request->post();
        $row    = LeadsModel::where(['id' => $id, 'owner_staff_id' => $this->auth->id])->find();
        if (empty($row)) {
            $this->error('您不是线索负责人无法修改信息');
        }
        // 表单验证
        if (($result = $this->qingdongValidate($params, get_class(), 'create')) !== true) {
            $this->error($result);
        }
        $result = FormField::checkFields(FormField::LEADS_TYPE, $params,$id);
        if ($result !== true) {
            $this->error($result);
        }
        try {
            $params= Form::updateFormParams(Form::LEADS_TYPE, $params);
            LeadsModel::updateLeads($params);
        } catch (Exception $e) {
            $this->error($e->getMessage());
        }
        if ($result) {
            $this->success('修改线索成功');
        }
    }


    //删除线索
    public function delLeads() {
        $id    = input('id');
        $model = new LeadsModel();
        $row   = $model->where(['owner_staff_id' => $this->auth->id, 'id' => $id])->find();
        if (empty($row)) {
            $this->error('您没有权限删除线索');
        }
        if($row->owner_staff_id != $this->auth->id){
            $this->error('您没有权限删除线索');
        }
        Db::startTrans();
        try{
            $model->where(['id' => $id])->delete();
            $enentWhere = [
                'relation_id'   =>  $id,
                'type'          =>  2,
                'relation_type'   =>  Event::LEADS_TYPE,
            ];
            Event::where($enentWhere)->delete();
            Db::commit();
        }catch (Exception $e){
            Db::rollback();
            $this->error($e->getMessage());
        }
        $this->success('删除成功');
    }


    //获取附件列表
    public function getFilesList() {
        $id = input('leads_id');

        $files = LeadsFile::where(['leads_id' => $id])->field('file_id')->with(['file'])->select();
        $this->success('请求成功', $files);
    }

}
