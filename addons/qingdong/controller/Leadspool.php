<?php

namespace addons\qingdong\controller;

use addons\qingdong\model\Event;
use addons\qingdong\model\Leads as LeadsModel;
use addons\qingdong\model\LeadsFile;
use addons\qingdong\model\LeadsOther;
use addons\qingdong\model\Staff;
use addons\qingdong\model\Record;
use think\Db;
use think\Exception;

/**
 * 线索池接口
 */
class Leadspool extends StaffApi {
    protected $noNeedLogin = [];
    protected $noNeedRight = [];
    public function _initialize()
    {
        parent::_initialize();
        try{
            \think\Db::execute("SET @@sql_mode='';");
        }catch (Exception $e){

        }
    }

    //添加线索
    public function addLeads() {
        $params = $this->request->post();
        if (empty($params['leads'])) {
            $this->error('线索信息不能为空');
        }

        try {
            $leadsId                     = LeadsModel::createLeads($params['leads'],1);
        } catch (Exception $e) {
            $this->error($e->getMessage());
        }
        if ($leadsId) {
            $this->success('添加线索成功');
        }
    }


    //获取线索列表
    public function getList() {
        $name   = input('name', '', 'trim');
        $mobile = input('mobile', '', 'trim');
        $limit  = input("limit/d", 10);
        $params = $this->request->post();
        $where = [];

        if (isset($params['createtime']) && $params['createtime']) {//跟进状态
            $createtime          = $params['createtime'];
            $createtime          = explode(',', $createtime);
            $where['createtime'] = ['between', [strtotime($createtime[0]), strtotime($createtime[1])+86400-1]];
        }


        if ($name) {
            $where['name'] = ['like', "%{$name}%"];
        }
        if ($mobile) {
            $where['mobile'] = ['like', "%{$mobile}%"];
        }
        $records           = LeadsModel::where($where)->where('owner_staff_id is null or owner_staff_id = 0')->with(['createStaff'])->field('id,create_staff_id,name,follow,mobile,level,next_time,source')->order('id desc')->paginate($limit);
        $this->success('请求成功', $records);
    }


    //获取线索详情
    public function getDetail() {
        $id    = input('id', '', 'intval');
        $leads = LeadsModel::where(['id' => $id])->with([
            'createStaff',
        ])->find();
        if(empty($leads)){
            $this->error('信息不存在');
        }
        $leads=$leads->toArray();
        $leads=LeadsOther::getOther($leads);
        $this->success('请求成功', $leads);
    }


    //获取选择列表
    public function getSelectList() {
        $name = input('name','');

        $where   = ['owner_staff_id' => $this->auth->id,'is_transform'=>0];
        if ($name) {
            $where['name'] = ['like',"%$name%"];
        }

        $records = LeadsModel::where($where)->field('id,owner_staff_id,name,follow,mobile')->order('id desc')->select();
        $this->success('请求成功', $records);
    }


    //转移线索
    public function transfer()
    {
        $id = input('id');
        $staff_id = input('staff_id');
        if (!$staff_id && !$id) {
            $this->error('参数错误');
        }
        $row = LeadsModel::where(['id' =>['in',$id] ])->find();
        if (empty($row)) {
            $this->error('线索不存在');
        }
        $id = explode(',',$id);
        try {
            LeadsModel::transfer($id, $staff_id);
        } catch (Exception $e) {
            $this->error($e->getMessage());
        }
        $this->success('转移成功');
    }


    //修改线索
    public function editLeads() {

        $id     = input('id');
        $params = $this->request->post();
        $row    = LeadsModel::where(['id' => $id])->find();
        if (empty($row)) {
            $this->error('线索信息不存在');
        }

        try {
            $result = LeadsModel::updateLeads($params);
        } catch (Exception $e) {
            $this->error($e->getMessage());
        }
        if ($result) {
            $this->success('修改线索成功');
        }
    }


    //删除线索
    public function delLeads() {
        $id    = input('id');
        $model = new LeadsModel();
        $row   = $model->where(['id' => $id])->find();
        if (empty($row)) {
            $this->error('线索不存在');
        }

        Db::startTrans();
        try{
            $model->where(['id' => $id])->delete();
            $enentWhere = [
                'relation_id'   =>  $id,
                'type'          =>  2,
                'relation_type'   =>  Event::LEADS_TYPE,
            ];
            Event::where($enentWhere)->delete();
            Db::commit();
        }catch (Exception $e){
            Db::rollback();
            $this->error($e->getMessage());
        }
        $this->success('删除成功');
    }


    //获取附件列表
    public function getFilesList() {
        $id = input('leads_id');

        $files = LeadsFile::where(['leads_id' => $id])->field('file_id')->with(['file'])->select();
        $this->success('请求成功', $files);
    }
//领取线索
    public function achieve()
    {
        $id = input('id');
        if (empty($id)) {
            $this->error('参数错误');
        }
        $row = LeadsModel::where(['id' =>$id ])->find();
        if (empty($row)) {
            $this->error('线索不存在');
        }
        try {
            LeadsModel::transfer($id, $this->auth->id);
        } catch (Exception $e) {
            $this->error($e->getMessage());
        }
        $this->success('领取成功');
    }

}
