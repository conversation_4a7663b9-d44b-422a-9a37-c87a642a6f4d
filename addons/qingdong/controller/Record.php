<?php

namespace addons\qingdong\controller;

use addons\qingdong\model\Comment;
use addons\qingdong\model\Contacts;
use addons\qingdong\model\Message;
use addons\qingdong\model\Record as RecordModel;
use addons\qingdong\model\RecordRead;
use addons\qingdong\model\Customer;
use addons\qingdong\model\Contract;
use addons\qingdong\model\Leads;
use addons\qingdong\model\Staff;
use addons\qingdong\model\Business;
use think\Db;
use think\Exception;
use function EasyWeChat\Kernel\Support\get_client_ip;

/**
 * 跟进记录
 */
class Record extends StaffApi {
	protected $noNeedLogin = [];
	protected $noNeedRight = [];

    /**
     * 获取跟进记录
     */
	public function getList() {
		$relation_type = input('relation_type', '', 'intval');// 1客户 2联系人 3合同 5商机
		$relation_id   = input('relation_id', '', 'intval');
		$limit         = input("limit/d", 10);
		$is_read       = input('is_read', 0);
		$type       = input('type', 0);// 0 全部 1 我创建 2 下属创建
        $follow_type       = input('follow_type', '');
        $times = input('times','');
		$where         = [];
		if ($relation_type) {
			$where['relation_type'] = $relation_type;
			if($relation_id){
                $where['relation_id']   = $relation_id;
            }

		}
        if ($type == 1) {//我的客户
            $where['create_staff_id'] = $this->auth->id;
        } elseif ($type == 2) {//下属负责的客户
            $where['create_staff_id'] = ['in', Staff::getLowerStaffId()];
        }else{
            $where['create_staff_id'] = ['in', Staff::getMyStaffIds()];
        }

		if($follow_type){
            $where['follow_type'] = $follow_type;
        }else{
            $where['follow_type'] = ['neq', '其它'];
        }
        if ($times) {
            $times = explode(',', $times);
            $where['createtime'] = ['between', [strtotime($times[0]), strtotime($times[1]) + 86400 - 1]];
        }
		$staff_id             = $this->auth->id;
		if ($is_read == 1) {//已读
			$ids         = RecordRead::where(['staff_id' => $staff_id])->column('record_id');
			$where['id'] = ['in', $ids];
		} elseif ($is_read == 2) {//未读
			$ids         = RecordRead::where(['staff_id' => $staff_id])->column('record_id');
			$where['id'] = ['not in', $ids];
		}
		$records = RecordModel::where($where)->with([
			'staff',
			'file',
			'read' => function ($query) use ($staff_id) {
				$query->where(['staff_id' => $staff_id]);
			}
		])->order('id desc')->paginate($limit)->toArray();

		$data = $records['data'];
		foreach ($data as $k => $v) {
            $customerWhere['id'] = $v['relation_id']?? '';
            $v['comment_num'] =0;//评论数

            if($v['relation_type'] == RecordModel::CUSTOMER_TYPE) {
                $v['comment_num'] =Comment::where(array('relation_type'=>1,'relation_id'=>$v['id']))->count();
                $v['relation_name'] = Customer::where(['id' => $v['relation_id']])->value('name');
            }elseif($v['relation_type'] == RecordModel::CONTACTS_TYPE) {
                $v['comment_num'] =Comment::where(array('relation_type'=>2,'relation_id'=>$v['id']))->count();
                $v['relation_name'] = Contacts::where(['id' => $v['relation_id']])->value('name');
            }elseif($v['relation_type'] == RecordModel::CONTRACT_TYPE) {
                $v['comment_num'] =Comment::where(array('relation_type'=>3,'relation_id'=>$v['id']))->count();
                $v['relation_name'] = Contract::where(['id' => $v['relation_id']])->value('name');
            }elseif($v['relation_type'] == RecordModel::LEADS_TYPE) {
                $v['comment_num'] =Comment::where(array('relation_type'=>4,'relation_id'=>$v['id']))->count();
                $v['relation_name'] = Leads::where(['id' => $v['relation_id']])->value('name');
            } elseif($v['relation_type'] == RecordModel::BUSINESS_TYPE) {
                $v['comment_num'] =Comment::where(array('relation_type'=>5,'relation_id'=>$v['id']))->count();
                $v['relation_name'] = Business::where(['id' => $v['relation_id']])->value('name');
            }else{
                $v['customer']  = [];
            }
			if (!empty($v['read'])) {
				$v['is_read'] = 1;
			} else {
				$v['is_read'] = 0;
			}
			if($v['staff_id']){
                $v['staff'] = Staff::where(['id'=>$v['staff_id']])->field('id,img,name,post')->find();
            }
			$data[$k] = $v;
		}
		$this->success('请求成功', [
			'total'        => $records['total'],
			'per_page'     => $records['per_page'],
			'current_page' => $records['current_page'],
			'last_page'    => $records['last_page'],
			'data'         => $data
		]);

		$this->success('请求成功', $records);
	}


    /**
     * 创建跟进记录
     */
	public function createRecord() {
		$params = $this->request->post();
		// 表单验证
        if (($result = $this->qingdongValidate($params, get_class(), 'create')) !== true) {
            $this->error($result);
        }
		Db::startTrans();
		try {
			$result             = RecordModel::createRecord($params);
			Db::commit();
		} catch (Exception $e) {
			Db::rollback();
			$this->error($e->getMessage());
		}
		if ($result) {
			$this->success('创建跟进记录成功');
		}
	}


    /**
     * 获取根据记录详情
     */
	public function getRecordDetail() {
		$id = input('id');
		if (empty($id)) {
			$this->error('参数不能为空');
		}
		$record = RecordModel::where(['id' => $id])->with([
			'staff',
			'file'
		])->find();
		if (empty($record)) {
			$this->error('根据记录不存在');
		}
        $record = $record->toArray();

		if ($record['relation_type'] == RecordModel::CUSTOMER_TYPE) {
			$record['relation_name'] = Customer::where(['id' => $record['relation_id']])->value('name');
		} elseif ($record['relation_type'] == RecordModel::CONTACTS_TYPE) {
			$record['relation_name'] = Contacts::where(['id' => $record['relation_id']])->value('name');
		} elseif ($record['relation_type'] == RecordModel::CONTRACT_TYPE) {
			$record['relation_name'] = Contract::where(['id' => $record['relation_id']])->value('name');
		} elseif($record['relation_type'] == RecordModel::LEADS_TYPE) {
			$record['relation_name'] = Leads::where(['id' => $record['relation_id']])->value('name');
		} elseif($record['relation_type'] == RecordModel::BUSINESS_TYPE) {
            $record['relation_name'] = Business::where(['id' => $record['relation_id']])->value('name');
        }else{
            $record['relation_name']='';
        }

		$reminds_id           = $record['reminds_id'];
		$reminds_id           = explode(',', $reminds_id);
		$names                = Staff::where(['id' => ['in', $reminds_id]])->column('name');
		$record['staff_name'] = implode(',', $names);
        if($record['staff_id']){
            $record['staff'] = Staff::where(['id'=>$record['staff_id']])->field('id,img,name,post')->find();
        }
		//标记通知已读
		Message::setRead(Message::RECORD_TYPE, $id, $this->auth->id);
		//添加阅读记录
		RecordRead::addRead($id, $this->auth->id);

		$this->success('请求成功', $record);
	}


    /**
     * 添加评论
     */
	public function addComment() {
		$content   = input('content');
		$record_id = input('record_id');
        $relation_type = input('relation_type');
		if (empty($content)) {
			$this->error('评论内容不能为空');
		}
		$data         = [
			'relation_type' => $relation_type,
			'relation_id'   => $record_id,
			'staff_id'      => $this->auth->id,
			'content'       => $content,
			'status'        => 1,
			'ip'            => get_client_ip(),
		];


		$commentModel = new Comment();
		$commentModel->save($data);

		$record = RecordModel::get($record_id);
		Message::addMessage(Message::COMMENT_TYPE,$record_id,$record['create_staff_id'],$this->auth->id);

		$staff_ids=$commentModel->where(['relation_type'=>$relation_type,'relation_id'=>$record_id])->group('staff_id')->column('staff_id');
		foreach ($staff_ids as $staff_id) {
			//发送通知
			if($staff_id != $this->auth->id){
				Message::addMessage(Message::COMMENT_TYPE,$record_id,$staff_id,$this->auth->id);
			}
		}
		$this->success('评论成功');
	}


    /**
     * 评论列表
     */
	public function commentList() {
		$record_id = input('record_id');
        $relation_type = input('relation_type');
		$comments = Comment::where([
			'relation_type' => $relation_type,
			'relation_id'   => $record_id,
			'status'        => 1
		])->field('id,staff_id,content,createtime')->with(['staff'])->select();
		$this->success('请求成功', $comments);
	}

    /**
     * 获取跟进客户
     */
    public function getcustomerList() {
        $limit         = input("limit/d", 10);
        $time       = input('time', 0);
        $type       = input('type', 1);

        $where['create_staff_id'] = $this->auth->id;
        if($type == 6){
            $where['relation_type'] = 3;
        }elseif($type == 7){
            $where['relation_type'] = 4;
        }elseif($type == 8){
            $where['relation_type'] = 2;
        }elseif($type == 9){
            $where['relation_type'] = 5;
        }else{
            $where['relation_type'] = 1;
        }
        $where['follow_type'] = ['neq', '其它'];
        $staff_id             = $this->auth->id;
        if ($time == 1) {//7天
            $where['next_time'] = array(array('egt',date('Y-m-d', strtotime('-7 day'))),array('lt',date('Y-m-d', strtotime('+1 day'))));
        } elseif ($time == 2) {//14天
            $where['next_time'] = array(array('egt',date('Y-m-d', strtotime('-14 day'))),array('lt',date('Y-m-d', strtotime('+1 day'))));
        }elseif($time == 3){
            $where['next_time'] = array(array('egt',date('Y-m-d', strtotime('-30 day'))),array('lt',date('Y-m-d', strtotime('+1 day'))));
        }elseif($time == 4){ //今日
            $where['next_time'] = array(array('egt',date('Y-m-d 00:00:00')),array('elt',date('Y-m-d 23:59:59')));
        }
        $where['status'] = 0;
        $records = RecordModel::where($where)->with([
            'staff',
            'file',
            'read' => function ($query) use ($staff_id) {
                $query->where(['staff_id' => $staff_id]);
            }
        ])->order('id desc')->paginate($limit)->toArray();

        $data = $records['data'];
        foreach ($data as $k => $v) {
            $customerWhere['id'] = $v['relation_id']?? '';
            if($type == 6){
                //合同
                $v['customer']  = Contract::where($customerWhere)->find();
            }elseif($type == 7){
                //线索
                $v['customer']  = Leads::where($customerWhere)->find();
            }elseif($type == 8){
                //联系人
                $v['customer']  = Contacts::where($customerWhere)->find();
            }elseif($type == 9){
                //商机
                $v['customer']  = Business::where($customerWhere)->find();
            }
            else{
                $v['customer'] = Customer::where($customerWhere)->find();
            }
            if (!empty($v['read'])) {
                $v['is_read'] = 1;
            } else {
                $v['is_read'] = 0;
            }
            $data[$k] = $v;
        }
        $this->success('请求成功', [
            'total'        => $records['total'],
            'per_page'     => $records['per_page'],
            'current_page' => $records['current_page'],
            'last_page'    => $records['last_page'],
            'data'         => $data
        ]);

        $this->success('请求成功', $records);
    }
    /*
     *待办跟进
     */
    public function record_add(){
        $type = input('type',0);
        $content   = input('remarks');
        $record_id = input('record_id');
        $relation_type = input('relation_type');
        if (empty($content)) {
            $this->error('备注不能为空');
        }
        $data         = [
            'relation_type' => $relation_type,
            'relation_id'   => $record_id,
            'staff_id'      => $this->auth->id,
            'content'       => $content,
            'status'        => 1,
            'ip'            => get_client_ip(),
        ];
        Db::startTrans();
        $commentModel = new Comment();
        $resultC = $commentModel->save($data);
        $recordU = RecordModel::where(array('id'=>$record_id))->update(array('status'=>1,'updatetime'=>time()));
        $resultR= true;
        if($type ==1){

            $params = $this->request->post();
            // 表单验证
            if (($result = $this->qingdongValidate($params, get_class(), 'create')) !== true) {
                $this->error($result);
            }
            try {
                unset($params['type']);
                unset($params['remarks']);
                unset($params['record_id']);
                $resultR             = RecordModel::createRecord($params);
            } catch (Exception $e) {
                Db::rollback();
                $this->error($e->getMessage());
            }
        }
        if(!$resultC || !$resultR || !$recordU){
            Db::rollback();
            $this->error('跟进失败');
        }
        Db::commit();
        $this->success('跟进成功');
    }
}
