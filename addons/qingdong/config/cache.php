<?php 
 return array (
  'table_name' => 'fa_qingdong_achievement,fa_qingdong_achievement_records,fa_qingdong_admin_config,fa_qingdong_approval,fa_qingdong_attendance,fa_qingdong_attendance_address,fa_qingdong_attendance_card,fa_qingdong_attendance_leave,fa_qingdong_attendance_rule,fa_qingdong_attendance_statisc,fa_qingdong_attendance_time,fa_qingdong_business,fa_qingdong_business_other,fa_qingdong_business_product,fa_qingdong_business_status,fa_qingdong_comment,fa_qingdong_consume,fa_qingdong_contacts,fa_qingdong_contacts_file,fa_qingdong_contacts_other,fa_qingdong_contract,fa_qingdong_contract_file,fa_qingdong_contract_other,fa_qingdong_contract_product,fa_qingdong_contract_ratio,fa_qingdong_customer,fa_qingdong_customer_file,fa_qingdong_customer_other,fa_qingdong_daily,fa_qingdong_daily_draft,fa_qingdong_daily_read,fa_qingdong_ding_contacts,fa_qingdong_ding_customer,fa_qingdong_ding_record,fa_qingdong_ding_staff,fa_qingdong_event,fa_qingdong_examine_record,fa_qingdong_fauser,fa_qingdong_feedback,fa_qingdong_field,fa_qingdong_file,fa_qingdong_flow,fa_qingdong_form,fa_qingdong_form_approval,fa_qingdong_form_field,fa_qingdong_goods,fa_qingdong_ku_customer,fa_qingdong_leads,fa_qingdong_leads_file,fa_qingdong_leads_other,fa_qingdong_login_token,fa_qingdong_message,fa_qingdong_notice,fa_qingdong_notice_template,fa_qingdong_operation_log,fa_qingdong_product,fa_qingdong_product_part,fa_qingdong_product_type,fa_qingdong_push_reload,fa_qingdong_receivables,fa_qingdong_receivables_other,fa_qingdong_receivables_plan,fa_qingdong_record,fa_qingdong_record_file,fa_qingdong_record_read,fa_qingdong_remind,fa_qingdong_seas_type,fa_qingdong_send_template,fa_qingdong_staff,fa_qingdong_staff_collect,fa_qingdong_staff_department,fa_qingdong_staff_ratio,fa_qingdong_staff_role,fa_qingdong_staff_rule,fa_qingdong_staff_sign_in',
  'self_path' => '',
  'update_data' => 'INSERT INTO `__PREFIX__qingdong_admin_config`
VALUES (1, \'seas\', \'auto\', \'1\', 1631875558, 1631875558, NULL);
INSERT INTO `__PREFIX__qingdong_admin_config`
VALUES (2, \'seas\', \'genjing\', \'1\', 1631875558, 1631875558, NULL);
INSERT INTO `__PREFIX__qingdong_admin_config`
VALUES (3, \'seas\', \'genjing_success\', \'1\', 1631875558, 1631875558, NULL);
INSERT INTO `__PREFIX__qingdong_admin_config`
VALUES (4, \'seas\', \'genjing_day\', \'0\', 1631875558, 1631875558, NULL);
INSERT INTO `__PREFIX__qingdong_admin_config`
VALUES (5, \'seas\', \'chengjiao\', \'0\', 1631875558, 1631875558, NULL);
INSERT INTO `__PREFIX__qingdong_admin_config`
VALUES (6, \'seas\', \'chengjiao_day\', \'0\', 1631875558, 1631875558, NULL);
INSERT INTO `__PREFIX__qingdong_admin_config`
VALUES (7, \'wechat\', \'mini_appid\', \'\', 1631875559, 1631875559, NULL);
INSERT INTO `__PREFIX__qingdong_admin_config`
VALUES (8, \'wechat\', \'mini_secret\', \'\', 1631875559, 1631875559, NULL);
INSERT INTO `__PREFIX__qingdong_admin_config`
VALUES (9, \'wechat\', \'appid\', \'\', 1631875559, 1631875559, NULL);
INSERT INTO `__PREFIX__qingdong_admin_config`
VALUES (10, \'wechat\', \'secret\', \'\', 1631875559, 1631875559, NULL);
INSERT INTO `__PREFIX__qingdong_admin_config`
VALUES (11, \'wechat\', \'examine_template\', \'0\', 1631875559, 1631875559, NULL);
INSERT INTO `__PREFIX__qingdong_admin_config`
VALUES (12, \'wechat\', \'record_template\', \'0\', 1631875559, 1631875559, NULL);
INSERT INTO `__PREFIX__qingdong_admin_config`
VALUES (13, \'wechat\', \'map_key\', \'\', 1631875559, 1631875559, NULL);
INSERT INTO `__PREFIX__qingdong_admin_config`
VALUES (14, \'wechat\', \'web_url\', \'\', 1631875559, 1631875559, NULL);
INSERT INTO `__PREFIX__qingdong_admin_config`
VALUES (15, \'dingding\', \'ding_key\', \'\', 1631875559, 1631875559, NULL);
INSERT INTO `__PREFIX__qingdong_admin_config`
VALUES (16, \'dingding\', \'ding_secret\', \'\', 1631875559, 1631875559, NULL);
INSERT INTO `__PREFIX__qingdong_field`
VALUES (138, \'leads\', \'线索来源\', \'[\\"电话营销\\",\\"拜访\\"]\');
INSERT INTO `__PREFIX__qingdong_field`
VALUES (139, \'leads\', \'客户行业\', \'[\\"互联网\\",\\"信息化\\"]\');
INSERT INTO `__PREFIX__qingdong_field`
VALUES (141, \'examine\', \'回款方式\', \'[\\"微信\\"]\');
INSERT INTO `__PREFIX__qingdong_field`
VALUES (147, \'contacts\', \'角色\',
        \'[\\"普通员工\\",\\"决策人\\",\\"分项决策人\\",\\"商务决策\\",\\"财务决策\\",\\"使用人\\",\\"意见影响人\\",\\"采购\\",\\"老板\\",\\"股东\\",\\"职业经理人\\"]\');
INSERT INTO `__PREFIX__qingdong_field`
VALUES (148, \'other\', \'跟进类型\', \'[\\"到访\\",\\"电话\\"]\');
INSERT INTO `__PREFIX__qingdong_field`
VALUES (149, \'other\', \'消费方式\', \'[\\"吃饭\\",\\"住酒店\\"]\');
INSERT INTO `__PREFIX__qingdong_field`
VALUES (151, \'customer\', \'客户所属\', \'[\\"重点客户\\",\\"普通客户\\",\\"非优先客户\\"]\');
INSERT INTO `__PREFIX__qingdong_field`
VALUES (152, \'customer\', \'客户状态\', \'[\\"初次接触\\",\\"正在跟进\\",\\"准备购买\\",\\"准备付款\\",\\"已经购买\\",\\"暂时搁置\\"]\');
INSERT INTO `__PREFIX__qingdong_field`
VALUES (153, \'customer\', \'客户来源\', \'[\\"行业活动\\",\\"搜索引擎\\",\\"客户转介绍\\",\\"展会\\",\\"朋友介绍\\",\\"市场推广\\"]\');
INSERT INTO `__PREFIX__qingdong_field`
VALUES (158, \'daily\', \'报告类型\', \'[\\"日报\\",\\"周报\\",\\"月报\\",\\"季报\\",\\"年报\\"]\');
INSERT INTO `__PREFIX__qingdong_form` VALUES (11, \'线索管理\', \'leads\', \'{\\"data\\":[{\\"id\\":\\"name\\",\\"name\\":\\"单行输入框\\",\\"type\\":1,\\"component\\":\\"input\\",\\"config\\":{\\"type\\":\\"text\\",\\"label\\":\\"线索名称\\",\\"readonly\\":false,\\"placeholder\\":\\"线索名称\\",\\"required\\":true,\\"is_delete\\":true}},{\\"id\\":\\"source\\",\\"name\\":\\"下拉选择框\\",\\"type\\":1,\\"component\\":\\"select\\",\\"config\\":{\\"label\\":\\"线索来源\\",\\"type\\":null,\\"content\\":[{\\"key\\":\\"\\",\\"value\\":\\"电话营销\\",\\"nodeKey\\":1,\\"isEdit\\":false,\\"label\\":\\"电话营销\\",\\"children\\":[],\\"expand\\":true,\\"__label\\":\\"电话营销\\",\\"__value\\":[\\"电话营销\\"]},{\\"key\\":\\"\\",\\"value\\":\\"主动来电\\",\\"__label\\":\\"拜访\\",\\"__value\\":[\\"主动来电\\"],\\"nodeKey\\":2,\\"isEdit\\":false,\\"label\\":\\"拜访\\"}],\\"required\\":true,\\"multiple\\":false,\\"is_delete\\":true,\\"placeholder\\":\\"请选择线索来源\\"}},{\\"id\\":\\"industry\\",\\"name\\":\\"下拉选择框\\",\\"type\\":1,\\"component\\":\\"select\\",\\"config\\":{\\"label\\":\\"客户行业\\",\\"type\\":null,\\"content\\":[{\\"key\\":\\"\\",\\"value\\":\\"行业1\\",\\"__label\\":\\"互联网\\",\\"__value\\":[\\"行业1\\"],\\"nodeKey\\":1,\\"isEdit\\":false,\\"label\\":\\"互联网\\"},{\\"key\\":\\"\\",\\"value\\":\\"行业2\\",\\"__value\\":[\\"行业2\\"],\\"nodeKey\\":2,\\"isEdit\\":false,\\"label\\":\\"信息化\\",\\"__label\\":\\"信息化\\"}],\\"required\\":true,\\"multiple\\":false,\\"is_delete\\":true,\\"placeholder\\":\\"请选择客户行业\\"}},{\\"id\\":\\"mobile\\",\\"name\\":\\"单行输入框\\",\\"type\\":1,\\"component\\":\\"input\\",\\"config\\":{\\"type\\":\\"text\\",\\"label\\":\\"手机号\\",\\"readonly\\":false,\\"placeholder\\":\\"手机号\\",\\"required\\":true,\\"is_delete\\":true}},{\\"id\\":\\"level\\",\\"name\\":\\"评分\\",\\"type\\":1,\\"component\\":\\"Rate\\",\\"config\\":{\\"label\\":\\"客户级别\\",\\"allowHalf\\":true,\\"disabled\\":false,\\"type\\":\\"default\\",\\"is_delete\\":true}},{\\"id\\":\\"telephone\\",\\"name\\":\\"单行输入框\\",\\"type\\":1,\\"component\\":\\"input\\",\\"config\\":{\\"type\\":\\"text\\",\\"label\\":\\"电话\\",\\"readonly\\":false,\\"placeholder\\":\\"电话\\",\\"required\\":false,\\"is_delete\\":true}},{\\"id\\":\\"address_detail\\",\\"name\\":\\"文本框\\",\\"type\\":1,\\"component\\":\\"textarea\\",\\"config\\":{\\"label\\":\\"地址\\",\\"readonly\\":false,\\"row\\":2,\\"placeholder\\":\\"请输入地址\\",\\"required\\":false,\\"is_delete\\":true}},{\\"id\\":\\"next_time\\",\\"name\\":\\"时间选择框\\",\\"type\\":1,\\"component\\":\\"TimePicker\\",\\"config\\":{\\"label\\":\\"下次联系时间\\",\\"placeholder\\":\\"点击选择\\",\\"type\\":\\"time\\",\\"format\\":\\"YYYY-MM-DD HH:mm:ss\\",\\"confirm\\":true,\\"required\\":false,\\"readonly\\":false,\\"is_delete\\":true}},{\\"id\\":\\"remarks\\",\\"name\\":\\"文本框\\",\\"type\\":1,\\"component\\":\\"textarea\\",\\"config\\":{\\"label\\":\\"备注信息\\",\\"readonly\\":false,\\"row\\":2,\\"placeholder\\":\\"备注信息\\",\\"required\\":false,\\"is_delete\\":true}}]}\', 1619592288, 1655433791, NULL);


INSERT INTO `__PREFIX__qingdong_form`
VALUES (12, \'客户管理\', \'customer\',
        \'{\\"data\\":[{\\"id\\":\\"name\\",\\"name\\":\\"单行输入框\\",\\"type\\":1,\\"component\\":\\"input\\",\\"config\\":{\\"type\\":\\"text\\",\\"label\\":\\"客户名称\\",\\"readonly\\":false,\\"placeholder\\":\\"请输入客户名称\\",\\"required\\":true,\\"is_delete\\":true}},{\\"id\\":\\"subname\\",\\"name\\":\\"单行输入框\\",\\"type\\":1,\\"component\\":\\"input\\",\\"config\\":{\\"type\\":\\"text\\",\\"label\\":\\"助记名称\\",\\"readonly\\":false,\\"placeholder\\":\\"请输入助记名称\\",\\"required\\":false,\\"is_delete\\":true}},{\\"id\\":\\"industry\\",\\"name\\":\\"下拉选择框\\",\\"type\\":1,\\"component\\":\\"select\\",\\"config\\":{\\"label\\":\\"客户所属\\",\\"type\\":null,\\"content\\":[{\\"key\\":\\"\\",\\"value\\":\\"终端客户\\",\\"__label\\":\\"重点客户\\",\\"__value\\":[\\"终端客户\\"],\\"nodeKey\\":1,\\"isEdit\\":false,\\"label\\":\\"重点客户\\"},{\\"key\\":\\"\\",\\"value\\":\\"代理商\\",\\"__label\\":\\"普通客户\\",\\"__value\\":[\\"代理商\\"],\\"nodeKey\\":2,\\"isEdit\\":false,\\"label\\":\\"普通客户\\"},{\\"key\\":\\"\\",\\"value\\":\\"同行\\",\\"__value\\":[\\"同行\\"],\\"nodeKey\\":3,\\"isEdit\\":false,\\"label\\":\\"非优先客户\\",\\"__label\\":\\"非优先客户\\"}],\\"required\\":true,\\"multiple\\":false,\\"is_delete\\":true,\\"placeholder\\":\\"请输入客户所属\\"}},{\\"id\\":\\"follow\\",\\"name\\":\\"下拉选择框\\",\\"type\\":1,\\"component\\":\\"select\\",\\"config\\":{\\"label\\":\\"客户状态\\",\\"type\\":null,\\"content\\":[{\\"key\\":\\"\\",\\"value\\":\\"初次接触\\",\\"__label\\":\\"初次接触\\",\\"__value\\":[\\"初次接触\\"],\\"nodeKey\\":1,\\"isEdit\\":false,\\"label\\":\\"初次接触\\"},{\\"key\\":\\"\\",\\"value\\":\\"正在跟进\\",\\"__label\\":\\"正在跟进\\",\\"__value\\":[\\"正在跟进\\"],\\"nodeKey\\":2,\\"isEdit\\":false,\\"label\\":\\"正在跟进\\"},{\\"key\\":\\"\\",\\"value\\":\\"准备购买\\",\\"__label\\":\\"准备购买\\",\\"__value\\":[\\"准备购买\\"],\\"nodeKey\\":3,\\"isEdit\\":false,\\"label\\":\\"准备购买\\"},{\\"key\\":\\"\\",\\"value\\":\\"准备付款\\",\\"__label\\":\\"准备付款\\",\\"__value\\":[\\"准备付款\\"],\\"nodeKey\\":4,\\"isEdit\\":false,\\"label\\":\\"准备付款\\"},{\\"key\\":\\"\\",\\"value\\":\\"已经购买\\",\\"__label\\":\\"已经购买\\",\\"__value\\":[\\"已经购买\\"],\\"nodeKey\\":5,\\"isEdit\\":false,\\"label\\":\\"已经购买\\"},{\\"key\\":\\"\\",\\"value\\":\\"暂时搁置\\",\\"__label\\":\\"暂时搁置\\",\\"__value\\":[\\"暂时搁置\\"],\\"nodeKey\\":6,\\"isEdit\\":false,\\"label\\":\\"暂时搁置\\"}],\\"required\\":true,\\"multiple\\":false,\\"is_delete\\":true,\\"placeholder\\":\\"请输入客户状态\\"}},{\\"id\\":\\"level\\",\\"name\\":\\"评分\\",\\"type\\":1,\\"component\\":\\"Rate\\",\\"config\\":{\\"label\\":\\"客户星级\\",\\"allowHalf\\":true,\\"disabled\\":false,\\"type\\":\\"default\\",\\"is_delete\\":true,\\"placeholder\\":\\"客户星级\\"}},{\\"id\\":\\"source\\",\\"name\\":\\"下拉选择框\\",\\"type\\":1,\\"component\\":\\"select\\",\\"config\\":{\\"label\\":\\"客户来源\\",\\"type\\":null,\\"content\\":[{\\"key\\":\\"\\",\\"value\\":\\"行业活动\\",\\"__label\\":\\"行业活动\\",\\"__value\\":[\\"行业活动\\"],\\"nodeKey\\":1,\\"isEdit\\":false,\\"label\\":\\"行业活动\\"},{\\"key\\":\\"\\",\\"value\\":\\"搜索引擎\\",\\"__label\\":\\"搜索引擎\\",\\"__value\\":[\\"搜索引擎\\"],\\"nodeKey\\":2,\\"isEdit\\":false,\\"label\\":\\"搜索引擎\\"},{\\"key\\":\\"\\",\\"value\\":\\"客户转介绍\\",\\"__label\\":\\"客户转介绍\\",\\"__value\\":[\\"客户转介绍\\"],\\"nodeKey\\":3,\\"isEdit\\":false,\\"label\\":\\"客户转介绍\\"},{\\"key\\":\\"\\",\\"value\\":\\"展会\\",\\"__label\\":\\"展会\\",\\"__value\\":[\\"展会\\"],\\"nodeKey\\":4,\\"isEdit\\":false,\\"label\\":\\"展会\\"},{\\"key\\":\\"\\",\\"value\\":\\"朋友介绍\\",\\"__label\\":\\"朋友介绍\\",\\"__value\\":[\\"朋友介绍\\"],\\"nodeKey\\":5,\\"isEdit\\":false,\\"label\\":\\"朋友介绍\\"},{\\"key\\":\\"\\",\\"value\\":\\"市场推广\\",\\"__value\\":[\\"市场推广\\"],\\"nodeKey\\":6,\\"isEdit\\":false,\\"label\\":\\"市场推广\\",\\"__label\\":\\"市场推广\\"}],\\"required\\":true,\\"multiple\\":false,\\"is_delete\\":true,\\"placeholder\\":\\"客户来源\\"}},{\\"id\\":\\"other_12771\\",\\"name\\":\\"文本框\\",\\"type\\":1,\\"component\\":\\"textarea\\",\\"config\\":{\\"label\\":\\"备注信息\\",\\"row\\":2,\\"placeholder\\":\\"请输入备注信息\\",\\"required\\":false,\\"is_delete\\":true}}]}\',
        1619592291, 1646217144, NULL);
INSERT INTO `__PREFIX__qingdong_form`
VALUES (13, \'联系人管理\', \'contacts\',
        \'{\\"data\\":[{\\"id\\":\\"name\\",\\"name\\":\\"单行输入框\\",\\"type\\":1,\\"component\\":\\"input\\",\\"config\\":{\\"type\\":\\"text\\",\\"label\\":\\"联系人姓名\\",\\"readonly\\":false,\\"placeholder\\":\\"请输入联系人姓名\\",\\"required\\":true,\\"is_delete\\":true}},{\\"id\\":\\"subname\\",\\"name\\":\\"单行输入框\\",\\"type\\":1,\\"component\\":\\"input\\",\\"config\\":{\\"type\\":\\"text\\",\\"label\\":\\"尊称\\",\\"readonly\\":false,\\"placeholder\\":\\"请输入尊称\\",\\"required\\":false,\\"is_delete\\":true}},{\\"id\\":\\"other_21517\\",\\"name\\":\\"单行输入框\\",\\"type\\":1,\\"component\\":\\"input\\",\\"config\\":{\\"type\\":\\"text\\",\\"label\\":\\"电话\\",\\"placeholder\\":\\"请输入联系人电话\\",\\"required\\":false,\\"is_delete\\":true}},{\\"id\\":\\"mobile\\",\\"name\\":\\"单行输入框\\",\\"type\\":1,\\"component\\":\\"input\\",\\"config\\":{\\"type\\":\\"text\\",\\"label\\":\\"手机号\\",\\"placeholder\\":\\"请输入联系人手机号\\",\\"required\\":true,\\"is_delete\\":true}},{\\"id\\":\\"role\\",\\"name\\":\\"下拉选择框\\",\\"type\\":1,\\"component\\":\\"select\\",\\"config\\":{\\"label\\":\\"角色\\",\\"type\\":null,\\"content\\":[{\\"key\\":\\"\\",\\"value\\":\\"普通员工\\",\\"__label\\":\\"普通员工\\",\\"__value\\":[\\"普通员工\\"],\\"nodeKey\\":1,\\"isEdit\\":false,\\"label\\":\\"普通员工\\"},{\\"key\\":\\"\\",\\"value\\":\\"决策人\\",\\"__label\\":\\"决策人\\",\\"__value\\":[\\"决策人\\"],\\"nodeKey\\":2,\\"isEdit\\":false,\\"label\\":\\"决策人\\"},{\\"key\\":\\"\\",\\"value\\":\\"分项决策人\\",\\"__label\\":\\"分项决策人\\",\\"__value\\":[\\"分项决策人\\"],\\"nodeKey\\":3,\\"isEdit\\":false,\\"label\\":\\"分项决策人\\"},{\\"key\\":\\"\\",\\"value\\":\\"商务决策\\",\\"__label\\":\\"商务决策\\",\\"__value\\":[\\"商务决策\\"],\\"nodeKey\\":4,\\"isEdit\\":false,\\"label\\":\\"商务决策\\"},{\\"key\\":\\"\\",\\"value\\":\\"财务决策\\",\\"__label\\":\\"财务决策\\",\\"__value\\":[\\"财务决策\\"],\\"nodeKey\\":5,\\"isEdit\\":false,\\"label\\":\\"财务决策\\"},{\\"key\\":\\"\\",\\"value\\":\\"使用人\\",\\"__label\\":\\"使用人\\",\\"__value\\":[\\"使用人\\"],\\"nodeKey\\":6,\\"isEdit\\":false,\\"label\\":\\"使用人\\"},{\\"key\\":\\"\\",\\"value\\":\\"意见影响人\\",\\"__label\\":\\"意见影响人\\",\\"__value\\":[\\"意见影响人\\"],\\"nodeKey\\":7,\\"isEdit\\":false,\\"label\\":\\"意见影响人\\"},{\\"key\\":\\"\\",\\"value\\":\\"采购\\",\\"__label\\":\\"采购\\",\\"__value\\":[\\"采购\\"],\\"nodeKey\\":8,\\"isEdit\\":false,\\"label\\":\\"采购\\"},{\\"key\\":\\"\\",\\"value\\":\\"老板\\",\\"__label\\":\\"老板\\",\\"__value\\":[\\"老板\\"],\\"nodeKey\\":9,\\"isEdit\\":false,\\"label\\":\\"老板\\"},{\\"key\\":\\"\\",\\"value\\":\\"股东\\",\\"__label\\":\\"股东\\",\\"__value\\":[\\"股东\\"],\\"nodeKey\\":10,\\"isEdit\\":false,\\"label\\":\\"股东\\"},{\\"key\\":\\"\\",\\"value\\":\\"职业经理人\\",\\"__value\\":[\\"职业经理人\\"],\\"nodeKey\\":11,\\"isEdit\\":false,\\"label\\":\\"职业经理人\\",\\"__label\\":\\"职业经理人\\"}],\\"required\\":true,\\"multiple\\":false,\\"is_delete\\":true,\\"placeholder\\":\\"请选择角色\\"}},{\\"id\\":\\"post\\",\\"name\\":\\"单行输入框\\",\\"type\\":1,\\"component\\":\\"input\\",\\"config\\":{\\"type\\":\\"text\\",\\"label\\":\\"部门职务\\",\\"placeholder\\":\\"请输入部门职务\\",\\"required\\":true,\\"is_delete\\":true}},{\\"id\\":\\"other_4367\\",\\"name\\":\\"日期选择框\\",\\"type\\":1,\\"component\\":\\"DatePicker\\",\\"config\\":{\\"label\\":\\"生日\\",\\"placeholder\\":\\"请输入联系人生日\\",\\"type\\":\\"date\\",\\"value\\":\\"\\",\\"required\\":false,\\"is_delete\\":true}},{\\"id\\":\\"email\\",\\"name\\":\\"单行输入框\\",\\"type\\":1,\\"component\\":\\"input\\",\\"config\\":{\\"type\\":\\"text\\",\\"label\\":\\"邮箱\\",\\"placeholder\\":\\"请输入邮箱\\",\\"required\\":false,\\"is_delete\\":true}},{\\"id\\":\\"other_17344\\",\\"name\\":\\"单行输入框\\",\\"type\\":1,\\"component\\":\\"input\\",\\"config\\":{\\"type\\":\\"text\\",\\"label\\":\\"微信\\",\\"placeholder\\":\\"请输入联系人微信\\",\\"required\\":false,\\"is_delete\\":true}},{\\"id\\":\\"other_23536\\",\\"name\\":\\"文本框\\",\\"type\\":1,\\"component\\":\\"textarea\\",\\"config\\":{\\"label\\":\\"备注信息\\",\\"row\\":2,\\"placeholder\\":\\"请输入备注信息\\",\\"required\\":false,\\"is_delete\\":true}}]}\',
        1619592291, 1643096926, NULL);
INSERT INTO `__PREFIX__qingdong_form`
VALUES (14, \'合同管理\', \'contract\',
        \'{\\"data\\":[{\\"id\\":\\"num\\",\\"name\\":\\"单行输入框\\",\\"type\\":1,\\"component\\":\\"input\\",\\"config\\":{\\"type\\":\\"text\\",\\"label\\":\\"合同编号\\",\\"readonly\\":false,\\"placeholder\\":\\"输入合同编号\\",\\"required\\":true,\\"is_delete\\":true}},{\\"id\\":\\"name\\",\\"name\\":\\"单行输入框\\",\\"type\\":1,\\"component\\":\\"input\\",\\"config\\":{\\"type\\":\\"text\\",\\"label\\":\\"合同名称\\",\\"readonly\\":false,\\"placeholder\\":\\"请输入合同名称\\",\\"required\\":true,\\"is_delete\\":true}},{\\"id\\":\\"money\\",\\"name\\":\\"单行输入框\\",\\"type\\":1,\\"component\\":\\"input\\",\\"config\\":{\\"type\\":\\"text\\",\\"label\\":\\"合同金额\\",\\"readonly\\":false,\\"placeholder\\":\\"请输入合同金额\\",\\"required\\":true,\\"is_delete\\":true}},{\\"id\\":\\"order_date\\",\\"name\\":\\"时间选择框\\",\\"type\\":1,\\"component\\":\\"TimePicker\\",\\"config\\":{\\"label\\":\\"下单时间\\",\\"placeholder\\":\\"点击选择\\",\\"type\\":\\"time\\",\\"format\\":\\"HH:mm:ss\\",\\"confirm\\":true,\\"required\\":true,\\"readonly\\":false,\\"is_delete\\":true}},{\\"id\\":\\"start_time\\",\\"name\\":\\"日期选择框\\",\\"type\\":1,\\"component\\":\\"DatePicker\\",\\"config\\":{\\"label\\":\\"合同开始时间\\",\\"placeholder\\":\\"点击选择\\",\\"type\\":\\"date\\",\\"format\\":\\"\\",\\"value\\":\\"\\",\\"required\\":true,\\"readonly\\":false,\\"is_delete\\":true}},{\\"id\\":\\"end_time\\",\\"name\\":\\"日期选择框\\",\\"type\\":1,\\"component\\":\\"DatePicker\\",\\"config\\":{\\"label\\":\\"合同结束时间\\",\\"placeholder\\":\\"点击选择\\",\\"type\\":\\"date\\",\\"format\\":\\"\\",\\"value\\":\\"\\",\\"required\\":true,\\"readonly\\":false,\\"is_delete\\":true}},{\\"id\\":\\"other_47006\\",\\"name\\":\\"单选框\\",\\"type\\":1,\\"component\\":\\"radio\\",\\"config\\":{\\"label\\":\\"是否含税\\",\\"content\\":[{\\"key\\":\\"\\",\\"value\\":\\"含税\\"},{\\"key\\":\\"\\",\\"value\\":\\"不含税\\"}],\\"placeholder\\":\\"请输入\\",\\"required\\":false,\\"is_delete\\":true}}]}\',
        1619592291, 1643020154, NULL);
INSERT INTO `__PREFIX__qingdong_form`
VALUES (15, \'回款管理\', \'examine\',
        \'{\\"data\\":[{\\"id\\":\\"number\\",\\"name\\":\\"单行输入框\\",\\"type\\":1,\\"component\\":\\"input\\",\\"config\\":{\\"type\\":\\"text\\",\\"label\\":\\"回款编号\\",\\"placeholder\\":\\"请输入回款编号\\",\\"required\\":true,\\"is_delete\\":true}},{\\"id\\":\\"return_time\\",\\"name\\":\\"日期选择框\\",\\"type\\":1,\\"component\\":\\"DatePicker\\",\\"config\\":{\\"label\\":\\"回款日期\\",\\"placeholder\\":\\"点击选择\\",\\"type\\":\\"date\\",\\"value\\":\\"\\",\\"required\\":true,\\"is_delete\\":true}},{\\"id\\":\\"money\\",\\"name\\":\\"单行输入框\\",\\"type\\":1,\\"component\\":\\"input\\",\\"config\\":{\\"type\\":\\"text\\",\\"label\\":\\"回款金额\\",\\"placeholder\\":\\"请输入回款金额\\",\\"required\\":true,\\"is_delete\\":true}},{\\"id\\":\\"return_type\\",\\"name\\":\\"下拉选择框\\",\\"type\\":1,\\"component\\":\\"select\\",\\"config\\":{\\"label\\":\\"回款方式\\",\\"type\\":null,\\"content\\":[{\\"key\\":\\"\\",\\"value\\":\\"微信\\",\\"__label\\":\\"微信\\",\\"__value\\":[\\"微信\\"],\\"nodeKey\\":1,\\"isEdit\\":false,\\"label\\":\\"微信\\"}],\\"required\\":true,\\"is_delete\\":true}}]}\',
        1619592291, 1643020163, NULL);
INSERT INTO `__PREFIX__qingdong_form` VALUES (16, \'日报管理\', \'daily\', \'{\\"data\\":[{\\"id\\":\\"other_43472\\",\\"name\\":\\"文本框\\",\\"type\\":1,\\"component\\":\\"textarea\\",\\"config\\":{\\"label\\":\\"1.1.完成了哪些工作\\",\\"row\\":2,\\"placeholder\\":\\"请输入内容\\",\\"required\\":true,\\"is_delete\\":true}},{\\"id\\":\\"other_48871\\",\\"name\\":\\"文本框\\",\\"type\\":1,\\"component\\":\\"textarea\\",\\"config\\":{\\"label\\":\\"1.2.取得了哪些成绩\\",\\"row\\":2,\\"placeholder\\":\\"请输入内容\\",\\"required\\":true,\\"is_delete\\":true}},{\\"id\\":\\"other_33048\\",\\"name\\":\\"文本框\\",\\"type\\":1,\\"component\\":\\"textarea\\",\\"config\\":{\\"label\\":\\"1.3.当前进展如何\\",\\"row\\":2,\\"placeholder\\":\\"请输入内容\\",\\"required\\":true,\\"is_delete\\":true}},{\\"id\\":\\"other_82389\\",\\"name\\":\\"文本框\\",\\"type\\":1,\\"component\\":\\"textarea\\",\\"config\\":{\\"label\\":\\"1.4.计划实施情况\\",\\"row\\":2,\\"placeholder\\":\\"请输入内容\\",\\"required\\":true,\\"is_delete\\":true}}]}\', 1619491859, 1640141699, NULL);
INSERT INTO `__PREFIX__qingdong_form` VALUES (17, \'周报管理\', \'weekly\', \'{\\"data\\":[{\\"id\\":\\"other_72263\\",\\"name\\":\\"文本框\\",\\"type\\":1,\\"component\\":\\"textarea\\",\\"config\\":{\\"label\\":\\"1.1.完成了哪些工作\\",\\"row\\":2,\\"placeholder\\":\\"请输入内容\\",\\"required\\":false,\\"is_delete\\":true}},{\\"id\\":\\"other_22415\\",\\"name\\":\\"文本框\\",\\"type\\":1,\\"component\\":\\"textarea\\",\\"config\\":{\\"label\\":\\"1.2.取得了哪些成绩\\",\\"row\\":2,\\"placeholder\\":\\"请输入内容\\",\\"required\\":false,\\"is_delete\\":true}},{\\"id\\":\\"other_41856\\",\\"name\\":\\"文本框\\",\\"type\\":1,\\"component\\":\\"textarea\\",\\"config\\":{\\"label\\":\\"1.3.当前进展如何\\",\\"row\\":2,\\"placeholder\\":\\"请输入内容\\",\\"required\\":false,\\"is_delete\\":true}}]}\', 1643020163, 1643020163, NULL);
INSERT INTO `__PREFIX__qingdong_form` VALUES (18, \'月报管理\', \'monthly\', \'{\\"data\\":[{\\"id\\":\\"other_79661\\",\\"name\\":\\"文本框\\",\\"type\\":1,\\"component\\":\\"textarea\\",\\"config\\":{\\"label\\":\\"1.1.完成了哪些工作\\",\\"row\\":2,\\"placeholder\\":\\"请输入内容\\",\\"required\\":false,\\"is_delete\\":true}},{\\"id\\":\\"other_52344\\",\\"name\\":\\"文本框\\",\\"type\\":1,\\"component\\":\\"textarea\\",\\"config\\":{\\"label\\":\\"1.2.取得了哪些成绩\\",\\"row\\":2,\\"placeholder\\":\\"请输入内容\\",\\"required\\":false,\\"is_delete\\":true}},{\\"id\\":\\"other_15457\\",\\"name\\":\\"文本框\\",\\"type\\":1,\\"component\\":\\"textarea\\",\\"config\\":{\\"label\\":\\"1.3.当前进展如何\\",\\"row\\":2,\\"placeholder\\":\\"请输入内容\\",\\"required\\":false,\\"is_delete\\":true}}]}\', 1643020163, 1643020163, NULL);
INSERT INTO `__PREFIX__qingdong_form` VALUES (19, \'季报管理\', \'quarterly\', \'{\\"data\\":[{\\"id\\":\\"other_43472\\",\\"name\\":\\"文本框\\",\\"type\\":1,\\"component\\":\\"textarea\\",\\"config\\":{\\"label\\":\\"1.1.完成了哪些工作\\",\\"row\\":2,\\"placeholder\\":\\"请输入内容\\",\\"required\\":true,\\"is_delete\\":true}},{\\"id\\":\\"other_48871\\",\\"name\\":\\"文本框\\",\\"type\\":1,\\"component\\":\\"textarea\\",\\"config\\":{\\"label\\":\\"1.2.取得了哪些成绩\\",\\"row\\":2,\\"placeholder\\":\\"请输入内容\\",\\"required\\":true,\\"is_delete\\":true}},{\\"id\\":\\"other_33048\\",\\"name\\":\\"文本框\\",\\"type\\":1,\\"component\\":\\"textarea\\",\\"config\\":{\\"label\\":\\"1.3.当前进展如何\\",\\"row\\":2,\\"placeholder\\":\\"请输入内容\\",\\"required\\":true,\\"is_delete\\":true}},{\\"id\\":\\"other_82389\\",\\"name\\":\\"文本框\\",\\"type\\":1,\\"component\\":\\"textarea\\",\\"config\\":{\\"label\\":\\"1.4.计划实施情况\\",\\"row\\":2,\\"placeholder\\":\\"请输入内容\\",\\"required\\":true,\\"is_delete\\":true}}]}\', 1619491859, 1640141727, NULL);
INSERT INTO `__PREFIX__qingdong_form` VALUES (20, \'年报管理\', \'yearly\', \'{\\"data\\":[{\\"id\\":\\"other_43472\\",\\"name\\":\\"文本框\\",\\"type\\":1,\\"component\\":\\"textarea\\",\\"config\\":{\\"label\\":\\"1.1.完成了哪些工作\\",\\"row\\":2,\\"placeholder\\":\\"请输入内容\\",\\"required\\":true,\\"is_delete\\":true}},{\\"id\\":\\"other_48871\\",\\"name\\":\\"文本框\\",\\"type\\":1,\\"component\\":\\"textarea\\",\\"config\\":{\\"label\\":\\"1.2.取得了哪些成绩\\",\\"row\\":2,\\"placeholder\\":\\"请输入内容\\",\\"required\\":true,\\"is_delete\\":true}},{\\"id\\":\\"other_33048\\",\\"name\\":\\"文本框\\",\\"type\\":1,\\"component\\":\\"textarea\\",\\"config\\":{\\"label\\":\\"1.3.当前进展如何\\",\\"row\\":2,\\"placeholder\\":\\"请输入内容\\",\\"required\\":true,\\"is_delete\\":true}},{\\"id\\":\\"other_82389\\",\\"name\\":\\"文本框\\",\\"type\\":1,\\"component\\":\\"textarea\\",\\"config\\":{\\"label\\":\\"1.4.计划实施情况\\",\\"row\\":2,\\"placeholder\\":\\"请输入内容\\",\\"required\\":true,\\"is_delete\\":true}},{\\"id\\":\\"other_90392\\",\\"name\\":\\"文本框\\",\\"type\\":1,\\"component\\":\\"textarea\\",\\"config\\":{\\"label\\":\\"2.1 面临哪些挑战\\",\\"row\\":2,\\"placeholder\\":\\"请输入内容\\",\\"required\\":false,\\"is_delete\\":true}},{\\"id\\":\\"other_72126\\",\\"name\\":\\"文本框\\",\\"type\\":1,\\"component\\":\\"textarea\\",\\"config\\":{\\"label\\":\\"2.2 什么原因导致的\\",\\"row\\":2,\\"placeholder\\":\\"请输入内容\\",\\"required\\":false,\\"is_delete\\":true}},{\\"id\\":\\"other_39787\\",\\"name\\":\\"文本框\\",\\"type\\":1,\\"component\\":\\"textarea\\",\\"config\\":{\\"label\\":\\"2.3 有何改进措施\\",\\"row\\":2,\\"placeholder\\":\\"请输入内容\\",\\"required\\":true,\\"is_delete\\":true}},{\\"id\\":\\"other_66752\\",\\"name\\":\\"文本框\\",\\"type\\":1,\\"component\\":\\"textarea\\",\\"config\\":{\\"label\\":\\"3.1 节约了哪些成本\\",\\"row\\":2,\\"placeholder\\":\\"请输入内容\\",\\"required\\":false,\\"is_delete\\":true}},{\\"id\\":\\"other_62312\\",\\"name\\":\\"文本框\\",\\"type\\":1,\\"component\\":\\"textarea\\",\\"config\\":{\\"label\\":\\"3.2 提升了哪些效率\\",\\"row\\":2,\\"placeholder\\":\\"请输入内容\\",\\"required\\":false,\\"is_delete\\":true}},{\\"id\\":\\"other_62889\\",\\"name\\":\\"文本框\\",\\"type\\":1,\\"component\\":\\"textarea\\",\\"config\\":{\\"label\\":\\"3.3 优化了哪些步骤\\",\\"row\\":2,\\"placeholder\\":\\"请输入内容\\",\\"required\\":false,\\"is_delete\\":true}},{\\"id\\":\\"other_98365\\",\\"name\\":\\"文本框\\",\\"type\\":1,\\"component\\":\\"textarea\\",\\"config\\":{\\"label\\":\\"3.4 拓宽了哪些渠道\\",\\"row\\":2,\\"placeholder\\":\\"请输入内容\\",\\"required\\":false,\\"is_delete\\":true}},{\\"id\\":\\"other_80726\\",\\"name\\":\\"文本框\\",\\"type\\":1,\\"component\\":\\"textarea\\",\\"config\\":{\\"label\\":\\"4.1 下一步的安排\\",\\"row\\":2,\\"placeholder\\":\\"请输入下一步的安排\\",\\"required\\":false,\\"is_delete\\":true}},{\\"id\\":\\"other_15038\\",\\"name\\":\\"文本框\\",\\"type\\":1,\\"component\\":\\"textarea\\",\\"config\\":{\\"label\\":\\"4.2 需要的资源支持\\",\\"row\\":2,\\"placeholder\\":\\"请输入需要的资源支持\\",\\"required\\":false,\\"is_delete\\":true}},{\\"id\\":\\"other_970\\",\\"name\\":\\"文本框\\",\\"type\\":1,\\"component\\":\\"textarea\\",\\"config\\":{\\"label\\":\\"4.3 预计达到的结果\\",\\"row\\":2,\\"placeholder\\":\\"请输入预计达到的结果\\",\\"required\\":false,\\"is_delete\\":true}}]}\', 1643020163, 1643020163, NULL);
INSERT INTO `__PREFIX__qingdong_form` VALUES (21, \'外勤签到\', \'signin\', \'{\\"data\\":[{\\"id\\":\\"other_35260\\",\\"name\\":\\"文本框\\",\\"type\\":1,\\"component\\":\\"textarea\\",\\"config\\":{\\"label\\":\\"备注\\",\\"row\\":2,\\"placeholder\\":\\"请输入备注\\",\\"required\\":false,\\"is_delete\\":true}},{\\"id\\":\\"other_65453\\",\\"name\\":\\"图片上传\\",\\"type\\":1,\\"component\\":\\"uploadImage\\",\\"config\\":{\\"label\\":\\"图片上传\\",\\"value\\":[],\\"required\\":false,\\"placeholder\\":\\"请上传图片\\",\\"maxnum\\":1,\\"is_delete\\":true,\\"format\\":[\\"jpg\\",\\"jpeg\\",\\"png\\"],\\"maxSize\\":5000}}],\\"isAddContact\\":true}\', 1643020163, 1657163099, NULL);
INSERT INTO `__PREFIX__qingdong_form` VALUES (22, \'商机管理\', \'business\', \'{\\"data\\":[{\\"id\\":\\"name\\",\\"name\\":\\"单行输入框\\",\\"type\\":1,\\"component\\":\\"input\\",\\"config\\":{\\"type\\":\\"text\\",\\"label\\":\\"商机名称\\",\\"readonly\\":false,\\"placeholder\\":\\"商机名称\\",\\"required\\":true,\\"is_delete\\":true}},{\\"id\\":\\"money\\",\\"name\\":\\"数字框\\",\\"type\\":1,\\"component\\":\\"input-number\\",\\"config\\":{\\"label\\":\\"商机金额\\",\\"placeholder\\":\\"请输入商机金额\\",\\"required\\":true,\\"is_delete\\":true}},{\\"id\\":\\"status\\",\\"name\\":\\"下拉选择框\\",\\"type\\":1,\\"component\\":\\"select\\",\\"config\\":{\\"label\\":\\"商机状态\\",\\"type\\":null,\\"content\\":[{\\"key\\":\\"\\",\\"value\\":\\"行业1\\",\\"__label\\":\\"验证客户\\",\\"__value\\":[\\"行业1\\"],\\"nodeKey\\":1,\\"isEdit\\":false,\\"label\\":\\"验证客户\\"},{\\"key\\":\\"\\",\\"value\\":\\"行业2\\",\\"__value\\":[\\"行业2\\"],\\"nodeKey\\":2,\\"isEdit\\":false,\\"label\\":\\"需求分析\\",\\"__label\\":\\"需求分析\\"},{\\"label\\":\\"方案/报价\\",\\"expand\\":true,\\"value\\":\\"0-0\\",\\"isEdit\\":false,\\"__label\\":\\"方案/报价\\",\\"__value\\":[\\"0-0\\"],\\"nodeKey\\":3},{\\"label\\":\\"赢单\\",\\"expand\\":true,\\"value\\":\\"0-0\\",\\"isEdit\\":false,\\"__label\\":\\"赢单\\",\\"__value\\":[\\"0-0\\"],\\"nodeKey\\":4},{\\"label\\":\\"输单\\",\\"expand\\":true,\\"value\\":\\"0-0\\",\\"isEdit\\":false,\\"__label\\":\\"输单\\",\\"__value\\":[\\"0-0\\"],\\"nodeKey\\":5}],\\"required\\":true,\\"multiple\\":false,\\"is_delete\\":true,\\"placeholder\\":\\"请选择商机状态\\"}},{\\"id\\":\\"expect_time\\",\\"name\\":\\"日期选择框\\",\\"type\\":1,\\"component\\":\\"DatePicker\\",\\"config\\":{\\"label\\":\\"预计成交时间\\",\\"placeholder\\":\\"请输入预计成交时间\\",\\"type\\":\\"date\\",\\"value\\":\\"\\",\\"required\\":true,\\"is_delete\\":true}},{\\"id\\":\\"next_time\\",\\"name\\":\\"时间选择框\\",\\"type\\":1,\\"component\\":\\"TimePicker\\",\\"config\\":{\\"label\\":\\"下次跟进时间\\",\\"placeholder\\":\\"点击选择\\",\\"type\\":\\"time\\",\\"format\\":\\"YYYY-MM-DD HH:mm:ss\\",\\"confirm\\":true,\\"required\\":false,\\"readonly\\":false,\\"is_delete\\":true}},{\\"id\\":\\"other_48629\\",\\"name\\":\\"图片上传\\",\\"type\\":1,\\"component\\":\\"uploadImage\\",\\"config\\":{\\"label\\":\\"图片上传\\",\\"value\\":[],\\"required\\":false,\\"placeholder\\":\\"请上传图片\\",\\"maxnum\\":1,\\"is_delete\\":true,\\"format\\":[\\"jpg\\",\\"jpeg\\",\\"png\\"],\\"maxSize\\":5000}},{\\"id\\":\\"remark\\",\\"name\\":\\"文本框\\",\\"type\\":1,\\"component\\":\\"textarea\\",\\"config\\":{\\"label\\":\\"备注信息\\",\\"readonly\\":false,\\"row\\":2,\\"placeholder\\":\\"备注信息\\",\\"required\\":false,\\"is_delete\\":true}}],\\"isAddContact\\":true}\', 1619592288, 1659082152, NULL);


INSERT INTO `__PREFIX__qingdong_notice_template`
VALUES (1, \'审批通知\', \'examine\',
        \'{\\"template_id\\":\\"\\",\\"first\\":\\"{{staff_name}}\\\\u53d1\\\\u8d77\\\\u4e00\\\\u4e2a\\\\u300a\\\\u5ba1\\\\u6279\\\\u300b\\\\u6307\\\\u5b9a\\\\u7531\\\\u60a8\\\\u5904\\\\u7406\\",\\"keyword1\\":\\"{{examine_type}}\\",\\"keyword1_color\\":\\"#666\\",\\"keyword2\\":\\"{{date}}\\",\\"keyword2_color\\":\\"#666\\",\\"keyword3\\":\\"\\",\\"keyword3_color\\":\\"#666\\",\\"keyword4\\":\\"\\",\\"keyword4_color\\":\\"#666\\",\\"keyword5\\":\\"\\",\\"keyword5_color\\":\\"#666\\",\\"remark\\":\\"\\\\u8bf7\\\\u53ca\\\\u65f6\\\\u5904\\\\u7406\\\\uff01\\",\\"remark_color\\":\\"#666\\"}\');
INSERT INTO `__PREFIX__qingdong_notice_template`
VALUES (3, \'新建跟进记录通知\', \'record\',
        \'{\\"template_id\\":\\"\\",\\"first\\":\\"{{staff_name}}\\\\u521a\\\\u521a\\\\u65b0\\\\u5efa\\\\u4e86\\\\u4e00\\\\u6761\\\\u300a\\\\u8ddf\\\\u8fdb\\\\u8bb0\\\\u5f55\\\\u300b\\\\u6307\\\\u5b9a\\\\u7531\\\\u60a8\\\\u5ba1\\\\u9605\\",\\"keyword1\\":\\"{{customer_name}}{{staff_department}}\\",\\"keyword1_color\\":\\"#666\\",\\"keyword2\\":\\"{{customer_follow}}\\",\\"keyword2_color\\":\\"#666\\",\\"keyword3\\":\\"{{date}}\\",\\"keyword3_color\\":\\"#666\\",\\"keyword4\\":\\"\\",\\"keyword4_color\\":\\"#666\\",\\"keyword5\\":\\"\\",\\"keyword5_color\\":\\"#666\\",\\"remark\\":\\"\\\\u8bf7\\\\u53ca\\\\u65f6\\\\u5904\\\\u7406\\\\uff01\\",\\"remark_color\\":\\"#666\\"}\');
INSERT INTO `__PREFIX__qingdong_notice_template`
VALUES (4, \'新建工作报告通知\', \'daily\',
        \'{\\"template_id\\":\\"\\",\\"first\\":\\"{{staff_name}}\\\\u521a\\\\u521a\\\\u65b0\\\\u5efa\\\\u4e86\\\\u4e00\\\\u6761\\\\u300a\\\\u5de5\\\\u4f5c\\\\u62a5\\\\u544a\\\\u300b\\\\u6307\\\\u5b9a\\\\u7531\\\\u60a8\\\\u5ba1\\\\u9605\\",\\"keyword1\\":\\"{staff_name}\\",\\"keyword1_color\\":\\"#666\\",\\"keyword2\\":\\"{daily_type}\\",\\"keyword2_color\\":\\"#666\\",\\"keyword3\\":\\"{date}\\",\\"keyword3_color\\":\\"#666\\",\\"keyword4\\":\\"\\",\\"keyword4_color\\":\\"#666\\",\\"keyword5\\":\\"\\",\\"keyword5_color\\":\\"#666\\",\\"remark\\":\\"\\",\\"remark_color\\":\\"#666\\"}\');
INSERT INTO `__PREFIX__qingdong_notice_template`
VALUES (5, \'回收客户到公海通知\', \'seas\',
        \'{\\"template_id\\":\\"\\",\\"first\\":\\"\\\\u60a8\\\\u7684\\\\u5ba2\\\\u6237\\\\u957f\\\\u65f6\\\\u95f4\\\\u672a\\\\u8ddf\\\\u8fdb\\\\uff0c\\\\u5df2\\\\u88ab\\\\u7cfb\\\\u7edf\\\\u81ea\\\\u52a8\\\\u56de\\\\u6536\\\\u5230\\\\u516c\\\\u6d77\\\\u6c60\\\\u5185\\\\u3002\\",\\"keyword1\\":\\"{{customer_name}}\\",\\"keyword1_color\\":\\"#666\\",\\"keyword2\\":\\"{{day}}\\",\\"keyword2_color\\":\\"#666\\",\\"keyword3\\":\\"{{date}}\\",\\"keyword3_color\\":\\"#666\\",\\"keyword4\\":\\"\\",\\"keyword4_color\\":\\"#666\\",\\"keyword5\\":\\"\\",\\"keyword5_color\\":\\"#666\\",\\"remark\\":\\"\\\\u5efa\\\\u8bae\\\\u5ba2\\\\u6237\\\\u53ca\\\\u65f6\\\\u8ddf\\\\u8fdb\\\\uff01\\",\\"remark_color\\":\\"#666\\"}\');
INSERT INTO `__PREFIX__qingdong_notice_template`
VALUES (6, \'出差签到通知\', \'sign\',
        \'{\\"template_id\\":\\"\\",\\"first\\":\\"{{staff_name}}\\\\u63d0\\\\u4ea4\\\\u4e86\\\\u4e00\\\\u4e2a\\\\u51fa\\\\u5dee\\\\u7b7e\\\\u5230\\\\uff0c\\\\u7531\\\\u60a8\\\\u5ba1\\\\u9605\\\\uff01\\",\\"keyword1\\":\\"{{address}}\\",\\"keyword1_color\\":\\"#666\\",\\"keyword2\\":\\"{{date}}\\",\\"keyword2_color\\":\\"#666\\",\\"keyword3\\":\\"{{customer_name}}\\",\\"keyword3_color\\":\\"#666\\",\\"keyword4\\":\\"\\",\\"keyword4_color\\":\\"#666\\",\\"keyword5\\":\\"\\",\\"keyword5_color\\":\\"#666\\",\\"remark\\":\\"\\\\u8bf7\\\\u53ca\\\\u65f6\\\\u5ba1\\\\u9605\\\\uff01\\",\\"remark_color\\":\\"#666\\"}\');
INSERT INTO `__PREFIX__qingdong_notice_template`
VALUES (7, \'审批通过通知\', \'examine_adopt\',
        \'{\\"template_id\\":\\"\\",\\"first\\":\\"\\\\u60a8\\\\u63d0\\\\u4ea4\\\\u7684\\\\u5ba1\\\\u6279\\\\u5df2\\\\u5ba1\\\\u6838\\\\u901a\\\\u8fc7\\\\uff01\\",\\"keyword1\\":\\"{{check_name}}\\",\\"keyword1_color\\":\\"#666\\",\\"keyword2\\":\\"{examine_type}\\",\\"keyword2_color\\":\\"#666\\",\\"keyword3\\":\\"{submit_time}\\",\\"keyword3_color\\":\\"#666\\",\\"keyword4\\":\\"\\",\\"keyword4_color\\":\\"#666\\",\\"keyword5\\":\\"\\",\\"keyword5_color\\":\\"#666\\",\\"remark\\":\\"\\\\u5ba1\\\\u6838\\\\u901a\\\\u8fc7\\\\uff01\\",\\"remark_color\\":\\"#666\\"}\');
INSERT INTO `__PREFIX__qingdong_notice_template`
VALUES (8, \'审批拒绝通知\', \'examine_refuse\',
        \'{\\"template_id\\":\\"\\",\\"first\\":\\"\\\\u60a8\\\\u63d0\\\\u4ea4\\\\u7684\\\\u5ba1\\\\u6279\\\\u88ab\\\\u62d2\\\\u7edd\\\\uff01\\",\\"keyword1\\":\\"{{staff_name}}\\",\\"keyword1_color\\":\\"#666\\",\\"keyword2\\":\\"{check_name}\\",\\"keyword2_color\\":\\"#666\\",\\"keyword3\\":\\"{examine_type}\\",\\"keyword3_color\\":\\"#666\\",\\"keyword4\\":\\"{{examine_desc}}\\",\\"keyword4_color\\":\\"#666\\",\\"keyword5\\":\\"\\",\\"keyword5_color\\":\\"#666\\",\\"remark\\":\\"\\\\u5ba1\\\\u6838\\\\u62d2\\\\u7edd\\\\uff01\\",\\"remark_color\\":\\"#666\\"}\');
INSERT INTO `__PREFIX__qingdong_notice_template`
VALUES (9, \'合同到期通知\', \'contract_expire\',
        \'{\\"template_id\\":\\"\\",\\"first\\":\\"\\\\u5408\\\\u540c\\\\u5373\\\\u5c06\\\\u5230\\\\u671f\\\\uff0c\\\\u8bf7\\\\u5c3d\\\\u5feb\\\\u8ddf\\\\u8fdb\\\\u56de\\\\u6b3e\\",\\"keyword1\\":\\"{customer_name}\\",\\"keyword1_color\\":\\"#666\\",\\"keyword2\\":\\"{money}\\",\\"keyword2_color\\":\\"#666\\",\\"keyword3\\":\\"\\",\\"keyword3_color\\":\\"#666\\",\\"keyword4\\":\\"\\",\\"keyword4_color\\":\\"#666\\",\\"keyword5\\":\\"\\",\\"keyword5_color\\":\\"#666\\",\\"remark\\":\\"\\\\u8bf7\\\\u53ca\\\\u65f6\\\\u8ddf\\\\u8fdb\\\\uff01\\",\\"remark_color\\":\\"#666\\"}\');
INSERT INTO `__PREFIX__qingdong_notice_template`
VALUES (10, \'回款计划到期通知\', \'plan_expire\',
        \'{\\"template_id\\":\\"\\",\\"first\\":\\"\\\\u5df2\\\\u5230\\\\u8ba1\\\\u5212\\\\u56de\\\\u6b3e\\\\u7ea6\\\\u5b9a\\\\u65f6\\\\u95f4\\\\uff0c\\\\u8bf7\\\\u5c3d\\\\u5feb\\\\u8ddf\\\\u8fdb\\\\u56de\\\\u6b3e\\",\\"keyword1\\":\\"{{customer_name}}\\",\\"keyword1_color\\":\\"#666\\",\\"keyword2\\":\\"{{plan_money}}\\",\\"keyword2_color\\":\\"#666\\",\\"keyword3\\":\\"{{plan_time}}\\",\\"keyword3_color\\":\\"#666\\",\\"keyword4\\":\\"\\",\\"keyword4_color\\":\\"#666\\",\\"keyword5\\":\\"\\",\\"keyword5_color\\":\\"#666\\",\\"remark\\":\\"\\\\u8bf7\\\\u53ca\\\\u65f6\\\\u8ddf\\\\u8fdb\\\\uff01\\",\\"remark_color\\":\\"#666\\"}\');
INSERT INTO `__PREFIX__qingdong_notice_template`
VALUES (11, \'评论通知\', \'comment\',
        \'{\\"template_id\\":\\"\\",\\"first\\":\\"\\\\u8bc4\\\\u8bba\\\\u4e86\\\\u60a8\\\\u63d0\\\\u4ea4\\\\u7684\\\\u8ddf\\\\u8fdb\\\\u8bb0\\\\u5f55\\\\uff01\\",\\"keyword1\\":\\"{{staff_name}}\\",\\"keyword1_color\\":\\"#666\\",\\"keyword2\\":\\"{{content}}\\",\\"keyword2_color\\":\\"#666\\",\\"keyword3\\":\\"{{date}}\\",\\"keyword3_color\\":\\"#666\\",\\"keyword4\\":\\"\\",\\"keyword4_color\\":\\"#666\\",\\"keyword5\\":\\"\\",\\"keyword5_color\\":\\"#666\\",\\"remark\\":\\"\\\\u8bf7\\\\u53ca\\\\u65f6\\\\u5904\\\\u7406\\\\uff01\\",\\"remark_color\\":\\"#666\\"}\');
INSERT INTO `__PREFIX__qingdong_notice_template`
VALUES (12, \'日程通知\', \'event\',
        \'{"template_id":"","first":"{{event_title}}日程即将开始","keyword1":"","keyword1_color":"#666","keyword2":"","keyword2_color":"#666","keyword3":"","keyword3_color":"#666","keyword4":"","keyword4_color":"#666","keyword5":"","keyword5_color":"#666","remark":"请及时处理！","remark_color":"#666"}\');

INSERT INTO `__PREFIX__qingdong_flow` VALUES (1, \'费用 \', \'[{}]\', 0, \'\', \'consume\', \'\', NULL, 1657014906, 1, 1657014906, 1657014906, NULL);
INSERT INTO `__PREFIX__qingdong_flow` VALUES (2, \'业绩\', \'[{}]\', 0, \'\', \'achievement\', \'\', NULL, 1657767346, 1, 1657767346, 1657767346, NULL);
INSERT INTO `__PREFIX__qingdong_flow` VALUES (3, \'回款\', \'[{}]\', 0, \'\', \'receivables\', \'\', NULL, 1658388920, 1, 1658388920, 1658388920, NULL);
INSERT INTO `__PREFIX__qingdong_flow` VALUES (4, \'合同\', \'[{}]\', 0, \'\', \'contract\', \'\', NULL, 1658388928, 1, 1658388928, 1658388928, NULL);
INSERT INTO `__PREFIX__qingdong_flow` VALUES (5, \'补卡\', \'[{}]\', 0, \'\', \'card\', \'\', NULL, 1658388928, 1, 1658388928, 1658388928, NULL);
INSERT INTO `__PREFIX__qingdong_flow` VALUES (6, \'请假\', \'[{}]\', 0, \'\', \'leave\', \'\', NULL, 1658388928, 1, 1658388928, 1658388928, NULL);

ALTER TABLE `__PREFIX__qingdong_achievement_records`
ADD COLUMN `flow_id` int(11) NULL COMMENT \'审核流程ID\' AFTER `flow_staff_ids`,
ADD COLUMN `order_id` int(11) NULL COMMENT \'审批流程步骤id\' AFTER `flow_id`;
ALTER TABLE `__PREFIX__qingdong_approval`
ADD COLUMN `flow_id` int(11) NULL COMMENT \'审核流程ID\' AFTER `check_status`,
ADD COLUMN `order_id` int(11) NULL COMMENT \'审批流程步骤id\' AFTER `flow_id`;
ALTER TABLE `__PREFIX__qingdong_consume`
ADD COLUMN `flow_id` int(11) NULL COMMENT \'审核流程ID\' AFTER `flow_staff_ids`,
ADD COLUMN `order_id` int(11) NULL COMMENT \'审批流程步骤id\' AFTER `flow_id`;
ALTER TABLE `__PREFIX__qingdong_contract`
ADD COLUMN `flow_id` int(11) NULL COMMENT \'审核流程ID\' AFTER `remarks`,
ADD COLUMN `order_id` int(11) NULL COMMENT \'审批流程步骤id\' AFTER `flow_id`;
ALTER TABLE `__PREFIX__qingdong_contract`
ADD COLUMN `ratios` varchar(255) NULL DEFAULT NULL COMMENT \'业绩比例\' AFTER `order_id`;
ALTER TABLE `__PREFIX__qingdong_contract` ADD COLUMN `business_id` int(11) NULL COMMENT \'商机ID\' AFTER `order_staff_id`;

ALTER TABLE `__PREFIX__qingdong_contract`
ADD COLUMN `ratio_id` int(11) NULL COMMENT \'绩效ID\' AFTER `order_id`;
ALTER TABLE `__PREFIX__qingdong_customer`
    ADD COLUMN `ro_staff_id` varchar(255) NULL DEFAULT \'\' COMMENT \'只读权限\' AFTER `owner_staff_id`,
ADD COLUMN `rw_staff_id` varchar(255) NULL DEFAULT \'\' COMMENT \'读写权限\' AFTER `ro_staff_id`;
ALTER TABLE `__PREFIX__qingdong_customer` MODIFY COLUMN `address_detail` varchar(255) NULL DEFAULT NULL COMMENT \'详细地址\' AFTER `address`;
ALTER TABLE `__PREFIX__qingdong_customer` MODIFY COLUMN `remarks` text NULL COMMENT \'备注信息\' AFTER `lat`;
ALTER TABLE `__PREFIX__qingdong_file`
MODIFY COLUMN `types` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT \'类型（file、image）\' AFTER `id`;
ALTER TABLE `__PREFIX__qingdong_flow`
ADD COLUMN `status` tinyint(1) DEFAULT NULL COMMENT \'1 固定审批 0 授权审批\' AFTER `examine_ids`,
ADD COLUMN `remark` varchar(255) DEFAULT NULL COMMENT \'备注\' AFTER `status`,
ADD COLUMN `relation_type` varchar(255) DEFAULT NULL COMMENT \'关联类型\' AFTER `remark`,
ADD COLUMN `group_ids` varchar(255) DEFAULT NULL COMMENT \'关联角色组\' AFTER `relation_type`,
ADD COLUMN `weight` tinyint(4) DEFAULT NULL COMMENT \'权重\' AFTER `group_ids`,
ADD COLUMN `last_modified` int(11) DEFAULT NULL COMMENT \'最后修改时间\' AFTER `weight`,
ADD COLUMN `last_admin_id` int(11) DEFAULT NULL COMMENT \'最后修改人\' AFTER `last_modified`,
ADD COLUMN `deletetime` int(11) DEFAULT NULL AFTER `updatetime`;
ALTER TABLE `__PREFIX__qingdong_form_approval`
ADD COLUMN `examine_ids` text COMMENT \'默认审核人id\' AFTER `form_id`,
ADD COLUMN `status` tinyint(1) DEFAULT NULL COMMENT \'1 固定审批 0 授权审批\' AFTER `examine_ids`;
ALTER TABLE `__PREFIX__qingdong_product`
ADD COLUMN `cost_price` decimal(10,2) DEFAULT NULL COMMENT \'成本价\' AFTER `min_price`,
MODIFY COLUMN `description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT \'产品描述\' AFTER `status`;
ALTER TABLE `__PREFIX__qingdong_product_part`
MODIFY COLUMN `description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT \'产品描述\' AFTER `img`;

ALTER TABLE `__PREFIX__qingdong_receivables`
ADD COLUMN `flow_id` int(11) NULL COMMENT \'审核流程ID\' AFTER `check_status`,
ADD COLUMN `order_id` int(11) NULL COMMENT \'审批流程步骤id\' AFTER `flow_id`;
ALTER TABLE `__PREFIX__qingdong_staff_sign_in` ADD COLUMN `other` text NULL COMMENT \'表单信息\' AFTER `file_ids`;

ALTER TABLE `__PREFIX__qingdong_customer` ADD COLUMN `seas_id` varchar(128) NULL DEFAULT NULL COMMENT \'所属公海\' AFTER `parent_id`;
ALTER TABLE `__PREFIX__qingdong_staff` MODIFY COLUMN `role` int(11) NULL DEFAULT NULL COMMENT \'角色id\' AFTER `group_ids`;
ALTER TABLE `__PREFIX__qingdong_customer` ADD COLUMN `sea_time` int(11) NULL COMMENT \'放入公海时间\' AFTER `rw_staff_id`;
INSERT INTO `__PREFIX__qingdong_seas_type` VALUES (1, \'默认公海\', \'客户不满足其他公海规则时，会移入当前公海\', NULL, NULL, NULL, NULL);

INSERT INTO `__PREFIX__qingdong_staff_rule` VALUES (1, \'menus\',  0, \'seas\', \'公海客户\', \'\', 1, \'\');
INSERT INTO `__PREFIX__qingdong_staff_rule` VALUES (2, \'data\',  1, 1, \'默认公海\', \'\', 0, \'\');

ALTER TABLE `__PREFIX__qingdong_notice_template`
MODIFY COLUMN `data` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT \'微信公众号\' AFTER `type`,
ADD COLUMN `wechat` text NULL COMMENT \'企业微信\' AFTER `data`,
ADD COLUMN `createtime` int(11) NULL COMMENT \'创建时间\' AFTER `wechat`,
ADD COLUMN `updatetime` int(11) NULL COMMENT \'修改时间\' AFTER `createtime`,
ADD COLUMN `deletetime` int(11) NULL COMMENT \'删除时间\' AFTER `updatetime`;
ALTER TABLE `__PREFIX__qingdong_staff`
ADD COLUMN `touser` varchar(50) NULL COMMENT \'企业微信userid\' AFTER `wx_openid`;

ALTER TABLE `__PREFIX__qingdong_leads`
ADD COLUMN `receive_time` int(11) NULL COMMENT \'分配时间\' AFTER `follow`;

ALTER TABLE `__PREFIX__qingdong_notice_template`
    ADD COLUMN `enterprise_data` text NULL COMMENT \'企业微信通知\' AFTER `data`;
ALTER TABLE `__PREFIX__qingdong_product`
    ADD COLUMN `goods_id` int(11) NULL DEFAULT NULL COMMENT \'商品ID\' AFTER `id`,
    ADD COLUMN  `type` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT \'规格\' AFTER `name`;
ALTER TABLE `__PREFIX__qingdong_product_type`
    ADD COLUMN `content` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT \'内容\' AFTER `image`,
    ADD COLUMN  `status` tinyint(1) NULL DEFAULT NULL COMMENT \'状态\' AFTER `content`;
ALTER TABLE `__PREFIX__qingdong_record`
    ADD COLUMN `location` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT \'定位信息\' AFTER `deletetime`,
    ADD COLUMN `lng` double(16, 12) NULL DEFAULT NULL COMMENT \'地理位置经度\' AFTER `location`,
    ADD COLUMN  `lat` double(16, 12) NULL DEFAULT NULL COMMENT \'地理位置维度\' AFTER `lng`;

',
);