-- 创建合同款项类别表
CREATE TABLE IF NOT EXISTS `fa_qingdong_contract_pay_step` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `contract_id` int(11) NOT NULL COMMENT '合同ID',
  `fund_type` varchar(100) DEFAULT NULL COMMENT '款项类别',
  `percent` decimal(5,2) DEFAULT NULL COMMENT '付款百分比',
  `total` decimal(18,2) DEFAULT NULL COMMENT '分项金额（万元）',
  `createtime` int(11) DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(11) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `contract_id_index` (`contract_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='合同款项类别';
