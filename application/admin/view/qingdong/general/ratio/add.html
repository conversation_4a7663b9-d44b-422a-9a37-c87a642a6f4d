<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-name" class="form-control" name="row[name]" data-rule="required" type="text">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">比例分配:</label>
        <div class="col-xs-12 col-sm-8">
        <dl class="fieldlist" data-name="row[ratio]" data-template="testtpl">
            <dd>
                <ins style="width: 200px">比例（总数：100%）</ins>
            </dd>
            <dd>
                <a href="javascript:;" class="btn btn-sm btn-success btn-append"><i class="fa fa-plus"></i> 追加</a>
            </dd>
            <textarea name="row[ratio]" class="form-control hide" data-rule="required" cols="30" rows="5"></textarea>
        </dl>
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_radios('row[status]', ['1'=>__('启用'), '0'=>__('禁用')])}
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
<!--定义模板，模板语法使用Art-Template模板语法-->
<script type="text/html" id="testtpl">
    <dd class="form-inline">
        <input type="number" name="row[<%=name%>][<%=index%>][ratio]" placeholder="比例" class="form-control" value="<%=row['ratio']%>" >%
        <span class="btn btn-sm btn-danger btn-remove"><i class="fa fa-times"></i></span> <span class="btn btn-sm btn-primary btn-dragsort"><i class="fa fa-arrows"></i></span>
    </dd>
</script>
