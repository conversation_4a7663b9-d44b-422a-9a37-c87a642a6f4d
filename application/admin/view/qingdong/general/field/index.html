<link href="__CDN__/assets/addons/qingdong/css/index.css?v={$Think.config.site.version}" rel="stylesheet">

<div class="panel panel-default panel-intro">
	{:build_heading()}

	<div class="panel-body">
		<div id="myTabContent" class="tab-content">
			<div class="tab-pane fade active in" id="one">
				<div class="widget-body no-padding">
					<div id="toolbar" class="toolbar">
						{:build_toolbar('refresh')}
					</div>
					<table id="table" class="table table-striped table-hover" width="100%">
					</table>
				</div>
			</div>

		</div>
	</div>
</div>
<style type="text/css">
	.example {
		height:100%;position: relative;
	}
	.example > span {
		position:absolute;left:15px;top:15px;
	}
</style>

<script id="itemtpl" type="text/html">

	<div class="col-sm-4 col-md-3">
		<!--下面四行是为了展示随机图片和标签，可移除  -->
		<% var imagearr = ['/assets/addons/qingdong/mini/field.jpg']; %>
		<% var image = imagearr[item.id % 1]; %>
		<% var labelarr = ['primary', 'success', 'info', 'danger', 'warning']; %>
		<% var label = labelarr[item.id % 5]; %>
		<div class="thumbnail example">
			<span class="btn btn-<%=label%>">ID:<%=item.id%></span>
			<img src="<%=image%>" style="width:100%;" alt="<%=item.title%>">
			<div class="caption">
				<h4><%=item.name?item.name:'无'%></h4>
				<p style="margin-top:20px;"></p>

				<p style="text-align: right;">

					<!--如果需要响应编辑或删除事件，可以给元素添加 btn-edit或btn-del的类和data-id这个属性值-->
					<a href="#" class="btn btn-success btn-edit" data-id="<%=item.id%>"><i class="fa fa-pencil"></i> 编辑</a>

				</p>
			</div>
		</div>
	</div>
</script>
