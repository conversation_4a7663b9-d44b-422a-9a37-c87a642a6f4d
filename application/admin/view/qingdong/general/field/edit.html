
<form id="edit-form" class="form-horizontal form-ajax" role="form" data-toggle="validator" method="POST" action="">

	<div class="form-group" id="title">
		<label  class="control-label col-xs-12 col-sm-2">标题:</label>
		<div class="col-xs-12 col-sm-8">
			<div class="form-control">{$row.name}</div>
			<input type="hidden" name="row[name]" value="{$row.name}"/>
		</div>
	</div>

	<div class="form-group" id="contents">
		<label  class="control-label col-xs-12 col-sm-2">详情:</label>
		<div class="col-xs-12 col-sm-8">

			<dl class="fieldlist" data-name="row[data]"  data-template="testtpl">
				<dd>
					<ins>字段值</ins>
				</dd>
				<dd>
					<a href="javascript:;" class="btn btn-sm btn-success btn-append"><i class="fa fa-plus"></i> 追加</a>
				</dd>
				<textarea name="row[data]" class="form-control hide" cols="30" rows="5">{$row.data}</textarea>
			</dl>

			<!--定义模板-->
			<script type="text/html" id="testtpl">
				<dd class="form-inline">
					<input type="text" name="row[<%=name%>][<%=index%>][field]" class="form-control" value="<%=row%>" size="10">
					<span class="btn btn-sm btn-danger btn-remove"><i class="fa fa-times"></i></span> <span class="btn btn-sm btn-primary btn-dragsort"><i class="fa fa-arrows"></i></span>
				</dd>
			</script>
		</div>
	</div>


	<div class="form-group  layer-footer">
		<div class="col-xs-2"></div>
		<div class="col-xs-12 col-sm-8">
			<button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
			<button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
		</div>
	</div>
</form>
