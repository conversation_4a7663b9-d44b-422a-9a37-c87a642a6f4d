
<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">
    {:token()}

    <div class="col-xs-12 col-sm-12">
        <div class="form-group col-sm-10">
            <label for="c-name">公海名称:</label>
            <input id="c-name" data-rule="required" placeholder="请输入公海名称" class="form-control" name="row[name]" type="text"  >
        </div>
    </div>

    <div class="col-xs-12 col-sm-12">
        <div class="form-group col-sm-10">
            <label for="c-remarks">备注:</label>
            <textarea id="c-remarks" class="form-control" rows="5" name="row[remarks]"
            placeholder="请输入公海备注"></textarea>
        </div>
    </div>


    <div class="form-group">
        <div class="col-sm-12">
            <blockquote style="padding: 2px 10px;text-align: left">
                <p>规则设置</p>
            </blockquote>
            <p class="bg-warning" style="padding: 10px">客户公海规则设置功能用于提高客户资源的利用率，通过设置划入规则，系统自动将符合规则的客户划入对应公海，以便分配员工公海客户权限。<br>
            注意：
                1.客户满足任意条件之一就会划入当前公海池；2.当一个客户满足多个公海客户规则时会同时掉入多个公海；
            </p>
        </div>
    </div>

    <div class="col-xs-12 col-sm-12">
        <div class="form-group col-sm-10">
            <label for="c-area_ids">所属区域:</label>
            <input id="c-area_ids" data-source="qingdong/general/seastype/getarea"
                   class="form-control selectpage"
                   name="row[rules][area_ids]" type="text" placeholder="请选择所属区域"
                   data-pagination="true" data-page-size="10"
                   value="" data-multiple="true" data-actions-box="true">
        </div>
    </div>
    {foreach name="form_data" item="val"}
        {eq name="val.component" value="select"}
        <div class="col-xs-12 col-sm-6">
            <div class="form-group col-sm-10">
                <label for="c-{$val.id}">{$val.config.label}:</label>
                <select id="c-{$val.id}"  class="form-control selectpicker"  name="row[rules][{$val.id}][]" multiple=""   data-live-search='true'>
                {foreach name="val.config.content" item="vo"}
                <option value="{$vo.label}">{$vo.label}</option>
                {/foreach}

                </select>
            </div>
        </div>
        {/eq}
    {/foreach}
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
