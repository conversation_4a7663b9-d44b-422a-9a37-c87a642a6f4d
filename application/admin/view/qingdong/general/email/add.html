<style>
    .input-color {
        width: 30%;
        height: 31px;
        background-color: #666;
        float: right;
    }
</style>

<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">
    <input type="hidden" name="row[type]" value="email">
    <div class="form-group">
        <label for="c-name" class="control-label col-xs-12 col-sm-2">模板名称:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-name" data-rule="required" placeholder="请输入模板名称" value="" class="form-control" name="row[name]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label for="c-content" class="control-label col-xs-12 col-sm-2">邮件内容:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group-btn">
                <button class="btn btn-default dropdown-toggle" type="button" id="dropdownMenu1" data-toggle="dropdown"  aria-haspopup="true" aria-expanded="false">
                    填入变量
                    <span class="caret"></span>
                </button>
                <ul class="dropdown-menu" aria-labelledby="dropdownMenu1">
                    <li><a class="variable_name" data-value="{{name}}">名称</a></li>
                    <li><a class="variable_name" data-value="{{contacts_name}}">联系人名称</a></li>
                    <li><a class="variable_name" data-value="{{email}}">邮箱</a></li>
                    <li><a class="variable_name" data-value="{{mobile}}">手机号</a></li>
                    <li><a class="variable_name" data-value="{{create_staff}}">负责人</a></li>
                </ul>
            </div>
            <textarea id="c-content" name="row[content]" rows="5" cols="50" class="form-control" data-rule="required" placeholder="请输入模板内容" ></textarea>

        </div>

    </div>

    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>