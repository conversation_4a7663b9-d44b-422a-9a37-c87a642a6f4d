<style type="text/css">
	.content {
		min-height: 100px;
	}
</style>
<form id="edit-form" class="form-horizontal col-sm-12" role="form" data-toggle="validator" method="POST" action="">
	{:token()}
	<input type="hidden" name="ids" value="{$ids}"/>
	<div class="form-group">
		<label for="c-template_id">邮件模板:</label>
		<div class="col-xs-12 col-sm-8">
			<select name="row[template_id]" id="c-template_id" class="form-control selectpicker" data-live-search="true">
				<option value="">请选择</option>
				{foreach name="templaters" item="val"}
				<option value="{$val.id}" {eq name="row.id" value="$val.id"}selected{/eq}  >{$val.name}</option>
				{/foreach}
			</select>
		</div>
	</div>

	<div class="form-group">
		<label for="c-name" class="control-label col-xs-12 col-sm-2">邮件标题:</label>
		<div class="col-xs-12 col-sm-8">
			<input id="c-name" data-rule="required" placeholder="请输入邮件标题" value="{$row.name}" class="form-control" name="row[name]" type="text"/>
		</div>
	</div>
	<div class="form-group">
		<label for="c-content" class="control-label col-xs-12 col-sm-2">邮件内容:</label>
		<div class="col-xs-12 col-sm-8">
			<textarea id="c-content" name="row[content]" rows="5" cols="50" class="form-control" data-rule="required" placeholder="请输入邮件内容" >{$row.content}</textarea>
		</div>

	</div>

	<div class="form-group layer-footer">
		<label class="control-label col-xs-12 col-sm-2"></label>
		<div class="col-xs-12 col-sm-8">
			<button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
			<button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
		</div>
	</div>
</form>
