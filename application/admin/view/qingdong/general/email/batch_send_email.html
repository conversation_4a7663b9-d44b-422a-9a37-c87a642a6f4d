<link href="__CDN__/assets/addons/qingdong/css/add.css?v={$Think.config.site.version}" rel="stylesheet">

<style type="text/css">
	.content {
		min-height: 100px;
	}
</style>
<form id="edit-form" class="form-horizontal col-sm-12" role="form" data-toggle="validator" method="POST" action="">
	{:token()}
	<input type="hidden" name="ids" value="{$ids}"/>
	<div class="form-group">
		<div class="col-xs-12 col-sm-6 form-group-selects">
			<div class="col-xs-12 col-sm-10">
				<label for="c-template_id">邮件模板:</label>
				<select name="row[template_id]" id="c-template_id" class="form-control selectpicker" data-live-search="true">
					<option value="">请选择</option>
					{foreach name="templaters" item="val"}
					<option value="{$val.id}"  >{$val.name}</option>
					{/foreach}
				</select>
			</div>
		</div>
	</div>

	<div class="form-group">
		<div class="col-xs-12 col-sm-6">
			<div class="col-xs-12 col-sm-10">
				<label for="c-name" >邮件标题:</label>
				<input id="c-name" data-rule="required" placeholder="请输入邮件标题" value="" class="form-control" name="row[name]" type="text">
			</div>
		</div>
	</div>
	<div class="form-group">
		<div class="col-xs-12 col-sm-6">
			<div class="form-group col-sm-10">
				<label for="c-content">邮件内容:</label>
				<textarea id="c-content" name="row[content]" rows="5"  class="form-control" data-rule="required" placeholder="请输入邮件内容" style="width:78% !important;"></textarea>
			</div>
		</div>
	</div>

	<div class="form-group layer-footer">
		<label class="control-label col-xs-12 col-sm-2"></label>
		<div class="col-xs-12 col-sm-8">
			<button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
			<button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
		</div>
	</div>
</form>
