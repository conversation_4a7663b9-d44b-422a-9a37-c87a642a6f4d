<style>
    .input-color {
        width: 30%;
        height: 31px;
        background-color: #666;
        float: right;
    }
</style>

<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label for="c-template_id" class="control-label col-xs-12 col-sm-2">模板ID:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-template_id" class="form-control"
                   name="row[template_id]" value="{$row.data.template_id??''}" type="text">
        </div>
    </div>
    <div class="form-group">
        <label for="c-first" class="control-label col-xs-12 col-sm-2">标题:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-first" data-rule="required" value="{$row.data.first??''}" class="form-control"
                       name="row[first]" type="text">
                <div class="input-group-btn">
                    <button class="btn btn-default dropdown-toggle" type="button" id="dropdownMenu1" data-toggle="dropdown"
                            aria-haspopup="true" aria-expanded="false">
                        填入变量
                        <span class="caret"></span>
                    </button>
                    <ul class="dropdown-menu" aria-labelledby="dropdownMenu1">
                        {foreach name="variable" item="val" key="key"}
                        <li><a class="variable_name" data-value="{$key}">{$val}{$key}</a></li>
                        {/foreach}
                    </ul>
                </div>
            </div>
        </div>

    </div>
    <div class="form-group">
        <label for="c-keyword1" class="col-xs-12 col-sm-2">
            <input  class="form-control" value="{$row.keyword1_key??''}" name="row[keyword1_key]"
                    type="text">
        </label>
        <div class="col-xs-12 col-sm-6">
            <div class="input-group">
                <input id="c-keyword1" class="form-control" value="{$row.data.keyword1??''}" name="row[keyword1]"
                       type="text">
                <div class="input-group-btn">
                    <button class="btn btn-default dropdown-toggle" type="button" id="dropdownMenu2" data-toggle="dropdown"
                            aria-haspopup="true" aria-expanded="false">
                        填入变量
                        <span class="caret"></span>
                    </button>
                    <ul class="dropdown-menu" aria-labelledby="dropdownMenu2">
                        {foreach name="variable" item="val" key="key"}
                        <li><a class="variable_name" data-key="6">{$val}{$key}</a></li>
                        {/foreach}
                    </ul>
                </div></div>
        </div>
        <div class="col-xs-12 col-sm-2">
            <input class="form-control keyword_color" name="row[keyword1_color]"
                   type="text" value="{$row.data.keyword1_color??'#666'}" style="width:70%;float: left;">
            <div class="input-color"></div>
        </div>
    </div>
    <div class="form-group">
        <label for="c-keyword2" class="col-xs-12 col-sm-2">
            <input  class="form-control" value="{$row.keyword2_key??''}" name="row[keyword2_key]"
                    type="text">
        </label>
        </label>
        <div class="col-xs-12 col-sm-6">
            <div class=" input-group">
                <input id="c-keyword2" class="form-control" value="{$row.data.keyword2??''}" name="row[keyword2]"
                       type="text">
                <div class="input-group-btn">
                    <button class="btn btn-default dropdown-toggle" type="button" id="dropdownMenu3" data-toggle="dropdown"
                            aria-haspopup="true" aria-expanded="false">
                        填入变量
                        <span class="caret"></span>
                    </button>
                    <ul class="dropdown-menu" aria-labelledby="dropdownMenu3">
                        {foreach name="variable" item="val" key="key"}
                        <li><a class="variable_name" data-value="{$key}">{$val}{$key}</a></li>
                        {/foreach}
                    </ul>
                </div></div>
        </div>
        <div class="col-xs-12 col-sm-2">
            <input class="form-control keyword_color" name="row[keyword2_color]"
                   type="text" value="{$row.data.keyword2_color??'#666'}" style="width:70%;float: left;">
            <div class="input-color"></div>
        </div>
    </div>
    <div class="form-group">
        <label for="c-keyword3" class="col-xs-12 col-sm-2">
            <input  class="form-control" value="{$row.keyword3_key??''}" name="row[keyword3_key]"
                    type="text">
        </label>
        <div class="col-xs-12  col-sm-6">
            <div class="input-group">
                <input id="c-keyword3" class="form-control" value="{$row.data.keyword3??''}" name="row[keyword3]"
                       type="text">
                <div class="input-group-btn">
                    <button class="btn btn-default dropdown-toggle" type="button" id="dropdownMenu4"
                            data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                        填入变量
                        <span class="caret"></span>
                    </button>
                    <ul class="dropdown-menu" aria-labelledby="dropdownMenu4">
                        {foreach name="variable" item="val" key="key"}
                        <li><a class="variable_name" data-value="{$key}">{$val}{$key}</a></li>
                        {/foreach}
                    </ul>
                </div>
            </div>
        </div>
        <div class="col-xs-12 col-sm-2">
            <input class="form-control keyword_color" name="row[keyword3_color]"
                   type="text" value="{$row.data.keyword3_color??'#666'}" style="width:70%;float: left;">
            <div class="input-color"></div>
        </div>
    </div>
    <div class="form-group">
        <label for="c-keyword4" class="col-xs-12 col-sm-2">
            <input  class="form-control" value="{$row.keyword4_key??''}" name="row[keyword4_key]"
                    type="text">
        </label>
        <div class="col-xs-12 col-sm-6">
            <div class="input-group">
                <input id="c-keyword4" class="form-control" value="{$row.data.keyword4??''}" name="row[keyword4]"
                       type="text">
                <div class="input-group-btn">
                    <button class="btn btn-default dropdown-toggle" type="button" id="dropdownMenu5"
                            data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                        填入变量
                        <span class="caret"></span>
                    </button>
                    <ul class="dropdown-menu" aria-labelledby="dropdownMenu5">
                        {foreach name="variable" item="val" key="key"}
                        <li><a class="variable_name" data-value="{$key}">{$val}{$key}</a></li>
                        {/foreach}
                    </ul>
                </div>
            </div>
        </div>
        <div class="col-xs-12 col-sm-2">
            <input class="form-control keyword_color" name="row[keyword4_color]"
                   type="text" value="{$row.data.keyword4_color??'#666'}" style="width:70%;float: left;">
            <div class="input-color"></div>
        </div>
    </div>

    <div class="form-group">
        <label for="c-keyword5" class="control-label col-xs-12 col-sm-2">
            <input  class="form-control" value="{$row.keyword5_key??''}" name="row[keyword5_key]"
                    type="text">
        </label>
        <div class="col-xs-12 col-sm-6">
            <div class="input-group">
                <input id="c-keyword5" class="form-control" value="{$row.data.keyword5??''}" name="row[keyword5]"
                       type="text">
                <div class="input-group-btn">
                    <button class="btn btn-default dropdown-toggle" type="button" id="dropdownMenu6"
                            data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                        填入变量
                        <span class="caret"></span>
                    </button>
                    <ul class="dropdown-menu" aria-labelledby="dropdownMenu6">
                        {foreach name="variable" item="val" key="key"}
                        <li><a class="variable_name" data-value="{$key}">{$val}{$key}</a></li>
                        {/foreach}
                    </ul>
                </div>
            </div>
        </div>
        <div class="col-xs-12 col-sm-2">
            <input class="form-control keyword_color" name="row[keyword5_color]"
                   type="text" value="{$row.data.keyword5_color??'#666'}" style="width:70%;float: left;">
            <div class="input-color "></div>
        </div>
    </div>


    <div class="form-group">
        <label for="c-remark" class="control-label col-xs-12 col-sm-2">remark:</label>
        <div class="col-xs-12 col-sm-6">
            <div class="input-group">
                <input id="c-remark" class="form-control" value="{$row.data.remark??''}" name="row[remark]" type="text">

                <div class="input-group-btn">
                    <button class="btn btn-default dropdown-toggle" type="button" id="dropdownMenu7"
                            data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                        填入变量
                        <span class="caret"></span>
                    </button>
                    <ul class="dropdown-menu" aria-labelledby="dropdownMenu7">
                        {foreach name="variable" item="val" key="key"}
                        <li><a class="variable_name" data-value="{$key}">{$val}{$key}</a></li>
                        {/foreach}
                    </ul>
                </div>
            </div>
        </div>
        <div class="col-xs-12 col-sm-2">
            <input class="form-control keyword_color" name="row[remark_color]"
                   type="text" value="{$row.data.remark_color??'#666'}" style="width:70%;float: left;">
            <div class="input-color"></div>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">跳转页面地址:</label>
        <div class="col-xs-12 col-sm-8">
            <select name="detail_href" class="form-control">
                <option value="">消息列表</option>
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">模板数据显示:</label>
    </div>
    {foreach name="row.data" item="val" key="key"}

    {if condition="($key neq 'template_id')"}
    {if condition="($key neq 'first')"}
    {if condition="($key neq 'remark')"}
    {if condition="($key neq 'remark_color')"}
    <div class="form-group">
        <label for="c-keyword1" class="col-xs-12 col-sm-2">
            <span  class="form-control"
            >{$key}</span>
        </label>
        <div class="col-xs-12 col-sm-6">
            <div class="input-group">
                <span class="form-control" >{$val}</span>
            </div>
        </div>

    </div>
    {/if}
    {/if}
    {/if}
    {/if}
    {/foreach}

    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
