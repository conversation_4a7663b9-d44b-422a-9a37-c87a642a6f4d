<style>
    .input-color {
        width: 30%;
        height: 31px;
        background-color: #666;
        float: right;
    }
</style>

<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">
    <div class="form-group">
        <label for="c-name" class="control-label col-xs-12 col-sm-2">模板名称:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-name" data-rule="required" placeholder="请输入模板名称" value="{$row.name}" class="form-control" name="row[name]" type="text">
        </div>
    </div>

    <div class="form-group">
        <label for="c-number" class="control-label col-xs-12 col-sm-2">短信模板编号:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-number" data-rule="required" placeholder="请输入短信模板编号" value="{$row.number}" class="form-control" name="row[number]" type="text">
        </div>
    </div>
    <div class="form-group">
        <label for="c-number" class="control-label col-xs-12 col-sm-2">模板参数:</label>
        <div class="col-xs-12 col-sm-8">
            <dl class="fieldlist" data-name="row[content]"  data-template="testtpl">
                <dd>
                    <ins>参数</ins>
                    <ins style="width: 200px!important;">参数值</ins>
                    <ins style="width: 150px!important;">填入变量</ins>
                </dd>
                <dd>
                    <a href="javascript:;" class="btn btn-sm btn-success btn-append"><i class="fa fa-plus"></i> 追加</a>
                </dd>
                <textarea name="row[content]" class="form-control hide" cols="30" rows="5">{$row.content}</textarea>
            </dl>
            <!--定义模板-->
            <script type="text/html" id="testtpl">
                <dd class="form-inline">
                    <input type="text" name="row[<%=name%>][<%=index%>][field]" class="form-control" value="<%=row['field']%>" size="10">
                    <input type="text" name="row[<%=name%>][<%=index%>][content]" style="width: 200px!important;" class="form-control" value="<%=row['content']%>" size="10">
                    <select name="<%=index%>" class="form-control sms_field">
                        <option value="">填入变量</option>
                        <option value="{{name}}">名称</option>
                        <option value="{{email}}">邮箱</option>
                        <option value="{{mobile}}">手机号</option>
                        <option value="{{create_staff}}">负责人</option>
                    </select>
                </dd>
            </script>

        </div>
    </div>
    <div class="form-group">
        <label for="c-preview" class="control-label col-xs-12 col-sm-2">短信模板内容:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-preview" name="row[preview]" rows="5" cols="50" class="form-control" data-rule="required" placeholder="请输入预览模板内容" >{$row.preview}</textarea>
            <span class="help-block">* 仅用于预览短信内容</span>
        </div>

    </div>



    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
