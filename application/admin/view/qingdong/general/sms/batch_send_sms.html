<form id="edit-form" class="form-horizontal col-sm-12" role="form" data-toggle="validator" method="POST" action="">
	{:token()}
	<input type="hidden" name="ids" value="{$ids}"/>
	<div class="form-group">
		<div class="col-xs-12 col-sm-6 form-group-selects">
			<div class="col-xs-12 col-sm-10">
				<label for="c-template_id">短信模板:</label>
				<select name="template_id" id="c-template_id" data-rule="required" class="form-control selectpicker" data-live-search="true">
					<option value="">请选择</option>
					{foreach name="templaters" item="val"}
					<option value="{$val.id}" data-preview="{$val.preview}" >{$val.name}</option>
					{/foreach}
				</select>
			</div>
		</div>
	</div>

	<div class="form-group">
		<div class="col-xs-12 col-sm-6">
			<div class="col-xs-12 col-sm-10">
				<label for="c-content" >短信内容:</label>
				<textarea id="c-content" name="row[preview]" rows="5"  class="form-control  "  placeholder="短信内容"  disabled></textarea>
			</div>
		</div>
	</div>

	<div class="form-group layer-footer">
		<label class="control-label col-xs-12 col-sm-2"></label>
		<div class="col-xs-12 col-sm-8">
			<button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
			<button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
		</div>
	</div>
</form>
