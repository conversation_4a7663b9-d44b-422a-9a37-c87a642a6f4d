<style type="text/css">
	input[type=radio],input[type=checkbox]{
		margin-top:5px!important;
	}
</style>
<div class="row animated fadeInRight">
	<div class="col-md-10">
		<div class="box box-success">
			<div class="panel-heading">
				规则设置
			</div>
			<div class="panel-body" style="line-height: 2em">
				<form id="update-form" class="form-horizontal form-ajax" role="form" data-toggle="validator" method="POST" action="">


					<div class="form-group" id="title">
						<label class="control-label col-xs-12 col-sm-2">公海回收规则:</label>
						<div class="col-xs-12 col-sm-8">

							<div class="radio">
								<label>
									<input name="row[auto]" {eq name="seas.auto" value="1" }checked{/eq} type="radio" value="1">自动回收
								</label>
								<label>
									<input name="row[auto]" {eq name="seas.auto" value="0" }checked{/eq} type="radio" value="0">不自动回收
								</label>
							</div>

							<div class="checkbox auto" style="display: none">
								<div>
									<label>
										<input  name="row[genjing]" {eq name="seas.genjing" value="1" }checked{/eq} type="checkbox" value="1">超过N天“无新建跟进（跟进记录）”的客户，由系统定时退回公海客户池
									</label>

									<div class="genjing" style="display: none">
										<div>
											选择不进入公海客户:
											<label>
												<input  name="row[genjing_success]" {eq name="seas.genjing_success" value="1" }checked{/eq} type="checkbox" value="1">已成交客户
											</label>
										</div>
										<div>
											超过<input type="number" name="row[genjing_day]" value="{$seas.genjing_day}"  style="width: 100px">天未跟进，进入公海
										</div>
									</div>
								</div>
								<br>
								<div>
									<label>
										<input name="row[chengjiao]"  {eq name="seas.chengjiao" value="1" }checked{/eq} type="checkbox" value="1">超过N天“未成交”的客户，由系统定时退回公海客户池
									</label>
									<div class="chengjiao" style="display: none">
										<div>超过<input type="number" name="row[chengjiao_day]" value="{$seas.chengjiao_day}" style="width: 100px">天未成交，进入公海</div>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div class="form-group" id="cong">
						<label class="control-label col-xs-12 col-sm-2">手机号重复:</label>
						<div class="col-xs-12 col-sm-8">

							<div class="radio">
								<label>
									<input name="row[is_linkman]"  type="radio" value="1" {eq name="seas.is_linkman" value="1" }checked{/eq}>允许
								</label>
								<label>
									<input name="row[is_linkman]"  type="radio" value="0" {eq name="seas.is_linkman" value="0" }checked{/eq}>不允许
								</label>

							</div>
							<br/>
							<div class="" role="alert">注:指两个客户的联系人手机号是否允许重复</div>
						</div>
					</div>

					<div class="form-group" id="leadspool">
						<label class="control-label col-xs-12 col-sm-2">线索池规则:</label>
						<div class="col-xs-12 col-sm-8">

							<div class="radio">
								<label>
									<input name="row[leadauto]" {eq name="lead.leadauto" value="1" }checked{/eq} type="radio" value="1">自动分配
								</label>
								<label>
									<input name="row[leadauto]" {eq name="lead.leadauto" value="0" }checked{/eq} type="radio" value="0">不自动分配
								</label>
							</div>

							<div class="checkbox leadauto" style="display: none">
								<div>
									<div style="margin-bottom: 20px;">
										部门:
										<label style="width:50%;">
											{:build_select('group[]', $groupdata, $lead['department'], ['class'=>'form-control selectpicker', 'multiple'=>''])}
										</label>
									</div>
									<div class="leadauto" style="display: none">
										<div>
											按分配量:
											<label>
												在<input type="number" name="row[lead_day]" value="{$lead.lead_day}"  style="width: 100px">天内分配量,由小到大依次分配
											</label>
										</div>
										<div>

											<div class="radio">
												按打卡情况:&nbsp;&nbsp;&nbsp;
												<label>
													<input name="row[leadcard]" {eq name="lead.leadcard" value="1" }checked{/eq} type="radio" value="1">打卡
												</label>
												<label>
													<input name="row[leadcard]" {eq name="lead.leadcard" value="2" }checked{/eq} type="radio" value="2">未打卡
												</label>
											</div>
										</div>
									</div>
								</div>

							</div>
							<br/>
							<div class="" role="alert">注:线索池自动分配规则分为按分配量和打卡规则来分配，配置天数后，在此天数内分配量由小到大依次自动分配给员工;若使用系统中的打卡功能，选择打卡，员工当天只要打过一次卡算出勤，会自动分配线索给此员工，若不使用打卡功能请选择未打卡，选择未打卡会按未打卡规则进行分配。</div>
						</div>
					</div>

					<div class="form-group layer-footer">
						<label class="control-label col-xs-12 col-sm-2"></label>
						<div class="col-xs-12 col-sm-8">
							<button type="submit" class="btn btn-success">{:__('Submit')}</button>
							<button type="reset" class="btn btn-default">{:__('Reset')}</button>
						</div>
					</div>
				</form>

			</div>
		</div>

	</div>

</div>