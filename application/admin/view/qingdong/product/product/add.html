<form id="edit-form" class="form-horizontal col-sm-12" role="form" data-toggle="validator" method="POST" action="">
	{:token()}
	<div class="form-group">

		<div class="col-xs-12 col-sm-6">
			<div class="form-group col-sm-10">
				<label for="c-type_id">产品分类:</label>
				<input id="c-type_id" type="text" data-source="qingdong/product/product/get_type"
					   class="form-control product selectpage"
					   name="row[type_id]" placeholder="请选择产品分类"
					   data-field="name" data-primary-key="id"
					   value=""/>
			</div>
		</div>

		<div class="col-xs-12 col-sm-6">
			<div class="form-group col-sm-10">
				<label for="c-name">产品名称:</label>
				<input id="c-name" data-rule="required" class="form-control" placeholder="请输入产品名称" name="row[name]"
					   type="text">
			</div>
		</div>
		<div class="col-xs-12 col-sm-6">
			<div class="form-group col-sm-10">
				<label for="c-num">产品编码:</label>
				<input id="c-num" class="form-control" placeholder="请输入产品编码" name="row[num]"
					   type="text">
			</div>
		</div>
		<div class="col-xs-12 col-sm-6">
			<div class="form-group col-sm-10">
				<label for="c-price">零销价:</label>
				<input id="c-price" class="form-control" placeholder="请输入价格" name="row[price]"
					   type="number">
			</div>
		</div>
		<div class="col-xs-12 col-sm-6">
			<div class="form-group col-sm-10">
				<label for="c-wholesale">批发价:</label>
				<input id="c-wholesale" class="form-control" placeholder="请输入价格" name="row[wholesale]"
					   type="number">
			</div>
		</div>

		<div class="col-xs-12 col-sm-6">
			<div class="form-group col-sm-10">
				<label for="c-unit">单位:</label>
				<input id="c-unit" class="form-control" placeholder="请输入单位" name="row[unit]"
					   type="text">
			</div>
		</div>

		<div class="col-xs-12 col-sm-10">
			<div class="form-group col-sm-10">
				<label for="c-image">产品图片:</label>
				<div class="input-group">
					<input id="c-image" class="form-control" size="35" placeholder="请上传产品图片" name="row[img]" type="text" value="">
					<div class="input-group-addon no-border no-padding">
							<span><button type="button" id="faupload-image" class="btn btn-danger faupload"
										  data-input-id="c-image"
										  data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp"
										  data-multiple="false" data-preview-id="p-image"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>

					</div>
					<span class="msg-box n-right"></span>
				</div>
				<ul class="row list-inline faupload-preview" id="p-image"></ul>
			</div>
		</div>

		<div class="col-xs-12 col-sm-10">
			<div class="form-group col-sm-10">
				<label for="c-description">产品描述:</label>
				<textarea id="c-description" class="form-control editor" rows="3" name="row[description]"
						  placeholder="请输入产品描述"></textarea>
			</div>
		</div>
		<div class="col-xs-12 col-sm-10">
			<div class="form-group col-sm-10">
			<label class="control-label">状态:</label>
				{:build_radios('row[status]', ['上架'=>__('上架'), '下架'=>__('下架')])}
			</div>
		</div>

	</div>


	<div class="form-group layer-footer">
		<label class="control-label col-xs-12 col-sm-2"></label>
		<div class="col-xs-12 col-sm-8">
			<button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
			<button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
		</div>
	</div>
</form>
