<link href="__CDN__/assets/addons/qingdong/css/index.css?v={$Think.config.site.version}" rel="stylesheet">

<div class="panel panel-default panel-intro">
    {:build_heading()}

    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade active in" id="one">
                <div class="widget-body no-padding">
                    <div id="toolbar" class="toolbar">
                        <a href="javascript:;" class="btn btn-primary btn-refresh" title="{:__('Refresh')}" ><i class="fa fa-refresh"></i> </a>
                        <a href="javascript:;" class="btn btn-success btn-add" title="{:__('Add')}" ><i class="fa fa-plus"></i> {:__('Add')}</a>
                        <a class="btn btn-success btn-change btn-dialog {:$auth->check('qingdong/product/producttype/import')?'':'hide'}" data-title="商品分类导入" data-url="qingdong/product/producttype/import" data-area='["500px","400px"]'  data-params="action=start"  href="javascript:;"><i class="fa fa-upload dz-message"></i> 导入</a>
                    </div>
                    <table id="table" class="table table-striped table-bordered table-hover table-nowrap"
                           data-operate-edit="{:$auth->check('qingdong/product/producttype/edit')}"
                           data-operate-del="{:$auth->check('qingdong/product/producttype/del')}"
                           width="100%">
                    </table>
                </div>
            </div>

        </div>
    </div>
</div>
