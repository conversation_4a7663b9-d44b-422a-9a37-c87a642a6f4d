<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">上级规格:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_select('row[pid]', $specs, null, ['class'=>'form-control', 'required'=>''])}
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">规格名称:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-name" data-rule="required" class="form-control" name="row[name]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">规格值-预设业务类型:</label>
        <div class="col-xs-12 col-sm-8">

            <table class="table table-responsive fieldlist" data-name="row[content]" data-template="testtpl2" data-tag="tr">
                <tr>
                    <td>规格值</td>
                    <td></td>
                </tr>
                <tr>
                    <td colspan="5"><a href="javascript:;" class="btn btn-sm btn-success btn-append"><i class="fa fa-plus"></i> 追加</a></td>
                </tr>
                <textarea name="row[content]" class="form-control hide" cols="30" rows="5"></textarea>
            </table>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_radios('row[status]', [1=>__('Normal'), 0=>__('Hidden')],1)}
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>

<!--定义模板，模板语法使用Art-Template模板语法-->
<script type="text/html" id="testtpl2">
    <tr class="form-inline">
        <td>
            <input type="text"  class="form-control" name="row[<%=name%>][<%=index%>][name]"
                   placeholder="请输入规格值" value="<%=row['name']%>"/></td>

        <td><span class="btn btn-sm btn-danger btn-remove"><i class="fa fa-times"></i></span> <span class="btn btn-sm btn-primary btn-dragsort"><i class="fa fa-arrows"></i></span></td>
    </tr>
</script>

