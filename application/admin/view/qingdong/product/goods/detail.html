<link href="__CDN__/assets/addons/qingdong/css/common.css?v={$Think.config.site.version}" rel="stylesheet">

<style type="text/css">
	.btn-list {
		position: fixed;
		right: 40px;
	}
	.div-flex {
		display: flex
	}
	.div-flex .form-group {
		margin-right: 50px;
		text-align: center;
		line-height: 25px;
		margin-bottom: 0;
	}
	.tab-pane {
		border: 1px solid #F6F6F6;
	}
	.i-color {
		font-size: 34px;margin-right: 10px;color: #86C3FF;
	}
	.thumbnail {
		display: flow-root;
		width: max-content;
	}
	.thumbnail img {
		width: 50px;
		float: left;
		padding: 5px;
	}
</style>
<div class="panel panel-default panel-intro">
	<input type="hidden" name="ids" id="ids" value="{$ids}"/>
	<div class="div-flex">
		<div style="line-height: 35px"><b>{$row.name}</b></div>
		<div class="btn-list">
			<a class="btn btn-success btn-edit">编辑</a>
			<a href="javascript:void(0)" class="btn btn-danger btn-del"  id="dels">删除</a>

		</div>
	</div>
	<div class="div-flex" style="margin-top: 20px">
		<div class="form-group">
			<span>商品分类</span>
			<p><label>{if condition="isset($row.type.name)"}{$row.type.name}{/if}</label></p>
		</div>
		<div class="form-group">
			<span>单位</span>
			<p><label>{$row.unit}</label></p>
		</div>

		<div class="form-group">
			<span>采购价</span>
			<p><label>{$row.cost_price}</label></p>
		</div>

	</div>

	<div>

		<ul class="nav nav-tabs" role="tablist">
			<li role="presentation" class="active"><a href="#basic" aria-controls="profile" role="tab" data-toggle="tab">基本信息</a></li>

			<li role="presentation"><a href="#product" aria-controls="profile" role="tab" data-toggle="tab">产品列表</a></li>

		</ul>

		<div class="tab-content" style="overflow-y: scroll;height:565px">

			<div role="tabpanel" class="tab-pane fade panel-body active in" id="basic">

				<div class="form-group">
					<div class="col-xs-6 col-sm-6">
						<div class="form-group col-sm-10">
							<span class="text-muted">商品名称:</span>
							{$row.name}
						</div>
					</div>
					<div class="col-xs-6 col-sm-6">
						<div class="form-group col-sm-10">
							<span class="text-muted">商品编码:</span>
							{$row.num}
						</div>
					</div>
					<div class="col-xs-6 col-sm-6">
						<div class="form-group col-sm-10">
							<span class="text-muted">零销价:</span>
							{$row.price}
						</div>
					</div>
					<div class="col-xs-6 col-sm-6">
						<div class="form-group col-sm-10">
							<span class="text-muted">批发价:</span>
							{$row.wholesale}
						</div>
					</div>
					<div class="col-xs-6 col-sm-6">
						<div class="form-group col-sm-10">
							<span class="text-muted">采购价:</span>
							{$row.cost_price}
						</div>
					</div>
					<div class="col-xs-6 col-sm-6">
						<div class="form-group col-sm-10">
							<span class="text-muted">产品图片:</span>
							{if condition="$row.img neq null"}
							<img src="{$row.img}" width="100" height="100" style="margin-left: 10px">
							{/if}
						</div>
					</div>
					<div class="col-xs-12 col-sm-12">
						<div class="form-group col-sm-10">
							<span class="text-muted">商品详情:</span>
							{$row.description|htmlspecialchars_decode}
						</div>
					</div>
				</div>


			</div>
			<div role="tabpanel" class="tab-pane fade panel-body" id="product">
				<div id="toolbar_product" class="toolbar">
					{:build_toolbar('refresh')}
				</div>
				<table id="operation_product" class="table table-striped table-bordered table-hover table-nowrap"
					   width="100%">
				</table>

			</div>


		</div>

	</div>
</div>