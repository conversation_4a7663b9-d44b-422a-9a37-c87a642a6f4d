<style type="text/css">
    .fieldlist .form-control{
        padding: 2px;
    }
</style>

<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">商品名称:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-name" data-rule="required" placeholder="请输入商品名称" class="form-control" name="row[name]" type="text" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">商品编号:</label>
        <div class="col-xs-12 col-sm-8">
            <input  class="form-control" name="row[num]" type="text" value="{$num}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">单位:</label>
        <div class="col-xs-12 col-sm-8">
            <select id="c-unit" name="row[unit]" class="form-control selectpicker" data-live-search="true">
                {foreach name="unit" id="v"}
                <option value="{$v.name}">{$v.name}</option>
                {/foreach}

            </select>
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">分类:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-type_id" data-rule="required" data-source="qingdong/product/goods/get_product_type" class="form-control selectpage" name="row[type_id]" placeholder="请选择分类" type="text" value="">
        </div>
    </div>
    <div class="form-group goodsinfo">
        <label class="control-label col-xs-12 col-sm-2">零售价:</label>
        <div class="col-xs-12 col-sm-8">
            <input  placeholder="请输入零售价" class="form-control" name="row[price]" type="number" value="">
        </div>
    </div>
    <div class="form-group goodsinfo">
        <label class="control-label col-xs-12 col-sm-2">批发价:</label>
        <div class="col-xs-12 col-sm-8">
            <input  placeholder="请输入批发价" class="form-control" name="row[wholesale]" type="number" value="">
        </div>
    </div>
    <div class="form-group goodsinfo">
        <label class="control-label col-xs-12 col-sm-2">采购价:</label>
        <div class="col-xs-12 col-sm-8">
            <input  placeholder="请输入采购价" class="form-control" name="row[cost_price]" type="number" value="">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">商品图:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-image"  class="form-control" size="50" name="row[img]" type="text">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-image" class="btn btn-danger faupload" data-input-id="c-image" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp" data-multiple="false" data-preview-id="p-image"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-image"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-image"></ul>
        </div>
    </div>
    <div class="form-group">
         <label class="control-label col-xs-12 col-sm-2">是否有规格:</label>
         <div class="col-xs-12 col-sm-8">
             {:build_radios('row[is_specs]', [0=>__('否'),1=>__('是')],0)}
         </div>
     </div>
    <div class="form-group specesinfo" style="display: none">
        <div class="col-xs-12 col-sm-12">
            <blockquote style="padding: 2px 10px;">
                <p>规格</p>
            </blockquote>
        </div>
    </div>
    <div class="form-group specesinfo" style="display: none">
        <label class="control-label col-xs-12 col-sm-2">规格:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="specs_id" type="text" name="row[specs_id]"  data-source="qingdong/product/goods/get_specs" class="form-control selectpage"  placeholder="请选择规格" value=""/>
        </div>

        <div class="col-xs-12 col-sm-8 col-sm-offset-2 " >
            <br>
            <div id="specslist">

            </div>
        </div>
    </div>

    <div class="form-group specesinfo" style="display: none">
        <div class="col-xs-12 col-sm-12">
            <blockquote style="padding: 2px 10px;">
                <p>产品信息</p>
            </blockquote>
        </div>
    </div>
    <div class="form-group specesinfo" style="display: none">
        <div class="col-xs-12 col-sm-12">
            <table class="table table-responsive fieldlist" data-name="row[product]" data-template="testtpl2"
                   data-tag="tr">
                <tr>
                    <td>产品名称</td>
                    <td>型号</td>
                    <td>零售价</td>
                    <td>批发价</td>
                    <td>采购价</td>
                    <td>备注</td>
                    <td>产品图片</td>
                    <td></td>
                </tr>
                <tr>
                    <td colspan="6"><a href="javascript:;" class="btn btn-xs btn-success btn-append hide"><i class="fa fa-plus"></i> 添加配置</a></td>
                </tr>
                <textarea name="row[product]" class="form-control hide" cols="30" rows="5">[]</textarea>
            </table>
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">商品详情:</label>
        <div class="col-xs-12 col-sm-8">

            <textarea  class="form-control editor" rows="3" name="row[description]"></textarea>
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>

<!--定义模板，模板语法使用Art-Template模板语法-->
<script type="text/html" id="testtpl2">
    <tr>
        <input type="hidden" class="form-control" name="row[<%=name%>][<%=index%>][index]"
               value="<%=row['index']%>"/></td>
        <td style="width: 150px">
            <input type="text" class="form-control" name="row[<%=name%>][<%=index%>][name]"
                                value="<%=row['name']%>"/></td>
        <td style="width: 80px" >
            <input type="text" class="form-control hide" name="row[<%=name%>][<%=index%>][type]"
                                value="<%=row['type']%>"/><%=row['type']%></td>
        <td style="width: 80px">
            <input type="number"  class="form-control" name="row[<%=name%>][<%=index%>][price]"
                   value="<%=row['price']%>"/></td>
        <td style="width: 80px">
            <input type="number"  class="form-control" name="row[<%=name%>][<%=index%>][wholesale]"
                   value="<%=row['wholesale']%>"/></td>
        <td style="width: 80px">
            <input type="number"  class="form-control" name="row[<%=name%>][<%=index%>][cost_price]"
                   value="<%=row['cost_price']%>"/></td>
        <td>
            <textarea name="row[<%=name%>][<%=index%>][remarks]" placeholder="请输入备注" class="form-control"><%=row['remarks']%></textarea>
            </td>
        <td>
            <div class="form-group">
                <div style="text-align: center">
                    <input id="c-avatar<%=index%>" class="form-control hidden" name="row[<%=name%>][<%=index%>][avatar]" value="<%=row['avatar']%>" type="text"/>
                    <button type="button" id="faupload-avatar<%=index%>" class="btn btn-xs btn-success faupload" data-input-id="c-avatar<%=index%>" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp" data-multiple="false" data-preview-id="p-avatar<%=index%>"><i class="fa fa-upload"></i> 上传</button>
                </div>
                <div id="p-avatar<%=index%>" data-template="desctpl" style="margin: 5px" ></div>
            </div></td>
        <td><span class="btn btn-xs btn-danger btn-remove"><i class="fa fa-times"></i></span></td>
    </tr>
</script>
<!--这里自定义图片预览的模板 开始-->
<script type="text/html" id="desctpl">
    <a href="<%=fullurl%>" data-url="<%=url%>" target="_blank" class="thumbnail">
        <img src="<%=fullurl%>" width="40" height="40" class="img-responsive">
    </a>
</script>
