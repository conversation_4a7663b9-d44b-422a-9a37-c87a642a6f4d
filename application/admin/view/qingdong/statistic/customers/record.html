
<div class="panel panel-default panel-intro">

    <div class="panel-body">
        <form name="search-form" role="form" method="get" data-toggle="validator"
              class="form-horizontal col-sm-12" action="">
            <div class="form-group">
                <div class="col-xs-12 col-sm-12">
                    <div class="input-group">
                        <input class="form-control datetimerange"
                               placeholder="指定日期" autoComplete='off'
                               data-locale='{"format":"YYYY-MM-DD"}'
                               style="width: 200px;margin: 10px" name="row[times]" value="{$times}" type="text">
                        <select class="form-control" name="row[type]" style="width: 200px;margin: 10px" >
                            <option value="0" {eq name="$type" value="0"}selected="selected"{/eq} >本人及下属</option>
                            <option value="1" {eq name="$type" value="1"}selected="selected"{/eq}>仅本人</option>
                            <option value="2" {eq name="$type" value="2"}selected="selected"{/eq}>仅下属</option>
                        </select>
                        <input data-source="qingdong/statistic/customers/getstaff" placeholder="选择员工" class="form-control selectpage" data-multiple="true" data-pagination="true" style="width: 200px;margin: 10px" name="row[staff_id]" type="text" value="{$staff_id}">

                        <button type="submit" class="btn btn-success" style="margin: 10px" >搜索</button>
                    </div>
                </div>
            </div>
        </form>
        <div class="col-xs-12">
            <div class="div-box">
                <div style="padding-top: 15px;padding-left:15px;margin-bottom:10px;">

                </div>
                <div id="echart" style="width:100%;height: 457px"></div>
            </div>
        </div>
        <div class="col-xs-12" style="padding-left:5%;padding-right:5%;padding-top:3%;">
            <table id="table" class="table table-striped table-bordered table-hover table-nowrap" width="100%" style="">
                <thead>
                <tr class="qd_table_color">

                    <th style="text-align: center; vertical-align: middle; " data-field="name">
                        <div class="th-inner ">员工姓名</div>
                        <div class="fht-cell"></div>
                    </th>
                    <th style="text-align: center; vertical-align: middle; " data-field="subname">
                        <div class="th-inner ">跟进次数</div>
                        <div class="fht-cell"></div>
                    </th>
                    <th style="text-align: center; vertical-align: middle; " data-field="owner_staff_id">
                        <div class="th-inner ">跟进客户数</div>
                        <div class="fht-cell"></div>
                    </th>
                </tr>
                </thead>
                <tbody data-listidx="0">
                {foreach name="customInfo" id="v"}
                <tr data-index="0" style="">

                    <td style="text-align: center; vertical-align: middle; ">
                        {$v.name}
                    </td>
                    <td style="text-align: center; vertical-align: middle; ">{$v.addcustomer}</td>
                    <td style="text-align: center; vertical-align: middle; ">{$v.usecustomer}</td>


                </tr>
                {/foreach}


                </tbody>
            </table>
        </div>
    </div>
</div>




<script>
    var Orderdata = {
        customerdata:{:json_encode($customerdata)}
    };
</script>