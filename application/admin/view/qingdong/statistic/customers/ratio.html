
<div class="panel panel-default panel-intro">

    <div class="panel-body">
        <h3>客户成交率分析 </h3>
        <div class="col-sm-12 col-xs-12">
            <div class="alert alert-warning alert-dismissible" role="alert">
                <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                注：首次成交率根据客户非续约签署合同计算；续约率根据合同续约签署合同计算；流失根据合同到期月续约计算。(时间以签约日期为准)
            </div>
        </div>
       <div class="col-sm-12 col-xs-12">
           <form name="search-form" role="form" method="get" data-toggle="validator"
                 class="form-horizontal col-sm-12" action="">
               <div class="form-group">
                   <div class="col-xs-12 col-sm-12">
                       <div class="input-group">

                           <input class="form-control datetimerange"
                                  placeholder="指定日期" autoComplete='off'
                                  data-locale='{"format":"YYYY-MM-DD"}'
                                  style="width: 200px;margin: 10px" name="row[times]" value="{$times}" type="text">

                           <select class="form-control selectpicker" name="row[group_id]" data-live-search="true" style="width: 200px;margin: 10px" >
                               <option value="">全部</option>
                               {foreach name="groupdata" item="val"}
                               <option value="{$val.id}" {eq name="group_id" value="$val.id"}selected="selected"{/eq} >{$val.name}</option>
                               {/foreach}
                           </select>
                           <input data-source="qingdong/statistic/customers/getstaff" placeholder="全部员工" class="form-control selectpage"  data-pagination="true" style="width: 200px;" name="row[staff_id]" type="text" value="{$staff_id}">

                           <button type="submit" class="btn btn-success" style="margin: 10px" >立即搜索</button>
                       </div>
                   </div>
               </div>
           </form>
       </div>
        <div class="col-xs-6 col-sm-6">
            <div class="div-box">
                <div style="padding-top: 15px;padding-left:15px;margin-bottom:10px;">

                </div>
                <div id="echart" style="width:100%;height: 457px"></div>
            </div>
        </div>
        <div class="col-xs-6 col-sm-6">
            <table id="table" class="table table-striped table-bordered table-hover table-nowrap" width="100%" style="">
                <thead>
                <tr class="qd_table_color">

                    <th>
                    </th>
                    <th>
                        个数
                    </th>
                    <th>
                        占比
                    </th>
                </tr>
                </thead>
                <tbody>
                    {foreach name="list" item="val"}
                        <tr>
                            <td>{$val.name}</td>
                            <td>{if condition="$val.number"}
                                <a href="qingdong/customer/customer/index?{$field}={$val.name}&createtime={$times}&group_id={$group_id}&owner_staff_id={$staff_id}"  class="btn-dialog" data-title="客户自定义分析" >{$val.number}</a>
                                {else/}
                                    {$val.number}
                                {/if} </td>
                            <td>{$val.ratio}%</td>
                        </tr>
                    {/foreach}
                </tbody>
            </table>
        </div>
    </div>
</div>


<script>
    var Orderdata = {
        customerdata:{:json_encode($customerdata)}
    };
</script>