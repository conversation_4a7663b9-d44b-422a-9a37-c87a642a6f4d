
<div class="panel panel-default panel-intro">

    <div class="panel-body">
        <div class="form-group">
            <h3 class="col-sm-2 col-xs-4">沟通日志统计</h3>
            <form name="search-form" role="form" method="get" data-toggle="validator"
                  class="form-horizontal " action="">
                <div class="col-sm-7 col-xs-7">
                    <div class="input-group">

                        <select name="row[year]" class="form-control" style="width: 80px;margin: 10px;font-size: 14px">
                            {foreach name="years" item="val"}
                            <option value="{$val}" {eq name="year" value="$val" }selected="selected"{/eq} >
                            {$val}年</option>
                            {/foreach}
                        </select>
                        <select name="row[month]" class="form-control " style="width: 60px;margin: 10px;font-size: 14px">
                            {for start="1" end="12"}
                            <option value="{$i}" {eq name="month" value="$i" }selected="selected"{/eq}  >{$i}月</option>
                            {/for}
                        </select>

                        <select class="form-control selectpicker" name="row[group_id]"
                                data-live-search="true" style="width: 100px;margin: 10px" >
                            <option value="">全部</option>
                            {foreach name="groupdata" item="val"}
                            <option value="{$val.id}" {eq name="group_id" value="$val.id"}selected="selected"{/eq} >{$val.name}</option>
                            {/foreach}
                        </select>

                    </div>
                </div>
                <div class="col-xs-2 col-sm-2">
                    <button type="submit" class="btn btn-success" style="margin: 10px">立即搜索</button>
                </div>
            </form>
        </div>
        <div class="col-xs-12 col-sm-12">

            <div class="widget-body no-padding" style="min-height: 564px">

                <table id="table" class="table table-striped table-bordered table-hover table-nowrap"
                       width="100%">
                    <thead>
                    <tr>
                        <th>
                            员工
                        </th>
                        <th>
                            本月
                        </th>
                        <th>
                            完成率
                        </th>
                        <th>
                            本年
                        </th>
                        <th>
                            完成率
                        </th>
                        {foreach name="dates" item="val"}
                        <th  title="{$val.search}">
                            {$val.date}
                        </th>
                        {/foreach}
                    </tr>
                    </thead>
                    <tbody>
                    {foreach name="list" item="ls"}
                    <tr>
                        <td>{$ls.name}</td>

                        <td>{$ls.month}</td>
                        <td>{$ls.month_ratio}%</td>
                        <td>{$ls.year}</td>
                        <td>{$ls.year_ratio}%</td>
                        {foreach name="dates" item="val"}
                        <td >
                            {if condition="$ls[$val['date']] > 0"}
                            <a href="qingdong/statistic/work/record_list?createtime={$val.search}&staff_id={$ls.id}" class="btn-dialog" data-title="工作报告">{$ls[$val['date']]}</a>
                            {else/}
                                {eq name="$ls[$val['date']]" value="-1"}
                                -
                                {else/}
                                <i class="fa fa-remove text-red"></i>
                                {/eq}
                            {/if}
                        </td>
                        {/foreach}
                    </tr>
                    {/foreach}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

