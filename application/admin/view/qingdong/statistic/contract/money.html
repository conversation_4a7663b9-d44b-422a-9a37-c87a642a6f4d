
<div class="panel panel-default panel-intro">

    <div class="panel-body">
        <form name="search-form" role="form" method="get" data-toggle="validator"
              class="form-horizontal col-sm-12" action="">
            <div class="form-group">
                <div class="col-xs-12 col-sm-12">
                    <div class="input-group">
                        <input class="form-control datetimepicker"
                               placeholder="指定年份" autoComplete='off'
                               data-date-format="YYYY"
                               style="width: 200px;margin: 10px" name="row[year]" value="{$year}" type="text">
                        <select class="form-control selectpicker" name="row[group_id]" data-live-search="true" style="width: 200px;margin: 10px" >
                            <option value="">全部</option>
                            {foreach name="groupdata" item="val"}
                            <option value="{$val.id}" {eq name="group_id" value="$val.id"}selected="selected"{/eq} >{$val.name}</option>
                            {/foreach}
                        </select>

                        <input data-source="qingdong/statistic/customers/getstaff" placeholder="选择员工" class="form-control selectpage" data-multiple="true" data-pagination="true" style="width: 200px;margin: 10px" name="row[staff_id]" type="text" value="{$staff_id}">

                        <button type="submit" class="btn btn-success" style="margin: 10px" >搜索</button>
                    </div>
                </div>
            </div>
        </form>
        <div class="col-xs-12 col-sm-12">
            <div class="div-box">
                <div style="padding-top: 15px;padding-left:15px;margin-bottom:10px;">
                </div>
                <div id="echart" style="width:100%;height: 457px"></div>
            </div>
        </div>
        <div class="col-xs-12 col-sm-12" style="padding-left:5%;padding-right:5%;padding-top:3%;">
            <table id="table" class="table table-striped table-bordered table-hover table-nowrap" width="100%" style="">
                <thead>
                <tr class="qd_table_color">

                    <th >
                       {$year}年度
                    </th>
                    {foreach name="$data.date" item="val"}
                    <th >
                        {$val}
                    </th>
                    {/foreach}
                </tr>
                </thead>
                <tbody>
                <tr>
                    <td>合同金额</td>
                    {foreach name="$data.number" item="val" key="k"}
                    <td> <a href="qingdong/customer/contract/index?check_status=2&order_date={$year}-{:$k+1}-01 00:00:00 - {$year}-{:$k+1}-31 23:59:59&group_id={$group_id}&staff_id={$staff_id}"  class="btn-dialog" data-title="合同金额统计" >{$val}</a></td>
                    {/foreach}

                </tr>
                <tr>
                    <td>环比增长(与上月对比)</td>
                    {foreach name="$data.month_ratio" item="val" key="k"}
                    <td>
                        {if condition="$val < 0"}
                        {$val}%<i class="fa fa-long-arrow-down text-red"></i>
                        {elseif condition="$val > 0"}
                        {$val}%<i class="fa fa-long-arrow-up text-success"></i>
                        {else/}
                        -
                        {/if}
                    </td>
                    {/foreach}
                </tr>
                <tr>
                    <td>同比增长(与去年对比)</td>
                    {foreach name="$data.year_ratio" item="val" key="k"}
                    <td>
                        {if condition="$val < 0"}
                        {$val}%<i class="fa fa-long-arrow-down text-red"></i>
                        {elseif condition="$val > 0"}
                        {$val}%<i class="fa fa-long-arrow-up text-success"></i>
                        {else/}
                        -
                        {/if}
                    </td>
                    {/foreach}
                </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>




<script>
    var Orderdata = {
        data:{:json_encode($data)}
    };
</script>