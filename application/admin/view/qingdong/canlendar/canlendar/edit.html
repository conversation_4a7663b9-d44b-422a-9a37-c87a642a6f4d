<form id="edit-form" class="form-horizontal form-ajax" role="form" data-toggle="validator" method="POST" action="">
	<div class="form-group">
		<label class="control-label col-xs-12 col-sm-2">日程标题:</label>
		<div class="col-xs-12 col-sm-8">
			<input type="text" name="row[title]" class="form-control" data-rule="required" value="{$row.title}"/>
		</div>
	</div>
	<div class="form-group">
		<label class="control-label col-xs-12 col-sm-2">开始时间:</label>
		<div class="col-xs-12 col-sm-8">
			<input type="text" name="row[start_time]" value="{$row.start_time}" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" data-rule="required"/>
		</div>
	</div>
	<div class="form-group">
		<label class="control-label col-xs-12 col-sm-2">结束时间:</label>
		<div class="col-xs-12 col-sm-8">
			<input type="text" name="row[end_time]" value="{$row.end_time}" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" data-rule="required"/>
		</div>
	</div>
	<div class="form-group">
		<label class="control-label col-xs-12 col-sm-2">提醒时间:</label>
		<div class="col-xs-12 col-sm-8">
			<input type="text" name="row[remind_time]" value="{$row.remind_time}" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" data-rule="required"/>
		</div>
	</div>
	<div class="form-group">
		<label  class="control-label col-xs-12 col-sm-2">日程状态:</label>
		<div class="col-xs-12 col-sm-8">
			<select  name="row[status]"  class="form-control">
				<option value="0">未开始</option>
				<option value="1">执行中</option>
				<option value="2">已结束</option>
				<option value="3">已取消</option>
			</select>
		</div>
	</div>
	<div class="form-group">
		<label  class="control-label col-xs-12 col-sm-2">紧要程度:</label>
		<div class="col-xs-12 col-sm-8">
			<select  name="row[level]"  class="form-control">
				<option value="1">重要</option>
				<option value="2">紧急</option>
				<option value="3">普通</option>
				<option value="4">重要且紧急</option>
			</select>
		</div>
	</div>
	<div class="form-group">
		<label  class="control-label col-xs-12 col-sm-2">关联类型:</label>

		<div class="col-xs-12 col-sm-8">
			<div class="radio">
				<label for="row[relation_type]-1">
					<input id="row[relation_type]-1"  {eq name="1" value="$row.relation_type"} checked="checked" {/eq} class="relation_type" name="row[relation_type]" type="radio" value="1"> 客户
				</label>
				<label for="row[relation_type]-2">
					<input id="row[relation_type]-2" name="row[relation_type]" {eq name="2" value="$row.relation_type"} checked="checked" {/eq}  class="relation_type" type="radio" value="2"> 联系人
				</label>
				<label for="row[relation_type]-3"><input id="row[relation_type]-3" class="relation_type" {eq name="3" value="$row.relation_type"} checked="checked" {/eq} name="row[relation_type]" type="radio" value="3"> 合同
				</label>
				<label for="row[relation_type]-4"><input id="row[relation_type]-4" class="relation_type" {eq name="4" value="$row.relation_type"} checked="checked" {/eq} name="row[relation_type]" type="radio" value="4"> 线索
				</label>
			</div>
		</div>

	</div>
	<div class="form-group">
		<label  class="control-label col-xs-12 col-sm-2">关联类型:</label>
		<div class="col-xs-12 col-sm-8">
			<select id="c-type" name="row[relation_id]"  class="form-control selectpicker" data-live-search="true" data-rule="required">
				{foreach name="customer" id="v"}
				<option value="{$v.id}" {eq name="$v.id" value="$row.relation_id"} selected {/eq}>{$v.name}</option>
				{/foreach}
			</select>
		</div>
	</div>
	<div class="form-group" id="contents">
		<label class="control-label col-xs-12 col-sm-2">备注:</label>
		<div class="col-xs-12 col-sm-8">
			<textarea  name="row[remark]" rows="5" cols="50" class="form-control" value="{$row.remark}">{$row.remark}</textarea>
		</div>
	</div>


	<div class="form-group hidden layer-footer">
		<div class="col-xs-2"></div>
		<div class="col-xs-12 col-sm-8">
			<button type="submit" class="btn btn-success btn-embossed">{:__('OK')}</button>
			<button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
		</div>
	</div>
</form>
