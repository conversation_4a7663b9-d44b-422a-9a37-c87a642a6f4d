<form id="add-form" class="form-horizontal form-ajax" role="form" data-toggle="validator" method="POST" action="">


    <div class="form-group" id="title">
        <label class="control-label col-xs-12 col-sm-2">审批流程:</label>
        <div class="col-xs-12 col-sm-8">
            <input type="text" name="row[name]" class="form-control" value="{$row.name}" data-rule="required"/>
        </div>
    </div>
    <div class="form-group" id="">
        <label class="control-label col-xs-12 col-sm-2">关联对象:</label>
        <div class="col-xs-12 col-sm-8">
            <select name="row[relation_type]" class="form-control">
                <option value="contract" {if condition="$row['relation_type'] == 'contract'" }
                        selected="selected"{/if} >合同</option>
                <option value="receivables" {if condition="$row['relation_type'] == 'receivables'" }selected="selected"{/if}>回款</option>
                <option value="consume" {if condition="$row['relation_type'] == 'consume'"}selected="selected"{/if}>费用</option>
                <option value="achievement" {if condition="$row['relation_type'] == 'achievement'"}selected="selected"{/if}>业绩目标</option>
                <option value="card"{if condition="$row['relation_type'] == 'card'"}selected="selected"{/if}>补卡</option>
                <option value="leave"{if condition="$row['relation_type'] == 'leave'"}selected="selected"{/if}>请假</option>
            </select>
        </div>
    </div>

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">适用范围:<br><span class="text-muted">（空为全公司）</span></label>
        <div class="col-xs-12 col-sm-8">
            {:build_select('row[group_ids][]', $groupdata, $row['group_ids'], ['class'=>'form-control selectpicker', 'multiple'=>''])}

        </div>
    </div>

    <div class="form-group" id="contents">
        <label class="control-label col-xs-12 col-sm-2">备注:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea name="row[remark]" class="form-control " placeholder="请输入备注" >{$row.remark}</textarea>
        </div>
    </div>

    <div class="form-group" id="status">
        <label class="control-label col-xs-12 col-sm-2">流程类型:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_radios('row[status]', ['1'=>__('固定审批'), '0'=>__('授权审批人')],$row['status'])}
            <p class="bg-warning" style="padding: 10px">
				<span>固定审批：
					当选择“直属上级”审批时，系统会通知负责人的直属上级<br>
					当选择“指定用户（任意一人）”表示指定用户中任意一人审批即可。<br>
					当选择“指定用户（多人会签）”表示指定用户中所有人都要审批。<br>
				</span>
                <span>授权审批人：负责人自行选择审批人</span>
            </p>
            <div class="form-group status_text" {eq name="$row.status" value="0"} style="display: none"{/eq} >
                <table class="table table-condensed table-bordered table-responsive fieldlist" data-name="row[examine_ids]" data-template="testtpl" data-tag="tr">
                    <tr>
                        <th width="80">审批层级</th>
                        <th width="150">审批类型</th>
                        <th style="width: 250px">选择</th>
                        <th width="50"></th>
                    </tr>
                    <tr>
                        <td colspan="4"><a href="javascript:;" class="btn btn-sm btn-success btn-append"><i class="fa fa-plus"></i> 添加审批层级</a></td>
                    </tr>
                    <textarea name="row[examine_ids]" class="form-control hide"
                              cols="30" rows="5">{$row.examine_ids}</textarea>
                </table>
            </div>

        </div>
    </div>

    <div class="form-group hidden layer-footer">
        <div class="col-xs-2"></div>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-success btn-embossed">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
<!--定义模板，模板语法使用Art-Template模板语法-->
<script type="text/html" id="testtpl">
    <tr class="form-inline">
        <td>第<%=index+1%>级</td>
        <td><select name="row[<%=name%>][<%=index%>][stafftype]" class="form-control stafftype" style="width: 100%">
            <option value="1" <%if (row['stafftype'] == 1){%>selected="selected"<%}%> >指定员工（任意一人）</option>
            <option value="2" <%if (row['stafftype'] == 2){%>selected="selected"<%}%> >指定用户（多人会签）</option>
            <option value="3" <%if (row['stafftype'] == 3){%>selected="selected"<%}%> >直属上级</option>

        </select></td>
        <td>
        <div <%if (row['stafftype'] == 3){%>style="display: none"<%}%>><input type="text" placeholder="请选择员工" class="form-control selectpage disabled" name="row[<%=name%>][<%=index%>][staff_id]" data-pagination="true" data-page-size="10" data-multiple="true"
                   style="width: 100%;" data-source="qingdong/work/flow/getstaff" value="<%=row['staff_id']%>" ></div></td>
        <td><span class="btn btn-sm btn-danger btn-remove"><i class="fa fa-times"></i></span></td>
    </tr>
</script>
