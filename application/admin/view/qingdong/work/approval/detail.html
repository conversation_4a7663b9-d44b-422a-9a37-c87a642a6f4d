<link href="__CDN__/assets/addons/qingdong/css/common.css?v={$Think.config.site.version}" rel="stylesheet">

<style type="text/css">
    .btn-list {
        position: fixed;
        right: 40px;
    }
    .div-flex {
        display: flex
    }
    .div-flex .form-group {
        margin-right: 50px;
        text-align: center;
        line-height: 25px;
        margin-bottom: 0;
    }
    .tab-pane {
        border: 1px solid #F6F6F6;
    }
    .i-color {
        font-size: 34px;margin-right: 10px;color: #86C3FF;
    }
    .thumbnail {
        display: flow-root;
        width: max-content;
    }
    .thumbnail img {
        width: 50px;
        float: left;
        padding: 5px;
    }
    .text-color{
        color:#777;
    }
</style>

<style type="text/css">
    .examine_title{
        font-size: 14px;
        color: #333;
        -webkit-box-flex: 1;
        -ms-flex: 1;
        flex: 1;
        display: block;
        height: 30px;
    }
    .examine_box{
        padding-left:20px;
        position: relative;
    }
    .examine_box .newicon{
        position: absolute;
        left:0;
        top:0;
        z-index: 2;
    }
    .examine_box .xian{
        width:1px;
        height:100%;
        background:#e6e6e6;
        position: absolute;
        left:4px;
        top:10px;
        z-index: 1;
    }
    .examine_box .examine_bg{
        position: relative;
        margin-top: 15px;
        border-radius: 4px;
        background-color: #f7f8fa;
        font-size: 12px;
        color: #929293;
        padding: 8px;
        line-height: 18px;
    }
    .rowbox{
        display: flex;
        justify-content: flex-start;
        align-items: center;
    }
    .stafflist{
        width: 70px;text-align: center;float: left;
    }
    .stafflist-right{
        width: 30px;text-align: center;float: left;margin: 0 20px;

    }
    .stafflist-right:last-child{
        display: none;
    }
    .stafflist img{
        width: 40px;
        height: 40px;
        margin: 10px;
        border-radius:50%;
    }
</style>
<div class="panel panel-default panel-intro">
    <input type="hidden" name="ids" id="ids" value="{$row.id}"/>
    <div class="div-flex">
        <i class="fa fa-address-card i-color"></i>
        <div style="line-height: 35px"><b>{$row.name}</b>      {switch name="row.check_status"}
            {case value="0"}<b class="text-success">(审核中)</b>{/case}
            {case value="1"}<b class="text-success">(审核中)</b>{/case}
            {case value="2"}<b class="text-warning">(审核通过)</b>{/case}
            {case value="3"}<b class="text-danger">(审核拒绝)</b>{/case}
            {case value="4"}<b class="text-danger">(撤销审批)</b>{/case}
            {default /}
            {/switch}</div>
        <div class="btn-list">
            {eq name="flow.is_check" value="1"}
            <a class="btn  btn-success btn-dialog" data-area="" data-shade="0.3"
               data-url="qingdong/examine/examine_record/examine?relation_type=approval&relation_id={$row.id}&status=1"
               data-title="审核通过" >审核通过</a>
            <a class="btn btn-danger btn-dialog" data-area="" data-shade="0.3"
               data-url="qingdong/examine/examine_record/examine?relation_type=approval&relation_id={$row.id}&status=2"
               data-title="审核拒绝" >审核拒绝</a>
            {/eq}

        </div>
    </div>

    <div style="margin-top: 10px">
    </div>
    {if condition="$flow.status == 1"}

    <div class="div-flex">
        <div class="form-group col-sm-10">

            <blockquote style="padding: 2px 10px;text-align: left">
                <p>审核信息 <span class="label label-danger">固定审批流</span></p>
            </blockquote>
            <div  style="text-align: left">
                <a href="javascript:void(0)" data-toggle="popover"
                   data-content="
                   <p class='examine_title'>审批流程</p>
                   {foreach name='$examine_record' item='examine_r'}
                {if condition='$examine_r.status == 0'}
                {elseif condition='$examine_r.status == 1'}
                    <div class='examine_box'>
                        <i class='fa fa-check-circle text-success newicon'></i>
                        <div class='xian'></div>
                        <p><span class='text-muted'>{$examine_r.check_time}</span></p>
                        <p>  {$examine_r.check_staff.name} <span class='text-muted'>通过了此申请</span></p>
                        {if condition='$examine_r.content'}<p class='examine_bg'>  {$examine_r.content}</p>{/if}
                    </div>
                {elseif condition='$examine_r.status == 2'}
                  <div class='examine_box'>
                        <i class='fa fa-window-close text-red newicon'></i>
                        <div class='xian'></div>
                        <p><span class='text-muted'>{$examine_r.check_time}</span></p>
                        <p>  {$examine_r.check_staff.name} <span class='text-muted'>拒绝了此申请</span></p>
                        {if condition='$examine_r.content'}<p class='examine_bg'>  {$examine_r.content}</p>{/if}
                  </div>
                {else/}

                  <div class='examine_box'>
                        <i class='fa fa-window-close text-red newicon'></i>
                        <div class='xian'></div>
                        <p><span class='text-muted'>{$examine_r.check_time}</span></p>
                        <p>  {$examine_r.check_staff.name} <span class='text-muted'>撤回了此申请</span></p>
                        {if condition='$examine_r.content'}<p class='examine_bg'>  {$examine_r.content}</p>{/if}
                  </div>
                {/if}
                {/foreach}" data-html="true">查看审批历史</a>
            </div>

            <div class="row rowbox" style="margin-left: 20px">
                {foreach name="$flow.stepList" item="val"}
                <div class="stafflist"  data-container="body" data-toggle="popover" data-placement="bottom" data-content="{foreach name='$val.stafflist' item='sf'}
                {if condition='$sf.examine_reord.status == 0'}
                <p><i class='fa fa-clock-o text-red'></i> {$sf.name} 待审核此申请</p>
                {elseif condition='$sf.examine_reord.status == 1'}
                <p><i class='fa fa-check-circle text-success'></i> {$sf.examine_reord.check_time}</p>
                <p>{$sf.name} 通过此申请</p>
                {elseif condition='$sf.examine_reord.status == 2'}
                <p><i class='fa fa-window-close text-red'></i> {$sf.examine_reord.check_time}</p>
                <p>{$sf.name} 拒绝此申请</p>
                {else/}
                <p><i class='fa fa-mail-reply text-red'></i> {$sf.name} 已撤销</p>
                {/if}
                {/foreach}" data-html="true">


                    <img src="/assets/addons/qingdong/img/avatar.png" class="img-circle">
                    {if condition="$val.status == 3"}
                    <span class="text-muted">发起人主管</span>
                    {elseif condition="$val.status == 2"/}
                    <span class="text-muted">{:count($val.stafflist)}人并签</span>
                    {else/}
                    <span class="text-muted">{:count($val.stafflist)}人或签</span>
                    {/if}

                    {if condition="$val.order_id < $flow.order_id"}
                    <p><span><i class="fa fa-check-circle text-success"></i> 审核通过</span></p>
                    {else/}
                    {if condition="$row.check_status == 3"}
                    <p> <span><i class="fa fa-window-close text-red"></i> 审核拒绝</span></p>
                    {else/}
                    <p><span><i class="fa fa-clock-o text-red"></i> 待审核</span></p>
                    {/if}
                    {/if}

                </div>

                <div class="stafflist-right">
                    <i class="fa fa-angle-right" style="color: #ccc;margin: 0 16px;font-size: 20px;font-weight: 600;"></i>
                </div>

                {/foreach}
            </div>


        </div>
    </div>

    {else/}

    <div class="div-flex">
        <div class="form-group col-sm-10">
            <blockquote style="padding: 2px 10px;text-align: left">
                <p>审核信息 <span class="label label-danger">发起人自选</span></p>
            </blockquote>
            <div  style="text-align: left">
                <a href="javascript:void(0)" data-toggle="popover"
                   data-content="
                   <p class='examine_title'>审批流程</p>
                   {foreach name='$examine_record' item='examine_r'}
                {if condition='$examine_r.status == 0'}
                {elseif condition='$examine_r.status == 1'}
                    <div class='examine_box'>
                        <i class='fa fa-check-circle text-success newicon'></i>
                        <div class='xian'></div>
                        <p><span class='text-muted'>{$examine_r.check_time}</span></p>
                        <p>  {$examine_r.check_staff.name} <span class='text-muted'>通过了此申请</span></p>
                        {if condition='$examine_r.content'}<p class='examine_bg'>  {$examine_r.content}</p>{/if}
                    </div>
                {elseif condition='$examine_r.status == 2'}
                  <div class='examine_box'>
                        <i class='fa fa-window-close text-red newicon'></i>
                        <div class='xian'></div>
                        <p><span class='text-muted'>{$examine_r.check_time}</span></p>
                        <p>  {$examine_r.check_staff.name} <span class='text-muted'>拒绝了此申请</span></p>
                        {if condition='$examine_r.content'}<p class='examine_bg'>  {$examine_r.content}</p>{/if}
                  </div>
                {else/}

                  <div class='examine_box'>
                        <i class='fa fa-window-close text-red newicon'></i>
                        <div class='xian'></div>
                        <p><span class='text-muted'>{$examine_r.check_time}</span></p>
                        <p>  {$examine_r.check_staff.name} <span class='text-muted'>撤回了此申请</span></p>
                        {if condition='$examine_r.content'}<p class='examine_bg'>  {$examine_r.content}</p>{/if}
                  </div>
                {/if}
                {/foreach}" data-html="true">查看审批历史</a>
            </div>
            <div class="row rowbox" style="margin-left: 20px">

                {foreach name="$flow.stepList.0.stafflist" item="val"}
                <div class="stafflist">
                    <img src="{$val.img}" class="img-circle">
                    <span class="text-muted">{$val.name}</span>
                    {if condition="$val.examine_reord.status == 0"}
                    <p><span><i class="fa fa-clock-o text-red"></i> 待审核</span></p>
                    {elseif condition="$val.examine_reord.status == 1"}
                    <p><span><i class="fa fa-check-circle text-success"></i> 审核通过</span></p>
                    {else/}
                    <p> <span><i class="fa fa-window-close text-red"></i> 审核拒绝</span></p>
                    {/if}
                </div>

                <div class="stafflist-right">
                    <i class="fa fa-angle-right" style="color: #ccc;margin: 0 16px;font-size: 20px;font-weight: 600;"></i>
                </div>

                {/foreach}
            </div>
        </div>
    </div>
    {/if}

    <div class="div-flex" style="margin-top: 20px">

        <div class="form-group">
            <span class="text-muted">审批状态</span>
            {switch name="row.check_status"}
            {case value="0"}<p class="text-success">审核中</p>{/case}
            {case value="1"}<p class="text-success">审核中</p>{/case}
            {case value="2"}<p class="text-primary">审核通过</p>{/case}
            {case value="3"}<p class="text-red">审核拒绝</p>{/case}
            {case value="4"}<p class="text-red">撤销审批</p>{/case}
            {default /}
            {/switch}

        </div>
        <div class="form-group">
            <span class="text-muted">提交时间</span>
            <p>{$row.createtime}</p>
        </div>
        <div class="form-group">
            <span class="text-muted">提交人</span>
            <p>{$row.create_staff.name}</p>
        </div>
    </div>
<div>
    <ul class="nav nav-tabs" role="tablist">
        <li role="presentation" class="active"><a href="#record" aria-controls="home" role="tab" data-toggle="tab">提交信息</a></li>
    </ul>
    <div class="tab-content" style="overflow-y: scroll;height:565px">

        <div class=" panel-body form-horizontal" id="record">
            <div class="form-group">
                {include file="qingdong/common/form_detail" /}
            </div>

        </div>

    </div>
</div>
</div>
