<link href="__CDN__/assets/addons/qingdong/css/index.css?v={$Think.config.site.version}" rel="stylesheet">
<style>
    .popover{
        width:200px !important;
    }
</style>
<div class="panel panel-default panel-intro">
    {:build_heading()}

    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade active in" id="one">
                <div class="widget-body no-padding">
                    <div id="toolbar" class="toolbar">
                        {:build_toolbar('refresh')}
                        <div class="dropdown btn-group">
                            <a class="btn btn-success btn-more dropdown-toggle" data-toggle="dropdown"><i class="fa fa-plus"></i> 新建审批</a>
                            <ul class="dropdown-menu text-left" role="menu">
                                {foreach name="form" id="v"}
                                <li><a class="btn btn-link btn-dialog" data-url="qingdong/work/approval/add/id/{$v.id}" data-area=["80%","80%"] data-title="{$v.name}" >{$v.name}</a></li>
                                {/foreach}
                            </ul>
                            <small style="line-height:34px;" >
                                <i class="fa fa-info-circle"></i>
                                <a href="javascript:;"  id="myPopover"  data-toggle="popover" data-placement="right" data-trigger="hover" data-template='<div class="popover" role="tooltip">
    <div class="arrow"></div>
    <h3 class="popover-title" style="width:200px;"></h3>
</div>'  data-original-title="办公审批类型为自定义配置,请在企业管理->办公审批配置中进行配置">无数据?</a>
                            </small>
                        </div>

                    </div>

                    <table id="table" class="table table-striped table-bordered table-hover" width="100%">
                    </table>
                </div>
            </div>

        </div>
    </div>
</div>


