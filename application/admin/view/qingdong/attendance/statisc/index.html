<link href="__CDN__/assets/addons/qingdong/css/index.css?v={$Think.config.site.version}" rel="stylesheet">

<div class="panel panel-default panel-intro">
    <div class="panel-heading">
        <ul class="nav nav-tabs">
            <li class="active"><a href="#first" data-toggle="tab">全部</a></li>
            <li><a href="#second" data-toggle="tab">我创建的</a></li>
            <li><a href="#third" data-toggle="tab">下属创建的</a></li>

        </ul>
    </div>
    {:build_heading()}

    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade active in" id="first">
                <div class="widget-body no-padding">
                    <div id="toolbar" class="toolbar">
                        {:build_toolbar('refresh')}
                    </div>
                    <table id="table" class="table table-striped table-bordered table-hover" width="100%">
                    </table>
                </div>
            </div>
            <div class="tab-pane fade" id="second">
                <div class="widget-body no-padding">
                    <div id="toolbar1" class="toolbar">
                        {:build_toolbar('refresh')}
                    </div>
                    <table id="table1" class="table table-striped table-bordered table-hover" width="100%">
                    </table>
                </div>
            </div>
            <div class="tab-pane fade" id="third">
                <div class="widget-body no-padding">
                    <div id="toolbar2" class="toolbar">
                        {:build_toolbar('refresh')}
                    </div>
                    <table id="table2" class="table table-striped table-bordered table-hover" width="100%">
                    </table>
                </div>
            </div>


        </div>
    </div>
</div>

<script id="customformtpl" type="text/html">
    <!--form表单必须添加form-commsearch这个类-->
    <form action="" class="form-commonsearch">
        <div style="border-radius:2px;margin-bottom:10px;background:#f5f5f5;padding:15px 20px;">
            <div class="row">

                <div class="col-xs-12 col-sm-6 col-md-3">
                    <label class="control-label">月份</label>
                    <input type="hidden" class="operate" data-name="time" value="like"/>

                    <input type="text" class="form-control datetimepicker"
                           data-date-format="YYYY-MM" name="time" value="{:date('Y-m')}"/>
                </div>
                <div class="col-xs-12 col-sm-6 col-md-3">
                    <label class="control-label"></label>
                    <div class="col-xs-6 input-group">
                        <input type="submit" class="btn btn-success btn-embossed" value="提交"/>
                        <input type="reset" class="btn btn-primary btn-embossed" value="重置"/>
                    </div>
                </div>

            </div>
        </div>
    </form>
</script>
