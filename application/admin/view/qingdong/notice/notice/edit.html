<style>
.input-group-addon{background-color:transparent !important;}
</style>
<form id="edit-form" class="form-horizontal form-ajax" role="form" data-toggle="validator" method="POST" action="">

	<div class="form-group" id="title">
		<label  class="control-label col-xs-12 col-sm-2">标题:</label>
		<div class="col-xs-12 col-sm-8">
			<input type="text" name="row[title]"  class="form-control" data-rule="required" value="{$row.title}" maxlength="50"/>
		</div>
	</div>

	<div class="form-group" id="contents">
		<label  class="control-label col-xs-12 col-sm-2">详情:</label>
		<div class="col-xs-12 col-sm-8">
			<textarea id="c-content" name="row[content]" rows="5" cols="50" class="form-control editor" data-rule="required">{$row.content}</textarea>
		</div>
	</div>


	<div class="form-group  layer-footer">
		<div class="col-xs-2"></div>
		<div class="col-xs-12 col-sm-8">
			<button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
			<button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
		</div>
	</div>
</form>
