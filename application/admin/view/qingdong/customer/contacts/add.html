
<form id="edit-form" class="form-horizontal col-sm-12" role="form" data-toggle="validator" method="POST" action="">
	{:token()}

	<div class="form-group">

		<div class="col-xs-12 col-sm-6">
			<div class="form-group col-sm-10">
				<label >归属客户:</label>
				<input type="hidden" name="row[customer_id]" data-rule="required" value="{$customer_id}" >
				<div class="form-control">
					<a href="javascript:void(0)" class="select-customer" data-name="row[customer_id]"
					   data-url="qingdong/customer/customer/index?isselect=1"
					>{if condition="$customer"}{$customer.name}{else/}【请点击选择】{/if}</a>
				</div>
			</div>
		</div>

		{include file="qingdong/common/form_add" /}

	</div>


	<div class="form-group layer-footer">
		<label class="control-label col-xs-12 col-sm-2"></label>
		<div class="col-xs-12 col-sm-8">
			<button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
			<button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
		</div>
	</div>
</form>
