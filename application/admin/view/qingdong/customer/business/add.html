<style type="text/css">
    .stafflist{
        width: 60px;text-align: center;float: left;
    }
    .stafflist img{
        width: 40px;
        height: 40px;
        margin: 10px;
        border-radius:50%;
    }
    .level{
        width: 100px;text-align: center;float: left;padding-top:18px;
        margin-left: 30px;
    }
    .level span:first-child{
        line-height: 2em
    }
    .form-group label{
        font-weight:normal !important;
    }
</style>
<form id="add-form" class="form-horizontal col-sm-12" role="form" data-toggle="validator" method="POST" action="">
        {:token()}
        <div class="col-xs-12 col-sm-6">
            <div class="form-group col-sm-10">
                <label >客户名称:</label>
                <input type="hidden" name="row[customer_id]" data-rule="required" value="{$customer_id}" >
                <div class="form-control">
                    <a href="javascript:void(0)" class="select-customer" data-name="row[customer_id]"
                       data-url="qingdong/customer/customer/index?isselect=1"
                    >{if condition="$customer"}{$customer.name}{else/}【请点击选择】{/if}</a>
                </div>
            </div>
        </div>
        <div class="col-xs-12 col-sm-6">
            <div class="form-group col-sm-10">
                <label for="owner_staff_id">销售负责人:</label>
                <select id="owner_staff_id" name="row[owner_staff_id]" data-rule="required" class="form-control selectpicker" data-live-search="true">
                    <option value="">请选择</option>
                    {foreach name="staffs" item="val"}
                    <option value="{$val.id}">{$val.name}</option>
                    {/foreach}
                </select>
            </div>
        </div>
        {include file="qingdong/common/form_add" /}
        <div class="clearfix"></div>
        <div class="col-xs-12 col-sm-6">
            <div class="form-group col-sm-10">
                <label>客户联系人: </label>
                <input type="hidden" name="row[contacts_id]" data-rule="required"  value="{$contacts.id??''}" >
                <div class="form-control">
                    <a href="javascript:void(0)" class="select-contacts" data-name="row[contacts_id]"
                       data-url="qingdong/customer/contacts/index?isselect=1"  >{if isset($contacts)}{$contacts.name}{else/}【请点击选择】{/if}</a>
                </div>
            </div>
        </div>

        <div class="col-xs-12 col-sm-6">
            <div class="form-group col-sm-10">
                <label >客户联系方式:</label>
                <input type="text" name="row[contacts_mobile]" class="form-control" data-rule="required" value="{$contacts.mobile??''}">
            </div>
        </div>

        <div class="col-xs-12 col-sm-12">
            <div class="form-group col-sm-11">
                <label >客户联系地址:</label>
                <input type="text" name="row[contacts_address]" class="form-control" data-rule="required" value="{$customer.address_detail??''}">
            </div>
        </div>
        <!--
        <div class="col-xs-12 col-sm-12">
            <div class="form-group col-sm-10">
                <label>产品列表:</label>
            </div>
        </div>
        <div class="col-xs-12 col-sm-12">
            <div class="form-group col-sm-12">
                <table class="table table-responsive fieldlist" data-name="row[product]" data-template="testtpl2" data-tag="tr">
                    <tr>
                        <td>商品名称</td>
                        <td>零售价</td>
                        <td>批发价</td>
                        <td>产品</td>
                        <td>规格</td>
                        <td>数量</td>
                        <td></td>
                    </tr>
                    <tr>
                        <td colspan="5"><a href="javascript:;" class="btn btn-sm btn-success btn-append"><i class="fa fa-plus"></i> 追加</a></td>
                    </tr>
                    <textarea name="row[product]" class="form-control hide" cols="30" rows="5">[]</textarea>
                </table>

            </div>
        </div>
        -->
        <div class="form-group layer-footer">
            <label class="control-label col-xs-12 col-sm-2"></label>
            <div class="col-xs-12 col-sm-8">
                <button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
                <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
            </div>
        </div>
</form>


<!--定义模板，模板语法使用Art-Template模板语法-->
<script type="text/html" id="testtpl2">
    <tr class="form-inline">
        <td>
            <input type="hidden" name="row[<%=name%>][<%=index%>][product_id]" value="<%=row['product_id']%>" >
            <div class="form-control" style="width: 90%">

                <a href="javascript:void(0)" class="select-product"
                   data-name="row[<%=name%>][<%=index%>][product_id]"
                   data-url="qingdong/product/product/index?isselect=1"  >
                    <% if(row['product_name']){%>
                    <%=row['product_name']%>
                    <% }else{%>
                    【请点击选择】
                    <% }%>
                </a>

            </div></td>

        <td style="width: 80px">
            <input type="number" name="row[<%=name%>][<%=index%>][price]" style="width: 80px"
                   class="form-control price" value="<%=row['price']%>" placeholder="--"></td>
        <td style="width: 80px">
            <input type="number" name="row[<%=name%>][<%=index%>][wholesale]" style="width: 80px"
                   class="form-control price2" value="<%=row['wholesale']%>" placeholder="0">
        </td>
        <td ><span class="goods"><%=row['goods_name']%></span></td>
        <td style="width: 80px"><span class="type"><%=row['type']%></span></td>
        <td style="width: 80px"><input type="number" name="row[<%=name%>][<%=index%>][number]" style="width: 80px"
                                       class="form-control number" value="<% if(row['number']){%><%=row['number']%><% }else{%>1<% }%>" ></td>

        <td style="width: 120px"><span class="btn btn-sm btn-danger btn-remove"><i class="fa fa-times"></i></span> <span class="btn btn-sm btn-primary btn-dragsort"><i class="fa fa-arrows"></i></span></td>
    </tr>
</script>
