<link href="__CDN__/assets/addons/qingdong/css/common.css?v={$Think.config.site.version}" rel="stylesheet">

<style type="text/css">
    .btn-list {
        position: fixed;
        right: 40px;
    }
    .div-flex {
        display: flex
    }
    .div-flex .form-group {
        margin-right: 50px;
        text-align: center;
        line-height: 25px;
        margin-bottom: 0;
    }
    .tab-pane {
        border: 1px solid #F6F6F6;
    }
    .i-color {
        font-size: 34px;margin-right: 10px;color: #86C3FF;
    }
    .thumbnail {
        display: flow-root;
        width: max-content;
    }
    .thumbnail img {
        width: 50px;
        float: left;
        padding: 5px;
    }
    .text-color{
        color:#777;
    }
</style>
<style type="text/css">
    .examine_title{
        font-size: 14px;
        color: #333;
        -webkit-box-flex: 1;
        -ms-flex: 1;
        flex: 1;
        display: block;
        height: 30px;
    }
    .examine_box{
        padding-left:20px;
        position: relative;
    }
    .examine_box .newicon{
        position: absolute;
        left:0;
        top:0;
        z-index: 2;
    }
    .examine_box .xian{
        width:1px;
        height:100%;
        background:#e6e6e6;
        position: absolute;
        left:4px;
        top:10px;
        z-index: 1;
    }
    .examine_box .examine_bg{
        position: relative;
        margin-top: 15px;
        border-radius: 4px;
        background-color: #f7f8fa;
        font-size: 12px;
        color: #929293;
        padding: 8px;
        line-height: 18px;
    }
    .rowbox{
        display: flex;
        justify-content: flex-start;
        align-items: center;
    }
    .stafflist{
        width: 70px;text-align: center;float: left;
    }
    .stafflist-right{
        width: 30px;text-align: center;float: left;margin: 0 20px;

    }
    .stafflist-right:last-child{
        display: none;
    }
    .stafflist img{
        width: 40px;
        height: 40px;
        margin: 10px;
        border-radius:50%;
    }
</style>
<style>
    /* 使用弹性居中布局让所有分部居中水平排列 */
    .steps {
        padding: 20px;
        display: flex;
        justify-content: center;
        width:100%;
    }

    /* 进度线宽，4份，25% */
    .step {
        width: 25%;
        height: 20px;
    }

    /* 进度线和序号，使用弹性居中布局让序号和进度线居中 */
    .step-progress {
        display: flex;
        justify-content: center;
    }

    /* 文本居中 */
    .step-text {
        width: 100%;
        margin-top: 10px;
        text-align: center;
    }

    /* 进度线 */
    .step-line {
        width: 100%;
        height: 9px;
        margin-top: 7px;
        background-color: #cccccc;
    }

    /* 数字序号 */
    .step-num {
        width: 18px;
        height: 18px;
        line-height: 17px;
        /* 圆角背景 */
        border-radius: 50%;
        color: #ffffff;
        font-size: 16px;
        /* 序号居中显示 */
        text-align: center;
        background-color: #cccccc;
        border: 2px solid #cccccc;
        /* 使用相对于父元素定位，强行回到原来的位置 */
        position: absolute;
    }

    /* 使用渐变背景处理两边 */
    .step-progress.right div{
        background: linear-gradient(to right, #cccccc 0%, #cccccc 50%, transparent 51%, transparent 100%);
    }

    .step-progress.left div{
        background: linear-gradient(to left, #cccccc 0%, #cccccc 50%, transparent 51%, transparent 100%);
    }

    /* 完成效果 */
    .step-progress.done div{
        background: #4395ff;
    }
    .step-progress.done span{
        background-color: #4395ff;
        border: 2px solid #4395ff;
    }

    /* 完成效果左右两边特制 */
    .step-progress.right.done div{
        background: linear-gradient(to right, #4395ff 0%, #4395ff 50%, transparent 51%, transparent 100%);
    }

    .step-progress.left.done div{
        background: linear-gradient(to left, #4395ff 0%, #4395ff 50%, transparent 51%, transparent 100%);
    }
</style>

<div class="panel panel-default panel-intro">
    <input type="hidden" name="ids" id="ids" value="{$ids}"/>
    <div class="div-flex">
        <i class="fa fa-address-card i-color"></i>
        <div style="line-height: 35px"><b>{$row.name}</b></div>
        <div class="btn-list">
            <a class="btn btn-success btn-status" data-id="{$row.id}">推进商机</a>
            <a class="btn btn-success btn-edit" data-id="{$row.id}">编辑</a>
            <a class="btn btn-success btn-transfer" data-id="{$row.id}"
                data-title="转移商机" >转移</a>
            <a class="btn btn-danger btn-danger btn-del"  id="dels">删除</a>

        </div>
    </div>

    <div class="div-flex" style="margin-top: 20px">


        <div class="form-group">
            <span class="text-muted">客户名称</span>
            <p>{if condition="isset($row.customer.name)"}{$row.customer.name}{/if}</p>
        </div>
        <div class="form-group">
            <span class="text-muted">商机金额（元）</span>
            <p>{$row.money}</p>
        </div>
        <div class="form-group">
            <span class="text-muted">预计成交时间</span>
            <p>{$row.expect_time}</p>
        </div>
        <div class="form-group">
            <span class="text-muted">下次联系时间</span>
            <p>{$row.next_time}</p>
        </div>
        <div class="form-group">
            <span class="text-muted">负责人</span>
            <p>{if condition="isset($row.owner_staff.name)"}{$row.owner_staff.name}{/if}</p>
        </div>
    </div>
    <div class="div-flex" style="margin-bottom:30px;">
        <!-- 步骤进度条 -->
        <div class="steps">
            <!-- 第一部分 -->
            <div class="step">
                <div class="step-progress left {if condition='$row.type egt 0'} done {/if}">
                    <div class="step-line"></div>
                    <span class="step-num">1</span>
                </div>
                <div class="step-text">
                    <span>初期沟通</span>
                </div>
            </div>
            <!-- 第二部分 -->
            <div class="step">
                <div class="step-progress {if condition='$row.type egt 1'} done {/if}">
                    <div class="step-line"></div>
                    <span class="step-num">2</span>
                </div>
                <div class="step-text">
                    <span>立项跟踪</span>
                </div>
            </div>
            <!-- 第三部分 -->
            <div class="step">
                <div class="step-progress {if condition='$row.type egt 2'} done {/if}">
                    <div class="step-line"></div>
                    <span class="step-num">3</span>
                </div>
                <div class="step-text">
                    <span>方案/报价</span>
                </div>
            </div>
            <!-- 第四部分 -->
            <div class="step">
                <div class="step-progress {if condition='$row.type egt 3'} done {/if}">
                    <div class="step-line"></div>
                    <span class="step-num">4</span>
                </div>
                <div class="step-text">
                    <span>谈判审核</span>
                </div>
            </div>

            <div class="step">
                <div class="step-progress right {if condition='$row.type egt 4'} done {/if}">
                    <div class="step-line"></div>
                    <span class="step-num">5</span>
                </div>
                <div class="step-text">
                    <span>{if condition='$row.type eq 5'} 输单 {elseif condition='$row.type eq 6'} 无效 {else\} 赢单 {/if}</span>
                </div>
            </div>


        </div>

    </div>
    <div>

        <ul class="nav nav-tabs" role="tablist">
            <li role="presentation" class="active"><a href="#record" aria-controls="home" role="tab" data-toggle="tab">跟进记录</a></li>
            <li role="presentation"><a href="#basic" aria-controls="profile" role="tab" data-toggle="tab">基本信息</a></li>
            <li role="presentation"><a href="#third" aria-controls="profile" role="tab" data-toggle="tab">合同信息</a></li>
            <li role="presentation"><a href="#four" aria-controls="profile" role="tab" data-toggle="tab">推进历史</a></li>

        </ul>

        <div class="tab-content" style="overflow-y: scroll;height:565px">
            <div role="tabpanel" class="tab-pane fade active in panel-body" id="record">

                <div id="toolbar_records" class="toolbar">
                    {:build_toolbar('refresh')}
                    <a class="btn btn-success btn-dialog {:$auth->check('qingdong/contract/record/add')?'':'hide'}"
                       data-url="qingdong/customer/business/record?ids={$row.id}" data-title="新建跟进" data-refresh="true" data-area=["90%","90%"]>新建跟进</a>
                </div>
                <table id="records" class="table table-striped table-bordered table-hover table-nowrap"
                       width="100%">
                </table>
            </div>
            <div role="tabpanel" class="tab-pane fade panel-body form-horizontal" id="basic">


                <div class="form-group">
                    <div class="col-xs-6 col-sm-5">
                        <div class="form-group col-sm-10">
                            <span class="text-muted">客户名称:</span>
                            <span class="text-black">{if condition="isset($row.customer.name)"}{$row.customer.name}{/if}</span>
                        </div>
                    </div>

                    <div class="col-xs-6 col-sm-5">
                        <div class="form-group col-sm-10">
                            <span class="text-muted">销售负责人:</span>
                            <span class="text-black">{if condition="isset($row.owner_staff.name)"}{$row.owner_staff.name}{/if}</span>
                        </div>
                    </div>

                    {include file="qingdong/common/form_detail" /}

                    <div class="col-xs-6 col-sm-5">
                        <div class="form-group col-sm-10">
                            <span class="text-muted">客户联系人:</span>
                            <span class="text-black">{if condition="isset($row.contacts.name)"}{$row.contacts.name}{/if}</span>
                        </div>
                    </div>

                    <div class="col-xs-6 col-sm-5">
                        <div class="form-group col-sm-10">
                            <span class="text-muted">客户联系方式:</span>
                            <span class="text-black">{$row.contacts_mobile|default='暂无'}</span>
                        </div>
                    </div>

                    <div class="col-xs-12 col-sm-10">
                        <div class="form-group col-sm-10">
                            <span class="text-muted">客户联系地址:</span>
                            <span class="text-black">{$row.contacts_address|default='暂无'}</span>
                        </div>
                    </div>

                </div>

            </div>


            <div role="tabpanel" class="tab-pane fade panel-body form-horizontal" id="third">

                <div id="toolbar_third" class="toolbar">
                    {:build_toolbar('refresh')}
                    <a class="btn btn-success btn-dialog {:$auth->check('qingdong/customer/contract/add')?'':'hide'}"
                       data-url="qingdong/customer/contract/add?business_id={$row.id}&customer_id={$row.customer_id}" data-title="新建合同" data-refresh="true" data-area=["90%","90%"] >新建合同</a>
                </div>
                <table id="contract" class="table table-striped table-bordered table-hover table-nowrap"
                       width="100%">
                </table>
            </div>

            <div role="tabpanel" class="tab-pane fade panel-body form-horizontal" id="four">

                <div id="toolbar_four" class="toolbar">
                    {:build_toolbar('refresh')}
                </div>
                <table id="history" class="table table-striped table-bordered table-hover table-nowrap"
                       width="100%">
                </table>
            </div>



        </div>

    </div>
</div>