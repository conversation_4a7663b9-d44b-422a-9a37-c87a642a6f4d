
<form id="add-form" class="form-horizontal form-ajax" role="form" data-toggle="validator" method="POST" action="">
    <div class="form-group" >
        <label class="control-label col-xs-3 col-sm-3">归属人:</label>
        <div class="col-xs-8 col-sm-8">
            <select name="staff_id" class="form-control selectpicker" 	data-live-search="true">
                {foreach name="staffs" item="val"}
                <option value="{$val.id}">{$val.name}</option>
                {/foreach}
            </select>
        </div>
    </div>
    <div class="form-group" style="margin-top:20px;margin-bottom:20px;">
        <label class="control-label col-xs-3 col-sm-3">文件上传:</label>
        <div class="col-xs-8 col-sm-8">
            <div class="input-group">
                <input type="text" name="file" id="file" class="form-control" value=""/>
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="plupload-local" class="btn btn-primary faupload" data-input-id="file" data-mimetype="xlsx,xls" data-multiple="false" data-url="{:url('ajax/upload')}"><i class="fa fa-upload"></i>&nbsp;&nbsp;选择文件</button></span>
                </div>
            </div>
        </div>
    </div>
    <div class="form-group">
        <label  class="control-label col-xs-3 col-sm-3">模板下载:</label>
        <div class="col-xs-8 col-sm-8">
            <a href="{:url('/qingdong/customer/leads/template')}" download="导入线索模板.xls" ><button type="button"  class="btn btn-danger" ><i class="fa fa-upload"></i> 模板下载</button></a>
        </div>
    </div>
    <div class="form-group  layer-footer">
        <div class="col-xs-2"></div>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>