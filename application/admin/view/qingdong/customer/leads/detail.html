<link href="__CDN__/assets/addons/qingdong/css/common.css?v={$Think.config.site.version}" rel="stylesheet">

<style type="text/css">
	.btn-list {
		position: fixed;
		right: 40px;
	}
	.div-flex {
		display: flex
	}
	.div-flex .form-group {
		margin-right: 50px;
		text-align: center;
		line-height: 25px;
		margin-bottom: 0;
	}
	.tab-pane {
		border: 1px solid #F6F6F6;
	}
	.i-color {
		font-size: 34px;margin-right: 10px;color: #86C3FF;
	}
	.thumbnail {
		display: flow-root;
		width: max-content;
	}
	.thumbnail img {
		width: 50px;
		float: left;
		padding: 5px;
	}
</style>

<div id="toolbar" class="toolbar hidden">
	<a href="javascript:;" onclick="javascript:location.reload()" class="btn btn-primary btn-refresh"><i class="fa fa-refresh"></i></a>
</div>
<div class="panel panel-default panel-intro">
	<input type="hidden" name="ids" id="ids" value="{$ids}"/>
	<div class="div-flex">
		<i class="fa fa-address-card i-color"></i>
		<div style="line-height: 35px"><b>{$row.name}</b></div>
		<div class="btn-list">
			<a class="btn btn-success btn-edit">编辑</a>
			<a class="btn btn-success btn-dialog" data-area='["400px","300px"]' data-shade="0.3"
			   data-url="qingdong/customer/leads/transfer?ids={$row.id}" data-title="转移线索" >转移</a>
			<a class="btn btn-success btn-convert-customer">转为客户</a>
			<a class="btn btn-success btn-convert-leadpool">放入线索池</a>
			<a class="btn btn-danger btn-danger btn-del"  id="dels">删除</a>

		</div>
	</div>

	<div style="margin-top: 10px">


	</div>
	<div class="div-flex" style="margin-top: 20px;margin-bottom: 20px">


		<div class="form-group">
			<span class="text-muted">线索来源</span>
			<p>{$row.source}</p>
		</div>

		<div class="form-group">
			<span class="text-muted">手机号</span>
			<p>{$row.mobile}</p>
		</div>

		<div class="form-group">
			<span class="text-muted">下次跟进时间</span>
			<p>{$row.next_time}</p>
		</div>
		<div class="form-group">
			<span class="text-muted">更新时间</span>
			<p>{$row.updatetime}</p>
		</div>
		<div class="form-group">
			<span class="text-muted">负责人</span>
			<p>{if condition="isset($row.owner_staff.name)"}{$row.owner_staff.name}{/if}</p>
		</div>
		<div class="form-group">
			<span class="text-muted">创建人</span>
			<p>{if condition="isset($row.create_staff.name)"}{$row.create_staff.name}{/if}</p>
		</div>
	</div>
	<div>

		<ul class="nav nav-tabs" role="tablist">
			<li role="presentation" class="active"><a href="#record" aria-controls="home" role="tab" data-toggle="tab">跟进记录</a></li>
			<li role="presentation"><a href="#basic" aria-controls="profile" role="tab" data-toggle="tab">基本信息</a></li>
<!--			<li role="presentation"><a href="#settings3" aria-controls="settings" role="tab" data-toggle="tab">附件</a></li>-->
			<li role="presentation"><a href="#settings4" aria-controls="settings" role="tab" data-toggle="tab">操作记录</a></li>
		</ul>

		<div class="tab-content" style="overflow-y: scroll;height:565px">
			<div role="tabpanel" class="tab-pane fade active in panel-body" id="record">
				<a class="btn btn-success btn-dialog" data-refresh="true"  data-url="qingdong/leads/record/add?ids={$row.id}" data-title="新建跟进" >新建跟进</a>

				<table id="records" class="table table-striped table-bordered table-hover table-nowrap"
					   width="100%">
				</table>

			</div>
			<div role="tabpanel" class="tab-pane fade panel-body form-horizontal" id="basic">
				<div class="form-group">
					<div class="alert alert-warning-light no-margin">
						基础信息
					</div>
				</div>
				<div class="form-group">

					{include file="qingdong/common/form_detail" /}

				</div>
			</div>
			<div role="tabpanel" class="tab-pane fade panel-body" id="settings3">
				<table id="files" class="table table-striped table-bordered table-hover table-nowrap"
					   width="100%">
				</table>
			</div>
			<div role="tabpanel" class="tab-pane fade panel-body" id="settings4">
				<table id="operation_log" class="table table-striped table-bordered table-hover table-nowrap"
					   width="100%">
				</table>


			</div>
		</div>

	</div>
</div>