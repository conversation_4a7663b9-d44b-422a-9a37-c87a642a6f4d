<link href="__CDN__/assets/addons/qingdong/css/index.css?v={$Think.config.site.version}" rel="stylesheet">

<div class="panel panel-default panel-intro">

    <div class="panel-heading">
        <ul class="nav nav-tabs">
            <li class="active"><a href="#first" data-toggle="tab">全部</a></li>
            <li><a href="#second" data-toggle="tab">我负责的</a></li>
            <li><a href="#third" data-toggle="tab">下属负责的</a></li>
            <li><a href="#four" data-toggle="tab">今日待跟进</a></li>
            <li><a href="#five" data-toggle="tab">今日已跟进</a></li>
            <li><a href="#six" data-toggle="tab">从未跟进的</a></li>
        </ul>
    </div>
    {:build_heading()}
    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane  fade active in" id="first">
                <div class="widget-body no-padding">
                    <div id="toolbar" class="toolbar">
                        {:build_toolbar('refresh,add')}
                        <a class="btn btn-warning btn-change btn-imports {:$auth->check('qingdong/customer/leads/import')?'':'hide'}"  data-params="action=start"  href="javascript:;"><i class="fa fa-upload dz-message"></i> 导入</a>
                        <a class="btn btn-success btn-export {:$auth->check('qingdong/customer/leads/export')?'':'hide'}" data-multiple="false" initialized="true" href="javascript:;"><i class="fa fa-download dz-message"></i> 导出</a>

                    </div>
                    <table id="table" class="table table-striped table-bordered table-hover table-nowrap"
                           width="100%">
                    </table>
                </div>
            </div>
            <div class="tab-pane fade" id="second">
                <div class="widget-body no-padding">
                    <div id="toolbar1" class="toolbar">
                        {:build_toolbar('refresh,add')}
                        <a class="btn btn-warning btn-change btn-imports {:$auth->check('qingdong/customer/leads/import')?'':'hide'}"  data-params="action=start"  href="javascript:;"><i class="fa fa-upload dz-message"></i> 导入</a>
                        <a class="btn btn-success btn-export {:$auth->check('qingdong/customer/leads/export')?'':'hide'}" data-multiple="false" initialized="true" href="javascript:;"><i class="fa fa-download dz-message"></i> 导出</a>

                    </div>
                    <table id="table1" class="table table-striped table-bordered table-hover table-nowrap"
                           width="100%">
                    </table>
                </div>
            </div>
            <div class="tab-pane fade" id="third">
                <div class="widget-body no-padding">
                    <div id="toolbar2" class="toolbar">
                        {:build_toolbar('refresh,add')}
                        <a class="btn btn-warning btn-change btn-imports {:$auth->check('qingdong/customer/leads/import')?'':'hide'}"  data-params="action=start"  href="javascript:;"><i class="fa fa-upload dz-message"></i> 导入</a>
                        <a class="btn btn-success btn-export {:$auth->check('qingdong/customer/leads/export')?'':'hide'}" data-multiple="false" initialized="true" href="javascript:;"><i class="fa fa-download dz-message"></i> 导出</a>

                    </div>
                    <table id="table2" class="table table-striped table-bordered table-hover table-nowrap"
                           width="100%">
                    </table>
                </div>
            </div>
            <div class="tab-pane fade" id="four">
                <div class="widget-body no-padding">
                    <div id="toolbar3" class="toolbar">
                        {:build_toolbar('refresh,add')}
                        <a class="btn btn-warning btn-change btn-imports {:$auth->check('qingdong/customer/leads/import')?'':'hide'}"  data-params="action=start"  href="javascript:;"><i class="fa fa-upload dz-message"></i> 导入</a>
                        <a class="btn btn-success btn-export {:$auth->check('qingdong/customer/leads/export')?'':'hide'}" data-multiple="false" initialized="true" href="javascript:;"><i class="fa fa-download dz-message"></i> 导出</a>

                    </div>
                    <table id="table3" class="table table-striped table-bordered table-hover table-nowrap"
                           width="100%">
                    </table>
                </div>
            </div>
            <div class="tab-pane fade" id="five">
                <div class="widget-body no-padding">
                    <div id="toolbar4" class="toolbar">
                        {:build_toolbar('refresh,add')}
                        <a class="btn btn-warning btn-change btn-imports {:$auth->check('qingdong/customer/leads/import')?'':'hide'}"  data-params="action=start"  href="javascript:;"><i class="fa fa-upload dz-message"></i> 导入</a>
                        <a class="btn btn-success btn-export {:$auth->check('qingdong/customer/leads/export')?'':'hide'}" data-multiple="false" initialized="true" href="javascript:;"><i class="fa fa-download dz-message"></i> 导出</a>

                    </div>
                    <table id="table4" class="table table-striped table-bordered table-hover table-nowrap"
                           width="100%">
                    </table>
                </div>
            </div>
            <div class="tab-pane fade" id="six">
                <div class="widget-body no-padding">
                    <div id="toolbar5" class="toolbar">
                        {:build_toolbar('refresh,add')}
                        <a class="btn btn-warning btn-change btn-imports {:$auth->check('qingdong/customer/leads/import')?'':'hide'}"  data-params="action=start"  href="javascript:;"><i class="fa fa-upload dz-message"></i> 导入</a>
                        <a class="btn btn-success btn-export {:$auth->check('qingdong/customer/leads/export')?'':'hide'}" data-multiple="false" initialized="true" href="javascript:;"><i class="fa fa-download dz-message"></i> 导出</a>

                    </div>
                    <table id="table5" class="table table-striped table-bordered table-hover table-nowrap"
                           width="100%">
                    </table>
                </div>
            </div>

        </div>
    </div>
</div>
