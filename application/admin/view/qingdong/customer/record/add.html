
<form id="edit-form" class="form-horizontal col-sm-12" role="form" data-toggle="validator" method="POST" action="">
	{:token()}

	<div class="form-group">

			<label for="c-type" class="control-label col-xs-12 col-sm-2">跟进类型:</label>
			<div class="form-group col-sm-8">
				<select id="c-type" name="row[follow_type]" data-rule="required" class="form-control">
					{:build_field_option('跟进类型')}
				</select>
			</div>

	</div>
		<div class="form-group">
			<label for="c-next_time" class="control-label col-xs-12 col-sm-2">下次跟进时间:</label>
			<div class="form-group col-sm-8">
				<input id="c-next_time" data-rule="required" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择" name="row[next_time]" type="text">
			</div>

		</div>


		{eq name="relation_type" value="2"}
	<div class="form-group">
			<label class="control-label col-xs-12 col-sm-2">联系人:</label>
			<div class="form-group col-sm-8">
				{:build_select('row[relation_id]', $contacts, $ids, ['class'=>'form-control selectpicker', 'required'=>'required', 'data-live-search'=>'true'])}
			</div>
	</div>
		{else/}
		<div class="form-group">
			<label class="control-label col-xs-12 col-sm-2">关联客户:</label>
			<div class="form-group col-sm-8">

				{:build_select('row[relation_id]', $customer, $ids, ['class'=>'form-control selectpicker', 'required'=>'required', 'data-live-search'=>'true'])}
			</div>

		</div>
		{/eq}
	<div class="form-group">
		<label class="control-label col-xs-12 col-sm-2">客户状态:</label>
		<div class="form-group col-sm-8">

			<select name="row[follow]" class="form-control">
				<option value="">点击选择</option>
				{foreach name="follow" item="val"}
				<option value="{$val}">{$val}</option>
				{/foreach}
			</select>
		</div>
	</div>
	<div class="form-group">
		<label class="control-label col-xs-12 col-sm-2">提醒谁看:</label>
		<div class="form-group col-sm-8">

			{:build_select('row[reminds_id]', $staff, $staff_ids, ['class'=>'form-control selectpicker', 'multiple'=>'', 'data-rule'=>'', 'data-live-search'=>'true'])}
		</div>

	</div>

		<input type="hidden" name="row[relation_type]" value="{$relation_type}">
	<div class="form-group">
		<label class="control-label col-xs-12 col-sm-2">上传附件:</label>
			<div class="form-group col-sm-8">

				<div class="input-group">
					<input id="c-image" class="form-control" size="35" name="row[files]" type="text" value="">
					<div class="input-group-addon no-border no-padding">
							<span><button type="button" id="faupload-image" class="btn btn-danger faupload"
										  data-resize-quality="0.8"
										  data-resize-width="1024"
										  data-resize-height="768"
										  data-input-id="c-image"
										  data-url="qingdong/base/upload"
										  data-multiple="true" data-preview-id="p-image"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>

					</div>
					<span class="msg-box n-right"></span>
				</div>
				<ul class="row list-inline faupload-preview" id="p-image"></ul>
			</div>

	</div>
	<div class="form-group">
		<label for="c-content" class="control-label col-xs-12 col-sm-2">跟进内容:</label>
			<div class="form-group col-sm-8">

				<textarea id="c-content" class="form-control"
						  data-rule="required" rows="5" name="row[content]"
						  placeholder="勤跟进，多签单"></textarea>
			</div>


	</div>


	<div class="form-group layer-footer">
		<label class="control-label col-xs-12 col-sm-2"></label>
		<div class="col-xs-12 col-sm-8">
			<button type="submit" class="btn btn-success btn-embossed">{:__('OK')}</button>
			<button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
		</div>
	</div>
</form>
