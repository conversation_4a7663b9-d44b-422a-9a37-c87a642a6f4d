
<form id="add-form" class="form-horizontal col-sm-12" role="form" data-toggle="validator" method="POST" action="">
    {:token()}
    <div class="form-group">
        <div class="col-xs-12 col-sm-6">
            <div class="form-group col-sm-10">
                <label>跟进人:</label>
                {if condition="isset($row.createname)"}{$row.createname}{/if}
            </div>
        </div>
        <div class="col-xs-12 col-sm-6">
            <div class="form-group col-sm-10">
                <label>跟进时间:</label>
                {$row.follow_time}
            </div>
        </div>
        <div class="col-xs-12 col-sm-6">
            <div class="form-group col-sm-10">
                <label>跟进类型:</label>
                {$row.follow_type}
            </div>
        </div>
        <div class="col-xs-12 col-sm-6">
            <div class="form-group col-sm-10">
                <label>跟进状态:</label>
                {$row.follow}
            </div>
        </div>
        <div class="col-xs-12 col-sm-6">
            <div class="form-group col-sm-10">
                <label>下次跟进时间:</label>
                {$row.next_time}
            </div>
        </div>
        <div class="col-xs-12 col-sm-12">
            <div class="form-group col-sm-12">
                <label>跟进内容:</label>
                {$row.content}
            </div>
        </div>
        <div class="col-xs-12 col-sm-12">
            <div class="form-group col-sm-12">
                <label>附件:</label>
                {foreach name="$row.file" item="f"}
                <a href="{$f.file_path}" target="_blank" style="margin-right:10px;">
                    {if condition="($f.types eq 'image/gif') or ($f.types eq 'image/jpeg') or ($f.types eq 'image/png') or ($f.types eq 'image/jpg') or ($f.types eq 'image/bmp')"}
                    <img data-tips-image src="{$f.file_path}" alt="{$f.name}" width="100px" height="100px">
                    {else\}
                    {$f.name}
                    {/if}
                </a>
                {/foreach}
            </div>
        </div>

    </div>

    <div  class="tab-pane" >

        <table id="comment" class="table table-striped table-bordered table-hover table-nowrap"
               width="100%">
        </table>
    </div>

</form>
