
<style type="text/css">
	.stafflist{
		width: 60px;text-align: center;float: left;
	}
	.stafflist img{
		width: 40px;
		height: 40px;
		margin: 10px;
		border-radius:50%;
	}
	.level{
		width: 100px;text-align: center;float: left;padding-top:18px;
		margin-left: 30px;
	}
	.level span:first-child{
		line-height: 2em
	}
</style>
<form id="add-form" class="form-horizontal col-sm-12" role="form" data-toggle="validator" method="POST" action="">
	{:token()}

	<div class="form-group">
		<div class="col-xs-12 col-sm-6">
			<div class="form-group col-sm-10">
				<label >关联客户:</label>
				<input type="hidden" name="row[customer_id]" data-rule="required" value="{$customer_id}" >
				<div class="form-control">
					<a href="javascript:void(0)" class="select-customer" data-name="row[customer_id]"
					   data-url="qingdong/customer/customer/index?isselect=1"
					>{if condition="$customer"}{$customer.name}{else/}【请点击选择】{/if}</a>
				</div>
			</div>
		</div>

		<div class="col-xs-12 col-sm-6">
			<div class="form-group col-sm-10">
				<label for="c-next_time">消费日期:</label>
				<input id="c-next_time" data-rule="required" class="form-control datetimepicker" data-date-format="YYYY-MM-DD" placeholder="请选择消费日期" name="row[consume_time]" type="text">
			</div>
		</div>
		<div class="col-xs-12 col-sm-6">
			<div class="form-group col-sm-10">
				<label for="c-type">消费方式:</label>
				<select id="c-type" name="row[consume_type]" data-rule="required" class="form-control">
					{:build_field_option('消费方式','','other')}
				</select>
			</div>
		</div>
		<div class="col-xs-12 col-sm-6">
			<div class="form-group col-sm-10">
				<label for="c-money">消费金额:</label>
				<input id="c-money" data-rule="required" class="form-control"  placeholder="请选择消费金额" name="row[money]" type="number">
			</div>
		</div>
		<div class="col-xs-12 col-sm-6">
			<div class="form-group col-sm-10">
				<label for="c-remark">备注:</label>
				<textarea id="c-remark" class="form-control" rows="3" name="row[remark]" placeholder="请输入备注"></textarea>
			</div>
		</div>
		<div class="col-xs-12 col-sm-10">
			<div class="form-group col-sm-10">
				<label for="c-file_ids">上传附件:</label>
				<div class="input-group">
					<input id="c-file_ids" class="form-control" placeholder="请上传附件" size="35" name="row[file_ids]" type="text" value="">
					<div class="input-group-addon no-border no-padding">
							<span><button type="button" id="faupload-image" class="btn btn-danger faupload"
										  data-input-id="c-file_ids"
										  data-url="qingdong/base/upload"
										  data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp"
										  data-multiple="true" data-preview-id="p-image"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>

					</div>
					<span class="msg-box n-right"></span>
				</div>
				<ul class="row list-inline faupload-preview" id="p-image"></ul>
			</div>
		</div>

		{if condition="$flow.status == 1"}

		<div class="col-xs-12 col-sm-12">
			<div class="form-group col-sm-10">

				<blockquote style="padding: 2px 10px;">
					<p>审核信息 <span class="label label-danger">固定审批流</span></p>
				</blockquote>

				{foreach name="$flow.stepList" item="val"}
				<div class="row">
					<div class="level">
						<span>第{$val.order_id}级</span><br>
						{if condition="$val.status == 3"}
						<span class="label label-info">发起人主管</span>
						{elseif condition="$val.status == 1"/}
						<span class="label label-info">{:count($val['stafflist'])}人或签</span>
						{else/}
						<span class="label label-info">{:count($val['stafflist'])}人并签</span>
						{/if}
					</div>
					{foreach name="$val.stafflist" item="ves"}
					<div class="stafflist">
						<img src="{$ves.img}" class="img-circle">
						{$ves.name}
					</div>
					{/foreach}
				</div>
				<hr>
				{/foreach}


			</div>
		</div>

		{else/}

		<div class="col-xs-12 col-sm-12">
			<div class="form-group col-sm-8">

				<blockquote style="padding: 2px 10px;">
					<p>审核信息 <span class="label label-danger">发起人自选</span></p>
				</blockquote>
				<div style="margin-left: 20px">
					<input data-source="qingdong/customer/contract/getstaff"
						   class="form-control selectpage" placeholder="请选择审批人" data-pagination="true" data-page-size="10" name="row[flow_staff_ids]" type="text" data-multiple="true">
				</div>

			</div>
		</div>
		{/if}
	</div>


	<div class="form-group layer-footer">
		<label class="control-label col-xs-12 col-sm-2"></label>
		<div class="col-xs-12 col-sm-8">
			<button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
			<button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
		</div>
	</div>
</form>
