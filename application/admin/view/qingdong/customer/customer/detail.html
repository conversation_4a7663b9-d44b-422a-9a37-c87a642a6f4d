<link href="__CDN__/assets/addons/qingdong/css/common.css?v={$Think.config.site.version}" rel="stylesheet">

<style type="text/css">
	.btn-list {
		position: fixed;
		right: 40px;
	}
	.div-flex {
		display: flex
	}
	.div-flex .form-group {
		margin-right: 50px;
		text-align: center;
		line-height: 25px;
		margin-bottom: 0;
	}
	.tab-pane {
		border: 1px solid #F6F6F6;
	}
	.i-color {
		font-size: 34px;margin-right: 10px;color: #86C3FF;
	}
	.thumbnail {
		display: flow-root;
		width: max-content;
	}
	.thumbnail img {
		width: 50px;
		float: left;
		padding: 5px;
	}

</style>
<div class="panel panel-default panel-intro">
	<input type="hidden" name="ids" id="ids" value="{$ids}"/>
	<div>
		<div class="div-flex">
			<i class="fa fa-address-card i-color"></i>
			<div style="line-height: 35px"><b>{$row.name}</b>
				{if condition="$row.lat"}
				<a href="https://apis.map.qq.com/uri/v1/marker?marker=coord:{$row.lat},{$row.lng};title:{$row.name};addr:{$row.address_detail}&referer=crm" target="_blank" ><i class="fa fa-map-marker text-blue" style="font-size: 18px;margin-right:4px"></i>{$row.address_detail}</a> {/if}</div>
			{if condition="$row.operation == 'update'"}
			<div class="btn-list">
				{if condition="$row.operation_team == 'update'"}
				<a class="btn btn-success btn-edit {:$auth->check('qingdong/customer/customer/edit')?'':'hide'}"  >编辑</a>
				{/if}
				{if condition="$row.operation_team == 'read'"}
				{neq name="row.owner_staff_id" value="0"}
				<a class="btn btn-success btn-change">转移</a>
				<a href="javascript:void(0)"
				   class="btn btn-success btn-seas {:$auth->check('qingdong/customer/customer/seas')?'':'hide'}" >放入公海</a>
				{else/}

				<a class="btn btn-success btn-change {:$auth->check('qingdong/customer/customer/change')?'':'hide'}">领取</a>
				{/neq}
				<a href="javascript:void(0)"
				   class="btn btn-success btn-word" data-id="{$ids}"
				    data-types="customer" data-title="导出word">导出word</a>
				<a href="javascript:void(0)"
				   class="btn btn-success btn-send-email {:$auth->check('qingdong/general/email/send_email')?'':'hide'}"
				   data-url="qingdong/general/email/send_email" data-types="customer" data-title="发送邮件">发送邮件</a>
				<a href="javascript:void(0)"
				   class="btn btn-success btn-send-sms {:$auth->check('qingdong/general/sms/send_sms')?'':'hide'}"
				   data-url="qingdong/general/sms/send_sms" data-types="customer" data-title="发送短信">发送短信</a>
				{neq name="row.owner_staff_id" value="0"}
				<a href="javascript:void(0)"
				   class="btn btn-danger btn-del {:$auth->check('qingdong/customer/customer/del')?'':'hide'}" >删除</a>
				{/neq}
				{/if}
			</div>
			{/if}
		</div>


		<div class="div-flex" style="margin-top: 20px;margin-bottom: 20px">
			<div class="form-group">
				<span class="text-muted">客户星级</span>
				<p>{$row.level}星</p>
			</div>

			<div class="form-group">
				<span class="text-muted">跟进状态</span>
				<p>{$row.follow}</p>
			</div>

			<div class="form-group">
				<span class="text-muted">创建人</span>
				<p>{if condition="isset($row.create_staff.name)"}{$row.create_staff.name}{/if}</p>
			</div>

			<div class="form-group">
				<span class="text-muted">负责人</span>
				<p>{if condition="isset($row.owner_staff.name)"}{$row.owner_staff.name}{/if}</p>
			</div>
			<div class="form-group">
				<span class="text-muted">更新时间</span>
				<p>{notempty name="row.updatetime"}
					{$row.updatetime|date="Y-m-d",###}
					{/notempty}</p>
			</div>
		</div>
	</div>

	<div>

		<ul class="nav nav-tabs" role="tablist">
			<li role="presentation" class="active"><a href="#record" aria-controls="home" role="tab" data-toggle="tab">跟进记录</a></li>
			<li role="presentation"><a href="#basic" aria-controls="profile" role="tab" data-toggle="tab">基本信息</a></li>
			<li role="presentation"><a href="#contacts" aria-controls="messages" role="tab" data-toggle="tab">联系人</a></li>
			<li role="presentation"><a href="#settings7" aria-controls="settings" role="tab" data-toggle="tab">商机</a></li>
			<li role="presentation"><a href="#settings1" aria-controls="settings" role="tab" data-toggle="tab">合同</a></li>
			<li role="presentation"><a href="#settings5" aria-controls="settings" role="tab" data-toggle="tab">费用</a></li>
			<li role="presentation"><a href="#settings2" aria-controls="settings" role="tab" data-toggle="tab">回款信息</a></li>
			{if condition="$row.operation == 'update' && $row.operation_team == 'read'"}
			<li role="presentation"><a href="#team" aria-controls="settings" role="tab"
									   data-toggle="tab">相关团队</a></li>
			{/if}
			<li role="presentation"><a href="#settings3" aria-controls="settings" role="tab" data-toggle="tab">附件</a></li>
			<li role="presentation"><a href="#settings6" aria-controls="settings" role="tab" data-toggle="tab">签到记录</a></li>
			<li role="presentation"><a href="#settings4" aria-controls="settings" role="tab" data-toggle="tab">操作记录</a></li>
		</ul>

		<div class="tab-content" style="overflow-y: scroll;height:565px">
			<div role="tabpanel" class="tab-pane fade active in panel-body" id="record">

				<div id="toolbar_records" class="toolbar">
					{:build_toolbar('refresh')}
					{if condition="$row.operation == 'update'"}
					<a class="btn btn-success btn-dialog {:$auth->check('qingdong/customer/record/add')?'':'hide'}"
					   data-url="qingdong/customer/record/add?ids={$row.id}" data-title="新建跟进" data-refresh="true" data-area=["90%","90%"]>新建跟进</a>
					{/if}
				</div>
				<table id="records" class="table table-striped table-bordered table-hover table-nowrap"
					   width="100%">
				</table>

			</div>
			<div role="tabpanel" class="tab-pane fade panel-body form-horizontal" id="basic">
				<div class="form-group">
					<div class="alert alert-warning-light no-margin">
						客户信息
					</div>
				</div>
				<div class="form-group">
					{include file="qingdong/common/form_detail" /}
					<div class="col-xs-6 col-sm-5">
						<div class="form-group col-sm-10">
							<span class="text-muted">所在地区:</span>
							{$row.address}
						</div>
					</div>
					<div class="col-xs-6 col-sm-5">
						<div class="form-group col-sm-10">
							<span class="text-muted">详细地址:</span>
							{$row.address_detail} {if condition="$row.lat"}
							<a href="https://apis.map.qq.com/uri/v1/marker?marker=coord:{$row.lat},{$row.lng};title:{$row.name};addr:{$row.address_detail}&referer=crm" target="_blank" ><i class="fa fa-map-marker text-blue" style="font-size: 18px;margin-right:4px"></i></a> {/if}
						</div>
					</div>
				</div>
			</div>
			<div role="tabpanel" class="tab-pane fade panel-body" id="contacts">


				<div id="toolbar_contacts" class="toolbar">
					{:build_toolbar('refresh')}
					{if condition="$row.operation == 'update'"}
					<a class="btn btn-success btn-dialog {:$auth->check('qingdong/customer/contacts/add')?'':'hide'}"
					   data-url="qingdong/customer/contacts/add?customer_id={$row.id}" data-title="新建联系人"  data-refresh="true" data-area=["90%","90%"]>新建联系人</a>
					{/if}
				</div>
				<table id="table-contacts" class="table table-striped table-bordered table-hover table-nowrap"
					   width="100%">
				</table>
			</div>
			<div role="tabpanel" class="tab-pane fade panel-body" id="settings7">

				<div id="toolbar_business" class="toolbar">
					{:build_toolbar('refresh')}
					{if condition="$row.operation == 'update'"}
					<a class="btn btn-success btn-dialog"
					   data-url="qingdong/customer/business/add?customer_id={$row.id}" data-title="新建商机" data-refresh="true" data-area=["90%","90%"]>新建商机</a>
					{/if}
				</div>
				<table id="business" class="table table-striped table-bordered table-hover table-nowrap"
					   width="100%">
				</table>
			</div>
			<div role="tabpanel" class="tab-pane fade panel-body" id="settings1">

				<div id="toolbar_contract" class="toolbar">
					{:build_toolbar('refresh')}
					{if condition="$row.operation == 'update'"}
					<a class="btn btn-success btn-dialog {:$auth->check('qingdong/customer/contract/add')?'':'hide'}"
					   data-url="qingdong/customer/contract/add?customer_id={$row.id}" data-title="新建合同" data-refresh="true" data-area=["90%","90%"] >新建合同</a>
					{/if}
				</div>
				<table id="contract" class="table table-striped table-bordered table-hover table-nowrap"
					   width="100%">
				</table>
			</div>
			<div role="tabpanel" class="tab-pane fade panel-body" id="settings5">

				<div id="toolbar_consume" class="toolbar">
					{:build_toolbar('refresh')}
					{if condition="$row.operation == 'update'"}
					<a class="btn btn-success btn-dialog {:$auth->check('qingdong/customer/consume/add')?'':'hide'}"
					   data-url="qingdong/customer/consume/add?customer_id={$row.id}" data-title="新建费用" data-refresh="true" data-area=["90%","90%"]>新建费用</a>
					{/if}
				</div>
				<table id="consume" class="table table-striped table-bordered table-hover table-nowrap"
					   width="100%">
				</table>
			</div>
			<div role="tabpanel" class="tab-pane fade panel-body" id="settings2">
				<div class="panel panel-warning">
					<div class="panel-heading">回款计划</div>
					<div class="panel-body">

						<div id="toolbar_receivablesplan" class="toolbar">
							{:build_toolbar('refresh')}
							{if condition="$row.operation == 'update'"}
							<a class="btn btn-success btn-dialog {:$auth->check('qingdong/customer/receivablesplan/add')?'':'hide'}"
							   data-url="qingdong/customer/receivablesplan/add?customer_id={$row.id}" data-title="新建回款计划" data-refresh="true"  >新建回款计划</a>
							{/if}
						</div>
						<table id="plan" class="table table-striped table-bordered table-hover table-nowrap"
							   width="100%">
						</table>
					</div>
				</div>

				<div class="panel panel-info">
					<div class="panel-heading">回款记录</div>
					<div class="panel-body">
						<div id="toolbar_receivables" class="toolbar">
							{:build_toolbar('refresh')}
							{if condition="$row.operation == 'update'"}
							<a class="btn btn-success btn-dialog {:$auth->check('qingdong/customer/receivables/add')?'':'hide'}"  data-refresh="true" data-url="qingdong/customer/receivables/add?customer_id={$row.id}" data-area=["90%","90%"] data-title="新建回款">新建回款</a>
							{/if}
						</div>

						<table id="receivables" class="table table-striped table-bordered table-hover table-nowrap"
							   width="100%">
						</table>
					</div>
				</div>

			</div>
			<div role="tabpanel" class="tab-pane fade panel-body" id="settings3">
				<div id="toolbar_files" class="toolbar">
					{:build_toolbar('refresh')}
				</div>
				<table id="files" class="table table-striped table-bordered table-hover table-nowrap"
					   width="100%">
				</table>
			</div>
			<div role="tabpanel" class="tab-pane fade panel-body" id="team">
				{if condition="$row.operation_team == 'read' && $row.operation == 'update'"}
				<div id="toolbar_team" class="toolbar">
					{:build_toolbar('refresh')}
					<a class="btn btn-info btn-dialog {:$auth->check('qingdong/customer/customer/addteam')?'':'hide'}"
					   data-url="qingdong/customer/customer/addteam?ids={$row.id}" data-title="添加团队成员" data-refresh="true" data-area='["500px","300px"]'  >添加团队成员</a>
				</div>
				<table id="table_team" class="table table-striped table-bordered table-hover table-nowrap"
					   width="100%">
				</table>
				{/if}
			</div>
			<div role="tabpanel" class="tab-pane fade panel-body" id="settings6">
				<div id="toolbar_sign" class="toolbar">
					{:build_toolbar('refresh')}
				</div>
				<table id="sign" class="table table-striped table-bordered table-hover table-nowrap"
					   width="100%">
				</table>
			</div>
			<div role="tabpanel" class="tab-pane fade panel-body" id="settings4">
				<div id="toolbar_log" class="toolbar">
					{:build_toolbar('refresh')}
				</div>
				<table id="operation_log" class="table table-striped table-bordered table-hover table-nowrap"
					   width="100%">
				</table>
			</div>
		</div>

	</div>
</div>