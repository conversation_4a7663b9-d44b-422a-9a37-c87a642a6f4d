<link href="__CDN__/assets/addons/qingdong/css/add.css?v={$Think.config.site.version}" rel="stylesheet">

<style type="text/css">
	.content {
		min-height: 100px;
	}
</style>
<form id="edit-form" class="form-horizontal col-sm-12" role="form" data-toggle="validator" method="POST" action="">
	{:token()}
	<input type="hidden" name="ids" value="{$ids}"/>
	<div class="col-xs-12 col-sm-6 form-group-selects">
		<div class="form-group col-sm-10">
			<label for="c-staff_id">变更负责人为:</label>
			<select name="row[owner_staff_id]" id="c-staff_id" class="form-control selectpicker" data-live-search="true">
				<option value="">请选择</option>
				{foreach name="staffs" item="val"}
				<option value="{$val.id}"  >{$val.name}</option>
				{/foreach}
			</select>
		</div>
	</div>
	<div class="form-group layer-footer">
		<label class="control-label col-xs-12 col-sm-2"></label>
		<div class="col-xs-12 col-sm-8">
			<button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
			<button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
		</div>
	</div>
</form>
