
<form id="add-form" class="form-horizontal form-ajax" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group" style="margin-top:20px;margin-bottom:20px;">
        <label class="control-label col-xs-2 col-sm-2">模板上传:</label>
        <div class="col-xs-8 col-sm-8">
            <div class="input-group">
                <input type="text" name="file" id="file" class="form-control" value=""/>
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="plupload-local" class="btn btn-primary faupload" data-input-id="file" data-mimetype="docx" data-multiple="false" data-url="{:url('ajax/upload')}"><i class="fa fa-upload"></i>&nbsp;&nbsp;选择文件</button></span>
                </div>
            </div>
        </div>
    </div>
    <div class="form-group" style="margin-top:20px;margin-bottom:20px;">
        <label class="control-label col-xs-2 col-sm-2"></label>
        <div class="col-xs-3 col-sm-3 checkbox">
            <label>
                <input type="checkbox" value="1" name="is_template" >另存为模板
            </label>
        </div>
        <div class="col-xs-4 col-sm-4" id="template_name" style="display: none" >
            <input type="text" name="template_name" class="form-control" size="10" placeholder="模板名称">
        </div>
    </div>
    <div class="form-group" style="margin-top:20px;margin-bottom:20px;">
        <label class="control-label col-xs-2 col-sm-2">选择已有模板:</label>
        <div class="col-xs-8 col-sm-8">
            <select name="template_id" class="form-control" >
                <option value="">请选择模板</option>
                {foreach name="templates" item="val"}
                <option value="{$val.id}" data-url="{$val.url}" >{$val.name}</option>
                {/foreach}
            </select>
        </div>
    </div>
    <div class="form-group">
        <label  class="control-label col-xs-2 col-sm-2">模板下载:</label>
        <div class="col-xs-8 col-sm-8">
            <a href="/assets/addons/qingdong/excel/customer.docx" download="customer.docx"><button type="button"  class="btn btn-danger" ><i class="fa fa-upload"></i> 示例模板下载</button></a>
        </div>
    </div>
    <div class="form-group">
        <label  class="control-label col-xs-2 col-sm-2">模板参数:</label>
        <div class="col-xs-8 col-sm-8 alert alert-success" >注：模板中对应位置添加所列的参数,不在模板参数中的参数无法识别</div>
        <div class="col-xs-8 col-sm-8 col-sm-offset-2">
            {foreach name="column" id="v"}
            <p>
               <span style="margin-right:10px;">{$v.name}:</span>
               <span>${{$v.key}}</span>
               <a href="javascript:void(0)" class="copy">点击复制</a>
            </p>
            {/foreach}
            <p><span style="margin-right:10px;">所在地区:</span><span>${address}</span>
                <a href="javascript:void(0)" class="copy">点击复制</a></p>
            <p><span style="margin-right:10px;">详细地址:</span><span>${address_detail}</span>
                <a href="javascript:void(0)" class="copy">点击复制</a></p>

        </div>
    </div>
    <div class="form-group  layer-footer">
        <div class="col-xs-2"></div>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-success btn-embossed disabled">导出</button>
        </div>
    </div>
</form>