<form id="edit-form" class="form-horizontal col-sm-12" role="form" data-toggle="validator" method="POST" action="">
	{:token()}

	<div class="form-group">
		<div class="alert alert-warning-light no-margin">
			跟进
		</div>
	</div>
	<div class="form-group">
		<div class="col-xs-12 col-sm-6">
			<div class="form-group col-sm-10">
				<label for="c-type">跟进类型:</label>
				<select id="c-type" name="row[follow_type]" data-rule="required" class="form-control">
					<option value="">点击选择</option>
					<option value="到访">到访</option>
					<option value="电话">电话</option>
				</select>
			</div>
		</div>
		<div class="col-xs-12 col-sm-6">
			<div class="form-group col-sm-10">
				<label for="c-next_time">下次跟进时间:</label>
				<input id="c-next_time" data-rule="required" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" placeholder="请选择" name="row[next_time]" type="text">
			</div>
		</div>
		<div class="col-xs-12 col-sm-10">
			<div class="form-group col-sm-10">
				<label for="c-image">上传附件:</label>
				<div class="input-group">
					<input id="c-image" class="form-control" size="35" name="row[files]" type="text" value="">
					<div class="input-group-addon no-border no-padding">
							<span><button type="button" id="faupload-image" class="btn btn-danger faupload"
										  data-input-id="c-image"
										  data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp"
										  data-multiple="false" data-preview-id="p-image"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
						<span><button type="button" id="fachoose-image" class="btn btn-primary fachoose"
									  data-input-id="c-image" data-mimetype="image/*" data-multiple="false"><i
								class="fa fa-list"></i> {:__('Choose')}</button></span>
					</div>
					<span class="msg-box n-right"></span>
				</div>
				<ul class="row list-inline faupload-preview" id="p-image"></ul>
			</div>
		</div>

		<div class="col-xs-12 col-sm-10">
			<div class="form-group col-sm-10">
				<label for="c-content">跟进内容:</label>
				<textarea id="c-content" class="form-control"
						  data-rule="required" rows="3" name="row[content]"
				placeholder="勤跟进，多签单"></textarea>
			</div>
		</div>
		<div class="col-xs-12 col-sm-10">
			<div class="form-group col-sm-10">
				<label>客户名称:</label>
				<select name="row[relation_id]" class="form-control">
					<option value="{$row.id}">{$row.name}</option>
				</select>
			</div>
		</div>
		<div class="col-xs-12 col-sm-10">
			<div class="form-group col-sm-10">
				<label>客户状态:</label>
				<select name="row[follow]" class="form-control">
					<option value="">点击选择</option>
					{foreach name="follow" item="val"}
					<option value="{$val}">{$val}</option>
					{/foreach}
				</select>
			</div>
		</div>
		<div class="col-xs-12 col-sm-10">
			<div class="form-group col-sm-10">
				<label>提醒谁看:</label>
				{:build_select('row[reminds_id]', $staff, null, ['class'=>'form-control selectpicker', 'multiple'=>'', 'data-rule'=>'required', 'data-live-search'=>'true'])}
			</div>
		</div>
		<input type="hidden" name="row[relation_type]" value="1">

	</div>


	<div class="form-group layer-footer">
		<label class="control-label col-xs-12 col-sm-2"></label>
		<div class="col-xs-12 col-sm-8">
			<button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
			<button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
		</div>
	</div>
</form>
