
<style type="text/css">
	.stafflist{
		width: 60px;text-align: center;float: left;
	}
	.stafflist img{
		width: 40px;
		height: 40px;
		margin: 10px;
		border-radius:50%;
	}
	.level{
		width: 100px;text-align: center;float: left;padding-top:18px;
		margin-left: 30px;
	}
	.level span:first-child{
		line-height: 2em
	}
</style>
<form id="edit-form" class="form-horizontal col-sm-12" role="form" data-toggle="validator" method="POST" action="">
	{:token()}


	<div class="form-group">
		<div class="col-xs-12 col-sm-6">
			<div class="form-group col-sm-10">
				<label >归属客户:</label>
				<input type="hidden" name="row[customer_id]" data-rule="required" value="{$customer_id}" >
				<div class="form-control">
					<a href="javascript:void(0)" class="select-customer" data-name="row[customer_id]"
					   data-url="qingdong/customer/customer/index?isselect=1"
					>{if condition="$customer"}{$customer.name}{else/}【请点击选择】{/if}</a>
				</div>
			</div>
		</div>
		<div class="col-xs-12 col-sm-6">
			<div class="form-group col-sm-10">
				<label >关联合同:</label>
				<input type="hidden" name="row[contract_id]" data-rule="required" value="{$contract_id}" >
				<div class="form-control">
					<a href="javascript:void(0)" class="select-contract" data-name="row[contract_id]"
					   data-url="qingdong/customer/contract/index?isselect=1"
					>{if condition="$contract"}{$contract.name}{else/}【请点击选择】{/if}</a>
				</div>
			</div>
		</div>

		<div class="col-xs-12 col-sm-6">
			<div class="form-group col-sm-10">
				<label >回款计划期数:</label>
				<select name="row[plan_id]" id="plan_id" class="form-control selectpicker">

				</select>
			</div>
		</div>

		{include file="qingdong/common/form_add" /}

		{if condition="$flow.status == 1"}

		<div class="col-xs-12 col-sm-12">
			<div class="form-group col-sm-10">

				<blockquote style="padding: 2px 10px;">
					<p>审核信息 <span class="label label-danger">固定审批流</span></p>
				</blockquote>

				{foreach name="$flow.stepList" item="val"}
				<div class="row">
					<div class="level">
						<span>第{$val.order_id}级</span><br>
						{if condition="$val.status == 3"}
						<span class="label label-info">发起人主管</span>
						{elseif condition="$val.status == 1"/}
						<span class="label label-info">{:count($val['stafflist'])}人或签</span>
						{else/}
						<span class="label label-info">{:count($val['stafflist'])}人并签</span>
						{/if}
					</div>
					{foreach name="$val.stafflist" item="ves"}
					<div class="stafflist">
						<img src="{$ves.img}" class="img-circle">
						{$ves.name}
					</div>
					{/foreach}
				</div>
				<hr>
				{/foreach}


			</div>
		</div>

		{else/}

		<div class="col-xs-12 col-sm-12">
			<div class="form-group col-sm-8">

				<blockquote style="padding: 2px 10px;">
					<p>审核信息 <span class="label label-danger">发起人自选</span></p>
				</blockquote>
				<div style="margin-left: 20px">
					<input data-source="qingdong/customer/receivables/getstaff"
						   class="form-control selectpage" placeholder="请选择审批人" data-pagination="true" data-page-size="10" name="row[flow_staff_ids]" type="text" data-multiple="true">
				</div>

			</div>
		</div>
		{/if}
	</div>


	<div class="form-group layer-footer">
		<label class="control-label col-xs-12 col-sm-2"></label>
		<div class="col-xs-12 col-sm-8">
			<button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
			<button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
		</div>
	</div>
</form>
