<link href="__CDN__/assets/addons/qingdong/css/common.css?v={$Think.config.site.version}" rel="stylesheet">

<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">
    <div class="col-xs-12 col-sm-6">
        <div class="form-group col-sm-10">
            <label >归属客户:</label>
            <input type="hidden" name="row[customer_id]" data-rule="required" value="{$row.customer_id}" >
            <div class="form-control">
                <a href="javascript:void(0)" class="select-customer" data-name="row[customer_id]"
                   data-url="qingdong/customer/customer/index?isselect=1"
                >{if condition="$row.customer"}{$row.customer.name}{else/}【请点击选择】{/if}</a>
            </div>
        </div>
    </div>
    <div class="col-xs-12 col-sm-6">
        <div class="form-group col-sm-10">
            <label >关联合同:</label>
            <input type="hidden" name="row[contract_id]" data-rule="required" value="{$row.contract_id}" >
            <div class="form-control">
                <a href="javascript:void(0)" class="select-contract" data-name="row[contract_id]"
                   data-url="qingdong/customer/contract/index?isselect=1"
                >{if condition="$row.contract"}{$row.contract.name}{else/}【请点击选择】{/if}</a>
            </div>
        </div>
    </div>

    <div class="col-xs-12 col-sm-6">
        <div class="form-group col-sm-10">
            <label>期数:</label>

            <input id="c-num" class="form-control" name="row[num]" type="number"  value="{$row.num|htmlentities}">
        </div>
    </div>

    <div class="col-xs-12 col-sm-6">
        <div class="form-group col-sm-10">
            <label>计划回款金额:</label>
            <input id="c-money" data-rule="required" class="form-control" step="0.01" name="row[money]" type="number"  value="{$row.money|htmlentities}">
        </div>
    </div>
    <div class="col-xs-12 col-sm-6">
        <div class="form-group col-sm-10">
            <label>计划回款日期:</label>
            <input id="c-return_date" class="form-control datetimepicker" data-date-format="YYYY-MM-DD" data-use-current="true" name="row[return_date]" type="text" value="{$row.return_date|htmlentities}">
        </div>
    </div>

    <div class="col-xs-12 col-sm-6">
        <div class="form-group col-sm-10">
            <label>计划回款方式:</label>
            <select name="row[return_type]" id="c-return_type" class="form-control">
                {:build_field_option('回款方式',$row['return_type'])}
            </select>
        </div>
    </div>
    <div class="col-xs-12 col-sm-6">
        <div class="form-group col-sm-10">
            <label>提前几天提醒:</label>
            <input id="c-remind" class="form-control" name="row[remind]" type="number"
                   value="{$row.remind|htmlentities}">
        </div>
    </div>
    <div class="col-xs-12 col-sm-6">
        <div class="form-group col-sm-10">
            <label>备注:</label>
            <textarea id="c-remarks" class="form-control" name="row[remarks]">{$row.remarks|htmlentities}</textarea>
        </div>
    </div>


    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
