<link href="__CDN__/assets/addons/qingdong/css/index.css?v={$Think.config.site.version}" rel="stylesheet">

<div class="panel panel-default panel-intro">
    <div class="panel-heading">
        <ul class="nav nav-tabs">
            <li class="active"><a href="#first" data-toggle="tab">全部</a></li>
            <li><a href="#second" data-toggle="tab">我创建的</a></li>
            <li><a href="#third" data-toggle="tab">下属创建的</a></li>
        </ul>
    </div>

    {:build_heading()}
    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade active in" id="first">
                <div class="widget-body no-padding">
                    <div id="toolbar" class="toolbar">
                        <a href="javascript:;" class="btn btn-primary btn-refresh" title="{:__('Refresh')}" ><i class="fa fa-refresh"></i> </a>
                        <a href="javascript:;" class="btn btn-success btn-add {:$auth->check('qingdong/customer/receivablesplan/add')?'':'hide'}" title="{:__('Add')}" ><i class="fa fa-plus"></i> {:__('Add')}</a>
                    </div>
                    <table id="table" class="table table-striped table-bordered table-hover table-nowrap"
                           data-operate-edit="{:$auth->check('qingdong/customer/receivablesplan/edit')}"
                           data-operate-del="{:$auth->check('qingdong/customer/receivablesplan/del')}"
                           width="100%">
                    </table>
                </div>
            </div>
            <div class="tab-pane fade" id="second">
                <div class="widget-body no-padding">
                    <div id="toolbar1" class="toolbar">
                        <a href="javascript:;" class="btn btn-primary btn-refresh" title="{:__('Refresh')}" ><i class="fa fa-refresh"></i> </a>
                        <a href="javascript:;" class="btn btn-success btn-add {:$auth->check('qingdong/customer/receivablesplan/add')?'':'hide'}" title="{:__('Add')}" ><i class="fa fa-plus"></i> {:__('Add')}</a>
                    </div>
                    <table id="table1" class="table table-striped table-bordered table-hover table-nowrap"
                           data-operate-edit="{:$auth->check('qingdong/customer/receivablesplan/edit')}"
                           data-operate-del="{:$auth->check('qingdong/customer/receivablesplan/del')}"
                           width="100%">
                    </table>
                </div>
            </div>
            <div class="tab-pane fade" id="third">
                <div class="widget-body no-padding">
                    <div id="toolbar2" class="toolbar">
                        <a href="javascript:;" class="btn btn-primary btn-refresh" title="{:__('Refresh')}" ><i class="fa fa-refresh"></i> </a>
                        <a href="javascript:;" class="btn btn-success btn-add {:$auth->check('qingdong/customer/receivablesplan/add')?'':'hide'}" title="{:__('Add')}" ><i class="fa fa-plus"></i> {:__('Add')}</a>
                    </div>
                    <table id="table2" class="table table-striped table-bordered table-hover table-nowrap"
                           data-operate-edit="{:$auth->check('qingdong/customer/receivablesplan/edit')}"
                           data-operate-del="{:$auth->check('qingdong/customer/receivablesplan/del')}"
                           width="100%">
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
