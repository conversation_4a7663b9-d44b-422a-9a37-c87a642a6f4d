<style>
    .form-group label {
        font-weight: normal !important;
    }
</style>
<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="col-xs-12 col-sm-6">
        <div class="form-group col-sm-10">
            <label >归属客户:</label>
            <input type="hidden" name="row[customer_id]" data-rule="required" value="{$customer_id}" >
            <div class="form-control">
                <a href="javascript:void(0)" class="select-customer" data-name="row[customer_id]"
                   data-url="qingdong/customer/customer/index?isselect=1"
                >{if condition="$customer"}{$customer.name}{else/}【请点击选择】{/if}</a>
            </div>
        </div>
    </div>
    <div class="col-xs-12 col-sm-6">
        <div class="form-group col-sm-10">
            <label >关联合同:</label>
            <input type="hidden" name="row[contract_id]" data-rule="required" value="{$contract_id}" >
            <div class="form-control">
                <a href="javascript:void(0)" class="select-contract" data-name="row[contract_id]"
                   data-url="qingdong/customer/contract/index?isselect=1"
                >{if condition="$contract"}{$contract.name}{else/}【请点击选择】{/if}</a>
            </div>
        </div>
    </div>

    <div class="col-xs-12 col-sm-6">
        <div class="form-group col-sm-10">
            <label>期数:</label>

            <input id="c-num" class="form-control" name="row[num]" placeholder="请输入期数" type="number" value="">
        </div>
    </div>

    <div class="col-xs-12 col-sm-6">
        <div class="form-group col-sm-10">
            <label>计划回款金额:</label>
            <input id="c-money" data-rule="required" class="form-control" step="0.01" name="row[money]" type="number" value="0.00">
        </div>
    </div>
    <div class="col-xs-12 col-sm-6">
        <div class="form-group col-sm-10">
            <label>计划回款日期:</label>
            <input id="c-return_date" class="form-control datetimepicker" data-date-format="YYYY-MM-DD" data-use-current="true" name="row[return_date]" type="text" value="{:date('Y-m-d')}">
        </div>
    </div>

    <div class="col-xs-12 col-sm-6">
        <div class="form-group col-sm-10">
            <label>计划回款方式:</label>
            <select name="row[return_type]" id="c-return_type" class="form-control">
                {:build_field_option('回款方式')}
            </select>
        </div>
    </div>
    <div class="col-xs-12 col-sm-6">
        <div class="form-group col-sm-10">
            <label>提前几天提醒:</label>
            <input id="c-remind" class="form-control" name="row[remind]" type="number" value="0">
        </div>
    </div>
    <div class="col-xs-12 col-sm-6">
        <div class="form-group col-sm-10">
            <label>备注:</label>
            <textarea id="c-remarks" class="form-control" placeholder="请输入备注" name="row[remarks]"></textarea>
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
