<style type="text/css">
	.content {
		min-height: 100px;
	}

</style>
<form id="edit-form" class="form-horizontal col-sm-12" role="form" data-toggle="validator" method="POST" action="">
	{:token()}


	<div class="row">

		<table id="table" class="table table-striped table-bordered table-hover table-nowrap" width="100%">
			<thead>
				<tr>

					<th style="text-align: center; vertical-align: middle; " data-field="name">
						<div class="th-inner ">字段名称</div>
						<div class="fht-cell"></div>
					</th>
					<th style="text-align: center; vertical-align: middle; " data-field="subname">
						<div class="th-inner ">主客户</div>
						<div class="fht-cell"></div>
					</th>
					<th style="text-align: center; vertical-align: middle; " data-field="contract_status">
						<div class="th-inner ">被合并客户</div>
						<div class="fht-cell"></div>
					</th>
					<th style="text-align: center; vertical-align: middle; " data-field="industry">
						<div class="th-inner ">内容合并</div>
						<div class="fht-cell"></div>
					</th>
					<th style="text-align: center; vertical-align: middle; " data-field="follow">
						<div class="th-inner ">合并后客户信息</div>
						<div class="fht-cell"></div>
					</th>

				</tr>
			</thead>
			<tbody data-listidx="0">
			{foreach name="form_data" item="val" $k="key"}
			{if condition="in_array($val['component'],['uploadImage','uploadFile'])"}
			{php}continue;{/php}
			{/if}
			<tr data-index="0" style="">
				<td style="text-align: center; vertical-align: middle; ">
					{$val.config.label}
				</td>
				<td style="text-align: center; vertical-align: middle; " class="zu_{$val.id}">
					{$val.value}
				</td>

				<td style="text-align: center; vertical-align: middle; " class="fu_{$val.id}">
					{$val.chilend.value}
				</td>
				<td style="text-align: center; vertical-align: middle;text-align-last: center; ">
					<select id="check_{$key}" data-name="{$val.id}"  class="form-control tongbu selectpicker" >
						<option value="1"  style="text-align: center;text-align-last: center;direction: rtl;">主客户</option>
						<option value="2"  style="text-align: center;text-align-last: center;direction: rtl;">被合并客户</option>
						{eq name="val.component" value="textarea"}
						<option value="3"  style="text-align: center;text-align-last: center;direction: rtl;">内容合并</option>
						{/eq}
					</select>

				</td>
				<td style="text-align: center; vertical-align: middle; ">
					{if condition="$val.config.is_delete == true || true"}
					{eq name="val.component" value="input"}
					<div class="col-xs-12 col-sm-12">
						<div class="form-group col-sm-12">
							<input id="c-{$val.id}" class="form-control" {if condition="$val.config.required == true"}data-rule="required"{/if} placeholder="{$val.config.placeholder}" name="row[{$val.id}]"
							type="text" value="{$val.value}">
						</div>
					</div>
					{/eq}
					{eq name="val.component" value="input-number"}
					<div class="col-xs-12 col-sm-12">
						<div class="form-group col-sm-12">
							<input id="c-{$val.id}" class="form-control"{if condition="$val.config.required == true"}data-rule="required"{/if} placeholder="{$val.config.placeholder}" name="row[{$val.id}]"
							type="number" value="{$val.value}">
						</div>
					</div>
					{/eq}
					{eq name="val.component" value="textarea"}
					<div class="col-xs-12 col-sm-12">
						<div class="form-group col-sm-12">
							<textarea id="c-{$val.id}" value="{$val.value}" class="form-control"{if condition="$val.config.required == true"}data-rule="required"{/if} rows="3" name="row[{$val.id}]"
							placeholder="{$val.config.placeholder}">{$val.value}</textarea>
						</div>
					</div>
					{/eq}

					{eq name="val.component" value="radio"}
					<div class="col-xs-12 col-sm-12">
						<div class="form-group col-sm-12">
							<div class="radio">
								{foreach name="val.config.content" item="vo"}
								<label><input name="row[{$val.id}]" type="radio" value="{$vo.value}" {eq name="$val.value??''" value="$vo['value']" }checked{/eq}/> {$vo.value}</label>
								{/foreach}
							</div>

						</div>
					</div>
					{/eq}

					{eq name="val.component" value="checkbox"}
					<div class="col-xs-12 col-sm-12">
						<div class="form-group col-sm-12">
							<div class="checkbox">
								{foreach name="val.config.content" item="vo"}
								<label class="checkbox-inline"><input name="row[{$val.id}][]" type="checkbox" value="{$vo.value}" {in name="$vo['value']" value="$val.value??[]" }checked{/in}/>
									{$vo.value}</label>
								{/foreach}
							</div>

						</div>
					</div>
					{/eq}
					{eq name="val.component" value="Cascader"}
					<div class="col-xs-12 col-sm-12">
						<div class="form-group col-sm-12">
							<div class='control-relative'>
								<input id="c-{$val.id}" {if condition="$val.config.required == true"}data-rule="required"{/if} class="form-control" data-toggle="city-picker" name="row[{$val.id}]" type="text"
								value="{:$val.value??''}" />
							</div>
						</div>
					</div>
					{/eq}

					{eq name="val.component" value="select"}
					<div class="col-xs-12 col-sm-12" >
						<div class="form-group col-sm-12" style="text-align: center; vertical-align: middle;text-align-last: center; ">
							<select id="c-{$val.id}" name="row[{$val.id}]" {if condition="$val.config.required == true"}data-rule="required"{/if} class="form-control selectpicker">
							<option value="" style="text-align: center;text-align-last: center;direction: rtl;">请选择</option>
							{foreach name="val.config.content" item="vo"}
							<option value="{$vo.label}" style="text-align: center;text-align-last: center;direction: rtl;" {eq name="$val['value']" value="$vo['label']" }selected{/eq}>{$vo.label}</option>
							{/foreach}

							</select>
						</div>
					</div>
					{/eq}

					{eq name="val.component" value="TimePicker"}
					<div class="col-xs-12 col-sm-12">
						<div class="form-group col-sm-12">
							<input id="c-{$val.id}" {if condition="$val.config.required == true"}data-rule="required"{/if} class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss"
							placeholder="{$val.config.placeholder}" name="row[{$val.id}]" type="text" value="{:$val.value??''}">
						</div>
					</div>
					{/eq}

					{eq name="val.component" value="DatePicker"}
					<div class="col-xs-12 col-sm-12">
						<div class="form-group col-sm-12">
							<input id="c-{$val.id}" {if condition="$val.config.required == true"}data-rule="required"{/if} class="form-control datetimepicker" data-date-format="YYYY-MM-DD" placeholder="{$val.config.placeholder}"
							name="row[{$val.id}]" type="text" value="{:$val.value??''}">
						</div>
					</div>
					{/eq}
					{eq name="val.component" value="Rate"}
					<div class="col-xs-12 col-sm-12">
						<div class="form-group col-sm-12" style="text-align: center; vertical-align: middle;text-align-last: center; ">
							<select id="c-{$val.id}" name="row[{$val.id}]" class="form-control selectpicker">
								{if condition="isset($val['value'])"}
								<option value="5" style="text-align: center;text-align-last: center;direction: rtl;" {eq name="$val['value']" value="5" }selected{/eq} >5</option>
								<option value="4" style="text-align: center;text-align-last: center;direction: rtl;" {eq name="$val['value']" value="4" }selected{/eq} >4</option>
								<option value="3" style="text-align: center;text-align-last: center;direction: rtl;" {eq name="$val['value']" value="3" }selected{/eq} >3</option>
								<option value="2" style="text-align: center;text-align-last: center;direction: rtl;" {eq name="$val['value']" value="2" }selected{/eq} >2</option>
								<option value="1" style="text-align: center;text-align-last: center;direction: rtl;" {eq name="$val['value']" value="1" }selected{/eq} >1</option>
								{else/}
								<option value="5" style="text-align: center;text-align-last: center;direction: rtl;">5</option>
								<option value="4" style="text-align: center;text-align-last: center;direction: rtl;">4</option>
								<option value="3" style="text-align: center;text-align-last: center;direction: rtl;">3</option>
								<option value="2" style="text-align: center;text-align-last: center;direction: rtl;">2</option>
								<option value="1" style="text-align: center;text-align-last: center;direction: rtl;">1</option>
								{/if}
							</select>
						</div>
					</div>
					{/eq}
				{/if}
				</td>

			</tr>
			{/foreach}
			</tbody>
		</table>


		<div class="form-group" style="padding-top:10px;">
			<label class="control-label col-xs-12 col-sm-2">合并后操作:</label>
			<div class="col-xs-12 col-sm-8">
				{:build_checkboxs('row[check]', $check);}
			</div>
		</div>
		<input type="hidden" value="{$ids}" name="row[id]">
		<input type="hidden" value="{$customer_ids}" name="row[customer_ids]">
	<div class="form-group layer-footer">
		<label class="control-label col-xs-12 col-sm-2"></label>
		<div class="col-xs-12 col-sm-8">
			<button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
			<button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
		</div>
	</div>
</form>
