
<style type="text/css">
    .stafflist{
        width: 60px;text-align: center;float: left;
    }
    .stafflist img{
        width: 40px;
        height: 40px;
        margin: 10px;
        border-radius:50%;
    }
    .level{
        width: 100px;text-align: center;float: left;padding-top:18px;
        margin-left: 30px;
    }
    .level span:first-child{
        line-height: 2em
    }
</style>
<form id="edit-form" class="form-horizontal col-sm-12" role="form" data-toggle="validator" method="POST" action="">
    {:token()}
    <div class="form-group">
        <!--合同相关 start-->
        {include file="qingdong/common/form_edit" /}
        <div class="col-xs-12 col-sm-6">
            <div class="form-group col-sm-10">
                <label for="project_staff">项目负责人:</label>
                <select id="project_staff" name="row[project_staff_id]" data-rule="required" class="form-control selectpicker" data-live-search="true">
                    <option value="">请选择</option>
                    {foreach name="staffs" item="val"}
                    <option value="{$val.id}" {eq name="$val.id" value="$row.project_staff_id"}selected{/eq}>{$val.name}</option>
                    {/foreach}
                </select>

            </div>
        </div>
        <div class="col-xs-12 col-sm-6">
            <div class="form-group col-sm-10">
                <label for="order_staff">销售负责人:</label>
                <select id="order_staff" name="row[order_staff_id]" data-rule="required" class="form-control selectpicker" data-live-search="true">
                    <option value="">请选择</option>
                    {foreach name="staffs" item="val"}
                    <option value="{$val.id}" {eq name="$val.id" value="$row.order_staff_id"}selected{/eq} >{$val.name}</option>
                    {/foreach}
                </select>

            </div>
        </div>
        <div class="clearfix"></div>
        <!--合同相关 end-->
        <!--客户相关 start-->
        <div class="col-xs-12 col-sm-6">
            <div class="form-group col-sm-10">
                <label >客户名称:</label>
                <input type="hidden" name="row[customer_id]" data-rule="required" value="{$row.customer_id}" >
                <div class="form-control">
                    <a href="javascript:void(0)" class="select-customer" data-name="row[customer_id]"
                       data-url="qingdong/customer/customer/index?isselect=1"  >
                        {if condition="$row.customer"}{$row.customer.name}{else/}【请点击选择】{/if}</a>
                </div>
            </div>
        </div>

        <div class="col-xs-12 col-sm-6">
            <div class="form-group col-sm-10">
                <label>客户联系人: </label>
                <input type="hidden" name="row[contacts_id]" data-rule="required"  value="{$row.contacts_id}" >
                <div class="form-control">
                    <a href="javascript:void(0)" class="select-contacts" data-name="row[contacts_id]"
                       data-url="qingdong/customer/contacts/index?isselect=1"  >
                        {if condition="$row.contacts"}{$row.contacts.name}{else/}【请点击选择】{/if}
                    </a>
                </div>

            </div>
        </div>
        <div class="col-xs-12 col-sm-6">
            <div class="form-group col-sm-10">
                <label>商机名称:</label>
                <input type="hidden" name="row[business_id]" value="{$row.business_id|default=''}" >
                <div class="form-control">
                    <a href="javascript:void(0)" class="select-business" data-name="row[business_id]"
                       data-url="qingdong/customer/business/index?isselect=1"  >
                        {if condition="$row.business"}{$row.business.name}{else/}【请点击选择】{/if}
                    </a>
                </div>
            </div>
        </div>
        <!--客户相关 end-->

        <div class="col-xs-12 col-sm-12">
            <div class="form-group col-sm-10">
                <label>款项类别:</label>
            </div>
        </div>
        <table class="table table-responsive fieldlist" data-name="row[product]" data-template="testtpl3" data-tag="tr">
            <tr>
                <td>款型类别</td>
                <td>付款百分比（%）</td>
                <td>分项金额（万元）</td>
                <td></td>
            </tr>
            <tr>
                <td colspan="5"><a href="javascript:;" class="btn btn-sm btn-success btn-append"><i class="fa fa-plus"></i> 追加</a></td>
            </tr>
            <textarea name="row[product]" class="form-control hide" cols="30" rows="5">{$row.product}</textarea>
        </table>

        <!--合同内容-->
        <div class="col-xs-12 col-sm-12">
            <div class="form-group col-sm-10">
                <label>合同内容:</label>
                <textarea name="row[content]" class="form-control" rows="5">{$row.content|default=''}</textarea>
            </div>
        </div>
        <div class="col-xs-12 col-sm-12">
            <div class="form-group col-sm-10">
                <label>备注:</label>
                <textarea name="row[remark]" class="form-control" rows="5">{$row.remark|default=''}</textarea>
            </div>
        </div>

        <div class="col-xs-12 col-sm-12">
            <div class="form-group col-sm-10">
                <label>附件:</label>
                <div class="input-group">
                    <input id="c-attachment" class="form-control" placeholder="上传合同附件（支持图片、文档、压缩包等）" size="35" name="row[attachment]" type="text" value="{$attachments|default=''}">
                    <div class="input-group-addon no-border no-padding">
                        <span><button type="button" class="btn btn-danger faupload"
                                      data-input-id="c-attachment"
                                      data-url="qingdong/base/upload"
                                      data-multiple="true"
                                      data-mimetype="jpg,png,bmp,jpeg,gif,webp,pdf,doc,docx,xls,xlsx,ppt,pptx,txt,zip,rar"
                                      data-preview-id="p-attachment"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    </div>
                    <span class="msg-box n-right"></span>
                </div>
                <ul class="row list-inline faupload-preview" id="p-attachment" data-template="contractAttachmentTpl"></ul>
            </div>
        </div>

    </div>


    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>

<script type="text/html" id="testtpl3">
    <tr class="form-inline">
        <td style="width: 120px">
            <input type="text" name="row[<%=name%>][<%=index%>][fund_type]"
                   class="form-control fund_type" value="<%=row['fund_type']%>" placeholder="输入款项类别"></td>
        <td style="width: 80px">
            <input type="number" name="row[<%=name%>][<%=index%>][percent]" style="width: 80px"
                   class="form-control percent" value="<%=row['percent']%>" placeholder="0"></td>
        <td style="width: 80px">
            <input type="number" name="row[<%=name%>][<%=index%>][total]" style="width: 80px"
                   class="form-control total" value="<%=row['total']%>" placeholder="0">
        </td>
        <td style="width: 120px"><span class="btn btn-sm btn-danger btn-remove"><i class="fa fa-times"></i></span> </td>
    </tr>
</script>

<!--附件预览模板-->
<script type="text/html" id="contractAttachmentTpl">
    <li class="col-xs-6 col-sm-4 col-md-3" style="margin-bottom: 15px;">
        <div class="attachment-item" style="position: relative; border: 1px solid #e5e5e5; border-radius: 6px; background: #fff; box-shadow: 0 2px 4px rgba(0,0,0,0.1); transition: all 0.3s ease; overflow: hidden;">
            <!-- 删除按钮 -->
            <a href="javascript:;" class="btn btn-danger btn-xs btn-trash" style="position: absolute; top: 8px; right: 8px; z-index: 10; border-radius: 50%; width: 24px; height: 24px; padding: 0; line-height: 22px; opacity: 0.8;">
                <i class="fa fa-times"></i>
            </a>

            <!-- 文件预览区域 -->
            <div class="file-preview" style="padding: 20px 15px 10px; text-align: center; min-height: 100px; display: flex; align-items: center; justify-content: center; background: #fafafa;">
                <% if(['jpg','jpeg','png','gif','bmp','webp'].indexOf(suffix.toLowerCase()) !== -1) { %>
                    <img src="<%=fullurl%>" style="max-width: 100%; max-height: 80px; border-radius: 4px; box-shadow: 0 1px 3px rgba(0,0,0,0.2);" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                    <div style="display: none; flex-direction: column; align-items: center;">
                        <i class="fa fa-file-image-o" style="font-size: 36px; color: #5cb85c; margin-bottom: 5px;"></i>
                        <span style="font-size: 10px; color: #999;">图片</span>
                    </div>
                <% } else { %>
                    <div style="display: flex; flex-direction: column; align-items: center;">
                        <% if(['pdf'].indexOf(suffix.toLowerCase()) !== -1) { %>
                            <i class="fa fa-file-pdf-o" style="font-size: 36px; color: #d9534f; margin-bottom: 5px;"></i>
                            <span style="font-size: 10px; color: #d9534f; font-weight: bold;">PDF</span>
                        <% } else if(['doc','docx'].indexOf(suffix.toLowerCase()) !== -1) { %>
                            <i class="fa fa-file-word-o" style="font-size: 36px; color: #337ab7; margin-bottom: 5px;"></i>
                            <span style="font-size: 10px; color: #337ab7; font-weight: bold;">WORD</span>
                        <% } else if(['xls','xlsx'].indexOf(suffix.toLowerCase()) !== -1) { %>
                            <i class="fa fa-file-excel-o" style="font-size: 36px; color: #5cb85c; margin-bottom: 5px;"></i>
                            <span style="font-size: 10px; color: #5cb85c; font-weight: bold;">EXCEL</span>
                        <% } else if(['ppt','pptx'].indexOf(suffix.toLowerCase()) !== -1) { %>
                            <i class="fa fa-file-powerpoint-o" style="font-size: 36px; color: #f0ad4e; margin-bottom: 5px;"></i>
                            <span style="font-size: 10px; color: #f0ad4e; font-weight: bold;">PPT</span>
                        <% } else if(['zip','rar','7z'].indexOf(suffix.toLowerCase()) !== -1) { %>
                            <i class="fa fa-file-archive-o" style="font-size: 36px; color: #5bc0de; margin-bottom: 5px;"></i>
                            <span style="font-size: 10px; color: #5bc0de; font-weight: bold;">压缩包</span>
                        <% } else if(['txt'].indexOf(suffix.toLowerCase()) !== -1) { %>
                            <i class="fa fa-file-text-o" style="font-size: 36px; color: #777; margin-bottom: 5px;"></i>
                            <span style="font-size: 10px; color: #777; font-weight: bold;">文本</span>
                        <% } else { %>
                            <i class="fa fa-file-o" style="font-size: 36px; color: #777; margin-bottom: 5px;"></i>
                            <span style="font-size: 10px; color: #777; font-weight: bold;">文件</span>
                        <% } %>
                    </div>
                <% } %>
            </div>

            <!-- 文件信息区域 -->
            <div class="file-info" style="padding: 10px 12px; border-top: 1px solid #f0f0f0; background: #fff;">
                <div style="font-size: 12px; color: #333; font-weight: 500; margin-bottom: 4px; line-height: 1.3; height: 32px; overflow: hidden; display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical;">
                    <% var filename = fullurl.split('/').pop(); %>
                    <%=filename%>
                </div>
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <span style="font-size: 10px; color: #999; text-transform: uppercase;"><%=suffix%></span>
                    <a href="<%=fullurl%>" target="_blank" class="btn btn-xs btn-primary" style="padding: 2px 8px; font-size: 10px; border-radius: 3px;" title="点击查看/下载">
                        <i class="fa fa-download"></i> 下载
                    </a>
                </div>
            </div>
        </div>
    </li>
</script>

<style>
.attachment-item:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
    transform: translateY(-2px);
}
.attachment-item:hover .btn-trash {
    opacity: 1 !important;
}
.attachment-item .btn-trash:hover {
    background-color: #c9302c !important;
    border-color: #ac2925 !important;
}
</style>
