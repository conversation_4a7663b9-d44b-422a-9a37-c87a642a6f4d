<style type="text/css">
	.stafflist{
		width: 60px;text-align: center;float: left;
	}
	.stafflist img{
		width: 40px;
		height: 40px;
		margin: 10px;
		border-radius:50%;
	}
	.level{
		width: 100px;text-align: center;float: left;padding-top:18px;
		margin-left: 30px;
	}
	.level span:first-child{
		line-height: 2em
	}
</style>
<form id="add-form" class="form-horizontal col-sm-12" role="form" data-toggle="validator" method="POST" action="">
	{:token()}

	<div class="form-group">

		<div class="col-xs-12 col-sm-6">
			<div class="form-group col-sm-10">
				<label >归属客户:</label>
				<input type="hidden" name="row[customer_id]" data-rule="required" value="" >
				<div class="form-control">
					<a href="javascript:void(0)" class="select-customer" data-name="row[customer_id]"
					   data-url="qingdong/customer/customer/index?isselect=1"  >【请点击选择】</a>
				</div>
			</div>
		</div>

		<div class="col-xs-12 col-sm-6">
			<div class="form-group col-sm-10">
				<label>客户签约人: </label>
				<input type="hidden" name="row[contacts_id]" data-rule="required"  value="" >
				<div class="form-control">
					<a href="javascript:void(0)" class="select-contacts" data-name="row[contacts_id]"
					   data-url="qingdong/customer/contacts/index?isselect=1"  >【请点击选择】</a>
				</div>

			</div>
		</div>
		<div class="col-xs-12 col-sm-6">
			<div class="form-group col-sm-10">
				<label>商机名称:</label>

				<input type="hidden" name="row[business_id]" value="" >
				<div class="form-control">
					<a href="javascript:void(0)" class="select-business" data-name="row[business_id]"
					   data-url="qingdong/customer/business/index?isselect=1"  >【请点击选择】</a>
				</div>
			</div>
		</div>

		<div class="col-xs-12 col-sm-6">
			<div class="form-group col-sm-10">
				<label for="order_staff">公司签约人:</label>
				<select id="order_staff" name="row[order_staff_id]" data-rule="required" class="form-control selectpicker" data-live-search="true">
					<option value="">请选择</option>
					{foreach name="staffs" item="val"}
					<option value="{$val.id}">{$val.name}</option>
					{/foreach}
				</select>

			</div>
		</div>

		{include file="qingdong/common/form_add" /}

		<div class="col-xs-12 col-sm-12">
			<div class="form-group col-sm-10">
				<label>产品列表:</label>
			</div>
		</div>
		<table class="table table-responsive fieldlist" data-name="row[product]" data-template="testtpl2" data-tag="tr">
			<tr>
				<td>商品名称</td>
				<td>零售价</td>
				<td>批发价</td>
				<td>产品</td>
				<td>规格</td>
				<td>数量</td>
				<td></td>
			</tr>
			<tr>
				<td colspan="5"><a href="javascript:;" class="btn btn-sm btn-success btn-append"><i class="fa fa-plus"></i> 追加</a></td>
			</tr>
			<textarea name="row[product]" class="form-control hide" cols="30" rows="5">[]</textarea>
		</table>

		<div class="col-xs-12 col-sm-12">
			<div class="form-group col-sm-10">
				<label>款项类别:</label>
			</div>
		</div>
		<table class="table table-responsive fieldlist" data-name="row[pay_step]" data-template="testtpl3" data-tag="tr">
			<tr>
				<td>款项类别</td>
				<td>付款百分比（%）</td>
				<td>分项金额（万元）</td>
				<td></td>
			</tr>
			<tr>
				<td colspan="4"><a href="javascript:;" class="btn btn-sm btn-success btn-append"><i class="fa fa-plus"></i> 追加</a></td>
			</tr>
			<textarea name="row[pay_step]" class="form-control hide" cols="30" rows="5">[]</textarea>
		</table>

		{if condition="$ratio neq null"}
		<div class="col-xs-12 col-sm-12">
			<div class="form-group col-sm-8">

				<blockquote style="padding: 2px 10px;">
					<div class="form-inline">
						<label for="ratio_id">业绩分成：</label>
						<select name="row[ratio_id]" id="ratio_id" class="form-control" >
							<option value="0">无</option>
							{foreach name="ratio" item="val"}
							<option value="{$val.id}" data-ratio='{$val.ratio|htmlentities}' >{$val.name}</option>
							{/foreach}
						</select>
					</div>
				</blockquote>

				<table class="table table_ratio" style="width: 300px;display: none">
					<thead>
					<tr>
						<th width="80">比例</th>
						<th>团队成员</th>
					</tr>
					</thead>
					<tbody>

					</tbody>
				</table>
			</div>
		</div>
		{/if}
		{if condition="$flow.status == 1"}

		<div class="col-xs-12 col-sm-12">
			<div class="form-group col-sm-10">

				<blockquote style="padding: 2px 10px;">
					<p>审核信息 <span class="label label-danger">固定审批流</span></p>
				</blockquote>

				{foreach name="$flow.stepList" item="val"}
				<div class="row">
					<div class="level">
						<span>第{$val.order_id}级</span><br>
						{if condition="$val.status == 3"}
						<span class="label label-info">发起人主管</span>
						{elseif condition="$val.status == 1"/}
						<span class="label label-info">{:count($val['stafflist'])}人或签</span>
						{else/}
						<span class="label label-info">{:count($val['stafflist'])}人并签</span>
						{/if}
					</div>
					{foreach name="$val.stafflist" item="ves"}
						<div class="stafflist">
							<img src="{$ves.img}" class="img-circle">
							{$ves.name}
						</div>
					{/foreach}
				</div>
				<hr>
				{/foreach}


			</div>
		</div>

		{else/}

		<div class="col-xs-12 col-sm-12">
			<div class="form-group col-sm-8">

				<blockquote style="padding: 2px 10px;">
					<p>审核信息 <span class="label label-danger">发起人自选</span></p>
				</blockquote>
				<div style="margin-left: 20px">
					<input data-source="qingdong/customer/contract/getstaff"
						   class="form-control selectpage" placeholder="请选择审批人" data-pagination="true" data-page-size="10" name="row[flow_staff_ids]" type="text" data-multiple="true">
				</div>

			</div>
		</div>
		{/if}


	</div>


	<div class="form-group layer-footer">
		<label class="control-label col-xs-12 col-sm-2"></label>
		<div class="col-xs-12 col-sm-8">
			<button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
			<button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
		</div>
	</div>
</form>

<!--定义模板，模板语法使用Art-Template模板语法-->
<script type="text/html" id="testtpl2">
	<tr class="form-inline">
		<td>
			<input type="hidden" name="row[<%=name%>][<%=index%>][product_id]" value="<%=row['product_id']%>" >
			<div class="form-control" style="width: 90%">

				<a href="javascript:void(0)" class="select-product"
				   data-name="row[<%=name%>][<%=index%>][product_id]"
				   data-url="qingdong/product/product/index?isselect=1"  >
					<% if(row['product_name']){%>
					  <%=row['product_name']%>
					<% }else{%>
					【请点击选择】
					<% }%>
				</a>

			</div></td>

		<td style="width: 80px">
			<input type="number" name="row[<%=name%>][<%=index%>][price]" style="width: 80px"
									   class="form-control price" value="<%=row['price']%>" placeholder="0"></td>
		<td style="width: 80px">
			<input type="number" name="row[<%=name%>][<%=index%>][wholesale]" style="width: 80px"
									   class="form-control price2" value="<%=row['wholesale']%>" placeholder="0">
		</td>
		<td ><span class="goods"><%=row['goods_name']%></span></td>
		<td style="width: 80px"><span class="type"><%=row['type']%></span></td>
		<td style="width: 80px"><input type="number" name="row[<%=name%>][<%=index%>][number]" style="width: 80px"
									   class="form-control number" value="<% if(row['number']){%><%=row['number']%><% }else{%>1<% }%>" ></td>

		<td style="width: 120px"><span class="btn btn-sm btn-danger btn-remove"><i class="fa fa-times"></i></span> <span class="btn btn-sm btn-primary btn-dragsort"><i class="fa fa-arrows"></i></span></td>
	</tr>
</script>

<!--款项类别模板-->
<script type="text/html" id="testtpl3">
	<tr class="form-inline">
		<td style="width: 120px">
			<input type="text" name="row[<%=name%>][<%=index%>][fund_type]"
				   class="form-control fund_type" value="<%=row['fund_type']%>" placeholder="输入款项类别"></td>
		<td style="width: 80px">
			<input type="number" name="row[<%=name%>][<%=index%>][percent]" style="width: 80px"
				   class="form-control percent" value="<%=row['percent']%>" placeholder="0"></td>
		<td style="width: 80px">
			<input type="number" name="row[<%=name%>][<%=index%>][total]" style="width: 80px"
				   class="form-control total" value="<%=row['total']%>" placeholder="0">
		</td>
		<td style="width: 120px"><span class="btn btn-sm btn-danger btn-remove"><i class="fa fa-times"></i></span> </td>
	</tr>
</script>

