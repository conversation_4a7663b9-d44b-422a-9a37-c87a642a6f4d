<form id="edit-form" class="form-horizontal col-sm-12" role="form" data-toggle="validator" method="POST" action="">
    <div class="form-group">
        <label  class="control-label col-xs-12 col-sm-2">同步目的:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="btn-group bootstrap-select form-control">
                {:build_radios('row[types]', ['0'=>__('客户'), '1'=>__('线索')])}
            </div>
            <span class="help-block">*选择所要同步的目的。</span>
        </div>

    </div>
    <div class="form-group">
        <label  class="control-label col-xs-12 col-sm-2">选择来源:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="btn-group bootstrap-select form-control">

                <select  name="row[source]" class="form-control selectpicker sources" id="sourcess">
                    {foreach name="follow" id="v"}
                    <option value="{$v}" >{$v}</option>
                    {/foreach}
                </select>
            </div>
            <span class="help-block">*来源类型对应选择的客户来源或者线索来源。</span>
        </div>
    </div>

    <div class="form-group">
        <label  class="control-label col-xs-12 col-sm-2">自定义:</label>
        <div class="col-xs-12 col-sm-8">
            <table id="table" class="table table-striped table-bordered table-hover table-nowrap" >
                <thead>
                <tr>

                    <th style="text-align: center; vertical-align: middle; " data-field="name">
                        <div class="th-inner ">会员字段名称</div>
                        <div class="fht-cell"></div>
                    </th>
                    <th style="text-align: center; vertical-align: middle; " data-field="subname">
                        <div class="th-inner qd_th">客户字段名称</div>
                        <div class="fht-cell"></div>
                    </th>
                    <th style="text-align: center; vertical-align: middle; " data-field="subname">
                        操作
                    </th>
                </tr>
                </thead>
                <tbody data-listidx="0" class="qd_tr">

                <tr data-index="0" style="">
                    <td style="text-align: center; vertical-align: middle;text-align-last: center; ">
                        <select name=""  data-id="0"  class="form-control userinfo_0 selectpicker userinfos" >
                            <option value=""  style="text-align: center;text-align-last: center;direction: rtl;">无</option>
                            {foreach name="user_data" id="v"}
                            <option data-id="1" value="{$v.key}"  style="text-align: center;text-align-last: center;direction: rtl;">{$v.value}</option>
                            {/foreach}
                        </select>
                    </td>
                    <td style="text-align: center; vertical-align: middle;text-align-last: center; ">
                        <select name="" data-id="0"  class="form-control customerinfo_0 selectpicker customerinfos" customerinfos>
                            <option value=""  style="text-align: center;text-align-last: center;direction: rtl;">无</option>
                            {foreach name="form_data" id="v"}
                            <option data-id="0" value="{$v.id}"  style="text-align: center;text-align-last: center;direction: rtl;">{$v.config.label}</option>
                            {/foreach}
                        </select>

                    </td>
                    <td style="text-align: center; vertical-align: middle;text-align-last: center; ">
                        <button type="button" class="btn btn-success qd_append">追加</button>
                    </td>
                </tr>
                </tbody>
            </table>
            <span class="help-block">*来源类型、备注等字段如果自定义中存在,取自定义,自定义优先级最高。</span>
        </div>

    </div>

    <div class="form-group">
        <label  class="control-label col-xs-12 col-sm-2">分配员工:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="btn-group bootstrap-select form-control">
                <select  name="row[staff_id]" class="form-control selectpicker">
                    <option value="" >请选择</option>
                    {foreach name="staff" id="v"}
                    <option value="{$v.id}" >{$v.name}</option>
                    {/foreach}
                </select>
            </div>
            <span class="help-block">*若未分配员工客户进入公海。</span>
        </div>
    </div>


    <input type="hidden" id="num"  value="0">
    <input type="hidden" id="type" name="row[type]"  value="0">
    <input type="hidden" value="{$idinfo}" name="row[idinfo]">
    <input type="hidden" value="{$typeinfo}" name="row[typeinfo]">
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
