{foreach name="form_data" item="val"}

{if condition="$val.config.is_delete == true || true"}
	{if condition="isset($val.config.addShow) && $val.config.addShow"}
			{php}continue;{/php}
	{/if}
	{eq name="val.component" value="input"}
	<div class="col-xs-12 col-sm-6">
		<div class="form-group col-sm-10">
			<label for="c-{$val.id}">{$val.config.label}:</label>
			{if condition="$val.id eq 'num' && $val.config.label eq '合同编号' "}
				<input id="c-{$val.id}" value="{$contractNum}" class="form-control" {if condition="$val.config.required == true"}data-rule="required"{/if} placeholder="{$val.config.placeholder}" name="row[{$val.id}]" type="text">
			{elseif condition="$val.id eq 'number' && $val.config.label eq '回款编号' "}
			<input id="c-{$val.id}" value="{$createNum}" class="form-control" {if condition="$val.config.required == true"}data-rule="required"{/if} placeholder="{$val.config.placeholder}" name="row[{$val.id}]" type="text">
			{else\}
			<input id="c-{$val.id}" class="form-control" {if condition="$val.config.required == true"}data-rule="required"{/if} placeholder="{$val.config.placeholder}" name="row[{$val.id}]"
				   type="text" value="{$val.config.value|default=''}" >
			{/if}
		</div>
	</div>
	{/eq}

	{eq name="val.component" value="input-number"}
	<div class="col-xs-12 col-sm-6">
		<div class="form-group col-sm-10">
			<label for="c-{$val.id}">{$val.config.label}:</label>
			<input id="c-{$val.id}" value="{$val.config.value|default=''}" class="form-control"{if condition="$val.config.required == true"}data-rule="required"{/if} placeholder="{$val.config.placeholder}" name="row[{$val.id}]"
				   type="number">
		</div>
	</div>
	{/eq}
	{eq name="val.component" value="textarea"}
	<div class="col-xs-12 col-sm-12">
		<div class="form-group col-sm-12">
			<label for="c-{$val.id}">{$val.config.label}:</label>
			<textarea id="c-{$val.id}" class="form-control"{if condition="$val.config.required == true"}data-rule="required"{/if} rows="5" name="row[{$val.id}]"
					  placeholder="{$val.config.placeholder}">{$val.config.value|default=''}</textarea>
		</div>
	</div>
	{/eq}

	{eq name="val.component" value="radio"}
	<div class="col-xs-12 col-sm-6">
		<div class="form-group col-sm-10">
			<label >{$val.config.label}:</label>
			<div class="radio">
				{foreach name="val.config.content" item="vo"}
<!--				{$val.config.value|default=''}-->
				<label><input name="row[{$val.id}]" type="radio" {if condition="isset($val.config.value)"}
							  {eq name="$val.config.value" value="$vo['value']" }checked{/eq}
					{/if} value="{$vo.value}"/> {$vo.value}</label>
				{/foreach}
			</div>

		</div>
	</div>
	{/eq}

	{eq name="val.component" value="checkbox"}
	<div class="col-xs-12 col-sm-6">
		<div class="form-group col-sm-10">
			<label >{$val.config.label}:</label>
			<div class="checkbox">
				{foreach name="val.config.content" item="vo"}
				<label class="checkbox-inline"><input name="row[{$val.id}][]" type="checkbox" {if condition="isset($val.config.value)"} {in name="$vo['value']" value="$val.config.value" }checked{/in}{/if} value="{$vo.value}"/>
					{$vo.value}</label>
				{/foreach}
			</div>

		</div>
	</div>
	{/eq}
	{eq name="val.component" value="Cascader"}
	<div class="col-xs-12 col-sm-6">
		<div class="form-group col-sm-10">
			<label for="c-{$val.id}">{$val.config.label}:</label>
			<!--<div class='form-group col-sm-10'>-->
				<input id="c-{$val.id}" {if condition="$val.config.required == true"}data-rule="required"{/if} class="form-control" data-toggle="city-picker" name="row[{$val.id}]" type="text"
					   value="{$val.config.value|default=''}"/>
			<!--</div>-->
		</div>
	</div>
	{/eq}

	{eq name="val.component" value="select"}
	<div class="col-xs-12 col-sm-6">
		<div class="form-group col-sm-10">
			<label for="c-{$val.id}">{$val.config.label}:</label>
			<select id="c-{$val.id}"  {if condition="$val.config.required == true"}data-rule="required"{/if} class="form-control selectpicker" {if condition="isset($val.config.multiple) && $val.config.multiple == true"} name="row[{$val.id}][]" multiple="" {else\} name="row[{$val.id}]" {/if} data-live-search='true'>
				<option value="">请选择</option>
				{foreach name="val.config.content" item="vo"}
				<option value="{$vo.label}" {if condition="isset($val.config.value)"}
						{in name="$vo['value']" value="$val.config.value" }selected{/in}
			{/if}>{$vo.label}</option>
				{/foreach}

			</select>
		</div>
	</div>
	{/eq}

	{eq name="val.component" value="TimePicker"}
	<div class="col-xs-12 col-sm-6">
		<div class="form-group col-sm-10">
			<label for="c-{$val.id}">{$val.config.label}:</label>

			<input id="c-{$val.id}" {if condition="$val.config.required == true"}data-rule="required"{/if} class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss"
				   placeholder="{$val.config.placeholder}" name="row[{$val.id}]"
			value="{$val.config.value|default=''}" type="text">
		</div>
	</div>
	{/eq}

	{eq name="val.component" value="DatePicker"}
	<div class="col-xs-12 col-sm-6">
		<div class="form-group col-sm-10">
			<label for="c-{$val.id}">{$val.config.label}:</label>

			<input id="c-{$val.id}" {if condition="$val.config.required == true"}data-rule="required"{/if} class="form-control datetimepicker" data-date-format="YYYY-MM-DD" placeholder="{$val.config.placeholder}"
				   name="row[{$val.id}]" value="{$val.config.value|default=''}" type="text">
		</div>
	</div>
	{/eq}
	{eq name="val.component" value="Rate"}
	<div class="col-xs-12 col-sm-6">
		<div class="form-group col-sm-10">
			<label for="c-{$val.id}">{$val.config.label}:</label>
			<select id="c-{$val.id}" name="row[{$val.id}]" class="form-control">
				{if condition="isset($val.config.value)"}
				<option value="5" {eq name="$val.config.value" value="5" }selected{/eq} >5</option>
				<option value="4" {eq name="$val.config.value" value="4" }selected{/eq} >4</option>
				<option value="3" {eq name="$val.config.value" value="3" }selected{/eq} >3</option>
				<option value="2" {eq name="$val.config.value" value="2" }selected{/eq} >2</option>
				<option value="1" {eq name="$val.config.value" value="1" }selected{/eq} >1</option>
				{else/}
				<option value="5">5</option>
				<option value="4">4</option>
				<option value="3">3</option>
				<option value="2">2</option>
				<option value="1">1</option>
				{/if}
			</select>

		</div>
	</div>
	{/eq}

	{eq name="val.component" value="uploadImage"}
	<div class="col-xs-12 col-sm-6">
		<div class="form-group col-sm-10">
			<label for="c-{$val.id}">{$val.config.label}:</label>
			<div class="input-group">
				<input id="p-{$val.id}" class="form-control" placeholder="{$val.config.placeholder}" size="35" name="row[{$val.id}]" type="text" value="" {if condition="$val.config.required == true"}data-rule="required"{/if}>
				<div class="input-group-addon no-border no-padding">
						<span><button type="button" class="btn btn-danger faupload"
									  data-resize-quality="0.8"
									  data-resize-width="1024"
									  data-resize-height="768"
									  data-input-id="p-{$val.id}"
									  data-ids="{$val.id}"
									  data-url="qingdong/base/upload"
									  data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp"
									  data-multiple="true" data-preview-id="c-{$val.id}"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>

				</div>
				<span class="msg-box n-right"></span>
			</div>
			<ul class="row list-inline faupload-preview" id="c-{$val.id}" data-id="{$val.id}"></ul>
		</div>
	</div>
	{/eq}

{eq name="val.component" value="uploadFile"}
<div class="col-xs-12 col-sm-6">
	<div class="form-group col-sm-10">
		<label for="c-{$val.id}">{$val.config.label}:</label>
		<div class="input-group">
			<input id="p-{$val.id}" class="form-control" placeholder="{$val.config.placeholder}" size="35" name="row[{$val.id}]" type="text" value="" {if condition="$val.config.required == true"}data-rule="required"{/if}>
			<div class="input-group-addon no-border no-padding">
						<span><button type="button" class="btn btn-danger faupload"
									  data-input-id="p-{$val.id}"
									  data-url="qingdong/base/upload"
									  data-multiple="true" data-preview-id="c-{$val.id}"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>

			</div>
			<span class="msg-box n-right"></span>
		</div>
	</div>
</div>
{/eq}

{/if}
{/foreach}