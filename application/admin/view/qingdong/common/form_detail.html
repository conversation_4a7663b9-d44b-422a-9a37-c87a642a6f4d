{foreach name="form_data" item="val"}

{if condition="$val.config.is_delete == true || true"}

	{eq name="val.component" value="input"}
		<div class="col-xs-6 col-sm-5">
			<div class="form-group col-sm-10">
				<span class="text-muted">{$val.config.label}:</span>
				{:$row[$val['id']]??''}
			</div>
		</div>
	{/eq}

	{eq name="val.component" value="input-number"}
		<div class="col-xs-6 col-sm-5">
			<div class="form-group col-sm-10">
				<span class="text-muted">{$val.config.label}:</span>
				{:$row[$val['id']]??''}
			</div>
		</div>
	{/eq}
	{eq name="val.component" value="textarea"}
	<div class="col-xs-12 col-sm-10">
		<div class="form-group col-sm-10">
			<span class="text-muted">{$val.config.label}:</span>
			{:$row[$val['id']]??''}
		</div>
	</div>
	{/eq}

	{eq name="val.component" value="radio"}
	<div class="col-xs-12 col-sm-5">
		<div class="form-group col-sm-10">
			<span class="text-muted">{$val.config.label}:</span>
			{:$row[$val['id']]??''}

		</div>
	</div>
	{/eq}

	{eq name="val.component" value="checkbox"}
	<div class="col-xs-12 col-sm-5">
		<div class="form-group col-sm-10">
			<span class="text-muted">{$val.config.label}:</span>
			{:$row[$val['id']]??''}
		</div>
	</div>
	{/eq}
	{eq name="val.component" value="Cascader"}
	<div class="col-xs-12 col-sm-5">
		<div class="form-group col-sm-10">
			<span class="text-muted">{$val.config.label}:</span>
			{:$row[$val['id']]??''}
		</div>
	</div>
	{/eq}

	{eq name="val.component" value="select"}
	<div class="col-xs-12 col-sm-5">
		<div class="form-group col-sm-10">
			<span class="text-muted">{$val.config.label}:</span>
			{:$row[$val['id']]??''}
		</div>
	</div>
	{/eq}

	{eq name="val.component" value="TimePicker"}
	<div class="col-xs-12 col-sm-5">
		<div class="form-group col-sm-10">
			<span class="text-muted">{$val.config.label}:</span>
			{:$row[$val['id']]??''}
		</div>
	</div>
	{/eq}

	{eq name="val.component" value="DatePicker"}
	<div class="col-xs-12 col-sm-5">
		<div class="form-group col-sm-10">
			<span class="text-muted">{$val.config.label}:</span>
			{:$row[$val['id']]??''}
		</div>
	</div>
	{/eq}
	{eq name="val.component" value="Rate"}
	<div class="col-xs-12 col-sm-5">
		<div class="form-group col-sm-10">
			<span class="text-muted">{$val.config.label}:</span>
			{:$row[$val['id']]??''}
		</div>
	</div>
	{/eq}
	{eq name="val.component" value="uploadImage"}
	<div class="col-xs-12 col-sm-5">
		<div class="form-group col-sm-10">
			<span class="text-muted">{$val.config.label}:</span>
			{if condition="isset($row[$val['id']]) && $row[$val['id']] neq ''"}
			{foreach name="$row[$val['id']]" id="v"}
			<a href="{:$v.file_path??''}" target="_blank" style="margin-right:10px;">
				<img  src="{:$v.file_path??''}" data-tips-image width="100px" height="100px" alt="">
			</a>
			{/foreach}
			{/if}
		</div>
	</div>
	{/eq}

	{eq name="val.component" value="uploadFile"}
	<div class="col-xs-12 col-sm-5">
		<div class="form-group col-sm-10">
			<span class="text-muted">{$val.config.label}:</span>
			{if condition="isset($row[$val['id']]) && $row[$val['id']] neq ''"}
			{foreach name="$row[$val['id']]" id="v" k="key"}
			<a href="{:$v.file_path??''}" target="_blank" style="margin-right:10px;">
				{$v.name}
			</a>
			{/foreach}
			{/if}
		</div>
	</div>
	{/eq}

{/if}
{/foreach}