<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">
    {:token()}
    <div class="form-group">
        <label  class="control-label col-xs-12 col-sm-2">姓名:</label>
        <div class="col-xs-12 col-sm-8">
            <input  data-rule="required" class="form-control" name="row[name]" type="text"  >
        </div>
    </div>
    <div class="form-group">
        <label  class="control-label col-xs-12 col-sm-2">员工编号:</label>
        <div class="col-xs-12 col-sm-8">
            <input  class="form-control" name="row[num]" type="text"  >
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">部门:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_select('group[]', $groupdata, null, ['class'=>'form-control selectpicker', 'multiple'=>'', 'data-rule'=>'required'])}
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">角色:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_select('row[role]', $roles, null, ['class'=>'form-control selectpicker',
            'data-rule'=>'required', 'data-live-search'=>'true'])}
        </div>
    </div>

    <div class="form-group">
		<label class="control-label col-xs-12 col-sm-2">直属上级:</label>
		<div class="col-xs-12 col-sm-8">
			{:build_select('row[parent_id]', $staffs,null, ['class'=>'form-control selectpicker', 'data-live-search'=>'true'])}
		</div>
    </div>
    <div class="form-group">
        <label  class="control-label col-xs-12 col-sm-2">邮箱:</label>
        <div class="col-xs-12 col-sm-8">
            <input class="form-control" name="row[email]" type="text"  placeholder="已经存在的邮箱号不可填写" >
        </div>
    </div>
    <div class="form-group">
        <label  class="control-label col-xs-12 col-sm-2">手机号码:</label>
        <div class="col-xs-12 col-sm-8">
            <input data-rule="required"    class="form-control" name="row[mobile]" type="text" placeholder="已经存在的手机号不可填写" >
        </div>
    </div>
    <div class="form-group">
        <label for="c-password" class="control-label col-xs-12 col-sm-2">{:__('Password')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-password" data-rule="password" class="form-control" name="row[password]" type="text" value=""
                   placeholder="请输入密码" autocomplete="password"/>
        </div>
    </div>
    <div class="form-group">
        <label  class="control-label col-xs-12 col-sm-2">岗位:</label>
        <div class="col-xs-12 col-sm-8">
            <input  data-rule="required" class="form-control" name="row[post]" type="text"  >
        </div>
    </div>
    <div class="form-group">
        <label for="c-image" class="control-label col-xs-12 col-sm-2">头像:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-image" class="form-control" size="35" name="row[img]" type="text" value="">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-image" class="btn btn-danger faupload" data-input-id="c-image" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp" data-multiple="false" data-preview-id="p-image"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                </div>
                <span class="msg-box n-right"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-image"></ul>
        </div>
    </div>
    <div class="form-group">
        <label  class="control-label col-xs-12 col-sm-2">性别:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_radios('row[sex]', ['1'=>__('男'), '2'=>__('女')])}
        </div>
    </div>


    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
