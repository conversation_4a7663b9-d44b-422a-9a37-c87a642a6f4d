<style type="text/css">
    .jstree-default .jstree-node{
        padding-left: 0;
        margin-left:0;
        font-size: 16px;
        font-weight: 700;
        line-height:2em;
        background-image:none!important;;
    }
    #treeview .jstree-leaf{
        padding-left: 0;
        font-size: 14px;
        font-weight: 500;
        background-image:none!important;
    }
    .jstree-default .jstree-ocl{
        display: none;
    }
</style>
<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">
    {:token()}
    <input type="hidden" name="row[ids]" id="ids" value="{$row.id}">
    <input type="hidden" name="row[rules]" value="{$row.rules}" />
    <div class="form-group">
        <div class="col-sm-12">
            <blockquote style="padding: 2px 10px;text-align: left">
                <p>数据权限</p>
            </blockquote>
            <label  class="control-label col-sm-1">权限:</label>
            <div class="col-sm-11">
                {:build_radios('row[role_type]',$rule,$row['role_type'])}
            </div>
        </div>
    </div>
    <div class="form-group">
        <div class="col-sm-12">
            <blockquote style="padding: 2px 10px;text-align: left">
                <p>模块功能</p>
            </blockquote>
            <div class="col-sm-11">
                <div id="treeview" class="treeview"></div>
            </div>
        </div>
    </div>

    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
