<div class="panel panel-default panel-intro">
    {:build_heading()}

    <div class="panel-heading">
        <ul class="nav nav-tabs" role="tablist">
            <li role="presentation" class="active">
                <a href="#basic" aria-controls="profile" role="tab"
                                                      data-toggle="tab">员工目标</a>
            </li>
        </ul>

    </div>
    <div class="panel-body">

        <div id="myTabContent" class="tab-content">
            <div role="tabpanel" class="tab-pane fade active in" id="basic">
                <div id="toolbar" class="toolbar">
                    {:build_toolbar('refresh')}
                    <a class="btn btn-danger {:$auth->check('qingdong/department/achievement/add')?'':'hide'} btn-dialog" data-refresh="true"  data-url="qingdong/department/achievement/add" data-area='["90%","90%"]'
                       data-title="设置个人目标">设置个人目标</a>
                    <a class="btn btn-default {:$auth->check('qingdong/department/achievement/records')?'':'hide'} btn-dialog" data-refresh="true"  data-url="qingdong/department/achievement/records" data-area='["90%","90%"]'
                       data-title="业绩修改记录">业绩修改记录</a>
                </div>
                <table id="table"
                       class="table table-striped table-bordered table-hover table-nowrap"
                       data-operate-save="{:$auth->check('qingdong/department/achievement/edit')}"
                       data-operate-delete="{:$auth->check('qingdong/department/achievement/del')}"width="100%">
                </table>
            </div>
        </div>
    </div>

</div>


<script id="searchformtpl" type="text/html">
    <form action="" class="form-horizontal form-commonsearch nice-validator n-default n-bootstrap">
            <div class="row">
                <div class="form-group col-xs-12 col-sm-3 col-md-2 ">
                    <label class="control-label col-xs-4">年份</label>
                    <div class="col-xs-8">
                        <input class="operate" type="hidden" data-name="year" value="eq"/>
                        <select name="year" id="year" class="form-control">
                            {foreach name="years" item="val"}
                            <option value="{$val.id}"
                                    {if condition="isset($val['selected'])"} selected="selected" {/if} >
                            {$val.name}</option>
                            {/foreach}
                        </select>
                    </div>
                </div>
                <div class="form-group col-xs-12 col-sm-3 col-md-3">
                    <label class="control-label col-xs-4">考核目标</label>
                    <div class="col-xs-8">
                        <input class="operate" type="hidden" data-name="status" value="eq"/>
                        <select name="status" id="status" class="form-control">
                            <option value="1">合同金额</option>
                            <option value="2">回款金额</option>
                        </select>
                    </div>
                </div>
                <div class="form-group col-xs-12 col-sm-3 col-md-3">
                    <label class="control-label col-xs-4">员工</label>
                    <div class="col-xs-8">
                        <input class="operate" type="hidden" data-name="staff_id" value="eq"/>
                        <select name="staff_id" class="form-control">
                            <option value="">全部</option>
                            {foreach name="staffs" item="val"}
                            <option value="{$val.id}">{$val.name}</option>
                            {/foreach}
                        </select>
                    </div>
                </div>

                <div class="form-group col-xs-12 col-sm-6 col-md-2">
                    <div class="col-sm-8 col-xs-offset-4">
                        <button type="submit" class="btn btn-success" formnovalidate="">搜索</button>
                    </div>
                </div>
            </div>
    </form>
</script>