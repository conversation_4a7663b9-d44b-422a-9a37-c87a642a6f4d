<div class="panel panel-default panel-intro">
    {:build_heading()}

    <div class="panel-heading">
        <ul class="nav nav-tabs" role="tablist">
            <li role="presentation" class="active"><a href="#basic" aria-controls="profile" role="tab"
                                                      data-toggle="tab">我的目标</a></li>
        </ul>

    </div>
    <div class="tab-content" style="overflow-y: scroll;">

        <div id="myTabContent" class="tab-content">
            <div role="tabpanel" class="tab-pane fade panel-body active in" id="basic">
                <div id="toolbar" class="toolbar">
                    <a class="btn btn-success btn-change btn-dialog" data-url="qingdong/department/achievement/records" data-title="查看修改记录"  href="javascript:;"><i class="fa fa-clock-o"></i> 查看修改记录</a>
                </div>
                <table id="table" class="table table-striped table-bordered table-hover table-nowrap" width="100%">
                </table>
            </div>

            <div role="tabpanel" class="tab-pane fade panel-body" id="department">

                <table id="" class="table table-striped table-bordered table-hover table-nowrap" width="100%">
                </table>
            </div>

        </div>
    </div>

</div>


<script id="searchformtpl" type="text/html">
    <form action="" class="form-horizontal form-commonsearch nice-validator n-default n-bootstrap">
        <div class="well">
            <div class="row">
                <div class="form-group col-xs-12 col-sm-3 col-md-2 ">
                    <label class="control-label col-xs-4">年份</label>
                    <div class="col-xs-8">
                        <input class="operate" type="hidden" data-name="year" value="eq"/>
                        <select name="year" id="year" class="form-control">
                            {foreach name="years" item="val"}
                            <option value="{$val.id}" {if condition="isset($val['selected'])"} selected="selected" {/if} >{$val.name}</option>
                            {/foreach}
                        </select>
                    </div>
                </div>
                <div class="form-group col-xs-12 col-sm-3 col-md-3">
                    <label class="control-label col-xs-4">目标类型</label>
                    <div class="col-xs-8">
                        <input class="operate" type="hidden" data-name="status" value="eq"/>
                        <select name="status" id="status" class="form-control">
                            <option value="1">合同金额</option>
                            <option value="2">回款金额</option>
                        </select>
                    </div>
                </div>

                <div class="form-group col-xs-12 col-sm-6 col-md-2">
                    <div class="col-sm-8 col-xs-offset-4">
                        <button type="submit" class="btn btn-success" formnovalidate="">搜索</button>
                    </div>
                </div>
            </div>
        </div>
    </form>
</script>