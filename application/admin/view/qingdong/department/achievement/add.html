<style type="text/css">
	.table tr td:nth-child(1){
		text-align: right;
		line-height: 2em;
	}
	.table tr td:nth-child(2){
		width: 150px;
	}
	.stafflist{
		width: 60px;text-align: center;float: left;
	}
	.stafflist img{
		width: 40px;
		height: 40px;
		margin: 10px;
		border-radius:50%;
	}
	.level{
		width: 100px;text-align: center;float: left;padding-top:18px;
		margin-left: 30px;
	}
	.level span:first-child{
		line-height: 2em
	}
</style>
<form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">
	{:token()}

	<div class="form-group">
		<label class="control-label col-xs-12 col-sm-1">员工:</label>
		<div class="col-xs-12 col-sm-4">
			<input data-source="qingdong/department/achievement/getstaff"
				   class="form-control selectpage" placeholder="请选择员工" data-pagination="true" data-page-size="10" name="row[staff_ids]" data-rule="required" type="text" data-multiple="true">
		</div>
	</div>
	<div class="form-group">
		<label class="control-label col-xs-12 col-sm-1">考核目标:</label>
		<div class="col-xs-12 col-sm-4">
			<select name="row[status]" id="status" data-rule="required" class="form-control">
				<option value="1">合同金额</option>
				<option value="2">回款金额</option>
			</select>
		</div>
	</div>

	<div class="form-group " >
		<div  style="width:100%;text-align: center;line-height: 2em;display: flex;
		justify-content: center;align-items: center;border:1px solid #f6f6f6;padding: 10px">
			<select name="row[year]" id="year" data-rule="required" class="form-control" style="width: 100px" >
			{foreach name="years" item="val"}
			<option value="{$val.id}" {$val.selected|default=""}>{$val.name}</option>
			{/foreach}
		</select> 的年度目标是￥
			<input type="number" name="row[yeartarget]" style="border:1px solid #ccc;width: 100px" id="yeartarget" value=""> 元

			<button type="button" class="btn btn-success btn-embossed average_month" style="margin-left: 50px">平均分配到月</button>
		</div>
	</div>
	<div class="form-group">
		<div class="col-xs-12 col-sm-3">
			<table class="table">
				<tr>
					<td>第一季度</td>
					<td><p id="one" class="form-control" disabled></p></td>
				</tr>
				<tr>
					<td>一月份</td>
					<td><input type="number" name="row[january]" id="january" min="0"
							   class="form-control month" value="{$row.january|default=0}"/></td>
				</tr>
				<tr>
					<td>二月份</td>
					<td><input type="number" name="row[february]" id="february" min="0" class="form-control month" value="{$row.february|default=0}"/>
					</td>
				</tr>
				<tr>
					<td>三月份</td>
					<td><input type="number" name="row[march]" id="march" min="0" class="form-control month" value="{$row.march|default=0}"/>
					</td>
				</tr>
			</table>
		</div>
		<div class="col-xs-12 col-sm-3">

			<table class="table">
				<tr>
					<td>第二季度</td>
					<td><p id="two" class="form-control" disabled></p></td>
				</tr>
				<tr>
					<td>四月份</td>
					<td><input type="number" name="row[april]" id="april" min="0"
							   class="form-control month" value="{$row.april|default=0}"/></td>
				</tr>
				<tr>
					<td>五月份</td>
					<td><input type="number" name="row[may]" id="may" min="0" class="form-control month" value="{$row.may|default=0}"/>
					</td>
				</tr>
				<tr>
					<td>六月份</td>
					<td><input type="number" name="row[june]" id="june" min="0" class="form-control month" value="{$row.june|default=0}"/>
					</td>
				</tr>
			</table>
		</div>
		<div class="col-xs-12 col-sm-3">

			<table class="table">
				<tr>
					<td>第三季度</td>
					<td><p id="three" class="form-control" disabled></p></td>
				</tr>
				<tr>
					<td>七月份</td>
					<td><input type="number" name="row[july]" id="july" min="0" class="form-control month" value="{$row.july|default=0}"/></td>
				</tr>
				<tr>
					<td>八月份</td>
					<td><input type="number" name="row[august]" id="august" min="0" class="form-control month" value="{$row.august|default=0}"/>
					</td>
				</tr>
				<tr>
					<td>九月份</td>
					<td><input type="number" name="row[september]" id="september" min="0" class="form-control month" value="{$row.september|default=0}"/>
					</td>
				</tr>
			</table>
		</div>
		<div class="col-xs-12 col-sm-3">

			<table class="table">
				<tr>
					<td>第四季度</td>
					<td><p id="four" class="form-control" disabled></p></td>
				</tr>
				<tr>
					<td>十月份</td>
					<td><input type="number" name="row[october]" id="october" min="0" class="form-control month" value="{$row.october|default=0}"/></td>
				</tr>
				<tr>
					<td>十一月份</td>
					<td><input type="number" name="row[november]" id="november" min="0" class="form-control month" value="{$row.november|default=0}"/>
					</td>
				</tr>
				<tr>
					<td>十二月份</td>
					<td><input type="number" name="row[december]" id="december" min="0" class="form-control month" value="{$row.december|default=0}"/>
					</td>
				</tr>
			</table>
		</div>
	</div>

	{if condition="$flow.status == 1"}

	<div class="col-xs-12 col-sm-12">
		<div class="form-group col-sm-10">

			<blockquote style="padding: 2px 10px;">
				<p>审核信息 <span class="label label-danger">固定审批流</span></p>
			</blockquote>

			{foreach name="$flow.stepList" item="val"}
			<div class="row">
				<div class="level">
					<span>第{$val.order_id}级</span><br>
					{if condition="$val.status == 3"}
					<span class="label label-info">发起人主管</span>
					{elseif condition="$val.status == 1"/}
					<span class="label label-info">{:count($val['stafflist'])}人或签</span>
					{else/}
					<span class="label label-info">{:count($val['stafflist'])}人并签</span>
					{/if}
				</div>
				{foreach name="$val.stafflist" item="ves"}
				<div class="stafflist">
					<img src="{$ves.img}" class="img-circle">
					{$ves.name}
				</div>
				{/foreach}
			</div>
			<hr>
			{/foreach}


		</div>
	</div>

	{else/}

	<div class="col-xs-12 col-sm-12">
		<div class="form-group col-sm-8">

			<blockquote style="padding: 2px 10px;">
				<p>审核信息 <span class="label label-danger">发起人自选</span></p>
			</blockquote>
			<div style="margin-left: 20px">
				<input data-source="qingdong/customer/contract/getstaff"
					   class="form-control selectpage" placeholder="请选择审批人" data-pagination="true" data-page-size="10" name="row[flow_staff_ids]" type="text" data-multiple="true">
			</div>

		</div>
	</div>
	{/if}
	<div class="form-group layer-footer">
		<label class="control-label col-xs-12 col-sm-2"></label>
		<div class="col-xs-12 col-sm-8">
			<button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
			<button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
		</div>
	</div>
</form>
