<?php

namespace app\admin\controller\qingdong\statistic;


use addons\qingdong\model\Contract;
use addons\qingdong\model\ContractRatio;
use addons\qingdong\model\Receivables;
use app\common\controller\Backend;
use addons\qingdong\model\Achievement as AchievementModel;
use addons\qingdong\model\Staff;

/**
 * 业绩统计
 */
class Achievement extends Backend
{
    /**
     * 业绩目标完成情况
     */
    public function index()
    {
        $row = input('row/a');
        if (isset($row['year']) && $row['year']) {
            $year = $row['year'];
        } else {
            $year = date('Y');
        }
        if (isset($row['status']) && $row['status']) {
            $status=$row['status'];
        }else{
            $status=1;
        }
        if (isset($row['type']) && $row['type']) {
            $type=$row['type'];
        }else{
            $type=0;
        }
        $staff = Staff::info();
        if ($type == 1) {//本人
            $ids = [$staff->id];
        } elseif ($type == 2) {//下属
            $ids = Staff::getLowerStaffId();
        } else {//全部
            $ids = Staff::getMyStaffIds();
        }
        if (isset($row['staff_id']) && $row['staff_id']) {
            $ids=$staff_id=$row['staff_id'];
        }else{
            $staff_id='';
        }
        $achievements=AchievementModel::where(['type'=>3,'obj_id'=>['in',$ids],'year'=>$year,'status'=>$status])
            ->field('sum(january) as january,sum(february) as february,sum(march) as march,sum(april) as april,sum(may) as may,sum(june) as june,sum(july) as july,sum(august) as august,sum(september) as september,sum(october) as october,sum(november) as november,sum(december) as december,sum(yeartarget) as yeartarget')->find()->toArray();
        if($status == 1){//合同金额
            $contracts=ContractRatio::where([
                'contract.check_status'=>2, 'contract_ratio.staff_id' => ['in',$ids],
                'contract.order_date'=>['like', $year . '%']
            ])->with(['contract'])->select();
            $contracts=collection($contracts)->toArray();
            $contractData=[];
            foreach ($contracts as $v) {
                $order_date = $v['contract']['order_date'];
                $month = date('Y-m', strtotime($order_date));
                $contractData[$month]['money'][] = $v['ratio_money'];
                $contractData[$month]['contract_id'][] = $v['contract_id'];
            }

            $list  = [];
            foreach ($contractData as $month=>$v) {
                $list[$month] = ['money'=>array_sum($v['money']),'count'=>count($v['contract_id'])];
            }
        }else{
            $receivables      = Receivables::where([
                'owner_staff_id' => ['in',$ids],
                'check_status'   => 2,
                'return_time'     => ['like', $year . '%'],
            ])->group('month')
            ->field('sum(money) as money,FROM_UNIXTIME(UNIX_TIMESTAMP(return_time),"%Y-%m") as month,count(*) as count')->select();
            $list  = [];
            foreach ($receivables as $v) {
                $list[$v['month']] = $v;
            }
        }
        $data = [];
        $echartdata=[];
        foreach ($achievements as $k => $v) {
            if ($month = AchievementModel::getFieldMonth($k)) {
                $month             = $year . '-' . $month;
                $row               = [
                    'month'       => $month,
                    'achievement' => $v,
                    'money'       => isset($list[$month]) ? $list[$month]['money'] : 0,
                    'count'       => isset($list[$month]) ? $list[$month]['count'] : 0,
                ];
                $row['ratio']      = ($row['money'] && intval($row['achievement'])) ? sprintf("%.2f", $row['money'] / $row['achievement'] * 100) : 0;
                $row['unit_price'] = ($row['money'] && $row['count']) ? sprintf("%.2f", $row['money'] / $row['count']) : 0;

                $echartdata['month'][] = $row['month'];
                $echartdata['achievement'][] = $row['achievement'];
                $echartdata['money'][] = $row['money'];
                $data[] = $row;
            }
        }

        $this->view->assign([
            'data' => $data,
            'echartdata' => $echartdata,
            'status' => $status,
            'year' => $year,
            'type' => $type,
            'staff_id' => $staff_id,
        ]);

        return $this->view->fetch();
    }

}
