<?php

namespace app\admin\controller\qingdong\statistic;


use addons\qingdong\model\Staff;
use app\common\controller\Backend;
use addons\qingdong\model\Contacts;
use addons\qingdong\model\Contract;
use addons\qingdong\model\Customer;
use addons\qingdong\model\Field;
use addons\qingdong\model\File;
use addons\qingdong\model\Receivables;
use addons\qingdong\model\Record;
use addons\qingdong\model\Leads;
use addons\qingdong\model\ExamineRecord;

use think\Config;

/**
 * 排行分析
 */
class Ranking extends Backend
{
    /**
     * 合同金额分析
     */
    public function index()
    {
        $row = input('row/a');
        if (isset($row['times']) && $row['times']) {
            $times = explode(' - ', $row['times']);
        } else {
            $times = [date('Y-01-01'), date('Y-m-d')];
        }
        if (isset($row['type']) && $row['type']) {
            $type = $row['type'];
        } else {
            $type = 0;
        }
        $staff = Staff::info();
        $between = [$times[0].' 00:00:00', $times[1].' 23:59:59'];
        if ($type == 1) {//本人
            $ids = [$staff->id];
        } elseif ($type == 2) {//下属
            $ids = Staff::getLowerStaffId();
        } else {//全部
            $ids = Staff::getMyStaffIds();
        }
        if (isset($row['staff_id']) && $row['staff_id']) {
            $ids = $staff_id = $row['staff_id'];
        } else {
            $staff_id = '';
        }

        $datalist = Contract::where([
            'check_status' => 2,
            'order_staff_id' => ['in', $ids],
            'order_date' => ['between', $between]
        ])->field("order_staff_id,sum(`money`) as c")->group('order_staff_id')->select();

        $datainfo = $this->arraySortByOneField($datalist, 'c');
        $typedata = [];
        foreach ($datainfo as $k => $v) {
            $staff = Staff::where(array('id' => $v['order_staff_id']))->value('name');
            if($k<10) {
                $typedata['name'][] = $staff;
                $typedata['num'][] = $v['c'];
            }
            $datainfo[$k]['name'] = $staff;
        }
        $rank = $this->arraySortByOneField($datainfo, 'c', SORT_DESC);

        $this->view->assign([
            'customerdata' => $typedata,
            'dataInfo' => $rank,
            'times' => implode(' - ', $times),
            'type' => $type,
            'staff_id' => $staff_id,
        ]);

        return $this->view->fetch();
    }

    /**
     * 回款金额分析
     */
    public function contract()
    {
        $row = input('row/a');
        if (isset($row['times']) && $row['times']) {
            $times = explode(' - ', $row['times']);
        } else {
            $times = [date('Y-01-01'), date('Y-m-d')];
        }
        if (isset($row['type']) && $row['type']) {
            $type = $row['type'];
        } else {
            $type = 0;
        }
        $staff = Staff::info();
        $between = [$times[0].' 00:00:00', $times[1].' 23:59:59'];
        if ($type == 1) {//本人
            $ids = [$staff->id];
        } elseif ($type == 2) {//下属
            $ids = Staff::getLowerStaffId();
        } else {//全部
            $ids = Staff::getMyStaffIds();
        }
        if (isset($row['staff_id']) && $row['staff_id']) {
            $ids = $staff_id = $row['staff_id'];
        } else {
            $staff_id = '';
        }

        $datalist = Receivables::where([
            'check_status' => 2,
            'owner_staff_id' => ['in', $ids],
            'return_time' => ['between', $between]
        ])->field("owner_staff_id,sum(`money`) as c")->group('owner_staff_id')->select();
        $datainfo = $this->arraySortByOneField($datalist, 'c');
        $typedata = [];
        foreach ($datainfo as $k => $v) {
            $staff = Staff::where(array('id' => $v['owner_staff_id']))->value('name');
            $typedata['name'][] = $staff;
            $typedata['num'][] = $v['c'];
            $datainfo[$k]['name'] = $staff;
        }
        $rank = $this->arraySortByOneField($datainfo, 'c', SORT_DESC);

        $this->view->assign([
            'customerdata' => $typedata,
            'dataInfo' => $rank,
            'times' => implode(' - ', $times),
            'type' => $type,
            'staff_id' => $staff_id,
        ]);

        return $this->view->fetch();
    }

    /**
     * 新增客户数分析
     */
    public function customer()
    {
        $row = input('row/a');
        if (isset($row['times']) && $row['times']) {
            $times = explode(' - ', $row['times']);
        } else {
            $times = [date('Y-01-01'), date('Y-m-d')];
        }
        if (isset($row['type']) && $row['type']) {
            $type = $row['type'];
        } else {
            $type = 0;
        }
        $staff = Staff::info();
        $between = [strtotime($times[0]), strtotime($times[1]) + 86400 - 1];
        if ($type == 1) {//本人
            $ids = [$staff->id];
        } elseif ($type == 2) {//下属
            $ids = Staff::getLowerStaffId();
        } else {//全部
            $ids = Staff::getMyStaffIds();
        }
        if (isset($row['staff_id']) && $row['staff_id']) {
            $ids = $staff_id = $row['staff_id'];
        } else {
            $staff_id = '';
        }

        $datalist = Customer::where(['owner_staff_id' => ['in', $ids], 'createtime' => ['between', $between]])
            ->field("owner_staff_id,count(*) as c")
            ->group('owner_staff_id')->select();
        $datainfo = $this->arraySortByOneField($datalist, 'c');
        $typedata = [];
        foreach ($datainfo as $k => $v) {
            $staff = Staff::where(array('id' => $v['owner_staff_id']))->value('name');
            $typedata['name'][] = $staff;
            $typedata['num'][] = $v['c'];
            $datainfo[$k]['name'] = $staff;
        }
        $rank = $this->arraySortByOneField($datainfo, 'c', SORT_DESC);

        $this->view->assign([
            'customerdata' => $typedata,
            'dataInfo' => $rank,
            'times' => implode(' - ', $times),
            'type' => $type,
            'staff_id' => $staff_id,
        ]);

        return $this->view->fetch();
    }

    /**
     * 跟进次数分析
     */
    public function record()
    {
        $row = input('row/a');
        if (isset($row['times']) && $row['times']) {
            $times = explode(' - ', $row['times']);
        } else {
            $times = [date('Y-01-01'), date('Y-m-d')];
        }
        if (isset($row['type']) && $row['type']) {
            $type = $row['type'];
        } else {
            $type = 0;
        }
        $staff = Staff::info();
        $between = [strtotime($times[0]), strtotime($times[1]) + 86400 - 1];
        if ($type == 1) {//本人
            $ids = [$staff->id];
        } elseif ($type == 2) {//下属
            $ids = Staff::getLowerStaffId();
        } else {//全部
            $ids = Staff::getMyStaffIds();
        }
        if (isset($row['staff_id']) && $row['staff_id']) {
            $ids = $staff_id = $row['staff_id'];
        } else {
            $staff_id = '';
        }

        $datalist = Record::where(['create_staff_id' => ['in', $ids], 'createtime' => ['between', $between],'follow_type'=>['neq','其它']])
            ->field("create_staff_id,count(*) as c")
            ->group('create_staff_id')->select();
        $datainfo = $this->arraySortByOneField($datalist, 'c');
        $typedata = [];
        foreach ($datainfo as $k => $v) {
            $staff = Staff::where(array('id' => $v['create_staff_id']))->value('name');
            $typedata['name'][] = $staff;
            $typedata['num'][] = $v['c'];
            $datainfo[$k]['name'] = $staff;
        }
        $rank = $this->arraySortByOneField($datainfo, 'c', SORT_DESC);

        $this->view->assign([
            'customerdata' => $typedata,
            'dataInfo' => $rank,
            'times' => implode(' - ', $times),
            'type' => $type,
            'staff_id' => $staff_id,
        ]);
        return $this->view->fetch();
    }

    /**
     * 排序
     * @param $data
     * @param $field
     * @param int $sort
     * @return mixed
     */
    public function arraySortByOneField($data, $field, $sort = SORT_ASC)
    {
        $field = array_column($data, $field);
        array_multisort($field, $sort, $data);
        return $data;
    }
}
