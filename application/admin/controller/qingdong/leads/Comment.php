<?php

namespace app\admin\controller\qingdong\leads;
use addons\qingdong\model\Staff;
use app\common\controller\Backend;
use addons\qingdong\model\Comment as CommentModel;
use think\DB;

/**
 * 线索评论列表
 */
class Comment extends Backend {
    public function _initialize() {
        parent::_initialize();
        $this->model = new CommentModel();
    }


    /**
     * 线索评论列表
     */
    public function index() {
        $this->request->filter(['strip_tags']);
        if ($this->request->isAjax()) {
            //0:全部 1：我负责的 2：下属负责的
            $type = input('type',0);
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            switch($type){
                case 1:
                    $staff = Staff::info();
                    $wheres['staff_id'] =  $staff->id;
                    break;
                case 2:
                    $wheres['staff_id'] =  array('in',Staff::getLowerStaffId());
                    break;
                default:
                    $wheres['staff_id'] =  array('in',Staff::getMyStaffIds());
                    break;

            }
            $wheres['relation_type'] = 4;
            $list   = $this->model->where($where)->where($wheres)->with(['staff','record'])->order($sort, $order)->paginate($limit);
            $row    = $list->items();
            $result = array("total" => $list->total(), "rows" => $row);

            return json($result);
        }

        return $this->view->fetch();
    }



}