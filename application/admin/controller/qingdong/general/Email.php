<?php

namespace app\admin\controller\qingdong\general;

use addons\qingdong\model\Contacts;
use addons\qingdong\model\Customer;
use addons\qingdong\model\Staff;
use app\common\controller\Backend;
use addons\qingdong\model\SendTemplater;
/**
 * 邮箱通知模板
 */
class Email extends Backend
{
    protected $relationSearch = true;
    /**
     * @var \addons\qingdong\model\SendTemplater
     */
    protected $model = null;


    public function _initialize()
    {
        parent::_initialize();
        $this->model = new SendTemplater();
    }


    /**
     * 查看
     */
    public function index()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            $list = $this->model->where(['type'=>'email'])->paginate();
            $result = array("total" => $list->total(), "rows" => $list->items());
            return json($result);
        }
        return $this->view->fetch();
    }

    /**
     * 发送邮件
     */
    public function send_email($ids = null) {

        $types=input('type');

        if($types == 'customer'){
            $row = Customer::where([
                'id' => $ids,
                'owner_staff_id' => ['in', Staff::getMyStaffIds()]
            ])->find();
        }elseif($types == 'contacts'){
            $row = Contacts::where([
                'id' => $ids,
                'owner_staff_id' => ['in', Staff::getMyStaffIds()]
            ])->find();
        }else{
            $this->error(__('No Results were found'));
        }
        if(empty($row)){
            $this->error(__('No Results were found'));
        }
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if(empty($params)){
                $this->error(__('Unknown data format'));
            }
            $result = $this->model::sendEmail($ids, $params['name'], $params['content'],$types);
            if ($result == true) {
                $this->success("成功发送");
            } else {
                $this->error("发送失败");
            }
        }
        $templaters=$this->model->where(['type'=>'email'])->field('id,name')->select();
        $template_id=input('templater_id');
        $row=$this->model->where(['id'=>$template_id])->find();

        $this->assign('row', $row);
        $this->assign('templaters', $templaters);
        $this->assign('ids',$ids);
        if($row){
            return $this->view->fetch('batch_send_email_template');
        }
        return $this->view->fetch('batch_send_email');
    }

    /**
     * 批量发送邮件
     */
    public function batch_send_email($ids = null) {

        $types=input('type');
        $ids = json_decode($ids, true);
        if($types == 'customer'){
            $ids = Customer::where([
                'id' => ['in', $ids],
                'owner_staff_id' => ['in', Staff::getMyStaffIds()]
            ])->column('id');
        }elseif($types == 'contacts'){
            $ids = Contacts::where([
                'id' => ['in', $ids],
                'owner_staff_id' => ['in', Staff::getMyStaffIds()]
            ])->column('id');
        }else{
            $this->error(__('No Results were found'));
        }

        if(empty($ids)){
            $this->error(__('No Results were found'));
        }
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if(empty($params)){
                $this->error(__('Unknown data format'));
            }
            $error = 0;
            $success = 0;
            foreach ($ids as $customer_id) {
                $result = $this->model::sendEmail($customer_id, $params['name'], $params['content'],$types);
                if ($result == true) {
                    $success++;
                } else {
                    $error++;
                }
            }
            $this->success("成功发送{$success}条,发送失败{$error}条");
        }
        $templaters=$this->model->where(['type'=>'email'])->field('id,name')->select();
        $template_id=input('templater_id');
        $row=$this->model->where(['id'=>$template_id])->find();

        $this->assign('row', $row);
        $this->assign('templaters', $templaters);
        $this->assign('ids', json_encode($ids));
        if($row){
            return $this->view->fetch('batch_send_email_template');
        }
        return $this->view->fetch();
    }


    /**
     * 获取模板详情
     */
    public function getDetail(){
        $id=input('id');
        $row=$this->model->where(['type'=>'email','id'=>$id])->find();
        if(empty($row)){
            $this->error('模板不存在');
        }
        $this->success('请求成功','',$row);
    }
    /**
     * 删除
     */
    public function del($ids = null) {
        if ($this->request->isAjax()) {

            $map['id'] = array('in', $ids);
            $result    = $this->model->destroy($map);
            if (!$result) {
                $this->error('删除失败');
            }
            $this->success('删除成功');
        }

        return $this->view->fetch();
    }
}
