<?php

namespace app\admin\controller\qingdong\general;

use app\admin\controller\qingdong\Base;
use app\admin\model\AuthGroup;
use fast\Tree;

/**
 * 常规设置
 */
class Adminconfig extends Base {
	protected $relationSearch = true;
	/**
	 * @var \addons\qingdong\model\AdminConfig
	 */
	protected $model       = null;


	public function _initialize() {
		parent::_initialize();
		$this->model = new \addons\qingdong\model\AdminConfig;
        $this->childrenAdminIds = $this->auth->getChildrenAdminIds($this->auth->isSuperAdmin());
        $this->childrenGroupIds = $this->auth->getChildrenGroupIds($this->auth->isSuperAdmin());

        //角色组
        $groupList = collection(AuthGroup::where('id', 'in', $this->childrenGroupIds)->select())->toArray();
        Tree::instance()->init($groupList);
        $groupdata = [];
        if ($this->auth->isSuperAdmin()) {
            $result = Tree::instance()->getTreeList(Tree::instance()->getTreeArray(0));
            foreach ($result as $k => $v) {
                $groupdata[$v['id']] = $v['name'];
            }
        } else {
            $result = [];
            $groups = $this->auth->getGroups();
            foreach ($groups as $m => $n) {
                $childlist = Tree::instance()->getTreeList(Tree::instance()->getTreeArray($n['id']));
                $temp = [];
                foreach ($childlist as $k => $v) {
                    $temp[$v['id']] = $v['name'];
                }
                $result[__($n['name'])] = $temp;
            }
            $groupdata = $result;
        }
        $this->view->assign('groupdata', $groupdata);
	}


	/**
	 * 查看
	 */
	public function index() {
		//设置过滤方法
        if ($this->request->isAjax()) {
            $row = $this->request->post('row/a', '', 'trim');

            $group = $this->request->post("group/a");
            //过滤不允许的组别,避免越权
            $group = array_intersect($this->childrenGroupIds, $group);
            $department='';
            if ($group) {
                $department=implode(',',$group);
            }

            $this->model::setConfig('auto', $row['auto'] ?? 0, $this->model::TYPE_SEAS);
            $this->model::setConfig('genjing', $row['genjing'] ?? 0, $this->model::TYPE_SEAS);
            $this->model::setConfig('genjing_success', $row['genjing_success'] ?? 0, $this->model::TYPE_SEAS);
            $this->model::setConfig('genjing_day', $row['genjing_day'] ?? 0, $this->model::TYPE_SEAS);
            $this->model::setConfig('chengjiao', $row['chengjiao'] ?? 0, $this->model::TYPE_SEAS);
            $this->model::setConfig('chengjiao_day', $row['chengjiao_day'] ?? 0, $this->model::TYPE_SEAS);
            $this->model::setConfig('is_linkman', $row['is_linkman'] ?? 0, $this->model::TYPE_SEAS);

            $this->model::setConfig('leadauto', $row['leadauto'] ?? 0, $this->model::TYPE_LEAD);
            $this->model::setConfig('lead_day', $row['lead_day'] ?? 0, $this->model::TYPE_LEAD);
            $this->model::setConfig('leadcard', $row['leadcard'] ?? 0, $this->model::TYPE_LEAD);
            $this->model::setConfig('department', $department ?? 0, $this->model::TYPE_LEAD);

            $this->success('设置成功');
        }
		$seas = $this->model->where(['type'=>$this->model::TYPE_SEAS])->column('field,value');
        $seas['auto'] = isset($seas['auto']) ? $seas['auto'] : 0;
        $seas['genjing'] = isset($seas['genjing']) ? $seas['genjing'] : 0;
        $seas['genjing_success'] = isset($seas['genjing_success']) ? $seas['genjing_success'] : 0;
        $seas['genjing_day'] = isset($seas['genjing_day']) ? $seas['genjing_day'] : 0;
        $seas['chengjiao'] = isset($seas['chengjiao']) ? $seas['chengjiao'] : 0;
        $seas['chengjiao_day'] = isset($seas['chengjiao_day']) ? $seas['chengjiao_day'] : 0;
        $seas['is_linkman'] = isset($seas['is_linkman']) ? $seas['is_linkman'] : 0;
        $lead = $this->model->where(['type'=>$this->model::TYPE_LEAD])->column('field,value');
        $lead['leadauto'] = isset($lead['leadauto']) ? $lead['leadauto'] : 0;
        $lead['lead_day'] = isset($lead['lead_day']) ? $lead['lead_day'] : 1;
        $lead['leadcard'] = isset($lead['leadcard']) ? $lead['leadcard'] : 2;
        $lead['department'] = isset($lead['department']) ? $lead['department'] : 0;



        $this->assign('lead',$lead);
		$this->assign('seas',$seas);
		return $this->view->fetch();
	}


	/**
	 *参数配置
	 */
	public function wechat() {
		//设置过滤方法
        if ($this->request->isAjax()) {

            $row = $this->request->post('row/a', '', 'trim');

            $this->model::setConfig('mini_appid', $row['mini_appid'], $this->model::TYPE_WECHAT);
            $this->model::setConfig('mini_secret', $row['mini_secret'], $this->model::TYPE_WECHAT);
            $this->model::setConfig('appid', $row['appid'], $this->model::TYPE_WECHAT);
            $this->model::setConfig('secret', $row['secret'], $this->model::TYPE_WECHAT);
            $this->model::setConfig('map_key', $row['map_key'], $this->model::TYPE_WECHAT);
            $this->model::setConfig('web_url', $row['web_url'], $this->model::TYPE_WECHAT);
            $this->model::setConfig('ding_key', $row['ding_key'], $this->model::TYPE_DING);
            $this->model::setConfig('ding_secret', $row['ding_secret'], $this->model::TYPE_DING);
            $this->model::setConfig('corpid', $row['corpid'], $this->model::TYPE_WECHAT);
            $this->model::setConfig('corpsecret', $row['corpsecret'], $this->model::TYPE_WECHAT);
            $this->model::setConfig('agentid', $row['agentid'], $this->model::TYPE_WECHAT);
            $this->success('设置成功');
        }
		$wechat = $this->model->where([
			'type'     => $this->model::TYPE_WECHAT
		])->column('field,value');
		$this->assign('wechat', $wechat);
        $ding = $this->model->where([
            'type'     => $this->model::TYPE_DING
        ])->column('field,value');
        $this->assign('ding', $ding);
		return $this->view->fetch();
	}


}
