<?php

namespace app\admin\controller\qingdong\general;

use app\admin\controller\qingdong\Base;
use app\common\library\Auth;
use addons\qingdong\model\Form;
use think\Db;
use think\Exception;

/**
 * 字段管理
 */
class Field extends Base {
	protected $relationSearch = true;
	/**
	 * @var \addons\qingdong\model\Field
	 */
	protected $model = null;


	public function _initialize() {
		parent::_initialize();
		$this->model = new \addons\qingdong\model\Field;
	}


	/**
	 * 查看
	 */
	public function index() {
		//设置过滤方法
		$this->request->filter(['strip_tags', 'trim']);
		if ($this->request->isAjax()) {

			$types = $this->model->select();
			$types=collection($types)->toArray();
			$result = array("total" => count($types), "rows" => $types);

			return json($result);
		}

		return $this->view->fetch();
	}


	/**
	 * 修改
	 */
	public function edit($ids = null) {
        $row = $this->model->get($ids);
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                if(empty($params['data'])){
                    $this->error('参数不能为空');
                }

                $data=[];
                $params['data']=json_decode($params['data'],true);

                foreach ($params['data'] as $v){
                    $data[]=isset($v['field']) ?  $v['field'] : $v;
                }

                $save=[
                    'data'=>json_encode($data,JSON_UNESCAPED_UNICODE)
                ];

                $form = Form::where(array('type'=>$row['type']))->find();
                if(!isset($form['data'])){
                    $form['data'] = '';
                }
                if(!$form['data']){
                    $form['data'] = json_encode(array());
                }
                $formdata = json_decode($form['data'],true);
                if(isset($formdata['data']) && $form['data']){
                    foreach($formdata['data'] as $k=>$v){
                        if($v['config']['label'] == $params['name']){
                            $v['config']['content'] = [];
                            if($data){
                                foreach($data as $ks=>$vs){
                                    $v['config']['content'][] = array(
                                        "key" => "",
                                        "value" => $params['name'].($ks+1),
                                        "__label" => $vs,
                                        "__value" => array($params['name'].($ks+1)),
                                        "nodeKey" => 1,
                                        "isEdit" => false,
                                        "label" => $vs,
                                    );
                                }
                            }
                            $formdata['data'][$k] = $v;
                        }
                    }
                    $formdata = json_encode($formdata,JSON_UNESCAPED_UNICODE);
                }

                $result = false;
                Db::startTrans();
                try {
                    $result= $this->model->save($save,['id'=>$ids]);
                    if(isset($formdata) && $formdata){
                        $resForm = Form::where(array('id'=>$form['id']))->update(array('data'=>$formdata));
                    }
                    Db::commit();
                } catch (Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
                if ($result !== false || !$resForm) {
                    $this->success();
                } else {
                    $this->error(__('No rows were inserted'));
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        if(empty($row)){
            $this->error('数据不存在');
        }
        $this->assign('row', $row);

        return $this->view->fetch();
	}


}
