<?php

namespace app\admin\controller\qingdong\general;

use app\common\controller\Backend;

/**
 * 员工业绩比例分割
 *
 * @icon fa fa-circle-o
 */
class Ratio extends Backend
{
    
    /**
     * Ratio模型对象
     * @var \addons\qingdong\model\Ratio
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \addons\qingdong\model\Ratio();

    }


    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */
    /**
     * 添加比例
     * @return string
     */
    public function add(){
        if ($this->request->isAjax()) {
            $data           = $this->request->post('row/a');
            if(empty($data['ratio'])){
                $this->error('');
            }
            $ratio=0;
            $data['ratio']=json_decode($data['ratio'],true);
            foreach ($data['ratio'] as $datum) {
                $ratio+=$datum['ratio'];
            }
            if($ratio != 100){
                $this->error('业绩比例总数必须等于100%');
            }
        }

        return parent::add();
    }

    /**
     * 修改比例
     * @return string
     */
    public function edit($ids=null){
        if ($this->request->isAjax()) {
            $data           = $this->request->post('row/a');
            if(empty($data['ratio'])){
                $this->error('');
            }
            $ratio=0;
            $data['ratio']=json_decode($data['ratio'],true);
            foreach ($data['ratio'] as $datum) {
                $ratio+=$datum['ratio'];
            }
            if($ratio != 100){
                $this->error('业绩比例总数必须等于100%');
            }
        }

        return parent::edit($ids);
    }

}
