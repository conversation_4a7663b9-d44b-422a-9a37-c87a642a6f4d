<?php

namespace app\admin\controller\qingdong\general;

use addons\qingdong\model\Staff;
use app\admin\controller\qingdong\Base;
use addons\qingdong\model\Field;
use think\Db;
use think\Exception;

/**
 * 提醒人管理
 */
class Remind extends Base {
	protected $relationSearch = true;
	/**
	 * @var \addons\qingdong\model\Remind
	 */
	protected $model       = null;


	public function _initialize() {
		parent::_initialize();
		$this->model =new \addons\qingdong\model\Remind();
	}


	/**
	 * 查看
	 */
	public function index() {
		//设置过滤方法
		$this->request->filter(['strip_tags', 'trim']);
		if ($this->request->isAjax()) {

			$reminds = $this->model->where([])->select();
            $reminds = collection($reminds)->toArray();


			$result = array("total" => count($reminds), "rows" => $reminds);

			return json($result);
		}

		return $this->view->fetch();
	}
    /**
     * 添加
     */
    public function add() {
        if ($this->request->isAjax()) {
            $data             = $this->request->post('row/a');
            $reminds = $this->model->where(array('type'=>$data['type']))->find();
            if($reminds){
                $this->error('此类型已经存在,请前去修改');
            }
            $result           = $this->model->save($data);
            if (!$result) {
                $this->error('提交失败');
            }
            $this->success('提交成功');
        }

        return $this->view->fetch();
    }

    /**
     * 修改
     */
    public function edit($ids = null) {
        $map['id'] = $ids;
        if ($this->request->isAjax()) {
            $data   = $this->request->post('row/a');
            $reminds = $this->model->where(array('type'=>$data['type'],'id'=>array('neq',$ids)))->find();
            if($reminds){
                $this->error('此类型已经存在,请前去修改');
            }
            $result = $this->model->save($data, $map);

            if (!$result) {
                $this->error('修改失败');
            }
            $this->success('修改成功');
        }
        $data = $this->model->where($map)->find();
        $this->view->assign("row", $data);

        return $this->view->fetch();
    }

    /**
     * 获取客户列表
     */
    public function getstaff()
    {
        $pageSize = input('pageSize');
        $pageNumber = input('pageNumber');
        $where = [];
        if ($keyValue = $this->request->request("keyValue")) {
            $where['id'] = ['in',explode(',',$keyValue)];
        }
        $name = input('name');
        if (!empty($name)) {
            $where['name'] = ['like', '%' . $name . '%'];
        }
        $customer = Staff::where($where)->field('id,name')->order('id desc')->paginate($pageSize, false, ['page' => $pageNumber]);
        return json(['list' => $customer->items(), 'total' => $customer->total()]);
    }

}
