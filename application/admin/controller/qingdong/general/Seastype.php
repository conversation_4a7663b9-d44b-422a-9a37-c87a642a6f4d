<?php

namespace app\admin\controller\qingdong\general;

use addons\qingdong\model\StaffRule;
use app\admin\controller\qingdong\Base;
use addons\qingdong\model\Form;
use app\common\model\Area;
use think\Db;
use think\Exception;

/**
 * 公海设置
 * @icon fa fa-gears
 */
class Seastype extends Base
{
    /**
     * @var \addons\qingdong\model\Seastype
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \addons\qingdong\model\Seastype();
    }
    public function index()
    {
        $types=$this->model->select();
        if (empty($types)) {//没有默认公海 则添加
            $insert = [
                'id' => 1,
                'name' => '默认公海',
                'remarks' => '客户不满足其他公海规则时，会移入当前公海'
            ];
            $this->model->insert($insert);
            $staff_rule = ['type' => 'data', 'pid' => 1, 'title' => '默认公海', 'name' => 1];
            StaffRule::create($staff_rule);
        }
        return parent::index();
    }

    /**
     * 添加公海设置
     */
    public function add()
    {
        if ($this->request->isAjax()) {
            $data = $this->request->post('row/a');
            Db::startTrans();
            try {
                $data['rules'] = json_encode($data['rules'],JSON_UNESCAPED_UNICODE);
                $this->model->allowField(true)->save($data);
                $lastId = $this->model->id;
                $staff_rule = [
                    'type' => 'data',
                    'pid' => 1,
                    'title' => $data['name'],
                    'name' => $lastId
                ];
                StaffRule::create($staff_rule);
                Db::commit();
            } catch (Exception $e) {
                Db::rollback();
                $this->error($e->getMessage());
            }
            $this->success('提交成功');
        }

        $customer=Form::getDataValue('customer');
        $this->view->assign('form_data',$customer);
        return $this->view->fetch();
    }


    /**
     * 修改公海设置
     */
    public function edit($ids = null)
    {
        $map['id'] = $ids;
        $row = $this->model->where($map)->find();
        if ($this->request->isAjax()) {
            $data = $this->request->post('row/a');
            Db::startTrans();
            try {
                $data['rules'] = json_encode($data['rules'],JSON_UNESCAPED_UNICODE);
                $this->model->save($data, $map);
                $ruleModel = new StaffRule();
                $rule = $ruleModel::where(['type' => 'data', 'pid' => 1, 'name' => $ids])->find();
                if ($rule) {
                    $ruleModel->save(['title' => $data['name']], ['id' => $rule['id']]);
                }
                Db::commit();
            } catch (Exception $e) {
                Db::rollback();
                $this->error($e->getMessage());
            }
            $this->success('修改成功');
        }
        $row['rules']=json_decode($row['rules'],true);
        $customer = Form::getDataValue('customer');

        $this->view->assign('form_data', $customer);
        $this->view->assign("row", $row);

        return $this->view->fetch();
    }

    /**
     * 获取区域
     */
    public function getarea()
    {
        $pageSize = input('pageSize');
        $pageNumber = input('pageNumber');
        $where = [];
        if ($keyValue = $this->request->request("keyValue")) {
            $where['id'] = ['in',$keyValue];
        }
        $name = input('name');
        if (!empty($name)) {
            $where['name'] = ['like', '%' . $name . '%'];
        }
        $where['pid'] = 0;
        $contacts = Area::where($where)->field('id,shortname as name')->order('id desc')->paginate($pageSize, false, ['page' => $pageNumber]);
        $item = $contacts->items();

        return json(['list' => $item, 'total' => $contacts->total()]);
    }
}