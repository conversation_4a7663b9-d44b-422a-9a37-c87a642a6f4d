<?php

namespace app\admin\controller\qingdong\general;

use app\common\controller\Backend;
use addons\qingdong\model\Field;
use think\Db;
use think\Exception;

/**
 * 通知模板
 */
class NoticeTemplate extends Backend {
    protected $relationSearch = true;
    /**
     * @var \addons\qingdong\model\NoticeTemplate
     */
    protected $model       = null;


    public function _initialize() {
        parent::_initialize();
        $this->model =new \addons\qingdong\model\NoticeTemplate;
    }


    /**
     * 查看
     */
    public function index() {
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            $list = $this->model->where([])->paginate();
            $result = array("total" => $list->total(), "rows" => $list->items());
            return json($result);
        }
        return $this->view->fetch();
    }

    /**
     * 修改通知模板
     */
    public function edit($id = null) {
        if ($this->request->isPost()) {
            $data = input('row/a');

            if (empty($id) || empty($data)) {
                $this->error('参数错误');
            }
            Db::startTrans();
            try {
                $newdata = [
                    'template_id'=>$data['template_id'],
                    'first'=>$data['first'],
                    'remark'=>$data['remark'],
                    'remark_color'=>$data['remark_color'],
                ];
                foreach($data as $k=>$v){
                    if($k=='keyword1_key' && $data['keyword1_key'] && $data['keyword1']){
                        $newdata[$data['keyword1_key']] =$data['keyword1'];
                    }
                    if($k=='keyword2_key' && $data['keyword2_key'] && $data['keyword2']){
                        $newdata[$data['keyword2_key']] =$data['keyword2'];
                    }
                    if($k=='keyword3_key' && $data['keyword3_key'] && $data['keyword3']){
                        $newdata[$data['keyword3_key']] =$data['keyword3'];
                    }
                    if($k=='keyword4_key' && $data['keyword4_key'] && $data['keyword4']){
                        $newdata[$data['keyword4_key']] =$data['keyword4'];
                    }
                    if($k=='keyword5_key' && $data['keyword5_key'] && $data['keyword5']){
                        $newdata[$data['keyword5_key']] =$data['keyword5'];
                    }
                }

                $this->model->save(['data' => json_encode($newdata,JSON_UNESCAPED_UNICODE)], ['id' => $id]);
                Db::commit();
            } catch (Exception $e) {
                Db::rollback();
                $this->error($e->getMessage());
            }
            $this->success('设置成功');
        }
        $row=$this->model->get($id);

        if(empty($row)){
            $this->error('数据不存在');
        }
        $this->view->assign('variable',$this->model->getVariable($row['type']));
        $this->view->assign('row',$row);
        return $this->view->fetch();
    }
    /**
     * 修改通知模板
     */
    public function edit_enterprise($id = null) {
        if ($this->request->isPost()) {
            $data = input('row/a');

            if (empty($id) || empty($data)) {
                $this->error('参数错误');
            }
            Db::startTrans();
            try {
                $this->model->save(['enterprise_data' => json_encode($data,JSON_UNESCAPED_UNICODE)], ['id' => $id]);
                Db::commit();
            } catch (Exception $e) {
                Db::rollback();
                $this->error($e->getMessage());
            }
            $this->success('设置成功');
        }
        $row=$this->model->get($id);
        if(empty($row)){
            $this->error('数据不存在');
        }
        $this->view->assign('variable',$this->model->getVariable($row['type']));
        $this->view->assign('row',$row);
        return $this->view->fetch();
    }


}
