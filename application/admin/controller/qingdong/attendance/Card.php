<?php

namespace app\admin\controller\qingdong\attendance;

use addons\qingdong\model\AttendanceCard;
use addons\qingdong\model\Staff;
use app\common\controller\Backend;

/**
 * 补卡记录
 */
class Card extends Backend {
    public function _initialize() {
        parent::_initialize();
        $this->model = new AttendanceCard();
    }


    /**
     * 补卡记录
     */
    public function index() {
        $this->request->filter(['strip_tags']);
        if ($this->request->isAjax()) {
            //0:全部 1：我的 2：下属的
            $type = input('type',0);
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            switch($type){
                case 1:
                    $staff = Staff::info();
                    $wheres['create_staff_id'] =  $staff->id;
                    break;
                case 2:
                    $wheres['create_staff_id'] =  array('in',Staff::getLowerStaffId());
                    break;
                default:
                    $wheres['create_staff_id'] =  array('in',Staff::getMyStaffIds());
                    break;

            }
            $wheres['type'] = 0;//上下班打卡
            $list   = $this->model->where($where)->where($wheres)->with(['createStaff'])->order($sort, $order)->paginate($limit);
            $row    = $list->items();
            $result = array("total" => $list->total(), "rows" => $row);

            return json($result);
        }
        return $this->view->fetch();
    }



}