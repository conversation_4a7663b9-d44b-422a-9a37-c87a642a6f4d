<?php

namespace app\admin\controller\qingdong\customer;

use addons\qingdong\model\AdminConfig;
use addons\qingdong\model\File;
use addons\qingdong\model\FormField;
use addons\qingdong\model\Staff;
use app\admin\controller\qingdong\Base;
use addons\qingdong\model\ContactsOther;
use addons\qingdong\model\Form;
use addons\qingdong\model\OperationLog;
use addons\qingdong\model\Record;
use addons\qingdong\model\Customer;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use PhpOffice\PhpSpreadsheet\Reader\Csv;
use PhpOffice\PhpSpreadsheet\Reader\Xls;
use PhpOffice\PhpSpreadsheet\Reader\Xlsx;
use PhpOffice\PhpSpreadsheet\RichText\RichText;
use think\Db;
use think\Exception;
use app\admin\controller\qingdong\synchronizate\Ku;

/**
 * 联系人管理
 * 操作文档：https://doc.fastadmin.net/qingdong
 * 软件介绍：https://www.fastadmin.net/store/qingdong.html
 * 售后微信：qingdong_crm
 */
class Contacts extends Base {
	protected $relationSearch = true;
	protected $searchFields   = 'id,username,nickname';
	/**
	 * @var \addons\qingdong\model\Contacts
	 */
	protected $model = null;


	public function _initialize() {
		parent::_initialize();
		$this->model = new \addons\qingdong\model\Contacts;
	}


	/**
	 * 查看
	 */
	public function index() {
		//设置过滤方法
		$this->request->filter(['strip_tags', 'trim']);
		if ($this->request->isAjax()) {
			//如果发送的来源是Selectpage，则转发到Selectpage
			if ($this->request->request('keyField')) {
				return $this->selectpage();
			}
            //0:全部 1：我负责的 2：下属负责的 3：今日待跟进 4:今日已跟进 5:从未跟进的
            $type = input('type',0);
			list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            switch($type){
                case 1:
                    $staff = Staff::info();
                    $wheres['owner_staff_id'] =  $staff->id;
                    break;
                case 2:
                    $wheres['owner_staff_id'] =  array('in',Staff::getLowerStaffId());
                    break;
                case 3:
                    $start = date('Y-m-d 00:00:00');
                    $end = date('Y-m-d 23:59:59');
                    $record = collection(Record::where(array('relation_type'=>2,'next_time'=>array(array('egt',$start),array('elt',$end))))->field("id,relation_id")->select())->toArray();
                    $relationId = [];
                    foreach($record as $k=>$v){
                        $whereRe['id'] = array('gt',$v['id']);
                        $whereRe['relation_id'] = $v['relation_id'];
                        $recordData = Record::where($whereRe)->count();
                        if($recordData == 0){
                            $relationId[] = $v['relation_id'];
                        }

                    }
                    $wheres['id'] =  array('in',$relationId);
                    $staff = Staff::info();
                    $wheres['owner_staff_id'] =  $staff->id;
                    break;
                case 4:
                    $start = date('Y-m-d 00:00:00');
                    $end = date('Y-m-d 23:59:59');
                    $record = collection(Record::where(array('relation_type'=>2,'next_time'=>array(array('egt',$start),array('elt',$end))))->field("id,relation_id")->select())->toArray();
                    $relationId = [];
                    foreach($record as $k=>$v){
                        $whereRe['id'] = array('gt',$v['id']);
                        $whereRe['relation_id'] = $v['relation_id'];
                        $recordData = Record::where($whereRe)->count();
                        if($recordData >=1){
                            $relationId[] = $v['relation_id'];
                        }

                    }
                    $wheres['id'] =  array('in',$relationId);
                    $staff = Staff::info();
                    $wheres['owner_staff_id'] =  $staff->id;
                    break;
                case 5:
                    $record = collection(Record::where(array('relation_type'=>2,'next_time'=>array('neq','')))->column('relation_id'))->toArray();
                    $wheres['id'] =  array('not in',$record);
                    $staff = Staff::info();
                    $wheres['owner_staff_id'] =  $staff->id;
                    break;
                default:
                    $wheres['owner_staff_id'] =  array('in',Staff::getMyStaffIds());
                    break;

            }

			$list = $this->model->with(['ownerStaff'])
                ->where($wheres)
                ->where($where)->with(['customer'])->order($sort, $order)->paginate($limit);

			$result = array("total" => $list->total(), "rows" => $list->items());

			return json($result);
		}

        $field = FormField::getFields(FormField::CONTACTS_TYPE);
        $this->assignconfig('fields', $field);
		return $this->view->fetch();
	}


	/**
	 * 添加
	 */
	public function add() {
		if ($this->request->isPost()) {
			$params = $this->request->post("row/a");
			if ($params) {
				$params = $this->preExcludeFields($params);
                // 表单验证
                if (($result = $this->qingdongValidate($params, 'Contacts', 'create')) !== true) {
                    $this->error($result);
                }

                $result = FormField::checkFields(FormField::CONTACTS_TYPE, $params);
                if ($result !== true) {
                    $this->error($result);
                }
				$result = false;
				Db::startTrans();
				try {
                    $is_linkman=AdminConfig::getConfigValue('is_linkman',AdminConfig::TYPE_SEAS);
                    //联系人手机号是否允许重复
                    if($is_linkman == 1){//不同客户的联系人是否允许重复
                        $contactsFind = $this->model->where(array('customer_id'=>$params['customer_id'],'mobile'=>$params['mobile']))->find();
                        if($contactsFind){
                            throw new Exception('手机号已存在');
                        }
                    }else{
                        $contactsFind = $this->model->where(array('mobile'=>$params['mobile']))->find();
                        if($contactsFind){
                            throw new Exception('手机号已存在');
                        }
                    }

                    if(isset($params['email'])){
                        $contactsFind = $this->model->where(array('customer_id'=>$params['customer_id'],'email'=>$params['email']))->find();
                        if($contactsFind){
                            throw new Exception('邮箱已存在');
                        }
                    }
                    $params = Form::updateFormParams(Form::CONTACTS_TYPE, $params);

					$result             = $this->model::createContacts($params);
					Db::commit();
				} catch (Exception $e) {
					Db::rollback();
					$this->error($e->getMessage());
				}
				if ($result !== false) {
					$this->success();
				} else {
					$this->error(__('No rows were inserted'));
				}
			}
			$this->error(__('Parameter %s can not be empty', ''));
		}
		$staffs = Customer::getList();
        $customer_id=input('customer_id');
        $customer= Customer::get($customer_id);

		$this->assign('staffs', $staffs);
		$this->assign('customer_id', input('customer_id'));
		$this->assign('customer', $customer);
		$this->assign('form_data', Form::getDataValue(Form::CONTACTS_TYPE));


		return $this->view->fetch();
	}


	/**
	 * 修改
	 */
	public function edit($ids = null) {
		$row = $this->model->get($ids);
		if ($this->request->isPost()) {
			$params = $this->request->post("row/a");
			if ($params) {
				$params = $this->preExcludeFields($params);
                // 表单验证
                if (($result = $this->qingdongValidate($params, 'Contacts', 'create')) !== true) {
                    $this->error($result);
                }
                $result = FormField::checkFields(FormField::CONTACTS_TYPE, $params,$ids);
                if ($result !== true) {
                    $this->error($result);
                }
				$result = false;
				Db::startTrans();
				try {
                    $is_linkman=AdminConfig::getConfigValue('is_linkman',AdminConfig::TYPE_SEAS);
                    if($is_linkman == 1){//不同客户的联系人是否允许重复
                        $contactsFind = $this->model->where(array('id'=>['neq',$row['id']],'customer_id'=>$row['customer_id'],'mobile'=>$params['mobile']))->find();
                        if($contactsFind){
                            throw new Exception('手机号已存在');
                        }
                    }else{
                        $contactsFind = $this->model->where(array('id'=>['neq',$row['id']],'mobile'=>$params['mobile']))->find();
                        if($contactsFind){
                            throw new Exception('手机号已存在');
                        }
                    }
                    if(isset($params['email'])){
                        $contactsFind = $this->model->where(array('id'=>['neq',$row['id']],'customer_id'=>$row['customer_id'],'email'=>$params['email']))->find();
                        if($contactsFind){
                            throw new Exception('邮箱已存在');
                        }
                    }
                    $params = Form::updateFormParams(Form::CONTACTS_TYPE, $params);

					$params['id']=$ids;
					$result = $row::updateContacts($params);
					Db::commit();
				} catch (Exception $e) {
					Db::rollback();
					$this->error($e->getMessage());
				}
				if ($result !== false) {
					$this->success();
				} else {
					$this->error(__('No rows were inserted'));
				}
			}
			$this->error(__('Parameter %s can not be empty', ''));
		}
		$staffs = \addons\qingdong\model\Customer::getList();
		$this->assign('staffs', $staffs);

        $row=$row->toArray();
        $row=ContactsOther::getOther($row);
		$this->assign('form_data', Form::getDataValue(Form::CONTACTS_TYPE,$row));
		$this->assign('row', $row);

		return $this->view->fetch();
	}

    /**
     * 联系人详情
     */
	public function detail($ids = null) {
		$row = $this->model->with(['customer'])->where(['id' => $ids])->find();
		if(empty($row)){
			$this->error('信息不存在');
		}
		//跟进记录
		$this->assign('records', Record::getList(Record::CONTACTS_TYPE, $ids));
		//操作记录
		$this->assign('operation_log', OperationLog::getList(OperationLog::CONTACTS_TYPE, $ids));
        $row=$row->toArray();
        $row=ContactsOther::getOther($row);

		$this->assign('form_data', Form::getDataValue(Form::CONTACTS_TYPE,$row));
		$this->assign('row', $row);
		$this->assign('ids', $ids);
        $this->assignconfig("idinfo", ['id' => $ids]);
		return $this->view->fetch();
	}


    /**
     * 获取附件记录
     */
	public function get_file($ids = null) {
		list($where, $sort, $order, $offset, $limit) = $this->buildparams();
		$list = \addons\qingdong\model\ContactsFile::where(['contacts_id' => $ids])->with(['file'])->field('file_id')->paginate($limit);

		$result = array("total" => $list->total(), "rows" => $list->items());

		return json($result);
	}


	/**
	 * 删除
	 */
	public function del($ids = "") {
		if (!$this->request->isPost()) {
			$this->error(__("Invalid parameters"));
		}
		$ids                 = $ids ? $ids : $this->request->post("ids");
		$row                 = $this->model->get($ids);
		$this->modelValidate = true;
		if (!$row) {
			$this->error(__('No Results were found'));
		}
		$map['id'] = $ids;
		$result    = $this->model->destroy($map);
		if (!$result) {
			$this->error('删除失败');
		}
		$this->success();
	}

    /**
     * 导入客户
     * @return string|void
     */
    public function import()
    {
        set_time_limit(0);
        if ($this->request->isPost()) {

            $file = $this->request->request('file');
            if (!$file) {
                $this->error(__('Parameter %s can not be empty', 'file'));
            }
            $filePath = ROOT_PATH . 'public' . $file;
            if (!is_file($filePath)) {
                $this->error(__('No results were found'));
            }
            //实例化reader
            $ext = pathinfo($filePath, PATHINFO_EXTENSION);
            if (!in_array($ext, ['csv', 'xls', 'xlsx'])) {
                $this->error(__('Unknown data format'));
            }
            if ($ext === 'csv') {
                $file = fopen($filePath, 'r');
                $filePath = tempnam(sys_get_temp_dir(), 'import_csv');
                $fp = fopen($filePath, "w");
                $n = 0;
                while ($line = fgets($file)) {
                    $line = rtrim($line, "\n\r\0");
                    $encoding = mb_detect_encoding($line, ['utf-8', 'gbk', 'latin1', 'big5']);
                    if ($encoding != 'utf-8') {
                        $line = mb_convert_encoding($line, 'utf-8', $encoding);
                    }
                    if ($n == 0 || preg_match('/^".*"$/', $line)) {
                        fwrite($fp, $line . "\n");
                    } else {
                        fwrite($fp, '"' . str_replace(['"', ','], ['""', '","'], $line) . "\"\n");
                    }
                    $n++;
                }
                fclose($file) || fclose($fp);

                $reader = new Csv();
            } elseif ($ext === 'xls') {
                $reader = new Xls();
            } else {
                $reader = new Xlsx();
            }
            if (!$PHPExcel = $reader->load($filePath)) {
                $this->error(__('Unknown data format'));
            }
            $currentSheet = $PHPExcel->getSheet(0);  //读取文件中的第一个工作表

            $allColumn = $currentSheet->getHighestDataColumn(); //取得最大的列号
            $allRow = $currentSheet->getHighestRow(); //取得一共有多少行

            $maxColumnNumber = Coordinate::columnIndexFromString($allColumn);
            //开始读取数据
            $fields = [];
            for ($currentRow = 1; $currentRow <= 1; $currentRow++) {
                for ($currentColumn = 1; $currentColumn <= $maxColumnNumber; $currentColumn++) {
                    $val = $currentSheet->getCellByColumnAndRow($currentColumn, $currentRow)->getValue();
                    $fields[$currentRow][] = $val;
                    if ($val instanceof RichText) {//富文本转换字符串
                        $val = $val->__toString();
                    }
                    $values[] = is_null($val) ? '' : trim($val);
                }
            }
            if (!isset($fields[1])) {
                $this->error('导入文件第一行没有数据');
            }
            $lastid = $this->model->withTrashed()->order('id desc')->value('id');

            $lastid = $lastid + 5;//防止重复

            $contactsRow = [];
            $errorInfo = [];

            $fieldnames = FormField::where(['types' => FormField::CONTACTS_TYPE])->column('field', 'name');
            if(!$fieldnames){
                $this->error('请在系统管理->字段管理中保存联系人管理表单生成联系人导入字段');
            }
            $customerNames=Customer::where([])->column('id,owner_staff_id','name');
            $fn = [];
            for ($currentRow = 1; $currentRow <= 1; $currentRow++) {
                $values = [];
                for ($currentColumn = 1; $currentColumn <= $maxColumnNumber; $currentColumn++) {
                    $val = $currentSheet->getCellByColumnAndRow($currentColumn, $currentRow)->getValue();
                    $values[] = is_null($val) ? NULL : $val;
                }
                foreach ($values as $l) {

                    $fn[] = $fieldnames[$l] ?? '';
                }
            }
            for ($currentRow = 2; $currentRow <= $allRow; $currentRow++) {
                $values = [];
                for ($currentColumn = 1; $currentColumn <= $maxColumnNumber; $currentColumn++) {
                    $val = $currentSheet->getCellByColumnAndRow($currentColumn, $currentRow)->getValue();
                    if ($val instanceof RichText) {//富文本转换字符串
                        $val = $val->__toString();
                    }
                    $values[] = is_null($val) ? NULL : trim($val);
                }
                $lastid++;
                $addContacts = ['id' => $lastid];
                $customer=isset($customerNames[$values[0]])?$customerNames[$values[0]]:0;
                if(empty($customer)){
                    $errorInfo[] = "第{$currentRow}行，客户名称不存在;";
                    continue;
                }
                $addContacts['customer_id']=$customer['id'];
                $addContacts['owner_staff_id']=$customer['owner_staff_id'];
                $addContacts['create_staff_id']=$customer['owner_staff_id'];
                foreach ($values as $kv => $value) {
                    if (!isset($fn[$kv]) || empty($fn[$kv])) {
                        continue;
                    }
                    $addContacts[$fn[$kv]] = $value;
                }
                if (empty($addContacts['name'])) {
                    $errorInfo[] = "第{$currentRow}行，联系人名称不能为空;";
                    continue;
                }
                if (empty($addContacts['mobile'])) {
                    $errorInfo[] = "第{$currentRow}行，联系人手机号不能为空;";
                    continue;
                }
                $contactsRow[] = $addContacts;
            }
            if (!empty($errorInfo)) {
                $this->error(implode(',', $errorInfo));
            }
            Db::startTrans();
            try {
                $this->model::importContacts($contactsRow);
                Db::commit();
            } catch (Exception $e) {
                Db::rollback();
                $this->error($e->getMessage());
            }
            $this->success('导入成功');
        }
        $this->assign('staffs', Staff::getList());
        return $this->view->fetch();
    }

    /**
     * 模板
     */
    public function template()
    {
        $title = [
            '客户名称'
        ];
        $contractData = Form::getDataValue(Form::CONTACTS_TYPE);

        foreach ($contractData as $val) {
            $title[] = $val['config']['label'];
        }

        $file = export_excel($title, [], '联系人');
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename=' . $file['fileName']);
        header('Cache-Control: max-age=0');

        $obj = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
        // 以下内容是excel文件的信息描述信息
        $obj->getProperties()->setTitle('导出文件'); //设置标题
        $obj->setActiveSheetIndex(0);
        $obj->getActiveSheet()->setTitle('导出文件');
        /* 循环读取每个单元格的数据 */
        $a = 'A';
        $currentSheet = $obj->getActiveSheet();
        foreach ($title as $key => $value) {
            //读取工作表1
            // 设置第一行加粗
            $obj->getActiveSheet()->getStyle($a . '1')->getFont()->setBold(true);
            //这里是设置单元格的内容
            $currentSheet->getCell($a . '1')->setValue($value);
            $a++;
        }

        $PHPWriter = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($obj);

        $PHPWriter->save('php://output');
    }

    /**
     * 导出信息
     */
    public function export()
    {
        $this->request->filter(['strip_tags', 'trim']);
        $ids = input('ids');
        $isall = input('isall');
        $wheres = array();
        //导出其中几条
        if (isset($ids)) {
            $wheres['id'] = array('in', $ids);
        }
        //导出全部
        if (isset($isall)) {
            if ($isall == 3) {
                unset($wheres['id']);
            }
        }
        //0:全部 1：我负责的 2：下属负责的 3：今日待跟进 4:今日已跟进 5:从未跟进的
        $type = input('type',0);
        list($where, $sort, $order, $offset, $limit) = $this->buildparams();
        switch($type){
            case 1:
                $staff = Staff::info();
                $wheres['owner_staff_id'] =  $staff->id;
                break;
            case 2:
                $wheres['owner_staff_id'] =  array('in',Staff::getLowerStaffId());
                break;
            case 3:
                $start = date('Y-m-d 00:00:00');
                $end = date('Y-m-d 23:59:59');
                $record = collection(Record::where(array('relation_type'=>2,'next_time'=>array(array('egt',$start),array('elt',$end))))->field("id,relation_id")->select())->toArray();
                $relationId = [];
                foreach($record as $k=>$v){
                    $whereRe['id'] = array('gt',$v['id']);
                    $whereRe['relation_id'] = $v['relation_id'];
                    $recordData = Record::where($whereRe)->count();
                    if($recordData == 0){
                        $relationId[] = $v['relation_id'];
                    }

                }
                $wheres['id'] =  array('in',$relationId);
                $staff = Staff::info();
                $wheres['owner_staff_id'] =  $staff->id;
                break;
            case 4:
                $start = date('Y-m-d 00:00:00');
                $end = date('Y-m-d 23:59:59');
                $record = collection(Record::where(array('relation_type'=>2,'next_time'=>array(array('egt',$start),array('elt',$end))))->field("id,relation_id")->select())->toArray();
                $relationId = [];
                foreach($record as $k=>$v){
                    $whereRe['id'] = array('gt',$v['id']);
                    $whereRe['relation_id'] = $v['relation_id'];
                    $recordData = Record::where($whereRe)->count();
                    if($recordData >=1){
                        $relationId[] = $v['relation_id'];
                    }

                }
                $wheres['id'] =  array('in',$relationId);
                $staff = Staff::info();
                $wheres['owner_staff_id'] =  $staff->id;
                break;
            case 5:
                $record = collection(Record::where(array('relation_type'=>2,'next_time'=>array('neq','')))->column('relation_id'))->toArray();
                $wheres['id'] =  array('not in',$record);
                $staff = Staff::info();
                $wheres['owner_staff_id'] =  $staff->id;
                break;
            default:
                $wheres['owner_staff_id'] =  array('in',Staff::getMyStaffIds());
                break;

        }

        $list = $this->model->with([
            'ownerStaff','contactsOther','customer'
        ])->where($where)->where($wheres)->order($sort, $order)->select();
        $list = collection($list)->toArray();

        if (!$list) {
            $this->error('无导出数据');
        }
        $title = [
            '序号',
            '客户名称',
            '归属人',
            '创建时间',
            '下次联系时间',
        ];

        $dataValue = Form::getDataValue(Form::CONTACTS_TYPE);

        foreach ($dataValue as $val) {
            $title[] = $val['config']['label'];
        }
        foreach ($list as $k => $v) {
            if($v['contacts_other']){//其他客户
                $other=$v['contacts_other']['otherdata'];
                $other=json_decode($other,true);
                $v = array_merge($v, $other);
            }
            $field = array(
                $v['id'],
                $v['customer']['name'],

                $v['owner_staff']['name'],
                date('Y-m-d H:i:s',$v['createtime']),
                $v['next_time']
            );
            foreach ($dataValue as $val) {
                if ($val['component'] == 'uploadImage' || $val['component'] == 'uploadFile') {
                    $field[] = $v[$val['id'] . '_str'] ?? '';
                } else {
                    $field[] = ($v[$val['id']] ?? '');
                }
            }
            $data[] = $field;
        }
        $file = export_excel($title, $data, '联系人');
        if ($file['filePath']) {
            $this->success('导出成功', '', $file);
        }
        $this->error('导出失败');
    }
}
