<?php

namespace app\admin\controller\qingdong\customer;

use addons\qingdong\model\File;
use addons\qingdong\model\FormField;
use addons\qingdong\model\Staff;
use app\admin\controller\qingdong\Base;
use addons\qingdong\model\Form;
use addons\qingdong\model\LeadsOther;
use addons\qingdong\model\OperationLog;
use addons\qingdong\model\Record;
use addons\qingdong\model\Customer;
use addons\qingdong\model\Contacts;
use addons\qingdong\model\LeadsFile;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Reader\Csv;
use PhpOffice\PhpSpreadsheet\Reader\Xls;
use PhpOffice\PhpSpreadsheet\Reader\Xlsx;
use PhpOffice\PhpSpreadsheet\RichText\RichText;
use think\Db;
use think\Exception;

/**
 * 线索
 * 操作文档：https://doc.fastadmin.net/qingdong
 * 软件介绍：https://www.fastadmin.net/store/qingdong.html
 * 售后微信：qingdong_crm
 */
class Leads extends Base
{
    protected $relationSearch = true;
    protected $searchFields = 'id,name';
    /**
     * @var \addons\qingdong\model\Leads
     */
    protected $model = null;


    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \addons\qingdong\model\Leads;
    }


    /**
     * 查看
     */
    public function index()
    {
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //0:全部 1：我负责的 2：下属负责的 3：今日待跟进 4:今日已跟进 5:从未跟进的
            $type = input('type', 0);
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            switch ($type) {
                case 1:
                    $staff = Staff::info();
                    $wheres['owner_staff_id'] = $staff->id;
                    break;
                case 2:
                    $wheres['owner_staff_id'] = array('in', Staff::getLowerStaffId());
                    break;
                case 3:
                    $start = date('Y-m-d 00:00:00');
                    $end = date('Y-m-d 23:59:59');
                    $record = collection(Record::where(array('relation_type' => 1, 'next_time' => array(array('egt', $start), array('elt', $end))))->field("id,relation_id")->select())->toArray();
                    $relationId = [];
                    foreach ($record as $k => $v) {
                        $whereRe['id'] = array('gt', $v['id']);
                        $whereRe['relation_id'] = $v['relation_id'];
                        $recordData = Record::where($whereRe)->count();
                        if ($recordData == 0) {
                            $relationId[] = $v['relation_id'];
                        }

                    }
                    $wheres['id'] = array('in', $relationId);
                    $staff = Staff::info();
                    $wheres['owner_staff_id'] = $staff->id;
                    break;
                case 4:
                    $start = date('Y-m-d 00:00:00');
                    $end = date('Y-m-d 23:59:59');
                    $record = collection(Record::where(array('relation_type' => 1, 'next_time' => array(array('egt', $start), array('elt', $end))))->field("id,relation_id")->select())->toArray();
                    $relationId = [];
                    foreach ($record as $k => $v) {
                        $whereRe['id'] = array('gt', $v['id']);
                        $whereRe['relation_id'] = $v['relation_id'];
                        $recordData = Record::where($whereRe)->count();
                        if ($recordData >= 1) {
                            $relationId[] = $v['relation_id'];
                        }

                    }
                    $wheres['id'] = array('in', $relationId);
                    $staff = Staff::info();
                    $wheres['owner_staff_id'] = $staff->id;
                    break;
                case 5:
                    $record = collection(Record::where(array('relation_type' => 4))->column('relation_id'))->toArray();
                    $wheres['id'] = array('not in', $record);
                    $staff = Staff::info();
                    $wheres['owner_staff_id'] = $staff->id;
                    break;
                default:
                    $wheres['owner_staff_id'] = array('in', Staff::getMyStaffIds());
                    break;

            }
            $wheres['is_transform'] = 0;
            $list = $this->model->where($where)->where($wheres)->order($sort, $order)->with(['ownerStaff'])->paginate($limit);

            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }

        $field = FormField::getFields(FormField::LEADS_TYPE);
        $this->assignconfig('fields', $field);
        return $this->view->fetch();
    }


    /**
     * 添加
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                $params = $this->preExcludeFields($params);
                // 表单验证
                if (($result = $this->qingdongValidate($params, 'Leads', 'create')) !== true) {
                    $this->error($result);
                }

                $result = FormField::checkFields('leads', $params);
                if ($result !== true) {
                    $this->error($result);
                }
                $result = false;
                Db::startTrans();
                try {
                    $params = Form::updateFormParams('leads', $params);

                    $result = $this->model::createLeads($params);
                    Db::commit();
                } catch (Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
                if ($result !== false) {
                    $this->success();
                } else {
                    $this->error(__('No rows were inserted'));
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $staffs = Staff::getList($this->_staff->id);

        $this->assign('staffs', $staffs);
        $this->view->assign('form_data', Form::getDataValue('leads'));
        return $this->view->fetch();
    }


    /**
     * 修改
     */
    public function edit($ids = null)
    {
        $row = $this->model->get($ids);
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                $params = $this->preExcludeFields($params);
                // 表单验证
                if (($result = $this->qingdongValidate($params, 'Leads', 'create')) !== true) {
                    $this->error($result);
                }
                $result = FormField::checkFields('leads', $params, $ids);
                if ($result !== true) {
                    $this->error($result);
                }
                $result = false;
                Db::startTrans();
                try {
                    $params = Form::updateFormParams('leads', $params);
                    $params['id'] = $ids;
                    $result = $this->model::updateLeads($params);
                    Db::commit();
                } catch (Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
                if ($result !== false) {
                    $this->success();
                } else {
                    $this->error(__('No rows were inserted'));
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $row = $row->toArray();
        $row = LeadsOther::getOther($row);
        $this->assign('row', $row);

        $this->view->assign('form_data', Form::getDataValue('leads', $row));
        return $this->view->fetch();
    }


    /**
     * 线索详情
     */
    public function detail($ids = null)
    {
        $row = $this->model->with(['create_staff', 'owner_staff'])->where(['id' => $ids])->find();

        //跟进记录
        $this->assign('records', Record::getList(Record::LEADS_TYPE, $ids));
        //操作记录
        $this->assign('operation_log', OperationLog::getList(OperationLog::LEADS_TYPE, $ids));


        $row = $row->toArray();
        $row = LeadsOther::getOther($row);
        $this->view->assign('form_data', Form::getDataValue('leads', $row));
        $this->assign('row', $row);
        $this->assign('ids', $ids);
        $this->assignconfig("idinfo", ['id' => $ids]);
        return $this->view->fetch();
    }

    /**
     * 转为客户
     */
    public function convert_customer($ids = null)
    {
        $row = $this->model->get($ids);
        if (empty($row)) {
            $this->error('线索不存在');
        }
        try {
            $customer = [
                'leads_id' => $row['id'],
                'name' => $row['name'],
                'level' => $row['level'],
                'industry' => $row['industry'],
                'remarks' => $row['remarks'],
                'source' => $row['source'],
                'follow' => $row['follow'],
                'address' => $row['address'],
                'address_detail' => $row['address_detail'],
                'create_staff_id' => $row['owner_staff_id'],
                'owner_staff_id' => $row['owner_staff_id'],
            ];
            //线索转化
            $leads_id = '';
            if (isset($customer['leads_id'])) {
                $leads_id = $customer['leads_id'];
            }
            $customer_id = Customer::createCustomer($customer, $leads_id);
            $contracts = [
                'customer_id' => $customer_id,
                'is_major' => 1,
                'name' => $row['name'],
                'mobile' => $row['mobile'],
                'remarks' => $row['remarks'],
                'create_staff_id' => $row['owner_staff_id'],
                'owner_staff_id' => $row['owner_staff_id'],
            ];
            $contacts_id = Contacts::createContacts($contracts);
        } catch (Exception $e) {
            $this->error($e->getMessage());
        }
        $this->success('转变成功');
    }

    /**
     * 获取附件记录
     */
    public function get_file($ids = null)
    {
        list($where, $sort, $order, $offset, $limit) = $this->buildparams();
        $list = LeadsFile::where(['leads_id' => $ids])->with(['file'])->field('file_id')->paginate($limit);

        $result = array("total" => $list->total(), "rows" => $list->items());

        return json($result);
    }

    /**
     * 转移线索
     */
    public function transfer($ids = null)
    {
        $row = $this->model::where(['id' => $ids])->find();
        if (empty($row)) {
            $this->error('线索不存在');
        }
        if ($this->request->isPost()) {
            $staff_id = input('staff_id');
            if (empty($staff_id)) {
                $this->error('参数错误');
            }
            $staff = Staff::get($staff_id);
            if (empty($staff)) {
                $this->error('接收对象不存在');
            }
            try {
                $this->model::transfer($ids, $staff_id);
            } catch (Exception $e) {
                $this->error($e->getMessage());
            }
            $this->success('转变成功');
        }
        $staffs = Staff::getList();

        $this->assign('staffs', $staffs);
        $this->assign('row', $row);
        return $this->view->fetch();
    }

    /**
     * 删除
     */
    public function del($ids = "")
    {
        if (!$this->request->isPost()) {
            $this->error(__("Invalid parameters"));
        }
        $ids = $ids ? $ids : $this->request->post("ids");
        $row = $this->model->get($ids);
        $this->modelValidate = true;
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $row->delete();
        $this->success();
    }

    /**
     * 导入
     */
    public function import()
    {
        set_time_limit(0);
        if ($this->request->isPost()) {

            $file = $this->request->request('file');
            $staff_id = $this->request->request('staff_id', 0);
            if (!$file) {
                $this->error(__('Parameter %s can not be empty', 'file'));
            }
            $filePath = ROOT_PATH . 'public' . $file;
            if (!is_file($filePath)) {
                $this->error(__('No results were found'));
            }
            //实例化reader
            $ext = pathinfo($filePath, PATHINFO_EXTENSION);
            if (!in_array($ext, ['csv', 'xls', 'xlsx'])) {
                $this->error(__('Unknown data format'));
            }
            if ($ext === 'csv') {
                $file = fopen($filePath, 'r');
                $filePath = tempnam(sys_get_temp_dir(), 'import_csv');
                $fp = fopen($filePath, "w");
                $n = 0;
                while ($line = fgets($file)) {
                    $line = rtrim($line, "\n\r\0");
                    $encoding = mb_detect_encoding($line, ['utf-8', 'gbk', 'latin1', 'big5']);
                    if ($encoding != 'utf-8') {
                        $line = mb_convert_encoding($line, 'utf-8', $encoding);
                    }
                    if ($n == 0 || preg_match('/^".*"$/', $line)) {
                        fwrite($fp, $line . "\n");
                    } else {
                        fwrite($fp, '"' . str_replace(['"', ','], ['""', '","'], $line) . "\"\n");
                    }
                    $n++;
                }
                fclose($file) || fclose($fp);

                $reader = new Csv();
            } elseif ($ext === 'xls') {
                $reader = new Xls();
            } else {
                $reader = new Xlsx();
            }
            if (!$PHPExcel = $reader->load($filePath)) {
                $this->error(__('Unknown data format'));
            }
            $currentSheet = $PHPExcel->getSheet(0);  //读取文件中的第一个工作表

            $allColumn = $currentSheet->getHighestDataColumn(); //取得最大的列号
            $allRow = $currentSheet->getHighestRow(); //取得一共有多少行

            $maxColumnNumber = Coordinate::columnIndexFromString($allColumn);
            //开始读取数据
            $fields = [];
            for ($currentRow = 1; $currentRow <= 1; $currentRow++) {
                for ($currentColumn = 1; $currentColumn <= $maxColumnNumber; $currentColumn++) {
                    $val = $currentSheet->getCellByColumnAndRow($currentColumn, $currentRow)->getValue();
                    $fields[$currentRow][] = $val;
                }
            }
            if (!isset($fields[1])) {
                $this->error('导入文件第一行没有数据');
            }
            $lastid = $this->model->withTrashed()->order('id desc')->value('id');
            $contacts_lastid = (new Contacts())->withTrashed()->order('id desc')->value('id');

            $lastid = $lastid + 5;//防止重复
            $contacts_lastid = $contacts_lastid + 5;//防止重复

            $leadRow = [];
            $errorInfo = [];
            $formrow = [];
            $names = $this->model->where([])->column('name');
            $fieldnames = FormField::where(['types' => FormField::LEADS_TYPE])->column('field', 'name');
            if(!$fieldnames){
                $this->error('请在系统管理->字段管理中保存线索管理表单生成线索导入字段');
            }
            $forms = Form::getDataValue('leads');
            foreach($forms as $k=>$v){
                $formrow[$v['id']] = $v['name'];
            }

            $fn = [];
            for ($currentRow = 1; $currentRow <= 1; $currentRow++) {
                $values = [];
                for ($currentColumn = 1; $currentColumn <= $maxColumnNumber; $currentColumn++) {
                    $val = $currentSheet->getCellByColumnAndRow($currentColumn, $currentRow)->getValue();
                    $values[] = is_null($val) ? '' : $val;
                }
                foreach ($values as $l) {
                    $fn[] = $fieldnames[$l] ?? '';
                }
            }
            for ($currentRow = 2; $currentRow <= $allRow; $currentRow++) {
                $values = [];
                for ($currentColumn = 1; $currentColumn <= $maxColumnNumber; $currentColumn++) {
                    $val = $currentSheet->getCellByColumnAndRow($currentColumn, $currentRow)->getValue();
                    if ($val instanceof RichText) {//富文本转换字符串
                        $val = $val->__toString();
                    }
                    $values[] = is_null($val) ? NULL : $val;
                }

                $lastid++;
                $contacts_lastid++;
                $addLeads = ['id' => $lastid, 'owner_staff_id' => $staff_id, 'create_staff_id' => $staff_id];
                foreach ($values as $kv => $value) {
                    if (!isset($fn[$kv]) || empty($fn[$kv])) {
                        continue;
                    }
                    $addLeads[$fn[$kv]] = $value;
                }

                //日期处理
                foreach($formrow as $k=>$v){
                    if($v=='日期选择框'){
                        if(isset($addLeads[$k]) && $addLeads[$k]){
                            $addLeads[$k] =date("Y-m-d", ($addLeads[$k] - 25569) * 24 * 3600);
                        }
                    }
                    if($v=='时间选择框'){
                        if(isset($addLeads[$k]) && $addLeads[$k]){
                            $addLeads[$k] =date("Y-m-d H:i:s", ($addLeads[$k] - 25569) * 24 * 3600);
                        }
                    }
                }
                if (!isset($addLeads['name']) || empty($addLeads['name'])) {
                    $errorInfo[] = "第{$currentRow}行，线索名称不能为空;";
                    continue;
                }
                if (in_array($addLeads['name'], $names)) {
                    $errorInfo[] = "第{$currentRow}行，线索名称`{$addLeads['name']}`已存在;";
                    continue;
                }
                $leadRow[] = $addLeads;
            }
            if (!empty($errorInfo)) {
                $this->error(implode(',', $errorInfo));
            }
            Db::startTrans();
            try {
                $this->model::importleads($leadRow);

                Db::commit();
            } catch (Exception $e) {
                Db::rollback();
                $this->error($e->getMessage());
            }
            $this->success('导入成功');
        }
        $this->assign('staffs', Staff::getList());
        return $this->view->fetch();
    }

    /**
     * 模板
     */
    public function template()
    {
        $title = [];
        $contractData = Form::getDataValue('leads');

        foreach ($contractData as $val) {
            $title[] = $val['config']['label'];
        }

        $file = export_excel($title, [], '线索');
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename=' . $file['fileName']);
        header('Cache-Control: max-age=0');

        $obj = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
        // 以下内容是excel文件的信息描述信息
        $obj->getProperties()->setTitle('导出文件'); //设置标题
        $obj->setActiveSheetIndex(0);
        $obj->getActiveSheet()->setTitle('导出文件');
        /* 循环读取每个单元格的数据 */
        $a = 'A';
        $currentSheet = $obj->getActiveSheet();
        foreach ($title as $key => $value) {
            //读取工作表1
            // 设置第一行加粗
            $obj->getActiveSheet()->getStyle($a . '1')->getFont()->setBold(true);
            //这里是设置单元格的内容
            $currentSheet->getCell($a . '1')->setValue($value);
            $a++;
        }

        $PHPWriter = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($obj);

        $PHPWriter->save('php://output');
    }

    /**
     * 导出信息
     */
    public function export()
    {
        $this->request->filter(['strip_tags', 'trim']);
        $ids = input('ids');
        $isall = input('isall');
        $wheres = array();
        //导出其中几条
        if (isset($ids)) {
            $wheres['id'] = array('in', $ids);
        }
        //导出全部
        if (isset($isall)) {
            if ($isall == 3) {
                unset($wheres['id']);
            }
        }
        $type = input('type', 0);
        list($where, $sort, $order, $offset, $limit) = $this->buildparams();

        switch ($type) {
            case 1:
                $staff = Staff::info();
                $wheres['owner_staff_id'] = $staff->id;
                break;
            case 2:
                $wheres['owner_staff_id'] = array('in', Staff::getLowerStaffId());
                break;
            case 3:
                $start = date('Y-m-d 00:00:00');
                $end = date('Y-m-d 23:59:59');
                $record = collection(Record::where(array('relation_type' => 1, 'next_time' => array(array('egt', $start), array('elt', $end))))->field("id,relation_id")->select())->toArray();
                $relationId = [];
                foreach ($record as $k => $v) {
                    $whereRe['id'] = array('gt', $v['id']);
                    $whereRe['relation_id'] = $v['relation_id'];
                    $recordData = Record::where($whereRe)->count();
                    if ($recordData == 0) {
                        $relationId[] = $v['relation_id'];
                    }

                }
                $wheres['id'] = array('in', $relationId);
                $staff = Staff::info();
                $wheres['owner_staff_id'] = $staff->id;
                break;
            case 4:
                $start = date('Y-m-d 00:00:00');
                $end = date('Y-m-d 23:59:59');
                $record = collection(Record::where(array('relation_type' => 1, 'next_time' => array(array('egt', $start), array('elt', $end))))->field("id,relation_id")->select())->toArray();
                $relationId = [];
                foreach ($record as $k => $v) {
                    $whereRe['id'] = array('gt', $v['id']);
                    $whereRe['relation_id'] = $v['relation_id'];
                    $recordData = Record::where($whereRe)->count();
                    if ($recordData >= 1) {
                        $relationId[] = $v['relation_id'];
                    }

                }
                $wheres['id'] = array('in', $relationId);
                $staff = Staff::info();
                $wheres['owner_staff_id'] = $staff->id;
                break;
            case 5:
                $record = collection(Record::where(array('relation_type' => 4))->column('relation_id'))->toArray();
                $wheres['id'] = array('not in', $record);
                $staff = Staff::info();
                $wheres['owner_staff_id'] = $staff->id;
                break;
            default:
                $wheres['owner_staff_id'] = array('in', Staff::getMyStaffIds());
                break;

        }

        $wheres['is_transform'] = 0;
        $list = $this->model->with([
            'ownerStaff','leadsOther'
        ])->where($where)->where($wheres)->order($sort, $order)->select();
        $list = collection($list)->toArray();

        if (!$list) {
            $this->error('无导出数据');
        }
        $title = [
            '序号',
            '归属人',
            '创建时间',
        ];

        $contractData = Form::getDataValue('leads');

        foreach ($contractData as $val) {
            $title[] = $val['config']['label'];
        }
        foreach ($list as $k => $v) {
            if($v['leads_other']){//其他线索
                $other=$v['leads_other']['otherdata'];
                $other=json_decode($other,true);
                $v = array_merge($v, $other);
            }
            $field = array(
                $k + 1,

                $v['owner_staff']['name'],
                $v['createtime']
            );
            foreach ($contractData as $val) {
                if ($val['component'] == 'uploadImage' || $val['component'] == 'uploadFile') {
                    $field[] = $v[$val['id'] . '_str'] ?? '';
                } else {
                    $field[] = ($v[$val['id']] ?? '');
                }
            }
            $data[] = $field;
        }
        $file = export_excel($title, $data, '线索');
        if ($file['filePath']) {
            $this->success('导出成功', '', $file);
        }
        $this->error('导出失败');
    }
    /**
     * 移入线索池
     */
    public function movepool()
    {
        $ids = input('ids');
        $reuslt= $this->model->where(['id' => $ids])->update(['owner_staff_id' => 0,'updatetime'=>time()]);
        if(!$reuslt){
            $this->error('放入失败');
        }
        $this->success('放入成功');
    }
}
