<?php

namespace app\admin\controller\qingdong\customer;
use addons\qingdong\model\Field;
use addons\qingdong\model\File;
use addons\qingdong\model\Remind;
use addons\qingdong\model\Staff;
use addons\qingdong\model\Customer;
use app\admin\controller\qingdong\Base;
use addons\qingdong\model\Contacts;
use addons\qingdong\model\Comment;
use addons\qingdong\model\Record as RecordModel;
use think\DB;
use think\Exception;

/**
 * 客户跟进记录
 */
class Record extends Base {
    public function _initialize() {
        parent::_initialize();
        $this->model = new RecordModel();
    }

    /**
     * 客户跟进记录
     */
    public function index() {

        $need = input('need','','trim');
        $customer_id = input('customer_id','','trim');
        $contacts_id = input('contacts_id','','trim');
        $leads_id = input('leads_id','','trim');
        $contract_id = input('contract_id','','trim');
        $busniess_id = input('busniess_id','','trim');
        //0:全部 1：我负责的 2：下属负责的 3：今日待跟进 4:今日已跟进
        $type = input('type',0);
        $this->request->filter(['strip_tags']);
        if ($this->request->isAjax()) {
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            switch($type){
                case 1:
                    $staff = Staff::info();
                    $wheres['create_staff_id'] =  $staff->id;
                    break;
                case 2:
                    $wheres['create_staff_id'] =  array('in',Staff::getLowerStaffId());
                    break;
                case 3:
                    $start = date('Y-m-d 00:00:00');
                    $end = date('Y-m-d 23:59:59');
                    $record = collection(RecordModel::where(array('relation_type'=>1,'next_time'=>array(array('egt',$start),array('elt',$end))))->field("id,relation_id")->select())->toArray();
                    $relationId = [];
                    foreach($record as $k=>$v){
                        $whereRe['id'] = array('gt',$v['id']);
                        $whereRe['relation_id'] = $v['relation_id'];
                        $recordData = RecordModel::where($whereRe)->count();
                        if($recordData == 0){
                            $relationId[] = $v['id'];
                        }

                    }
                    $wheres['id'] =  array('in',$relationId);
                    $staff = Staff::info();
                    $wheres['create_staff_id'] =  $staff->id;
                    break;
                case 4:
                    $start = date('Y-m-d 00:00:00');
                    $end = date('Y-m-d 23:59:59');
                    $record = collection(RecordModel::where(array('relation_type'=>1,'next_time'=>array(array('egt',$start),array('elt',$end))))->field("id,relation_id")->select())->toArray();
                    $relationId = [];
                    foreach($record as $k=>$v){
                        $whereRe['id'] = array('gt',$v['id']);
                        $whereRe['relation_id'] = $v['relation_id'];
                        $recordData = RecordModel::where($whereRe)->count();
                        if($recordData >=1){
                            $relationId[] = $v['id'];
                        }

                    }
                    $wheres['id'] =  array('in',$relationId);
                    $staff = Staff::info();
                    $wheres['create_staff_id'] =  $staff->id;
                    break;
                default:
                    $wheres['create_staff_id'] =  array('in',Staff::getMyStaffIds());
                    break;

            }

            $wheres['relation_type'] = 1;
            if(isset($need) && $need =='customer'){
                $staff = Staff::info();
                $wheres['create_staff_id'] =  $staff->id;
                $wheres['status'] = 0;
                $wheres['next_time'] = array(array('egt',date('Y-m-d 00:00:00')),array('lt',date('Y-m-d 23:59:59')));
                $customerlist = $this->model->where($wheres)->column('relation_id');
                if($customerlist){
                    $whereExit['relation_id'] = array('in',$customerlist);
                    $whereExit['next_time'] = array('gt',date('Y-m-d 23:59:59'));
                    $recordIds = $this->model->where($whereExit)->column('id');
                    if($recordIds){
                        $wheres['id'] = array('in',$recordIds);
                    }
                }
            }
            if(isset($need) && $need =='contacts'){
                $staff = Staff::info();
                $wheres['create_staff_id'] =  $staff->id;
                $wheres['status'] = 0;
                $wheres['relation_type'] = 2;
                $wheres['next_time'] = array(array('egt',date('Y-m-d 00:00:00')),array('lt',date('Y-m-d 23:59:59')));
                $customerlist = $this->model->where($wheres)->column('relation_id');
                if($customerlist){
                    $whereExit['relation_id'] = array('in',$customerlist);
                    $whereExit['next_time'] = array('gt',date('Y-m-d 23:59:59'));
                    $recordIds = $this->model->where($whereExit)->column('id');
                    if($recordIds){
                        $wheres['id'] = array('in',$recordIds);
                    }
                }
            }
            if(isset($customer_id) && $customer_id){
                $wheres['relation_type'] = 1;
                $wheres['relation_id'] = $customer_id;
                unset($wheres['create_staff_id']);
            }
            if(isset($contacts_id) && $contacts_id){
                $wheres['relation_type'] = 2;
                $wheres['relation_id'] = $contacts_id;
                unset($wheres['create_staff_id']);
            }
            if(isset($leads_id) && $leads_id){
                $wheres['relation_type'] = 4;
                $wheres['relation_id'] = $leads_id;
                unset($wheres['create_staff_id']);

            }
            if(isset($contract_id) && $contract_id){
                $wheres['relation_type'] = 3;
                $wheres['relation_id'] = $contract_id;
                unset($wheres['create_staff_id']);
            }
            if(isset($busniess_id) && $busniess_id){
                $wheres['relation_type'] = 5;
                $wheres['relation_id'] = $busniess_id;
                unset($wheres['create_staff_id']);
            }

            $list   = $this->model->where($where)->where($wheres)->with(['staff','customer'])->order($sort, $order)->paginate($limit);
            foreach($list as $k=>$v){
                if(!isset($v['customer']) || !$v['customer']){
                    $v['customer'] = array('id'=>'','name'=>'');
                }
            }
            $row    = $list->items();
            $result = array("total" => $list->total(), "rows" => $row);

            return json($result);
        }

        return $this->view->fetch();
    }

    /**
     * 添加跟进
     */
    public function add($ids = null) {
        $relation_type=input('relation_type',1);
        if ($this->request->isPost()) {
            $params = $this->request->post('row/a');
            // 表单验证
            if (($result = $this->qingdongValidate($params, 'Record', 'create')) !== true) {
                $this->error($result);
            }
            if(!empty($params['files'])){
                $params['files']=File::getId($params['files']);
            }
            Db::startTrans();
            try {
                $result = $this->model::createRecord($params);
                Db::commit();
            } catch (Exception $e) {
                Db::rollback();
                $this->error($e->getMessage());
            }
            if ($result) {
                $this->success('创建跟进记录成功');
            }
            $this->error('创建失败');
        }

        $follow= Field::getField('客户状态');
        $customer=Customer::getList();
        $customers = [];
        foreach($customer as $k=>$v){
            $customers[$v['id']] = $v['name'];
        }
        if($relation_type == 2){
            $contacts=Contacts::where(['id'=>$ids])->column('id,name');
            $this->assign('contacts',$contacts);
        }

        $remind=Remind::where(['type'=>Remind::RECORD_TYPE])->find();
        $staff_ids=$remind['staff_ids']??'';
        $this->assign('staff_ids', $staff_ids);
        $this->assign('ids', $ids);
        $this->assign('relation_type', $relation_type);
        $this->assign('customer', $customers);
        $this->assign('follow', $follow);
        $this->assign('staff', Staff::where([])->column('name','id'));
        return $this->view->fetch();
    }
    /**
     * 跟进详情
     */
    public function detail($ids = null) {
        $row = $this->model->with(['staff','file'])->where([
            'id' => $ids,
        ])->find();
        if (empty($row)) {
            $this->error(__('No Results were found'));
        }
        $row = $row->toArray();
        if($row['staff_id']){
            $createname = Staff::where(['id'=>$row['staff_id']])->value('name');
        }else{
            $createname = isset($row['staff']['name']) ?  $row['staff']['name'] : '';
        }

        $row['createname'] = $createname;
        $this->assign('row', $row);
        $this->assignconfig("idinfo", ['id' => $ids]);
        return $this->view->fetch();
    }
    /**
     * 评论
     */
    public function comment() {
        $this->request->filter(['strip_tags']);
        if ($this->request->isAjax()) {
            $record_id = input('record_id','');
            $wheres['relation_id'] = $record_id;
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            $list   = Comment::where($where)->where($wheres)->with(['staff','record'])->order($sort, $order)->paginate($limit);
            $row    = $list->items();
            $result = array("total" => $list->total(), "rows" => $row);

            return json($result);
        }

        return $this->view->fetch();
    }

}