<?php

namespace app\admin\controller\qingdong\customer;

use addons\qingdong\model\ExamineRecord;
use addons\qingdong\model\File;
use addons\qingdong\model\Flow;
use addons\qingdong\model\Message;
use addons\qingdong\model\Staff;
use app\admin\controller\qingdong\Base;
use addons\qingdong\model\ContractOther;
use addons\qingdong\model\Form;
use addons\qingdong\model\Contacts;
use addons\qingdong\model\Customer;
use addons\qingdong\model\OperationLog;
use addons\qingdong\model\Record;
use addons\qingdong\model\ContractFile;
use think\Db;
use think\Exception;


/**
 * 费用管理
 * 操作文档：https://doc.fastadmin.net/qingdong
 * 软件介绍：https://www.fastadmin.net/store/qingdong.html
 * 售后微信：qingdong_crm
 */
class Consume extends Base {
	protected $relationSearch = true;
	/**
	 * @var \addons\qingdong\model\Consume
	 */
	protected $model = null;


	public function _initialize() {
		parent::_initialize();
		$this->model = new \addons\qingdong\model\Consume;
	}


	/**
	 * 查看
	 */
	public function index() {
		//设置过滤方法
		$this->request->filter(['strip_tags', 'trim']);
		if ($this->request->isAjax()) {
			if ($this->request->request('keyField')) {
				return $this->selectpage();
			}
            //0:全部 1：我负责的 2：下属负责的
            $type = input('type',0);
            switch($type){
                case 1:
                    $staff = Staff::info();
                    $wheres['staff_id'] =  $staff->id;
                    break;
                case 2:
                    $wheres['staff_id'] =  array('in',Staff::getLowerStaffId());
                    break;
                default:
                    $wheres['staff_id'] =  array('in',Staff::getMyStaffIds());
                    break;

            }
			list($where, $sort, $order, $offset, $limit) = $this->buildparams();

			$list = $this->model->where($where)->where($wheres)->with(['customer','staff'])->order($sort, $order)->paginate($limit);

			$result = array("total" => $list->total(), "rows" => $list->items());

			return json($result);
		}

		return $this->view->fetch();
	}


	/**
	 * 添加
	 */
    public function add()
    {
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                $params = $this->preExcludeFields($params);
                // 表单验证
                if (($result = $this->qingdongValidate($params, 'Consume', 'create')) !== true) {
                    $this->error($result);
                }
                $result = false;
                Db::startTrans();
                try {
                    if(!empty($params['file_ids'])){
                        $params['file_ids']=File::getId($params['file_ids']);
                    }
                    $result = $this->model::createConsume($params);
                    Db::commit();
                } catch (Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
                if ($result !== false) {
                    $this->success('添加成功');
                } else {
                    $this->error(__('No rows were inserted'));
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $flow= Flow::getsteplist(Flow::CONSUME_STATUS);
        if (empty($flow)) {
            $this->error('无可用审批流，请联系管理员','qingdong/work/flow');
        }
        $customer_id=input('customer_id',null);

        $this->assign('customer_id',$customer_id);
        $this->assign('customer',Customer::get($customer_id));
        $this->assign('flow',$flow);
        $this->assign('staff',  Staff::getStaff());

        return $this->view->fetch();
    }

    /**
     * 修改费用
     */
	public function edit($ids = null) {
		$map['id'] = $ids;
		if ($this->request->isAjax()) {
			$params = $this->request->post('row/a');
			$params = $this->preExcludeFields($params);
            $row = $this->model::where(['id' => $ids, 'check_status' => ['in', [0,1,3, 4,5]]])->find();
            if (empty($row)) {
                $this->error('审核通过的费用不可编辑');
            }
            Db::startTrans();
            try {
                $params['id'] = $ids;
                if(!empty($params['file_ids'])){
                    $params['file_ids']=File::getId($params['file_ids']);
                }
                $result = $this->model::updateConsume($params);
                Db::commit();
            } catch (Exception $e) {
                Db::rollback();
                $this->error($e->getMessage());
            }
            if ($result !== false) {
                $this->success('修改成功');
            } else {
                $this->error('修改失败');
            }
		}
		$row = $this->model->where($map)->with(['customer'])->find();
        $customers=Customer::where(['owner_staff_id'=>['in',Staff::getMyStaffIds()]])->column('name','id');
        if($row['file_ids']){
            $wheres['id']=array('in',$row['file_ids']);
            $file = File::where($wheres)->column('file_path');
            $row['file_ids'] = implode(',',$file);
        }
        $flow= Flow::getsteplist(Flow::CONSUME_STATUS);
        if (empty($flow)) {
            $this->error('无可用审批流，请联系管理员','qingdong/work/flow');
        }
        $this->assign('flow',$flow);

		$this->view->assign("row", $row);
		$this->assign('customer', $customers);
        $this->assign('staff',  Staff::getStaff());
		$this->assign('form_data', Form::getDataValue('contract'));

		return $this->view->fetch();
	}

    /**
     * 费用详情
     */
    public function detail($ids = null)
    {
        $row = $this->model->with(['staff','customer', 'followStaff'])->where([
            'id' => $ids,
        ])->find();
        if (empty($row)) {
            $this->error(__('No Results were found'));
        }

        $this->assign('flow',Flow::getstepdetail(Flow::CONSUME_STATUS,$ids));
        //标记通知已读
        Message::setRead(Message::CONSUME_TYPE, $ids, $this->_staff->id);
        //审批记录
        $this->assign('examine_record', ExamineRecord::getList(ExamineRecord::CONSUME_TYPE,$ids));
        $this->assign('row', $row);
        $this->assign('ids', $ids);

        return $this->view->fetch();
    }
    /**
     * 删除
     */
    public function del($ids = "") {
        if (!$this->request->isPost()) {
            $this->error(__("Invalid parameters"));
        }
        $ids                 = $ids ? $ids : $this->request->post("ids");
        $row = $this->model::where(['id' => $ids, 'check_status' => ['in', [0,1,3, 4,5]]])->find();
        if (empty($row)) {
            $this->error('审核通过的费用不可删除');
        }
        $row                 = $this->model->get($ids);
        $this->modelValidate = true;
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $row->delete();
        $this->success();
    }
}
