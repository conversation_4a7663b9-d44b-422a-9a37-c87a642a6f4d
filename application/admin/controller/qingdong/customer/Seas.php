<?php

namespace app\admin\controller\qingdong\customer;

use addons\qingdong\model\Staff;
use app\admin\controller\qingdong\Base;
use app\common\library\Auth;
use think\Db;
use think\Exception;

/**
 * 公海
 * 操作文档：https://doc.fastadmin.net/qingdong
 * 软件介绍：https://www.fastadmin.net/store/qingdong.html
 * 售后微信：qingdong_crm
 */
class Seas extends Base {
	protected $relationSearch = true;
	protected $searchFields   = 'id,name';
	/**
	 * @var \addons\qingdong\model\Customer
	 */
	protected $model = null;


	public function _initialize() {
		parent::_initialize();
		$this->model = new \addons\qingdong\model\Customer;
	}


	/**
	 * 查看
	 */
	public function index() {
		$this->request->filter(['strip_tags', 'trim']);
		if ($this->request->isAjax()) {

			list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            $order = 'sea_time desc';
            //公海权限
            $rules=Staff::getStaffRule('seas');
            $list = $this->model->where($where)
                ->where(function ($query) use ($rules){
                    foreach ($rules as $rule) {
                        $query->whereOr(['seas_id'=>['like',"%,{$rule},%"]]);
                    }
                })
                ->where('owner_staff_id is null or owner_staff_id = 0')->order($sort, $order) ->paginate($limit);
            $result = array("total" => $list->total(), "rows" => $list->items());

			return json($result);
		}

		return $this->view->fetch();
	}


	/**
	 * 添加
	 */
	public function add() {
		if ($this->request->isPost()) {
			$params = $this->request->post("row/a");
			if ($params) {
				$params = $this->preExcludeFields($params);

				$result = false;
				Db::startTrans();
				try {
					$result = $this->model->allowField(true)->save($params);
					Db::commit();
				} catch (Exception $e) {
					Db::rollback();
					$this->error($e->getMessage());
				}
				if ($result !== false) {
					$this->success();
				} else {
					$this->error(__('No rows were inserted'));
				}
			}
			$this->error(__('Parameter %s can not be empty', ''));
		}
		return $this->view->fetch();
	}

    /**
     * 公海详情
     */
	public function detail($ids=null){
		$row=$this->model->where(['id'=>$ids])->find();
		$this->assign('row',$row);
		$this->assign('ids',$ids);
		return $this->view->fetch();
	}

	/**
	 * 删除
	 */
	public function del($ids = "") {
		if (!$this->request->isPost()) {
			$this->error(__("Invalid parameters"));
		}
		$ids                 = $ids ? $ids : $this->request->post("ids");
		$row                 = $this->model->get($ids);
		$this->modelValidate = true;
		if (!$row) {
			$this->error(__('No Results were found'));
		}
		Auth::instance()->delete($row['id']);
		$this->success();
	}
}
