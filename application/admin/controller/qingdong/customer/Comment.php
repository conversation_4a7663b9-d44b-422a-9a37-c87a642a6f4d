<?php

namespace app\admin\controller\qingdong\customer;
use addons\qingdong\model\Staff;
use app\admin\controller\qingdong\Base;
use addons\qingdong\model\Comment as CommentModel;
use addons\qingdong\model\Record;
use addons\qingdong\model\Message;
use think\DB;
use function EasyWeChat\Kernel\Support\get_client_ip;
/**
 * 客户评论
 */
class Comment extends Base {
    public function _initialize() {
        parent::_initialize();
        $this->model = new CommentModel();
    }


    /**
     * 评论列表
     */
    public function index() {
        $this->request->filter(['strip_tags']);
        if ($this->request->isAjax()) {
            //0:全部 1：我负责的 2：下属负责的
            $type = input('type',0);
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            switch($type){
                case 1:
                    $staff = Staff::info();
                    $wheres['staff_id'] =  $staff->id;
                    break;
                case 2:
                    $wheres['staff_id'] =  array('in',Staff::getLowerStaffId());
                    break;
                default:
                    $wheres['staff_id'] =  array('in',Staff::getMyStaffIds());
                    break;

            }

            $wheres['relation_type'] = 1;
            $list   = $this->model->where($where)->where($wheres)->with(['staff','record'])->order($sort, $order)->paginate($limit);
            $row    = $list->items();
            $result = array("total" => $list->total(), "rows" => $row);

            return json($result);
        }

        return $this->view->fetch();
    }

    /**
     * 添加评论
     * @param null $ids
     * @return string
     * @throws \think\Exception
     * @throws \think\exception\DbException
     */
    public function add($ids=null) {
        if ($this->request->isPost()) {
            $content = input('content');
            if (empty($content)) {
                $this->error('评论内容不能为空');
            }
            $record = Record::get($ids);
            $data = [
                'relation_type' => $record['relation_type'],
                'relation_id' => $ids,
                'staff_id' => $this->_staff->id,
                'content' => $content,
                'status' => 1,
                'ip' => get_client_ip(),
            ];


            $commentModel = new CommentModel();
            $commentModel->save($data);


            Message::addMessage(Message::COMMENT_TYPE, $ids, $record['create_staff_id'], $this->_staff->id);

            $staff_ids = $commentModel->where(['relation_type' => $record['relation_type'], 'relation_id' => $ids])->group('staff_id')->column('staff_id');
            foreach ($staff_ids as $staff_id) {
                //发送通知
                if ($staff_id != $this->_staff->id) {
                    Message::addMessage(Message::COMMENT_TYPE, $ids, $staff_id, $this->_staff->id);
                }
            }
            $this->success('评论成功');
        }
        return $this->view->fetch();
    }


}