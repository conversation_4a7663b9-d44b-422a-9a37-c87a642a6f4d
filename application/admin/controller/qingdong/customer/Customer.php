<?php

namespace app\admin\controller\qingdong\customer;

use addons\qingdong\model\AdminConfig;
use addons\qingdong\model\BusinessStatus;
use addons\qingdong\model\ContactsOther;
use addons\qingdong\model\FormField;
use addons\qingdong\model\Remind;
use addons\qingdong\model\Seastype;
use addons\qingdong\model\WordTemplate;
use app\admin\controller\qingdong\Base;
use addons\qingdong\model\CustomerOther;
use addons\qingdong\model\Form;
use addons\qingdong\model\Record;
use addons\qingdong\model\Staff;
use addons\qingdong\model\Consume;
use addons\qingdong\model\Contacts;
use addons\qingdong\model\Contract;
use addons\qingdong\model\Receivables;
use addons\qingdong\model\ReceivablesPlan;
use addons\qingdong\model\CustomerFile;
use addons\qingdong\model\StaffSignIn;
use addons\qingdong\model\File;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use PhpOffice\PhpSpreadsheet\Reader\Csv;
use PhpOffice\PhpSpreadsheet\Reader\Xls;
use PhpOffice\PhpSpreadsheet\Reader\Xlsx;
use PhpOffice\PhpSpreadsheet\RichText\RichText;
use addons\qingdong\model\Business;
use PhpOffice\PhpWord\TemplateProcessor;
use PhpOffice\PhpWord\Settings;
use think\Db;
use think\Exception;

/**
 * 客户管理
 * 操作文档：https://doc.fastadmin.net/qingdong
 * 软件介绍：https://www.fastadmin.net/store/qingdong.html
 * 售后微信：qingdong_crm
 */
class Customer extends Base
{
    protected $relationSearch = true;
    protected $searchFields = 'id,name';
    /**
     * @var \addons\qingdong\model\Customer
     */
    protected $model = null;


    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \addons\qingdong\model\Customer;
    }


    /**
     * 查看
     */
    public function index()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            //0:全部 1：我负责的 2：下属负责的 3：今日待跟进 4:今日已跟进 5:从未跟进的
            $type = input('type', 0);
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            $staff = Staff::info();
            $staff_id = $staff->id;
            switch ($type) {
                case 1://我负责
                    $wheres['owner_staff_id'] = $staff_id;
                    break;
                case 2://下属负责
                    $wheres['owner_staff_id'] = array('in', Staff::getLowerStaffId());
                    break;
                case 3:
                    $start = date('Y-m-d 00:00:00');
                    $end = date('Y-m-d 23:59:59');
                    $record = collection(Record::where(array('relation_type' => 1, 'next_time' => array(array('egt', $start), array('elt', $end))))->field("id,relation_id")->select())->toArray();
                    $relationId = [];
                    foreach ($record as $k => $v) {
                        $whereRe['id'] = array('gt', $v['id']);
                        $whereRe['relation_id'] = $v['relation_id'];
                        $recordData = Record::where($whereRe)->count();
                        if ($recordData == 0) {
                            $relationId[] = $v['relation_id'];
                        }

                    }
                    $wheres['id'] = array('in', $relationId);
                    $staff = Staff::info();
                    $wheres['owner_staff_id'] = $staff->id;
                    break;
                case 4:
                    $start = date('Y-m-d 00:00:00');
                    $end = date('Y-m-d 23:59:59');
                    $record = collection(Record::where(array('relation_type' => 1, 'next_time' => array(array('egt', $start), array('elt', $end))))->field("id,relation_id")->select())->toArray();
                    $relationId = [];
                    foreach ($record as $k => $v) {
                        $whereRe['id'] = array('gt', $v['id']);
                        $whereRe['relation_id'] = $v['relation_id'];
                        $recordData = Record::where($whereRe)->count();
                        if ($recordData >= 1) {
                            $relationId[] = $v['relation_id'];
                        }

                    }
                    $wheres['id'] = array('in', $relationId);
                    $staff = Staff::info();
                    $wheres['owner_staff_id'] = $staff->id;
                    break;
                case 5:
                    $record = collection(Record::where(array('relation_type' => 1, 'next_time' => array('neq', '')))->column('relation_id'))->toArray();
                    $wheres['id'] = array('not in', $record);
                    $staff = Staff::info();
                    $wheres['owner_staff_id'] = $staff->id;
                    break;
                default:
                    $wheres['owner_staff_id'] = array('in', Staff::getMyStaffIds());
                    break;

            }
            $owner_staff_id = $wheres['owner_staff_id'];
            unset($wheres['owner_staff_id']);
            $list = $this->model->where($where)->with(['ownerStaff'])
                ->where($wheres)->where(function ($query) use ($staff_id, $owner_staff_id, $type) {
                    if ($type == 0) {//全部
                        $query->where(['ro_staff_id' => ['like', "%,{$staff_id},%"]])
                            ->whereOr('rw_staff_id', 'like', "%,{$staff_id},%")
                            ->whereOr(['owner_staff_id' => $owner_staff_id]);
                    } else {
                        $query->where(['owner_staff_id' => $owner_staff_id]);
                    }
                })
                ->where('owner_staff_id != 0 and owner_staff_id is not null')
                ->order($sort, $order)->paginate($limit);
            $rows = $list->items();
            foreach ($rows as &$v) {
                $v['operation_team'] = 'read';
                if ($v['owner_staff_id'] == $this->_staff->id ||
                    in_array($v['owner_staff_id'], Staff::getLowerStaffId()) ||
                    in_array($this->_staff->id, explode(',', $v['rw_staff_id']))) {
                    if (in_array($this->_staff->id, explode(',', $v['rw_staff_id']))) {
                        $v['operation_team'] = 'update';//可修改客户不可操作其他
                    }
                    $v['operation'] = 'update';//修改权限
                } else {
                    $v['operation'] = 'read';//只读权限
                }

            }
            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        $field = FormField::getFields(FormField::CUSTOMER_TYPE);
        $this->assignconfig('fields', $field);
        return $this->view->fetch();
    }


    /**
     * 添加
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                $params = $this->preExcludeFields($params);
                // 表单验证
                if (($result = $this->qingdongValidate($params, 'Customer', 'create')) !== true) {
                    $this->error($result);
                }
                $mobile='';
                if(isset($params['mobile']) && $params['mobile']){
                    $mobile = $params['mobile'];
                }
                if(isset($params['name']) && $params['name']){
                    $customerData = $this->model->where(['name'=>$params['name']])->find();
                    if($customerData){
                        $this->error('客户名称已存在');
                    }
                }
                $result = FormField::checkFields(FormField::CUSTOMER_TYPE, $params);
                if ($result !== true) {
                    $this->error($result);
                }
                $result = false;
                Db::startTrans();
                try {
                    $params = Form::updateFormParams(Form::CUSTOMER_TYPE, $params);
                    $result = $this->model::createCustomer($params);
                    if($mobile){
                        $retC = array(
                            'customer_id'=>$result,
                            'is_major'=>1,
                            'name'=>$params['name'],
                            'mobile'=>$mobile,
                            'next_time'=>date('Y-m-d H:i:s'),
                            'create_staff_id'=>$this->_staff->id,
                            'owner_staff_id'=>$this->_staff->id,
                        );
                        Contacts::create($retC);
                    }
                    Db::commit();
                } catch (Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
                if ($result !== false) {
                    $this->success();
                } else {
                    $this->error(__('No rows were inserted'));
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $this->assign('form_data', Form::getDataValue(Form::CUSTOMER_TYPE));
        $remind = Remind::where(['type' => Remind::CUSTOMER_TYPE])->find();
        $staff_ids = $remind['staff_ids'] ?? '';

        $this->assign('seastype', Seastype::where([])->column('name', 'id'));
        $this->assign('staff_ids', $staff_ids);
        $this->assign('staff', Staff::where([])->column('name', 'id'));
        return $this->view->fetch();
    }


    /**
     * 修改
     */
    public function edit($ids = null)
    {
        $row = $this->model->where([
            'id' => $ids,
        ])->find();
        if (empty($row)) {
            $this->error(__('No Results were found'));
        }
        if (!in_array($row['owner_staff_id'], Staff::getMyStaffIds()) && $row['owner_staff_id'] != 0) {

            if (!in_array($this->_staff->id, explode(',', $row['rw_staff_id'])) &&
                !in_array($this->_staff->id, explode(',', $row['ro_staff_id']))) {//是否在团队内
                $this->error('权限不足');
            }
        }
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                $params = $this->preExcludeFields($params);
                // 表单验证
                if (($result = $this->qingdongValidate($params, 'Customer', 'create')) !== true) {
                    $this->error($result);
                }
                $result = FormField::checkFields(FormField::CUSTOMER_TYPE, $params,$ids);
                if ($result !== true) {
                    $this->error($result);
                }
                $mobile='';
                if(isset($params['mobile']) && $params['mobile']){
                    $mobile = $params['mobile'];
                }
                $result = false;
                Db::startTrans();
                try {
                    $params = Form::updateFormParams(Form::CUSTOMER_TYPE, $params);
                    $params['id'] = $ids;
                    $result = $this->model::updateCustomer($params);
                    if($mobile){
                        Contacts::where(array('customer_id'=>$ids,'is_major'=>1))->update(array('mobile'=>$mobile,'updatetime'=>time()));
                    }
                    Db::commit();
                } catch (Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
                if ($result !== false) {
                    $this->success();
                } else {
                    $this->error(__('No rows were inserted'));
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }

        $row = $row->toArray();
        $row = CustomerOther::getOther($row);
        $form = Form::getDataValue(Form::CUSTOMER_TYPE, $row);

        $this->assign('seastype', Seastype::where([])->column('name', 'id'));
        $this->assign('row', $row);
        $this->assign('form_data', $form);

        return $this->view->fetch();
    }

    /**
     * 搜索下拉列表
     */
    public function stafflist()
    {
        $data = ['searchlist' => Staff::getList()];
        $this->success('', null, $data);
    }

    /**
     * 转移客户
     */
    public function change($ids = null)
    {
        $row = $this->model->where([
            'id' => $ids,
        ])->find();
        if (empty($row)) {
            $this->error(__('No Results were found'));
        }

        if (!in_array($row['owner_staff_id'], Staff::getMyStaffIds()) && $row['owner_staff_id'] != 0) {

            if (!in_array($this->_staff->id, explode(',', $row['rw_staff_id'])) &&
                !in_array($this->_staff->id, explode(',', $row['ro_staff_id']))) {//是否在团队内
                $this->error('权限不足');
            }
        }
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                $params = $this->preExcludeFields($params);

                $result = false;
                Db::startTrans();
                try {
                    $result = $this->model::transfer($ids, $params['owner_staff_id']);
                    Db::commit();
                } catch (Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
                if ($result !== false) {
                    $this->success();
                } else {
                    $this->error(__('No rows were inserted'));
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $staffs = Staff::getList();

        $this->assign('staffs', $staffs);
        $this->assign('row', $row);

        return $this->view->fetch();
    }


    /**
     * 批量转移客户
     */
    public function batch_change($ids = null)
    {
        $ids = json_decode($ids, true);
        $ids = $this->model->where([
            'id' => ['in', $ids]
        ])->column('id');
        if (empty($ids)) {
            $this->error(__('No Results were found'));
        }
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                $params = $this->preExcludeFields($params);

                $result = false;
                Db::startTrans();
                try {
                    $result = $this->model::batchTransfer($ids, $params['owner_staff_id']);
                    Db::commit();
                } catch (Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
                if ($result !== false) {
                    $this->success();
                } else {
                    $this->error(__('No rows were inserted'));
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $staffs = Staff::getList();
        $this->assign('staffs', $staffs);
        $this->assign('ids', json_encode($ids));

        return $this->view->fetch();
    }


    /**
     * 客户详情
     */
    public function detail($ids = null)
    {
        $row = $this->model->with(['create_staff', 'owner_staff'])->where([
            'id' => $ids,
        ])->find();
        if (empty($row)) {
            $this->error(__('No Results were found'));
        }
        if (!in_array($row['owner_staff_id'], Staff::getMyStaffIds()) && $row['owner_staff_id'] != 0) {

            if (!in_array($this->_staff->id, explode(',', $row['rw_staff_id'])) &&
                !in_array($this->_staff->id, explode(',', $row['ro_staff_id']))) {//是否在团队内
                $this->error('权限不足');
            }
        }
        $row = $row->toArray();
        $row = CustomerOther::getOther($row);

        $row['operation_team'] = 'read';
        if ($row['owner_staff_id'] == $this->_staff->id ||
            in_array($row['owner_staff_id'], Staff::getLowerStaffId()) ||
            in_array($this->_staff->id, explode(',', $row['rw_staff_id']))) {
            if (in_array($this->_staff->id, explode(',', $row['rw_staff_id']))) {
                $row['operation_team'] = 'update';//可修改客户不可操作其他
            }
            $row['operation'] = 'update';//修改权限
        } else {
            $row['operation'] = 'read';//只读权限
        }

        $form = Form::getDataValue(Form::CUSTOMER_TYPE, $row);
        foreach ($form as $ks => $vs) {
            //备注显示
            if ($vs['component'] == 'textarea' && ($vs['config']['label'] == '备注信息' || $vs['config']['label'] == '备注')) {
                $row[$vs['id']] = isset($row[$vs['id']]) ? $row[$vs['id']] : $row['remarks'];
            }
        }
        $this->assign('records', Record::getList(Record::CUSTOMER_TYPE, $ids));
        $this->assign('form_data', $form);
        $this->assign('row', $row);
        $this->assign('ids', $ids);
        $this->assignconfig("idinfo", ['id' => $ids, 'operation' => $row['operation'], 'operation_team' => $row['operation_team']]);
        return $this->view->fetch();
    }



    /**
     * 获取相关联系人
     * @param null $ids
     * @return \think\response\Json
     */
    public function get_contacts($ids = null)
    {
        list($where, $sort, $order, $offset, $limit) = $this->buildparams();

        $list = Contacts::where(['customer_id' => $ids])->order('id desc')->paginate($limit);
        $result = array("total" => $list->total(), "rows" => $list->items());

        return json($result);
    }


    /**
     * 获取相关合同
     */
    public function get_contract($ids = null)
    {
        list($where, $sort, $order, $offset, $limit) = $this->buildparams();
        $list = Contract::where([
            'customer_id' => $ids,
        ])->with('customer')->order('id desc')->paginate($limit);
        $result = array("total" => $list->total(), "rows" => $list->items());

        return json($result);
    }


    /**
     * 添加费用
     */
    public function add_consume($ids = null)
    {
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                // 表单验证
                if (($result = $this->qingdongValidate($params, 'Consume', 'create')) !== true) {
                    $this->error($result);
                }
                Db::startTrans();
                try {
                    $result = Consume::createConsume($params);
                    Db::commit();
                } catch (Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
                if ($result) {
                    $this->success('添加费用成功');
                }

            }
            $this->error(__('Parameter %s can not be empty', ''));
        }

        $customer = $this->model->where(['id' => $ids])->find();

        $this->assign('staff', Staff::where([])->column('name', 'id'));
        $this->view->assign('customer', $customer);
        return $this->view->fetch();
    }

    /**
     * 获取费用
     */
    public function get_consume($ids = null)
    {
        list($where, $sort, $order, $offset, $limit) = $this->buildparams();
        $list = Consume::where([
            'customer_id' => $ids,
        ])->with('follow_staff')->order('id desc')->paginate($limit);
        $result = array("total" => $list->total(), "rows" => $list->items());

        return json($result);
    }

    /**
     * 获取团队成员
     * @param null $ids
     */
    public function getteam($ids = null)
    {
        $customer = $this->model::get($ids);
        if (empty($customer)) {
            $this->error('客户不存在');
        }
        $where = [];
        if ($keyValue = $this->request->request("keyValue")) {
            $where['id'] = ['eq', $keyValue];
        }
        if ($where) {
            $owner_staff = Staff::where($where)->find();
            $rw_staffs = [];
            $ro_staffs = [];
        } else {
            $owner_staff = Staff::where(['id' => $customer->owner_staff_id])->find();
            $rw_staffs = Staff::where(['id' => ['in', explode(',', $customer->rw_staff_id)]])->select();
            $ro_staffs = Staff::where(['id' => ['in', explode(',', $customer->ro_staff_id)]])->select();
        }

        $staffs = [];
        if ($owner_staff) {
            $staffs[] = [
                'id' => $owner_staff->id,
                'name' => $owner_staff->name,
                'img' => $owner_staff->img,
                'post' => $owner_staff->post,
                'roles' => '负责人',
                'is_edit' => '读写',
                'customer_id' => $customer->id,
            ];
        }
        foreach ($rw_staffs as $v) {
            $staffs[] = [
                'id' => $v->id,
                'name' => $v->name,
                'img' => $v->img,
                'post' => $v->post,
                'roles' => '普通员工',
                'is_edit' => '读写',
                'customer_id' => $customer->id,
            ];
        }
        foreach ($ro_staffs as $v) {
            $staffs[] = [
                'id' => $v->id,
                'name' => $v->name,
                'img' => $v->img,
                'post' => $v->post,
                'roles' => '普通员工',
                'is_edit' => '只读',
                'customer_id' => $customer->id,
            ];
        }
        $result = array("total" => count($staffs), "rows" => $staffs);

        return json($result);
    }

    /**
     * 添加团队成员
     */
    public function addteam($ids = null)
    {

        $customer = \addons\qingdong\model\Customer::get($ids);
        if (empty($customer)) {
            $this->error('客户不存在');
        }

        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            $staff_ids = $params['staff_id'];
            if (empty($staff_ids)) {
                $this->error('员工不存在');
            }
            $staff_ids = explode(',', $staff_ids);
            foreach ($staff_ids as $k => $id) {
                if ($id == $customer->owner_staff_id) {
                    unset($staff_ids[$k]);
                }
            }
            $ro_staff_id = explode(',', trim($customer->ro_staff_id, ','));
            $rw_staff_id = explode(',', trim($customer->rw_staff_id, ','));
            if ($params['rules'] == 1) {//读写
                $rw_staff_id = array_merge($rw_staff_id, $staff_ids);
                $rw_staff_id = array_unique($rw_staff_id);
            } else {
                $ro_staff_id = array_merge($ro_staff_id, $staff_ids);
                $ro_staff_id = array_unique($ro_staff_id);
            }
            $ro_staff_id = array_diff($ro_staff_id, $rw_staff_id);
            $rw_staff_id = implode(',', $rw_staff_id);
            $ro_staff_id = implode(',', $ro_staff_id);
            $result = $customer->save(['rw_staff_id' => ",{$rw_staff_id},",
                'ro_staff_id' => ",{$ro_staff_id},"]);
            if ($result === false) {
                $this->error('修改失败');
            }
            $this->success('修改成功');
        }

        $owner_staff = Staff::where(['id' => $customer->owner_staff_id])->find();
        $rw_staffs = Staff::where(['id' => ['in', explode(',', $customer->rw_staff_id)]])->select();
        $ro_staffs = Staff::where(['id' => ['in', explode(',', $customer->ro_staff_id)]])->select();

        $staffs = [];
        if ($owner_staff) {
            $staffs[] = [
                'id' => $owner_staff->id,
                'name' => $owner_staff->name,
                'img' => $owner_staff->img,
                'post' => $owner_staff->post,
                'mobile' => $owner_staff->mobile,
                'roles' => 1,
                'is_edit' => 1,
            ];
        }
        foreach ($rw_staffs as $v) {
            $staffs[] = [
                'id' => $v->id,
                'name' => $v->name,
                'img' => $v->img,
                'post' => $v->post,
                'mobile' => $v->mobile,
                'roles' => 2,
                'is_edit' => 1,
            ];
        }
        foreach ($ro_staffs as $v) {
            $staffs[] = [
                'id' => $v->id,
                'name' => $v->name,
                'img' => $v->img,
                'post' => $v->post,
                'mobile' => $v->mobile,
                'roles' => 2,
                'is_edit' => 0,
            ];
        }
        $this->assign('staffs', $staffs);
        return $this->view->fetch();
    }

    /**
     * 修改团队成员
     */
    public function editteam($ids = null)
    {
        $customer_id = input('customer_id');
        $customer = $this->model->get($customer_id);
        if (empty($customer)) {
            $this->error('客户不存在');
        }
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            $staff_id = $params['staff_id'];
            if (empty($staff_id)) {
                $this->error('员工不存在');
            }
            $ro_staff_id = explode(',', trim($customer->ro_staff_id, ','));
            $rw_staff_id = explode(',', trim($customer->rw_staff_id, ','));
            if ($params['rules'] == 1) {//读写
                $rw_staff_id[] = $staff_id;
                $rw_staff_id = array_unique($rw_staff_id);
            } else {
                foreach ($rw_staff_id as $k => $id) {
                    if ($id == $staff_id) {
                        unset($rw_staff_id[$k]);
                    }
                }
                $ro_staff_id[] = $staff_id;
                $ro_staff_id = array_unique($ro_staff_id);
            }
            $ro_staff_id = array_diff($ro_staff_id, $rw_staff_id);
            $rw_staff_id = implode(',', $rw_staff_id);
            $ro_staff_id = implode(',', $ro_staff_id);
            $result = $customer->save(['rw_staff_id' => ",{$rw_staff_id},",
                'ro_staff_id' => ",{$ro_staff_id},"]);
            if ($result === false) {
                $this->error('修改失败');
            }
            $this->success('修改成功');
            $this->success('修改成功');
        }
        if (in_array($ids, explode(',', $customer->rw_staff_id))) {
            $is_edit = 1;
        } else {
            $is_edit = 0;
        }
        $this->assign('is_edit', $is_edit);
        $this->assign('staff_id', $ids);
        return $this->view->fetch();
    }

    /**
     * 删除团队成员
     */
    public function delteam()
    {
        $customer_id = input('customer_id');
        $customer = $this->model->get($customer_id);
        if (empty($customer)) {
            $this->error('客户不存在');
        }
        if ($this->request->isPost()) {
            $staff_id = input('staff_id');
            if (empty($staff_id)) {
                $this->error('员工不存在');
            }
            $ro_staff_id = explode(',', trim($customer->ro_staff_id, ','));
            $rw_staff_id = explode(',', trim($customer->rw_staff_id, ','));
            foreach ($ro_staff_id as $k => $id) {
                if ($id == $staff_id || empty($id)) {
                    unset($ro_staff_id[$k]);
                }
            }
            foreach ($rw_staff_id as $k => $id) {
                if ($id == $staff_id || empty($id)) {
                    unset($rw_staff_id[$k]);
                }
            }
            $rw_staff_id = implode(',', $rw_staff_id);
            $ro_staff_id = implode(',', $ro_staff_id);
            $result = $customer->save(['rw_staff_id' => ",{$rw_staff_id},",
                'ro_staff_id' => ",{$ro_staff_id},"]);
            if ($result === false) {
                $this->error('删除失败');
            }
            $this->success('删除成功');
        }

    }

    /**
     * 获取回款计划
     * @param null $ids
     */
    public function get_receivables_plan($ids = null)
    {
        list($where, $sort, $order, $offset, $limit) = $this->buildparams();
        $list = ReceivablesPlan::where(['customer_id' => $ids])->with([
            'customer',
            'contract'
        ])->order('id desc')->paginate($limit);

        $result = array("total" => $list->total(), "rows" => $list->items());

        return json($result);
    }


    /**
     * 获取回款记录
     */
    public function get_receivables($ids = null)
    {
        list($where, $sort, $order, $offset, $limit) = $this->buildparams();
        $list = Receivables::where(['customer_id' => $ids])->with([
            'customer',
            'contract',
            'ownerStaff',
        ])->order('id desc')->paginate($limit);

        $result = array("total" => $list->total(), "rows" => $list->items());

        return json($result);
    }


    /**
     * 获取附件记录
     */
    public function get_file($ids = null)
    {
        list($where, $sort, $order, $offset, $limit) = $this->buildparams();
        $list = CustomerFile::where(['customer_id' => $ids])->with(['file'])->order('id desc')->field('file_id')->paginate($limit);

        $result = array("total" => $list->total(), "rows" => $list->items());

        return json($result);
    }


    /**
     * 移入公海
     */
    public function seas($ids = "")
    {
        $row = $this->model->where([
            'id' => $ids,
            'owner_staff_id' => ['in', Staff::getMyStaffIds()]
        ])->find();
        if (empty($row)) {
            $this->error(__('No Results were found'));
        }
        if ($this->request->isPost()) {
            $result = false;
            Db::startTrans();
            try {
                $result = $this->model::moveSeas($ids);
                Db::commit();
            } catch (Exception $e) {
                Db::rollback();
                $this->error($e->getMessage());
            }
            if ($result !== false) {
                $this->success();
            } else {
                $this->error(__('No rows were inserted'));
            }

            $this->error(__('Parameter %s can not be empty', ''));
        }
    }


    /**
     * 领取客户
     */
    public function receive($ids = "")
    {
        $row = $this->model->where([
            'id' => $ids,
            'owner_staff_id' => 0
        ])->find();

        if (empty($row)) {
            $this->error(__('No Results were found'));
        }
        if ($this->request->isPost()) {
            $result = false;
            Db::startTrans();
            try {
                $result = $this->model::receive($ids);
                Db::commit();
            } catch (Exception $e) {
                Db::rollback();
                $this->error($e->getMessage());
            }
            if ($result !== false) {
                $this->success('领取客户成功');
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
    }


    /**
     * 删除
     */
    public function del($ids = "")
    {
        if (!$this->request->isPost()) {
            $this->error(__("Invalid parameters"));
        }
        $ids = $ids ? $ids : $this->request->post("ids");
        $row = $this->model->where([
            'id' => $ids,
            'owner_staff_id' => ['in', Staff::getMyStaffIds()]
        ])->find();
        $this->modelValidate = true;
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        //如果有合同和回款，不可删除客户
        $Contract = Contract::where(array('customer_id' => $ids))->find();
        if ($Contract) {
            $this->error('已创建合同不可删除');
        }
        $Receivables = Receivables::where(array('customer_id' => $ids))->find();
        if ($Receivables) {
            $this->error('已创建回款不可删除');
        }
        $map['id'] = $ids;
        $result = $this->model->destroy($map);
        if (!$result) {
            $this->error('删除失败');
        }
        $this->success();
    }

    /**
     * 获取团队人员列表
     */
    public function getstaff()
    {
        $pageSize = input('pageSize');
        $pageNumber = input('pageNumber');
        $where = [];
        if ($keyValue = $this->request->request("keyValue")) {
            $where['id'] = ['in', $keyValue];
        }
        $name = input('name');
        if (!empty($name)) {
            $where['name'] = ['like', '%' . $name . '%'];
        }
        $staff = Staff::where($where)->field('id,name')->order('id desc')->paginate($pageSize, false, ['page' => $pageNumber]);
        return json(['list' => $staff->items(), 'total' => $staff->total()]);
    }

    /**
     * 地图
     */
    public function map()
    {
        $key = AdminConfig::getConfigValue('map_key', 'wechat');
        $this->assign('key', $key);
        return $this->view->fetch();
    }

    /**
     * 获取签到记录
     */
    public function get_sign()
    {
        $customerId = input('customer_id', '');
        list($where, $sort, $order, $offset, $limit) = $this->buildparams();
        $list = StaffSignIn::where([
            'customer_id' => $customerId,
        ])->order('id desc')->paginate($limit);

        $result = array("total" => $list->total(), "rows" => $list->items());

        return json($result);
    }

    /**
     * 获取签到记录
     */
    public function get_sign_detail($ids = null)
    {
        $staffSign = StaffSignIn::where(['id' => $ids])->find();
        if (empty($staffSign)) {
            $this->error('数据不存在');
        }

        if ($staffSign['other']) {
            $other = json_decode($staffSign['other'], true);
            $form = Form::getDataValue(Form::SIGNIN_TYPE);
            foreach ($form as $k => $v) {
                if ($v['component'] == 'uploadImage' || $v['component'] == 'uploadFile') {
                    $other[$v['id'] . '_str'] = '';
                    if (key_exists($v['id'], $other)) {
                        if (isset($other[$v['id']]) && $other[$v['id']]) {
                            $whereT['id'] = array('in', $other[$v['id']]);
                            $fileinfo = File::where($whereT)->field('id,name,file_path,save_name')->select();
                            if ($fileinfo) {
                                $other[$v['id']] = $fileinfo;
                                $fileinfodata = '';
                                foreach ($fileinfo as $kss => $vss) {
                                    $fileinfodata = $vss['save_name'] . ',' . $fileinfodata;
                                }
                                $other[$v['id'] . '_str'] = rtrim($fileinfodata, ',');
                            }
                        }
                    }
                }
            }
            $staffSign['other'] = $other;
        }

        $this->assign('form_data', Form::getDataValue(Form::SIGNIN_TYPE, $staffSign['other']));
        $this->assign('row', $staffSign['other']);
        return $this->view->fetch();

    }

    /**
     * 获取相关商机
     * @param null $ids
     * @return \think\response\Json
     */
    public function get_business()
    {
        list($where, $sort, $order, $offset, $limit) = $this->buildparams();
        $customer_id = input('customer_id');
        $list = Business::where(['customer_id' => $customer_id])->order('id desc')->paginate($limit);
        foreach ($list as $k => $v) {
            $types = BusinessStatus::where(array('business_id' => $v['id']))->order('id desc')->value('type');
            $list[$k]['type'] = $types ? (int)$types : 0;
        }
        $result = array("total" => $list->total(), "rows" => $list->items());

        return json($result);
    }


    /**
     * 导入客户
     * @return string|void
     */
    public function import()
    {
        set_time_limit(0);
        if ($this->request->isPost()) {

            $file = $this->request->request('file');
            $staff_id = $this->request->request('staff_id', 0);
            $seas_type_id = input('seas_type_id',0);
            if (!$file) {
                $this->error(__('Parameter %s can not be empty', 'file'));
            }
            $filePath = ROOT_PATH . 'public' . $file;
            if (!is_file($filePath)) {
                $this->error(__('No results were found'));
            }
            //实例化reader
            $ext = pathinfo($filePath, PATHINFO_EXTENSION);
            if (!in_array($ext, ['csv', 'xls', 'xlsx'])) {
                $this->error(__('Unknown data format'));
            }
            if ($ext === 'csv') {
                $file = fopen($filePath, 'r');
                $filePath = tempnam(sys_get_temp_dir(), 'import_csv');
                $fp = fopen($filePath, "w");
                $n = 0;
                while ($line = fgets($file)) {
                    $line = rtrim($line, "\n\r\0");
                    $encoding = mb_detect_encoding($line, ['utf-8', 'gbk', 'latin1', 'big5']);
                    if ($encoding != 'utf-8') {
                        $line = mb_convert_encoding($line, 'utf-8', $encoding);
                    }
                    if ($n == 0 || preg_match('/^".*"$/', $line)) {
                        fwrite($fp, $line . "\n");
                    } else {
                        fwrite($fp, '"' . str_replace(['"', ','], ['""', '","'], $line) . "\"\n");
                    }
                    $n++;
                }
                fclose($file) || fclose($fp);

                $reader = new Csv();
            } elseif ($ext === 'xls') {
                $reader = new Xls();
            } else {
                $reader = new Xlsx();
            }
            if (!$PHPExcel = $reader->load($filePath)) {
                $this->error(__('Unknown data format'));
            }
            $currentSheet = $PHPExcel->getSheet(0);  //读取文件中的第一个工作表

            $allColumn = $currentSheet->getHighestDataColumn(); //取得最大的列号
            $allRow = $currentSheet->getHighestRow(); //取得一共有多少行

            $maxColumnNumber = Coordinate::columnIndexFromString($allColumn);
            //开始读取数据
            $fields = [];
            for ($currentRow = 1; $currentRow <= 1; $currentRow++) {
                for ($currentColumn = 1; $currentColumn <= $maxColumnNumber; $currentColumn++) {
                    $val = $currentSheet->getCellByColumnAndRow($currentColumn, $currentRow)->getValue();
                    $fields[$currentRow][] = $val;
                    if ($val instanceof RichText) {//富文本转换字符串
                        $val = $val->__toString();
                    }
                    $values[] = is_null($val) ? '' : trim($val);
                }
            }
            if (!isset($fields[1])) {
                $this->error('导入文件第一行没有数据');
            }
            $lastid = $this->model->withTrashed()->order('id desc')->value('id');

            $lastid = $lastid + 5;//防止重复

            $customerRow = [];
            $errorInfo = [];
            $formrow = [];
            $names = $this->model->where([])->column('name');
            $fieldnames = FormField::where(['types' => FormField::CUSTOMER_TYPE])->column('field', 'name');
            if(!$fieldnames){
                $this->error('请在系统管理->字段管理中保存客户管理表单生成客户导入字段');
            }
            $forms = Form::getDataValue(Form::CUSTOMER_TYPE);
            foreach($forms as $k=>$v){
                $formrow[$v['id']] = $v['name'];
            }
            $fn = [];
            for ($currentRow = 1; $currentRow <= 1; $currentRow++) {
                $values = [];
                for ($currentColumn = 1; $currentColumn <= $maxColumnNumber; $currentColumn++) {
                    $val = $currentSheet->getCellByColumnAndRow($currentColumn, $currentRow)->getValue();
                    $values[] = is_null($val) ? '' : $val;
                }
                foreach ($values as $l) {
                    $fn[] = $fieldnames[$l] ?? '';
                }
            }
            for ($currentRow = 2; $currentRow <= $allRow; $currentRow++) {
                $values = [];
                for ($currentColumn = 1; $currentColumn <= $maxColumnNumber; $currentColumn++) {
                    $val = $currentSheet->getCellByColumnAndRow($currentColumn, $currentRow)->getValue();
                    if ($val instanceof RichText) {//富文本转换字符串
                        $val = $val->__toString();
                    }

                    $values[] = is_null($val) ? NULL : trim($val);
                }
                $lastid++;
                $addCustomers = ['id' => $lastid, 'owner_staff_id' => $staff_id, 'create_staff_id' => $staff_id];

                foreach ($values as $kv => $value) {
                    if (!isset($fn[$kv]) || empty($fn[$kv])) {
                        continue;
                    }
                    $addCustomers[$fn[$kv]] = $value;
                }
                //日期格式处理
                foreach($formrow as $k=>$v){
                    if($v=='日期选择框'){
                        if(isset($addCustomers[$k]) && $addCustomers[$k]){
                            $addCustomers[$k] =date("Y-m-d", ($addCustomers[$k] - 25569) * 24 * 3600);
                        }
                    }
                    if($v=='时间选择框'){
                        if(isset($addCustomers[$k]) && $addCustomers[$k]){
                            $addCustomers[$k] =date("Y-m-d H:i:s", ($addCustomers[$k] - 25569) * 24 * 3600);
                        }
                    }
                }
                if (empty($addCustomers['name'])) {
                    $errorInfo[] = "第{$currentRow}行，客户名称不能为空;";
                    continue;
                }
                if (in_array($addCustomers['name'], $names)) {
                    $errorInfo[] = "第{$currentRow}行，客户名称`{$addCustomers['name']}`已存在;";
                    continue;
                }
                $addCustomers['seas_id']=",{$seas_type_id},";
                $customerRow[] = $addCustomers;
            }
            if (!empty($errorInfo)) {
                $this->error(implode(',', $errorInfo));
            }
            Db::startTrans();
            try {
                $this->model::importCustomer($customerRow);
                Db::commit();
            } catch (Exception $e) {
                Db::rollback();
                $this->error($e->getMessage());
            }
            $this->success('导入成功');
        }

        $this->assign('seas_type', Seastype::where([])->field('id,name')->select());
        $this->assign('staffs', Staff::getList());
        return $this->view->fetch();
    }

    /**
     * 模板
     */
    public function template()
    {
        $title = [];
        $dataValue = Form::getDataValue(Form::CUSTOMER_TYPE);

        foreach ($dataValue as $val) {
            $title[] = $val['config']['label'];
        }

        $file = export_excel($title, [], '客户');
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename=' . $file['fileName']);
        header('Cache-Control: max-age=0');

        $obj = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
        // 以下内容是excel文件的信息描述信息
        $obj->getProperties()->setTitle('导出文件'); //设置标题
        $obj->setActiveSheetIndex(0);
        $obj->getActiveSheet()->setTitle('导出文件');
        /* 循环读取每个单元格的数据 */
        $a = 'A';
        $currentSheet = $obj->getActiveSheet();
        foreach ($title as $key => $value) {
            //读取工作表1
            // 设置第一行加粗
            $obj->getActiveSheet()->getStyle($a . '1')->getFont()->setBold(true);
            //这里是设置单元格的内容
            $currentSheet->getCell($a . '1')->setValue($value);
            $a++;
        }

        $PHPWriter = new \PhpOffice\PhpSpreadsheet\Writer\Xlsx($obj);

        $PHPWriter->save('php://output');
    }

    /**
     * 导出信息
     */
    public function export()
    {
        $this->request->filter(['strip_tags', 'trim']);
        $ids = input('ids');
        $isall = input('isall');
        $wheres = array();
        //导出其中几条
        if (isset($ids)) {
            $wheres['id'] = array('in', $ids);
        }
        //导出全部
        if (isset($isall)) {
            if ($isall == 3) {
                unset($wheres['id']);
            }
        }
        //0:全部 1：我负责的 2：下属负责的 3：今日待跟进 4:今日已跟进 5:从未跟进的
        $type = input('type', 0);
        $staff = Staff::info();
        $staff_id = $staff->id;

        list($where, $sort, $order, $offset, $limit) = $this->buildparams();
        switch ($type) {
            case 1://我负责
                $wheres['owner_staff_id'] = $staff_id;
                break;
            case 2://下属负责
                $wheres['owner_staff_id'] = array('in', Staff::getLowerStaffId());
                break;
            case 3:
                $start = date('Y-m-d 00:00:00');
                $end = date('Y-m-d 23:59:59');
                $record = collection(Record::where(array('relation_type' => 1, 'next_time' => array(array('egt', $start), array('elt', $end))))->field("id,relation_id")->select())->toArray();
                $relationId = [];
                foreach ($record as $k => $v) {
                    $whereRe['id'] = array('gt', $v['id']);
                    $whereRe['relation_id'] = $v['relation_id'];
                    $recordData = Record::where($whereRe)->count();
                    if ($recordData == 0) {
                        $relationId[] = $v['relation_id'];
                    }

                }
                $wheres['id'] = array('in', $relationId);
                $staff = Staff::info();
                $wheres['owner_staff_id'] = $staff->id;
                break;
            case 4:
                $start = date('Y-m-d 00:00:00');
                $end = date('Y-m-d 23:59:59');
                $record = collection(Record::where(array('relation_type' => 1, 'next_time' => array(array('egt', $start), array('elt', $end))))->field("id,relation_id")->select())->toArray();
                $relationId = [];
                foreach ($record as $k => $v) {
                    $whereRe['id'] = array('gt', $v['id']);
                    $whereRe['relation_id'] = $v['relation_id'];
                    $recordData = Record::where($whereRe)->count();
                    if ($recordData >= 1) {
                        $relationId[] = $v['relation_id'];
                    }

                }
                $wheres['id'] = array('in', $relationId);
                $staff = Staff::info();
                $wheres['owner_staff_id'] = $staff->id;
                break;
            case 5:
                $record = collection(Record::where(array('relation_type' => 1, 'next_time' => array('neq', '')))->column('relation_id'))->toArray();
                $wheres['id'] = array('not in', $record);
                $staff = Staff::info();
                $wheres['owner_staff_id'] = $staff->id;
                break;
            default:
                $wheres['owner_staff_id'] = array('in', Staff::getMyStaffIds());
                break;

        }
        $owner_staff_id = $wheres['owner_staff_id'];
        unset($wheres['owner_staff_id']);
        $list = $this->model->where($where)->with(['ownerStaff','customerOther'])
            ->where($wheres)
            ->where(function ($query) use ($staff_id, $owner_staff_id, $type) {
                if ($type == 0) {//全部
                    $query->where(['ro_staff_id' => ['like', "%,{$staff_id},%"]])
                        ->whereOr('rw_staff_id', 'like', "%,{$staff_id},%")
                        ->whereOr(['owner_staff_id' => $owner_staff_id]);
                } else {
                    $query->where(['owner_staff_id' => $owner_staff_id]);
                }
            })
            ->where('owner_staff_id != 0 and owner_staff_id is not null')->order($sort, $order)->select();
        $list = collection($list)->toArray();

        if (!$list) {
            $this->error('无导出数据');
        }
        $title = [
            '序号',
            '归属人',
            '创建时间',
            '下次联系时间',
            '最后跟进时间',
        ];

        $dataValue = Form::getDataValue(Form::CUSTOMER_TYPE);

        foreach ($dataValue as $val) {
            $title[] = $val['config']['label'];
        }
        $data=[];
        foreach ($list as $k => $v) {
            if($v['customer_other']){//其他客户
                $other=$v['customer_other']['otherdata'];
                $other=json_decode($other,true);
                $v = array_merge($v, $other);
            }
            $field = array(
                $v['id'],

                $v['owner_staff']['name'],
                $v['createtime'],
                $v['next_time'],
                $v['last_time'],
            );
            foreach ($dataValue as $val) {
                if ($val['component'] == 'uploadImage' || $val['component'] == 'uploadFile') {
                    $field[] = $v[$val['id'] . '_str'] ?? '';
                } else {
                    $field[] = ($v[$val['id']] ?? '');
                }
            }
            $data[] = $field;
        }
        $file = export_excel($title, $data, '客户');
        if ($file['filePath']) {
            $this->success('导出成功', '', $file);
        }
        $this->error('导出失败');
    }

    /**
     * 导出word
     */
    public function words($ids = null)
    {
        if ($this->request->isPost()) {
            $file = input('file', '');
            $is_template=input('is_template',0);
            $template_name=input('template_name','');
            if (empty($file)) {
                $this->error('导出模板');
            }
            if($is_template == 1){
                if(empty($template_name)){
                    $this->error('模板名称不能为空');
                }
                $create=[
                    'type'=>'customer',
                    'name'=>$template_name,
                    'url'=>$file
                ];
                $wordTemplateModel = new WordTemplate();
                $wordTemplateModel->save($create);
            }
            $tmp = new TemplateProcessor('.' . $file);
            Settings::setCompatibility(true);
            Settings::setOutputEscapingEnabled(true);

            $row = $this->model::get($ids);
            $row = $row->toArray();
            $row = CustomerOther::getOther($row);
            $form = Form::getDataValue(Form::CUSTOMER_TYPE);
            $imgNames=[];
            foreach($form as $k=>$v){
                if($v['component'] == 'uploadImage'){
                    $imgNames[]=$v['id'];
                }
            }
            foreach ($row as $key => $item) {
                if(in_array($key,$imgNames) && $item ){
                    if(is_array($item)){
                        foreach ($item as $it){
                            $tmp->setImageValue($key,ltrim(parse_url($it['file_path'])['path'],'/'));
                        }
                    }else{
                        $fileinfo = File::where(['id'=>['in',$item]])->field('id,name,file_path,save_name')->select();
                        foreach ($fileinfo as $it){
                            $tmp->setImageValue($key,ltrim(parse_url($it['file_path'])['path'],'/'));
                        }
                    }

                }else{
                    if(is_array($item)){
                        $tmp->setValue($key , '');//
                        continue;
                    }
                    $tmp->setValue($key , $item);//
                }
            }
            $contacts = Contacts::where(['customer_id' => $ids])->find();
            if ($contacts) {
                $contacts=$contacts->toArray();
                $contacts = ContactsOther::getOther($contacts);
                foreach ($contacts as $key => $item) {
                    if(is_array($item)){
                        continue;
                    }
                    if ($key == 'name') {
                        $key = 'con_name';
                    }
                    if ($key == 'subname') {
                        $key = 'con_subname';
                    }
                    $tmp->setValue($key, $item);//
                }
            }else{
                $form = Form::getDataValue(Form::CONTACTS_TYPE);
                foreach ($form as $f) {
                    if ($f['id'] == 'name') {
                        $f['id'] = 'con_name';
                    }
                    if ($f['id'] == 'subname') {
                        $f['id'] = 'con_subname';
                    }
                    $tmp->setValue($f['id'], '');//
                }

            }
            //end 配置
            $filename = $row['name'];
            $filepath = $filename . '.docx';
            $fileurl = './uploads/' . date('Ymd') . '/';
            if (!file_exists($fileurl)) {
                mkdir($fileurl);
            }
            $tmp->saveAs($fileurl . $filepath);//另存为
            $model = new File();
            $data = [
                'types' => 'file',
                'name' => $filepath,
                'save_name' => $fileurl . $filepath,
                'size' => filesize($fileurl . $filepath),
                'file_path' => trim($fileurl, '.') . $filepath,
            ];
            $model->save($data);
            $lastid = $model->id;
            $file = cdnurl($model::getUrl($lastid), true);

            $this->success('成功', '', ['file' => $file, 'id' => $lastid, 'filename' => $filepath]);
        }
        $form = Form::getDataValue(Form::CUSTOMER_TYPE);
        $column = array();
        foreach ($form as $k => $v) {
            if ($v['config']['label'] == '备注信息') {
                $v['config']['label'] = '客户备注';
            }
            $column[] = array(
                'key' => $v['id'],
                'name' => $v['config']['label'],
            );
        }
        $templates=WordTemplate::where(['type'=>'customer'])->select();
        $contacts = Form::getDataValue(Form::CONTACTS_TYPE);
        $columns = array();
        foreach ($contacts as $k => $v) {
            if ($v['id'] == 'name') {
                $v['id'] = 'con_name';
            }
            if ($v['id'] == 'subname') {
                $v['id'] = 'con_subname';
            }
            if ($v['config']['label'] == '备注信息') {
                $v['config']['label'] = '联系人备注';
            }
            $columns[] = array(
                'key' => $v['id'],
                'name' => $v['config']['label'],
            );
        }
        $column = array_merge($column, $columns);
        $this->assign('column', $column);
        $this->assign('templates', $templates);
        $this->assign('ids', $ids);
        return $this->view->fetch();
    }

}
