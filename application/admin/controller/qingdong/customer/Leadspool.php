<?php

namespace app\admin\controller\qingdong\customer;

use addons\qingdong\model\File;
use addons\qingdong\model\Staff;
use app\admin\controller\qingdong\Base;
use addons\qingdong\model\Form;
use addons\qingdong\model\LeadsOther;
use addons\qingdong\model\OperationLog;
use addons\qingdong\model\Record;
use addons\qingdong\model\Customer;
use addons\qingdong\model\Contacts;
use addons\qingdong\model\LeadsFile;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use PhpOffice\PhpSpreadsheet\Reader\Csv;
use PhpOffice\PhpSpreadsheet\Reader\Xls;
use PhpOffice\PhpSpreadsheet\Reader\Xlsx;
use think\Db;
use think\Exception;

/**
 * 线索池
 * 操作文档：https://doc.fastadmin.net/qingdong
 * 软件介绍：https://www.fastadmin.net/store/qingdong.html
 * 售后微信：qingdong_crm
 */
class Leadspool extends Base {
	protected $relationSearch = true;
	protected $searchFields   = 'id,name';
	/**
	 * @var \addons\qingdong\model\Leads
	 */
	protected $model = null;


	public function _initialize() {
		parent::_initialize();
		$this->model = new \addons\qingdong\model\Leads;
	}


	/**
	 * 查看
	 */
	public function index() {
        $this->request->filter(['strip_tags', 'trim']);
		if ($this->request->isAjax()) {
			list($where, $sort, $order, $offset, $limit) = $this->buildparams();
			$list = $this->model->where($where)->where('owner_staff_id is null or owner_staff_id = 0')->order($sort, $order)->with(['createStaff'])->paginate($limit);

			$result = array("total" => $list->total(), "rows" => $list->items());

			return json($result);
		}

		return $this->view->fetch();
	}


	/**
	 * 添加
	 */
	public function add() {
		if ($this->request->isPost()) {
			$params = $this->request->post("row/a");
			if ($params) {
				$params = $this->preExcludeFields($params);

				$result = false;
				Db::startTrans();
				try {
                    $form = Form::getDataValue('leads');
                    foreach($form as $k=>$v){
                        if($v['component'] == 'uploadImage' || $v['component'] == 'uploadFile'){
                            if(key_exists($v['id'],$params)){
                                if($params[$v['id']]){
                                    $filearr = explode(',',$params[$v['id']]);
                                    $files ='';
                                    if($filearr){
                                        foreach($filearr as $ks=>$vs){
                                            $files = File::where(array('file_path'=>$vs))->order('id desc')->value('id').','.$files;
                                        }
                                        $params[$v['id']] = rtrim($files,',');
                                    }
                                }
                            }
                        }elseif($v['component'] == 'select'){
                            if(isset($v['config']['multiple']) &&  $v['config']['multiple'] == true){
                                if(key_exists($v['id'],$params)){
                                    if($params[$v['id']]){
                                        $params[$v['id']] = implode(',',$params[$v['id']]);
                                    }
                                }
                            }
                        }
                    }
					$result             =$this->model::createLeads($params,1);
					Db::commit();
				} catch (Exception $e) {
					Db::rollback();
					$this->error($e->getMessage());
				}
				if ($result !== false) {
					$this->success();
				} else {
					$this->error(__('No rows were inserted'));
				}
			}
			$this->error(__('Parameter %s can not be empty', ''));
		}
        $staffs = Staff::getList($this->_staff->id);

        $this->assign('staffs', $staffs);
		$this->view->assign('form_data', Form::getDataValue('leads'));
		return $this->view->fetch();
	}


	/**
	 * 修改
	 */
	public function edit($ids = null) {
		$row = $this->model->get($ids);
		if ($this->request->isPost()) {
			$params = $this->request->post("row/a");
			if ($params) {
                $form = Form::getDataValue('leads');
                foreach($form as $k=>$v){
                    if($v['component'] == 'uploadImage' || $v['component'] == 'uploadFile'){
                        if(key_exists($v['id'],$params)){
                            if($params[$v['id']]){
                                $filearr = explode(',',$params[$v['id']]);
                                $files ='';
                                if($filearr){
                                    foreach($filearr as $ks=>$vs){
                                        $files = File::where(array('file_path'=>$vs))->order('id desc')->value('id').','.$files;
                                    }
                                    $params[$v['id']] = rtrim($files,',');
                                }
                            }
                        }
                    }elseif($v['component'] == 'select'){
                        if(isset($v['config']['multiple']) &&  $v['config']['multiple'] == true){
                            if(key_exists($v['id'],$params)){
                                if($params[$v['id']]){
                                    $params[$v['id']] = implode(',',$params[$v['id']]);
                                }
                            }
                        }
                    }
                }
				$params = $this->preExcludeFields($params);

				$result = false;
				Db::startTrans();
				try {
					$params['id']=$ids;
					$result=$this->model::updateLeads($params);
					Db::commit();
				} catch (Exception $e) {
					Db::rollback();
					$this->error($e->getMessage());
				}
				if ($result !== false) {
					$this->success();
				} else {
					$this->error(__('No rows were inserted'));
				}
			}
			$this->error(__('Parameter %s can not be empty', ''));
		}
        $row=$row->toArray();
        $row=LeadsOther::getOther($row);
		$this->assign('row', $row);

		$this->view->assign('form_data', Form::getDataValue('leads',$row));
		return $this->view->fetch();
	}


    /**
     * 线索详情
     */
	public function detail($ids = null) {
		$row = $this->model->with(['create_staff', 'owner_staff'])->where(['id' => $ids])->find();

		//跟进记录
		$this->assign('records', Record::getList(Record::LEADS_TYPE, $ids));
		//操作记录
		$this->assign('operation_log', OperationLog::getList(OperationLog::LEADS_TYPE, $ids));


        $row=$row->toArray();
        $row=LeadsOther::getOther($row);
        $this->view->assign('form_data', Form::getDataValue('leads',$row));
		$this->assign('row', $row);
		$this->assign('ids', $ids);
        $this->assignconfig("idinfo", ['id' => $ids]);
		return $this->view->fetch();
	}


    /**
     * 获取附件记录
     */
	public function get_file($ids = null) {
		list($where, $sort, $order, $offset, $limit) = $this->buildparams();
		$list = LeadsFile::where(['leads_id' => $ids])->with(['file'])->field('file_id')->paginate($limit);

		$result = array("total" => $list->total(), "rows" => $list->items());

		return json($result);
	}

    /**
     * 分配线索
     */
    public function transfer($ids=null){

        if ($this->request->isPost()) {
            $leadId = input('ids');
            $staff_id = input('staff_id');
            if (!$staff_id || !$leadId) {
                $this->error('参数错误');
            }
            $staff = Staff::get($staff_id);
            if (empty($staff)) {
                $this->error('接收对象不存在');
            }
            try {
                $ids = explode(',',$ids);
                $this->model::transfer($ids, $staff_id);
            } catch (Exception $e) {
                $this->error($e->getMessage());
            }
            $this->success('分配成功');
        }
        $ids = json_decode($ids, true);
        $row = $this->model::where(['id' => ['in',$ids]])->find();
        if (empty($row)) {
            $this->error('线索不存在');
        }
        $staffs = Staff::getList();
        $this->assign('staffs', $staffs);
        $this->assign('row', $row);
        $this->assign('ids', implode(',',$ids));
        return $this->view->fetch();
    }

	/**
	 * 删除
	 */
	public function del($ids = "") {
		if (!$this->request->isPost()) {
			$this->error(__("Invalid parameters"));
		}
		$ids                 = $ids ? $ids : $this->request->post("ids");
		$row                 = $this->model->get($ids);
		$this->modelValidate = true;
		if (!$row) {
			$this->error(__('No Results were found'));
		}
		$row->delete();
		$this->success();
	}
    /**
     * 导入
     */
    public function import()
    {
        set_time_limit(0);
        if ($this->request->isPost()) {

            $file = $this->request->request('file');
            if (!$file) {
                $this->error(__('Parameter %s can not be empty', 'file'));
            }
            $filePath = ROOT_PATH . 'public' . $file;
            if (!is_file($filePath)) {
                $this->error(__('No results were found'));
            }
            //实例化reader
            $ext = pathinfo($filePath, PATHINFO_EXTENSION);
            if (!in_array($ext, ['csv', 'xls', 'xlsx'])) {
                $this->error(__('Unknown data format'));
            }
            $staff = Staff::info();
            if ($ext === 'csv') {
                $file = fopen($filePath, 'r');
                $filePath = tempnam(sys_get_temp_dir(), 'import_csv');
                $fp = fopen($filePath, "w");
                $n = 0;
                while ($line = fgets($file)) {
                    $line = rtrim($line, "\n\r\0");
                    $encoding = mb_detect_encoding($line, ['utf-8', 'gbk', 'latin1', 'big5']);
                    if ($encoding != 'utf-8') {
                        $line = mb_convert_encoding($line, 'utf-8', $encoding);
                    }
                    if ($n == 0 || preg_match('/^".*"$/', $line)) {
                        fwrite($fp, $line . "\n");
                    } else {
                        fwrite($fp, '"' . str_replace(['"', ','], ['""', '","'], $line) . "\"\n");
                    }
                    $n++;
                }
                fclose($file) || fclose($fp);

                $reader = new Csv();
            } elseif ($ext === 'xls') {
                $reader = new Xls();
            } else {
                $reader = new Xlsx();
            }
            if (!$PHPExcel = $reader->load($filePath)) {
                $this->error(__('Unknown data format'));
            }
            $currentSheet = $PHPExcel->getSheet(0);  //读取文件中的第一个工作表

            $allColumn = $currentSheet->getHighestDataColumn(); //取得最大的列号
            $allRow = $currentSheet->getHighestRow(); //取得一共有多少行

            $maxColumnNumber = Coordinate::columnIndexFromString($allColumn);
            //开始读取数据
            $fields = [];
            for ($currentRow = 1; $currentRow <= 1; $currentRow++) {
                for ($currentColumn = 1; $currentColumn <= $maxColumnNumber; $currentColumn++) {
                    $val = $currentSheet->getCellByColumnAndRow($currentColumn, $currentRow)->getValue();
                    $fields[$currentRow][] = $val;
                }
            }
            if (!isset($fields[1])) {
                $this->error('导入文件第一行没有数据');
            }
            $lastid = $this->model->withTrashed()->order('id desc')->value('id');

            $lastid = $lastid + 5;//防止重复

            $customerRow = [];
            $errorInfo = [];
            $names = $this->model->where([])->column('name');

            for ($currentRow = 2; $currentRow <= $allRow; $currentRow++) {
                $values = [];
                for ($currentColumn = 1; $currentColumn <= $maxColumnNumber; $currentColumn++) {
                    $val = $currentSheet->getCellByColumnAndRow($currentColumn, $currentRow)->getValue();
                    $values[] = is_null($val) ? '' : $val;
                }

                $lastid++;

                $addCustomers = ['id' => $lastid, 'owner_staff_id' => 0, 'create_staff_id' => $staff->id];

                $addCustomers['name'] = $values[0];
                $addCustomers['source'] = $values[1];
                $addCustomers['mobile'] = $values[2];
                $addCustomers['address_detail'] = $values[3];

                $addCustomers['telephone'] = $values[4];
                $addCustomers['sex'] = $values[5];
                $addCustomers['industry'] = $values[6];
                $addCustomers['remarks'] = $values[7];

                if (empty($addCustomers['name'])) {
                    $errorInfo[] = "第{$currentRow}行，线索名称不能为空;";
                    continue;
                }
                if (in_array($addCustomers['name'], $names)) {
                    $errorInfo[] = "第{$currentRow}行，线索名称`{$addCustomers['name']}`已存在;";
                    continue;
                }
                $customerRow[] = $addCustomers;
            }
            if (!empty($errorInfo)) {
                $this->error(implode(',', $errorInfo));
            }
            Db::startTrans();
            try {
                $this->model::importleads($customerRow);

                Db::commit();
            } catch (Exception $e) {
                Db::rollback();
                $this->error($e->getMessage());
            }
            $this->success('导入成功');
        }
        $this->assign('staffs', Staff::getList());
        return $this->view->fetch();
    }
}
