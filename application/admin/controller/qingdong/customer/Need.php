<?php

namespace app\admin\controller\qingdong\customer;

use addons\qingdong\model\ReceivablesPlan;
use app\admin\controller\qingdong\Base;
use addons\qingdong\model\Staff;
use think\Db;
use think\Exception;
use addons\qingdong\model\Event;
use addons\qingdong\model\ExamineRecord;

use addons\qingdong\model\Record;

/**
 * 待办管理
 * 操作文档：https://doc.fastadmin.net/qingdong
 * 软件介绍：https://www.fastadmin.net/store/qingdong.html
 * 售后微信：qingdong_crm
 */
class Need extends Base
{
    protected $relationSearch = true;

    protected $model = null;


    public function _initialize()
    {
        parent::_initialize();

    }
    /**
     * 待办列表
     */
    public function index(){
        $where['create_staff_id'] = $this->_staff->id;
        $where['next_time'] = array(array('egt',date('Y-m-d 00:00:00')),array('lt',date('Y-m-d 23:59:59')));
        $where['follow_type'] = ['neq', '其它'];
        $where['status'] = 0;
        // 待跟进客户
        $where1['relation_type'] = 1;
        $customerlist = Record::where($where)->where($where1)->column('relation_id');
        $customerlist1 = 0;
        if($customerlist){
            $whereExit['relation_id'] = array('in',$customerlist);
            $whereExit['next_time'] = array('gt',date('Y-m-d 23:59:59'));
            $customerlist1 = Record::where($whereExit)->count();
        }
        $customer = count($customerlist) - $customerlist1;
        if($customer <=0){
            $customer =0;
        }
        //待跟进合同
        $where2['relation_type'] = 3;
        $contractlist = Record::where($where)->where($where2)->column('relation_id');
        $contractlist1 = 0;
        if($contractlist){
            $whereExitC['relation_id'] = array('in',$contractlist);
            $whereExitC['next_time'] = array('gt',date('Y-m-d 23:59:59'));
            $contractlist1 = Record::where($whereExitC)->count();
        }
        $contract = count($contractlist)-$contractlist1;
        if($contract <=0){
            $contract =0;
        }
        //待跟进线索
        $where3['relation_type'] = 4;
        $leadlist = Record::where($where)->where($where3)->column('relation_id');
        $leadlist1 = 0;
        if($leadlist){
            $whereExitL['relation_id'] = array('in',$leadlist);
            $whereExitL['next_time'] = array('gt',date('Y-m-d 23:59:59'));
            $leadlist1 = Record::where($whereExitL)->count();
        }
        $lead = count($leadlist)-$leadlist1;
        if($lead <=0){
            $lead =0;
        }
        //费用审核
        $consume = ExamineRecord::where([
            'relation_type'  => ExamineRecord::CONSUME_TYPE,
            'status'         => 0,
            'check_staff_id' => $this->_staff->id
        ])->count();
        //待处理日程
        $eventOne = Event::where([
            'type'       => 1,
            'status'     => ['in', [0, 1]],
            'staff_id'   => $this->_staff->id,
        ])->count();
        //待审核合同
        $examine = ExamineRecord::where([
            'relation_type'  => ExamineRecord::CONTRACT_TYPE,
            'status'         => 0,
            'check_staff_id' => $this->_staff->id
        ])->count();
        //待审核回款
        $receivables = ExamineRecord::where([
            'relation_type'  => ExamineRecord::RECEIVABLES_TYPE,
            'status'         => 0,
            'check_staff_id' => $this->_staff->id,
        ])->count();
        //待审批办公
        $approval = ExamineRecord::where([
            'relation_type'  => ExamineRecord::APPROVAL_TYPE,
            'status'         => 0,
            'check_staff_id' => $this->_staff->id,
        ])->count();
        //超时任务
        $eventtask = Event::where([
            'type'       => 1,
            'status'     => ['in', [0, 1]],
            'start_time' => ['lt', date('Y-m-d', strtotime('+1 day'))],
            'staff_id'   => $this->_staff->id,
        ])->count();
        //业绩审核
        $achievement = ExamineRecord::where([
            'relation_type'  => ExamineRecord::ACHIEVEMENT_TYPE,
            'status'         => 0,
            'check_staff_id' => $this->_staff->id
        ])->count();
        $info = array(
            'customer'=>$customer,
            'contract'=>$contract,
            'lead'=>$lead,
            'consume'=>$consume,
            'eventOne'=>$eventOne,
            'examine'=>$examine,
            'receivables'=>$receivables,
            'approval'=>$approval,
            'eventtask'=>$eventtask,
            'achievement'=>$achievement,
        );
        $this->view->assign('row',$info);
        return $this->view->fetch();
    }
    /**
     * 获取通知数量
     */
    public function get_need_number(){
         $where['create_staff_id'] = $this->_staff->id;
        $where['next_time'] = array(array('egt',date('Y-m-d 00:00:00')),array('lt',date('Y-m-d 23:59:59')));
        $where['follow_type'] = ['neq', '其它'];
        $where['status'] = 0;
        // 待跟进客户
        $where1['relation_type'] = 1;
        $customerlist = Record::where($where)->where($where1)->column('relation_id');
        $customerlist1 = 0;
        if($customerlist){
            $whereExit['relation_id'] = array('in',$customerlist);
            $whereExit['next_time'] = array('gt',date('Y-m-d 23:59:59'));
            $customerlist1 = Record::where($whereExit)->count();
        }
        $customer = count($customerlist) - $customerlist1;
        if($customer <=0){
            $customer =0;
        }
        //待跟进合同
        $where2['relation_type'] = 3;
        $contractlist = Record::where($where)->where($where2)->column('relation_id');
        $contractlist1 = 0;
        if($contractlist){
            $whereExitC['relation_id'] = array('in',$contractlist);
            $whereExitC['next_time'] = array('gt',date('Y-m-d 23:59:59'));
            $contractlist1 = Record::where($whereExitC)->count();
        }
        $contract = count($contractlist)-$contractlist1;
        if($contract <=0){
            $contract =0;
        }
        //待跟进线索
        $where3['relation_type'] = 4;
        $leadlist = Record::where($where)->where($where3)->column('relation_id');
        $leadlist1 = 0;
        if($leadlist){
            $whereExitL['relation_id'] = array('in',$leadlist);
            $whereExitL['next_time'] = array('gt',date('Y-m-d 23:59:59'));
            $leadlist1 = Record::where($whereExitL)->count();
        }
        $lead = count($leadlist)-$leadlist1;
        if($lead <=0){
            $lead =0;
        }
        //费用审核
        $consume = ExamineRecord::where([
            'relation_type'  => ExamineRecord::CONSUME_TYPE,
            'status'         => 0,
            'check_staff_id' => $this->_staff->id
        ])->count();

        //待处理日程
        $eventOne = Event::where([
            'type'       => 1,
            'status'     => ['in', [0, 1]],
            'staff_id'   => ['in',$this->_staff->id],
        ])->count();
        //待审核合同
        $examine = ExamineRecord::where([
            'relation_type'  => ExamineRecord::CONTRACT_TYPE,
            'status'         => 0,
            'check_staff_id' => $this->_staff->id
        ])->count();
        //待审核回款
        $receivables = ExamineRecord::where([
            'relation_type'  => ExamineRecord::RECEIVABLES_TYPE,
            'status'         => 0,
            'check_staff_id' => $this->_staff->id
        ])->count();
        //待审批办公
        $approval = ExamineRecord::where([
            'relation_type'  => ExamineRecord::APPROVAL_TYPE,
            'status'         => 0,
            'check_staff_id' =>  $this->_staff->id
        ])->count();
        //超时任务
        $eventtask = Event::where([
            'type'       => 1,
            'status'     => ['in', [0, 1]],
            'start_time' => ['lt', date('Y-m-d', strtotime('+1 day'))],
            'staff_id'   =>  $this->_staff->id
        ])->count();
        //业绩审核
        $achievement = ExamineRecord::where([
            'relation_type'  => ExamineRecord::ACHIEVEMENT_TYPE,
            'status'         => 0,
            'check_staff_id' => $this->_staff->id
        ])->count();
        $info = array(
            'customer'=>$customer,
            'contract'=>$contract,
            'lead'=>$lead,
            'consume'=>$consume,
            'eventOne'=>$eventOne,
            'examine'=>$examine,
            'receivables'=>$receivables,
            'approval'=>$approval,
            'eventtask'=>$eventtask,
            'achievement'=>$achievement,
        );
        $number=array_sum(array_values($info));
        $info['number']=$number;
        $this->success('请求成功','',$info);
    }
}