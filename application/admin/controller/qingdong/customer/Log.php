<?php

namespace app\admin\controller\qingdong\customer;

use app\admin\controller\qingdong\Base;
use addons\qingdong\model\OperationLog;
use think\Db;
use think\Exception;

/**
 * 操作文档：https://doc.fastadmin.net/qingdong
 * 软件介绍：https://www.fastadmin.net/store/qingdong.html
 * 售后微信：qingdong_crm
 * 日志管理
 */
class Log extends Base
{
    protected $model = null;
    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \addons\qingdong\model\OperationLog;
    }
    /**
     * 查看
     */
    public function index()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        $customer_id = input('customer_id','','trim');
        $contacts_id = input('contacts_id','','trim');
        $leads_id = input('leads_id','','trim');
        $contract_id = input('contract_id','','trim');
        if ($this->request->isAjax()) {
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            $wheres = [];
            if(isset($customer_id) && $customer_id){
                $wheres['relation_type'] = 1;
                $wheres['relation_id'] = $customer_id;
            }
            if(isset($contacts_id) && $contacts_id){
                $wheres['relation_type'] = 2;
                $wheres['relation_id'] = $contacts_id;
            }
            if(isset($leads_id) && $leads_id){
                $wheres['relation_type'] = 4;
                $wheres['relation_id'] = $leads_id;
            }
            if(isset($contract_id) && $contract_id){
                $wheres['relation_type'] = 3;
                $wheres['relation_id'] = $contract_id;
            }
            $list = $this->model->with('staff')->where($where)->where($wheres)
                ->order($sort, $order)->paginate($limit);
            $rows = $list->items();

            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }

        return $this->view->fetch();
    }
}