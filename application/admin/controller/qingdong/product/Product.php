<?php

namespace app\admin\controller\qingdong\product;

use addons\qingdong\model\Delivery;
use addons\qingdong\model\DeliveryProduct;
use addons\qingdong\model\Goods;
use addons\qingdong\model\InstockProduct;
use addons\qingdong\model\ProductPart;
use addons\qingdong\model\Producttype;
use addons\qingdong\model\ProductWarehouse;
use addons\qingdong\model\Staff;
use app\admin\controller\qingdong\Base;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use PhpOffice\PhpSpreadsheet\Reader\Csv;
use PhpOffice\PhpSpreadsheet\Reader\Xls;
use PhpOffice\PhpSpreadsheet\Reader\Xlsx;
use PhpOffice\PhpSpreadsheet\RichText\RichText;
use think\Db;
use think\Exception;
/**
 * 产品列表
 * 操作文档：https://doc.fastadmin.net/qingdong
 * 软件介绍：https://www.fastadmin.net/store/qingdong.html
 * 售后微信：qingdong_crm
 */

class Product extends Base
{
    protected $relationSearch = true;
    protected $searchFields = 'id,name';
    /**
     * @var \addons\qingdong\model\Product
     */
    protected $model = null;


    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \addons\qingdong\model\Product;
    }


    /**
     * 产品列表
     */
    public function index()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {

            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            //负责人为空
            $list = $this->model->where($where)->order($sort, $order)->with(['producttype','goods'])->paginate($limit);
            $row=$list->items();
            foreach ($row as $k => $v) {
                $v['type_id'] = $v['producttype']['name']??'';
                $row[$k] = $v;
            }
            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }

        return $this->view->fetch();
    }


    /**
     * 添加产品
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                $params = $this->preExcludeFields($params);

                $result = false;
                Db::startTrans();
                try {
                    $result = $this->model->allowField(true)->save($params);
                    Db::commit();
                } catch (Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
                if ($result !== false) {
                    $this->success();
                } else {
                    $this->error(__('No rows were inserted'));
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        return $this->view->fetch();
    }

    /**
     * 修改产品
     */
    public function edit($ids = null)
    {
        $map['id'] = $ids;
        if ($this->request->isAjax()) {
            $params = $this->request->post('row/a');
            $params = $this->preExcludeFields($params);
            $result = false;
            Db::startTrans();
            try {
                $result = $this->model->allowField(true)->update($params, $map);
                Db::commit();
            } catch (Exception $e) {
                Db::rollback();
                $this->error($e->getMessage());
            }
            if ($result !== false) {
                $this->success('修改成功');
            } else {
                $this->error('修改失败');
            }
        }
        $data = $this->model->where($map)->find();

        $this->view->assign("row", $data);

        return $this->view->fetch();
    }


    /**
     * 产品详情
     */
    public function detail($ids = null)
    {
        $row = $this->model->get($ids);
        $this->assign('row', $row);
        $this->assign('ids', $ids);
        return $this->view->fetch();
    }

    /**
     * 删除产品
     */
    public function del($ids = "")
    {
        if (!$this->request->isPost()) {
            $this->error(__("Invalid parameters"));
        }
        $ids = $ids ? $ids : $this->request->post("ids");
        $row = $this->model->get($ids);
        $this->modelValidate = true;
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $row->delete();
        $this->success();
    }

    //导入
    public function import()
    {
        set_time_limit(0);
        if ($this->request->isPost()) {

            $file = $this->request->request('file');
            $staff_id = $this->request->request('staff_id', 0);
            if (!$file) {
                $this->error(__('Parameter %s can not be empty', 'file'));
            }
            $filePath = ROOT_PATH . 'public' . $file;
            if (!is_file($filePath)) {
                $this->error(__('No results were found'));
            }
            //实例化reader
            $ext = pathinfo($filePath, PATHINFO_EXTENSION);
            if (!in_array($ext, ['csv', 'xls', 'xlsx'])) {
                $this->error(__('Unknown data format'));
            }
            if ($ext === 'csv') {
                $file = fopen($filePath, 'r');
                $filePath = tempnam(sys_get_temp_dir(), 'import_csv');
                $fp = fopen($filePath, "w");
                $n = 0;
                while ($line = fgets($file)) {
                    $line = rtrim($line, "\n\r\0");
                    $encoding = mb_detect_encoding($line, ['utf-8', 'gbk', 'latin1', 'big5']);
                    if ($encoding != 'utf-8') {
                        $line = mb_convert_encoding($line, 'utf-8', $encoding);
                    }
                    if ($n == 0 || preg_match('/^".*"$/', $line)) {
                        fwrite($fp, $line . "\n");
                    } else {
                        fwrite($fp, '"' . str_replace(['"', ','], ['""', '","'], $line) . "\"\n");
                    }
                    $n++;
                }
                fclose($file) || fclose($fp);

                $reader = new Csv();
            } elseif ($ext === 'xls') {
                $reader = new Xls();
            } else {
                $reader = new Xlsx();
            }
            if (!$PHPExcel = $reader->load($filePath)) {
                $this->error(__('Unknown data format'));
            }
            $currentSheet = $PHPExcel->getSheet(0);  //读取文件中的第一个工作表

            $allColumn = $currentSheet->getHighestDataColumn(); //取得最大的列号
            $allRow = $currentSheet->getHighestRow(); //取得一共有多少行

            $maxColumnNumber = Coordinate::columnIndexFromString($allColumn);
            //开始读取数据
            $fields = [];
            for ($currentRow = 1; $currentRow <= 1; $currentRow++) {
                for ($currentColumn = 1; $currentColumn <= $maxColumnNumber; $currentColumn++) {
                    $val = $currentSheet->getCellByColumnAndRow($currentColumn, $currentRow)->getValue();
                    $fields[$currentRow][] = $val;
                    if($val instanceof RichText) {//富文本转换字符串
                        $val = $val->__toString();
                    }
                    $values[] = is_null($val) ? '' : trim($val);
                }
            }
            if (!isset($fields[1])) {
                $this->error('导入文件第一行没有数据');
            }
            $types=Producttype::where([])->column('id','name');
            $goods=Goods::where([])->column('id','name');
            $addProduct=[];
            $errorInfo=[];
            for ($currentRow = 2; $currentRow <= $allRow; $currentRow++) {
                $values = [];
                for ($currentColumn = 1; $currentColumn <= $maxColumnNumber; $currentColumn++) {
                    $val = $currentSheet->getCellByColumnAndRow($currentColumn, $currentRow)->getValue();
                    if($val instanceof RichText) {//富文本转换字符串
                        $val = $val->__toString();
                    }
                    $values[] = is_null($val) ? '' : trim($val);
                }

                $params = [
                    'name' => $values[0],
                    'type_id' => $types[$values[1]] ?? 0,
                    'num' => $values[2],
                    'goods_id' => $goods[$values[3]] ?? 0,
                    'price' => $values[4],
                    'cost_price' => $values[5],
                    'unit' => $values[6],
                    'stock' => $values[7],
                    'instock' => $values[8],
                    'outstock' => $values[9],
                    'status' => '上架'
                ];
                $addProduct[] = $params;
            }
            if (!empty($errorInfo)) {
                $this->error(implode(',', $errorInfo));
            }
            Db::startTrans();
            try {
                $this->model->allowField(true)->saveAll($addProduct);
                Db::commit();
            } catch (Exception $e) {
                Db::rollback();
                $this->error($e->getMessage());
            }
            $this->success('导入成功');
        }
        $this->assign('staffs', Staff::getList());
        return $this->view->fetch();
    }

    /**
     * 获取产品分类
     */
    public function get_type(){
        $type = input('type');
        if ($type == 'search') {//搜索项
            $types = Producttype::where([])->field('id,name')->select();
            return json($types);
        }
        $where = [];
        if ($keyValue = $this->request->request("keyValue")) {
            $where['id'] = $keyValue;
        }
        $name       = input('name');
        if(!empty($name)){
            $where['name'] = ['like','%'.$name.'%'];
        }
        $countrys = Producttype::where($where)->field('id,name')->order('id desc')->select();
        return json(['list' => $countrys, 'total' => count($countrys)]);
    }
    /**
     * 配件列表
     */
    public function part_list()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            $product_id = input('ids');

            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            $model =new ProductPart();
            //负责人为空
            $list = $model->where(['product_id' => $product_id])->order('id desc')->paginate($limit);

            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }

        return $this->view->fetch();
    }

    /**
     * 添加配件
     */
    public function add_part()
    {
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            $product_id=input('product_id',0);
            if(empty($product_id)){
                $this->error('参数错误');
            }
            $model = new ProductPart();
            if ($params) {
                $params = $this->preExcludeFields($params);

                $result = false;
                Db::startTrans();
                try {
                    $params['product_id'] = $product_id;
                    $result = $model->allowField(true)->save($params);
                    Db::commit();
                } catch (Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
                if ($result !== false) {
                    $this->success();
                } else {
                    $this->error(__('No rows were inserted'));
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        return $this->view->fetch();
    }

    /**
     * 修改配件
     */
    public function edit_part($ids = null)
    {
        $map['id'] = $ids;
        $model = new ProductPart();
        if ($this->request->isAjax()) {
            $params = $this->request->post('row/a');
            $params = $this->preExcludeFields($params);
            $result = false;
            Db::startTrans();
            try {
                $result = $model->allowField(true)->update($params, $map);
                Db::commit();
            } catch (Exception $e) {
                Db::rollback();
                $this->error($e->getMessage());
            }
            if ($result !== false) {
                $this->success('修改成功');
            } else {
                $this->error('修改失败');
            }
        }
        $data = $model->where($map)->find();
        $this->view->assign("row", $data);

        return $this->view->fetch();
    }

    /**
     * 删除配件
     */
    public function del_part($ids = "")
    {
        if (!$this->request->isPost()) {
            $this->error(__("Invalid parameters"));
        }
        $ids = $ids ? $ids : $this->request->post("ids");

        $model = new ProductPart();

        $row = $model->get($ids);
        $this->modelValidate = true;
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $row->delete();
        $this->success();
    }
}
