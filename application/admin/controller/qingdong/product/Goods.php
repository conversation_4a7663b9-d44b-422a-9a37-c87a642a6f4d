<?php

namespace app\admin\controller\qingdong\product;

use addons\qingdong\model\Product;
use addons\qingdong\model\Producttype;
use addons\qingdong\model\Staff;
use app\admin\controller\qingdong\Base;
use addons\qingdong\model\Specs;
use addons\qingdong\model\GoodsUnit;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use PhpOffice\PhpSpreadsheet\Reader\Csv;
use PhpOffice\PhpSpreadsheet\Reader\Xls;
use PhpOffice\PhpSpreadsheet\Reader\Xlsx;
use PhpOffice\PhpSpreadsheet\RichText\RichText;
use think\Db;
use think\Exception;

/**
 * 商品管理
 * @icon fa fa-pied-piper-pp
 */
class Goods extends Base
{

    /**
     * Goods模型对象
     * @var \addons\qingdong\model\Goods
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \addons\qingdong\model\Goods();

    }

    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = false;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            $list = $this->model
                ->with(['type','product'])
                ->where($where)
                ->order($sort, $order)
                ->paginate($limit);


            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }

    /**
     * 新增
     * @return string|void
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                $params = $this->preExcludeFields($params);
                $productModel = new Product();
                if($params['is_specs'] ==1){ //有规格
                    $product = json_decode($params['product'], true);
                    if(!$product){
                        $this->error('请选择规格');
                    }
                    foreach ($product as $k=>$v) {
                        if(!$v['price']){
                            $this->error('零销价必须大于0');
                        }
                        if(!$v['wholesale']){
                            $this->error('批发价必须大于0');
                        }
                    }
                    $addProduct = [];
                    foreach ($product as $k=>$v) {
                        $addProduct[] = [
                            'num' => 'P'.time().$k,
                            'type_id' => $params['type_id'],
                            'type' => $v['type'],
                            'name' => $params['name'] . $v['name'],
                            'price' => $v['price'],
                            'wholesale' => $v['wholesale'],
                            'cost_price' => $v['cost_price'],
                            'img' => $v['avatar'],
                            'unit' => $params['unit'],
                            'status' => '上架',
                            'description' => $v['remarks'],
                            'createtime' => time(),
                            'updatetime' => time()
                        ];
                    }

                    $result = false;
                    Db::startTrans();
                    try {
                        $params['price'] = 0;
                        $params['wholesale']=0;
                        $result = $this->model->allowField(true)->save($params);
                        foreach ($addProduct as $k => $v) {
                            $v['goods_id'] = $this->model->id;
                            $addProduct[$k] = $v;
                        }
                        $productResult = $productModel->allowField(true)->saveAll($addProduct);
                        if(!$result || !$productResult){
                            throw new Exception('添加失败');
                        }
                        Db::commit();
                    } catch (Exception $e) {
                        Db::rollback();
                        $this->error($e->getMessage());
                    }

                }else{
                    Db::startTrans();
                    try {
                        $result = $this->model->allowField(true)->save($params);
                        $addProduct = [
                            'goods_id' => $this->model->id,
                            'num' => 'P'.time(),
                            'type_id' => $params['type_id'],
                            'name' => $params['name'],
                            'price' => $params['price'],
                            'wholesale' => $params['wholesale'],
                            'cost_price' => $params['cost_price'],
                            'img' => $params['img'],
                            'unit' => $params['unit'],
                            'description' => $params['description'],
                            'status' => '上架',
                            'createtime' => time(),
                            'updatetime' => time()
                        ];
                        $productResult = $productModel->allowField(true)->save($addProduct);
                        if(!$result || !$productResult){
                            throw new Exception('添加失败');
                        }
                        Db::commit();
                    } catch (Exception $e) {
                        Db::rollback();
                        $this->error($e->getMessage());
                    }
                }
                $this->success();
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $unit = GoodsUnit::where(['status'=>1])->order('sort desc')->select();
        $num = 'G'.genRandomString(10,2);
        $this->assign('num', $num);
        $this->assign('unit', $unit);
        return $this->view->fetch();
    }

    /**
     * 编辑
     * @return string|void
     */
    public function edit($ids=null)
    {
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                $params = $this->preExcludeFields($params);
                $productModel = new Product();
                if($params['is_specs'] ==1) { //有规格

                    $product = json_decode($params['product'], true);
                    if(!$product){
                        $this->error('请选择规格');
                    }
                    foreach ($product as $k=>$v) {
                        if(!$v['price']){
                            $this->error('零销价必须大于0');
                        }
                        if(!$v['wholesale']){
                            $this->error('批发价必须大于0');
                        }
                    }
                    $result = false;
                    $productId = [];
                    Db::startTrans();
                    try {
                        foreach ($product as $k=>$v) {
                            $addProduct = [
                                'type_id' => $params['type_id'],
                                'type' => $v['type'],
                                'name' => $params['name'] . $v['name'],
                                'price' => $v['price'],
                                'wholesale' => $v['wholesale'],
                                'cost_price' => $v['cost_price'],
                                'img' => $v['avatar'],
                                'unit' => $params['unit'],
                                'status' => '上架',
                                'description' => $v['remarks'],
                                'createtime' => time(),
                                'updatetime' => time()
                            ];
                            $productData = $productModel->where(array('goods_id'=>$ids,'type'=>$v['type']))->find();
                            if($productData){
                                $addProduct['name'] = $v['name'];
                                $productRes = $productModel->where(array('id'=>$productData['id']))->update($addProduct);
                                $productIds = $productData['id'];
                            }else{
                                $addProduct['goods_id']=$ids;
                                $addProduct['num']= 'P'.time().$k;
                                $addProduct['createtime']  =time();
                                $productRes = $productModel->allowField(true)->create($addProduct);
                                $productIds = (int)$productModel->getLastInsID();
                            }
                            if(!$productRes){
                                throw new Exception('修改失败');
                            }
                            $productId[] =$productIds;
                        }
                        $productModel->where(['id'=>['not in',$productId],'goods_id'=>$ids])->update(['updatetime'=>time(),'deletetime'=>time()]);
                        $params['price'] = 0;
                        $params['wholesale']=0;
                        $result = $this->model->allowField(true)->save($params,['id'=>$ids]);
                        if(!$result){
                            throw new Exception('修改失败');
                        }
                        Db::commit();
                    } catch (Exception $e) {
                        Db::rollback();
                        $this->error($e->getMessage());
                    }
                }else{
                    try {
                        $addProduct = [
                            'goods_id' => $ids,
                            'num' => 'P'.time(),
                            'type_id' => $params['type_id'],
                            'name' => $params['name'],
                            'price' => $params['price'],
                            'wholesale' => $params['wholesale'],
                            'cost_price' => $params['cost_price'],
                            'img' => $params['img'],
                            'unit' => $params['unit'],
                            'description' => $params['description'],
                            'status' => '上架',
                            'createtime' => time(),
                            'updatetime' => time()
                        ];
                        $productinfo = $productModel->where(['goods_id'=>$ids])->find();
                        if($productinfo){
                            $productModel->allowField(true)->save($addProduct,['goods_id'=>$ids]);
                        }else{
                            $productModel->allowField(true)->save($addProduct);
                        }
                        $result = $this->model->allowField(true)->save($params,['id'=>$ids]);

                        if(!$result){
                            throw new Exception('修改失败');
                        }
                        Db::commit();
                    } catch (Exception $e) {
                        Db::rollback();
                        $this->error($e->getMessage());
                    }
                }

                $this->success();
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }

        $row = $this->model->with(['type'])->where(['id'=>$ids])->find()->toArray();
        $product=Product::where(['goods_id'=>$ids])->select();
        $row['product']=json_encode($product,true);
        $unit = GoodsUnit::where(['status'=>1])->order('sort desc')->select();
        $this->assign('unit', $unit);
        $this->assign('row', $row);
        return $this->view->fetch();
    }

    //导入
    public function import()
    {
        set_time_limit(0);
        if ($this->request->isPost()) {

            $file = $this->request->request('file');
            $staff_id = $this->request->request('staff_id', 0);
            if (!$file) {
                $this->error(__('Parameter %s can not be empty', 'file'));
            }
            $filePath = ROOT_PATH . 'public' . $file;
            if (!is_file($filePath)) {
                $this->error(__('No results were found'));
            }
            //实例化reader
            $ext = pathinfo($filePath, PATHINFO_EXTENSION);
            if (!in_array($ext, ['csv', 'xls', 'xlsx'])) {
                $this->error(__('Unknown data format'));
            }
            if ($ext === 'csv') {
                $file = fopen($filePath, 'r');
                $filePath = tempnam(sys_get_temp_dir(), 'import_csv');
                $fp = fopen($filePath, "w");
                $n = 0;
                while ($line = fgets($file)) {
                    $line = rtrim($line, "\n\r\0");
                    $encoding = mb_detect_encoding($line, ['utf-8', 'gbk', 'latin1', 'big5']);
                    if ($encoding != 'utf-8') {
                        $line = mb_convert_encoding($line, 'utf-8', $encoding);
                    }
                    if ($n == 0 || preg_match('/^".*"$/', $line)) {
                        fwrite($fp, $line . "\n");
                    } else {
                        fwrite($fp, '"' . str_replace(['"', ','], ['""', '","'], $line) . "\"\n");
                    }
                    $n++;
                }
                fclose($file) || fclose($fp);

                $reader = new Csv();
            } elseif ($ext === 'xls') {
                $reader = new Xls();
            } else {
                $reader = new Xlsx();
            }
            if (!$PHPExcel = $reader->load($filePath)) {
                $this->error(__('Unknown data format'));
            }
            $currentSheet = $PHPExcel->getSheet(0);  //读取文件中的第一个工作表

            $allColumn = $currentSheet->getHighestDataColumn(); //取得最大的列号
            $allRow = $currentSheet->getHighestRow(); //取得一共有多少行

            $maxColumnNumber = Coordinate::columnIndexFromString($allColumn);
            //开始读取数据
            $fields = [];
            for ($currentRow = 1; $currentRow <= 1; $currentRow++) {
                for ($currentColumn = 1; $currentColumn <= $maxColumnNumber; $currentColumn++) {
                    $val = $currentSheet->getCellByColumnAndRow($currentColumn, $currentRow)->getValue();
                    $fields[$currentRow][] = $val;
                    if($val instanceof RichText) {//富文本转换字符串
                        $val = $val->__toString();
                    }
                    $values[] = is_null($val) ? '' : trim($val);
                }
            }
            if (!isset($fields[1])) {
                $this->error('导入文件第一行没有数据');
            }
            $types=Producttype::withTrashed()->where([])->column('id','name');
            $addShop=[];
            $errorInfo=[];

            for ($currentRow = 2; $currentRow <= $allRow; $currentRow++) {
                $values = [];
                for ($currentColumn = 1; $currentColumn <= $maxColumnNumber; $currentColumn++) {
                    $val = $currentSheet->getCellByColumnAndRow($currentColumn, $currentRow)->getValue();
                    if($val instanceof RichText) {//富文本转换字符串
                        $val = $val->__toString();
                    }
                    $values[] = is_null($val) ? '' : trim($val);
                }
                if(!$values[0]){
                    continue;
                }
                $params=[
                    'name'=>$values[0],
                    'type_id'=>$types[$values[1]]??0,
                    'price'=>$values[2],
                    'unit'=>$values[3],
                    'wholesale'=>$values[4],
                    'cost_price'=>$values[5],
                    'status'=>'上架',
                    'createtime'=>time(),
                    'updatetime'=>time()
                ];
                    Db::startTrans();
                    try {
                        $goodsRe = $this->model->allowField(true)->save($params);
                        if(!$goodsRe){
                            throw new Exception('商品导入失败');
                        }
                        $addProduct = [
                            'goods_id' => $this->model->getLastInsID(),
                            'num' => 'P'.time(),
                            'type_id' => $types[$values[1]]??0,
                            'name' => $values[0],
                            'price' => $values[2],
                            'unit'=>$values[3],
                            'wholesale'=>$values[4],
                            'cost_price'=>$values[5],
                            'status' => '上架',
                            'createtime' => time(),
                            'updatetime' => time()
                        ];
                        $productModel = new Product();
                        $productResult = $productModel->allowField(true)->save($addProduct);
                        if(!$productResult){
                            throw new Exception('商品导入失败');
                        }
                       Db::commit();
                    } catch (Exception $e) {
                        Db::rollback();
                        $this->error($e->getMessage());
                    }
                }
            if (!empty($errorInfo)) {
                $this->error(implode(',', $errorInfo));
            }



            $this->success('导入成功');
        }
        $this->assign('staffs', Staff::getList());
        return $this->view->fetch();
    }

    /**
     * 商品详情
     */
    public function detail($ids = null)
    {
        $row = $this->model->with(['type'])->where(['id'=>$ids])->find();
        $this->assign('row', $row);
        $this->assign('ids', $ids);
        return $this->view->fetch();
    }

    /**
     * 获取商品 产品列表
     */
    public function get_product()
    {
        $goods_id = input('goods_id');
        $where = [];
        if ($keyValue = $this->request->request("keyValue")) {
            $where['id'] = $keyValue;
        }
        $name = input('name');
        if (!empty($name)) {
            $where['name'] = ['like', '%' . $name . '%'];
        }
        if ($goods_id) {
            $where['goods_id'] = $goods_id;
        }
        $product = Product::where($where)->with(['producttype'])->order('id desc')->paginate();

        $result = array("total" => $product->total(), "rows" => $product->items());

        return json($result);
    }

    /**
     * 获取产品分类
     */
    public function get_product_type()
    {
        $pageSize = input('pageSize');
        $pageNumber = input('pageNumber');
        $where = [];
        if ($keyValue = $this->request->request("keyValue")) {
            $where['id'] = $keyValue;
        }
        if(empty($pageSize) && empty($keyValue)){
            return json(Producttype::where($where)->field('id,name')->select());
        }
        $name = input('name');
        if (!empty($name)) {
            $where['name'] = ['like', '%' . $name . '%'];
        }
        $producttype = Producttype::where($where)->field('id,name')->order('id desc')->paginate($pageSize, false, ['page' => $pageNumber]);
        return json(['list' => $producttype->items(), 'total' => $producttype->total()]);
    }

    /**
     * 获取规格
     */
    public function get_specs()
    {
        $where = [];
        if ($keyValue = $this->request->request("keyValue")) {
            $where['id'] = $keyValue;
        }
        $name = input('name');
        if (!empty($name)) {
            $where['name'] = ['like', '%' . $name . '%'];
        }
        $countrys = Specs::where($where)->field('id,name')->order('id desc')->select();
        return json(['list' => $countrys, 'total' => count($countrys)]);
    }

    /**
     * 获取规格详情
     */
    public function get_specs_detail()
    {
        $ids = input('ids');
        $specs_id = input('specs_id');
        $good = [];
        if($ids){
            $where['goods_id'] = $ids;
            $product = Product::where($where)->column('type');
            if($product){
                foreach($product as $k=>$v){
                    $good[$v] = $v;
                }
            }
        }
        $specs = Specs::where(['id' => $specs_id])->find();
        $content = json_decode($specs['content'], true);
        if($content && is_array($content)){
            foreach($content as $k=>$v){
                $content[$k]['check'] =0;
                if($good){
                    if(key_exists($v['name'],$good)){
                        $content[$k]['check'] =1;
                    }
                }
            }
        }

        $this->success('请求成功', '', $content);
    }
    /**
     * 删除商品
     */
    public function del($ids = "")
    {
        if (!$this->request->isPost()) {
            $this->error(__("Invalid parameters"));
        }
        $ids = $ids ? $ids : $this->request->post("ids");
        $row = $this->model->get($ids);
        $this->modelValidate = true;
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $model=new Product();
        $model->where(['goods_id'=>$ids])->delete();
        $row->delete();
        $this->success();
    }

}
