<?php

namespace app\admin\controller\qingdong\canlendar;
use addons\qingdong\model\Staff;
use app\admin\controller\qingdong\Base;
use addons\qingdong\model\Event;
use addons\qingdong\model\Customer;
use addons\qingdong\model\Contacts;
use addons\qingdong\model\Contract;
use addons\qingdong\model\Leads;
use think\DB;
use fast\Tree;


/**
 * 日程
 */
class Canlendar extends Base {
	public function _initialize() {
		parent::_initialize();
		$this->model = new Event();
	}


    /**
     * 日程列表
     */
	public function index() {

		$this->request->filter(['strip_tags']);
        $need = input('need','');
		if ($this->request->isAjax()) {
			list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            $wheres['type'] =1;
			$wheres['staff_id'] = ['in',Staff::getMyStaffIds()];
            if(isset($need) && $need  == 'canlendar'){
                $wheres['staff_id'] = $this->_staff->id;
                $wheres['status'] = array('in','0,1');
            }
            if(isset($need) && $need  == 'task'){
                $wheres['staff_id'] = $this->_staff->id;
                $wheres['status'] = array('in','0,1');
                $wheres['start_time'] = ['lt', date('Y-m-d', strtotime('+1 day'))];
            }

			$list   = $this->model->where($where)->where($wheres)->order($sort, $order)->paginate($limit);
			$row    = $list->items();
			$result = array("total" => $list->total(), "rows" => $row);

			return json($result);
		}

		return $this->view->fetch();
	}


    /**
     * 添加日程
     */
	public function add() {
		if ($this->request->isAjax()) {
			$data             = $this->request->post('row/a');
			$staff = Staff::info();
            $data['staff_id'] = $staff->id;
			$result           = $this->model->save($data);
			if (!$result) {
				$this->error('提交失败');
			}
			$this->success('提交成功');
		}
        $customer = Customer::where(['owner_staff_id'=>['in',Staff::getMyStaffIds()]])->field('id,name')->select();
        $this->view->assign("customer", $customer);
		return $this->view->fetch();
	}

    /**
     * 修改日程
     */
	public function edit($ids = null) {
		$map['id'] = $ids;
		if ($this->request->isAjax()) {
			$data   = $this->request->post('row/a');
			$result = $this->model->save($data, $map);

			if (!$result) {
				$this->error('修改失败');
			}
			$this->success('修改成功');
		}

		$data = $this->model->where($map)->find();
		if($data['relation_type'] == 1){
            $customer = Customer::where(['owner_staff_id'=>['in',Staff::getMyStaffIds()]])->field('id,name')->select();
        }elseif($data['relation_type'] == 2){
            $customer = Contacts::where(['owner_staff_id'=>['in',Staff::getMyStaffIds()]])->field('id,name')->select();
        }elseif($data['relation_type'] == 3){
            $customer = Contract::where(['owner_staff_id'=>['in',Staff::getMyStaffIds()]])->field('id,name')->select();
        }elseif($data['relation_type'] == 4){
            $customer = Leads::where(['owner_staff_id'=>['in',Staff::getMyStaffIds()]])->field('id,name')->select();
        }else{
            $customer = Customer::where(['owner_staff_id'=>['in',Staff::getMyStaffIds()]])->field('id,name')->select();
        }
        $this->view->assign("customer", $customer);
		$this->view->assign("row", $data);

		return $this->view->fetch();
	}

    /**
     * 删除日程
     */
	public function del($ids = null) {
		if ($this->request->isAjax()) {
			$map['id'] = array('in', $ids);
			$result    = $this->model->destroy($map);
			if (!$result) {
				$this->error('删除失败');
			}
			$this->success('删除成功');
		}

		return $this->view->fetch();
	}

    /**
     * 获取客户列表
     */
    public function customer(){
	    $list = Customer::where(['owner_staff_id'=>['in',Staff::getMyStaffIds()]])->field('id,name')->select();
	    $this->success('','',$list);
    }

    /**
     * 获取联系人列表
     */
    public function contacts(){
        $list = Contacts::where(['owner_staff_id'=>['in',Staff::getMyStaffIds()]])->field('id,name')->select();
        $this->success('','',$list);
    }
    /**
     * 获取合同列表
     */
    public function contract(){
        $list = Contract::where(['owner_staff_id'=>['in',Staff::getMyStaffIds()]])->field('id,name')->select();
        $this->success('','',$list);
    }
    /**
     * 获取线索列表
     */
    public function leads(){
        $list = Leads::where(['owner_staff_id'=>['in',Staff::getMyStaffIds()]])->field('id,name')->select();
        $this->success('','',$list);
    }
}