<?php

namespace app\admin\controller\qingdong;

use addons\qingdong\controller\File;
use addons\qingdong\model\Staff;
use app\common\controller\Backend;
use app\common\exception\UploadException;
use app\common\library\Upload;

/**
 * qingdong 基础控制器
 *
 * @icon fa fa-circle-o
 */
class Base extends Backend
{

    protected $noNeedRight = [];
    protected $_staff = null;

    public function _initialize()
    {
        parent::_initialize();
        $staff = Staff::where(['admin_id' => $this->auth->id])->find();
        if (!empty($staff)) {
            $this->_staff = $staff;
        }else{
            $data = [
                'admin_id' => $this->auth->id,
                'group_ids' => implode(',', $this->auth->getGroupIds()),
                'name' => $this->auth->nickname,
                'num'=>'01',
                'mobile' => $this->auth->username,
                'salt' => $this->auth->salt,
                'password' => $this->auth->password,
                'img' => '/assets/addons/qingdong/mini/avatar.png',
                'email' => $this->auth->email,
                'sex' => 1,
                'status' => 1,
                'createtime' => $this->auth->createtime,
                'updatetime' => $this->auth->updatetime
            ];
            $staffModel = new Staff();
            $result = $staffModel->save($data);
            if ($result) {
                $staff = Staff::where(['admin_id' => $this->auth->id])->find();
                $this->_staff = $staff;
            } else {
                $this->error('当前账号未绑定员工,要使用插件功能，请在员工列表添加账号', 'qingdong/department/staff');
            }
        }
    }

    public function getStaff()
    {
        return $this->_staff;
    }


    protected function qingdongValidate($params, $class, $scene, $rules = []) {
        $validate = validate('addons\qingdong\validate\\'.$class);
        if (!$validate->check($params, $rules, $scene)) {
            return $validate->getError();
        }
        return true;
    }

    /**
     * 上传文件
     * @ApiMethod (POST)
     * @param File $file 文件流
     */
    public function upload() {

        $attachment = null;
        //默认普通上传文件
        $file = $this->request->file('file');
        try {
            $upload     = new Upload($file);
            $attachment = $upload->upload();
            $info = $attachment->toArray();
            $file = new \addons\qingdong\model\File();
            $params = [
                'name'          =>  $info['filename'],
                'save_name'     =>  $info['url'],
                'size'          =>  isset($info['filesize'])?$info['filesize']:0,
                'types'         =>  $info['mimetype'],
                'file_path'     =>  $info['url'],
                'create_staff_id'     =>  empty($staff)?0:$staff->id,
            ];
            $file->data(array_filter($params));
            $file->save();
            $fileId = $file->id;

        } catch (UploadException $e) {
            return json_encode(['code' => 0, 'msg' => $e->getMessage()]);
        }
        $this->success(__('Uploaded successful'),'', [
            'id'  => $fileId,
            'url' =>$params['file_path']
        ]);
    }
}
