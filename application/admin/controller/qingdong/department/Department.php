<?php

namespace app\admin\controller\qingdong\department;
use app\admin\controller\qingdong\Base;
use addons\qingdong\model\StaffDepartment;
use app\common\controller\Backend;
use fast\Tree;
/**
 * 部门列表
 */
class Department extends Backend {
	public function _initialize() {
		parent::_initialize();
		$this->model = new StaffDepartment();
		// 必须将结果集转换为数组
		$ruleList = collection($this->model->where([])->order('id', 'desc')->select())->toArray();
		foreach ($ruleList as $k => &$v) {
			$v['name'] = __($v['name']);
		}
		unset($v);
		Tree::instance()->init($ruleList);
		$this->rulelist = Tree::instance()->getTreeList(Tree::instance()->getTreeArray(0), 'name');

		$ruledata = [0 => __('None')];
		foreach ($this->rulelist as $k => &$v) {
			$ruledata[$v['id']] = $v['name'];
		}

		$this->view->assign('department', $ruledata);
	}

    /**
     * 部门列表
     */
	public function index() {
		$this->request->filter(['strip_tags']);
		if ($this->request->isAjax()) {

			$list  = $this->rulelist;
			$total = count($this->rulelist);

			$result = array("total" => $total, "rows" => $list);

			return json($result);
		}

		return $this->view->fetch();
	}


    /**
     * 添加部门
     */
	public function add() {
		if ($this->request->isAjax()) {
			$data   = $this->request->post('row/a');
			$result = StaffDepartment::create($data);
			if (!$result) {
				$this->error('提交失败');
			}
			$this->success('提交成功');
		}

		return $this->view->fetch();
	}

    /**
     * 修改部门
     */
	public function edit($ids = null) {
		$map['id'] = $ids;
		if ($this->request->isAjax()) {
			$data   = $this->request->post('row/a');
			$result = StaffDepartment::update($data, $map);

			if (!$result) {
				$this->error('修改失败');
			}
			$this->success('修改成功');
		}
		$data = StaffDepartment::where($map)->find();
		$this->view->assign("row", $data);

		return $this->view->fetch();
	}


    /**
     * 删除部门
     */
	public function del($ids = null) {
		if ($this->request->isAjax()) {

            //删除子部门
			$where=['pid' => array('in', $ids)];
			$result    = StaffDepartment::destroy($where);

			$map['id'] = array('in', $ids);
			$result    = StaffDepartment::destroy($map);
			if (!$result) {
				$this->error('删除失败');
			}
			$this->success('删除成功');
		}

		return $this->view->fetch();
	}
}