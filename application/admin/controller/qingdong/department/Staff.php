<?php

namespace app\admin\controller\qingdong\department;

use addons\qingdong\model\Customer;
use addons\qingdong\model\Staff as StaffModel;
use addons\qingdong\model\StaffRole;
use app\admin\model\AuthGroup;
use app\admin\controller\qingdong\Base;
use fast\Tree;
use think\Db;
use think\Exception;
use app\admin\model\Admin;

/**
 * 员工管理
 */
class Staff extends  Base {

	public function _initialize() {
		parent::_initialize();
		$this->model      = new StaffModel();

        $this->childrenAdminIds = $this->auth->getChildrenAdminIds($this->auth->isSuperAdmin());
        $this->childrenGroupIds = $this->auth->getChildrenGroupIds($this->auth->isSuperAdmin());

        //角色组
        $groupList = collection(AuthGroup::where('id', 'in', $this->childrenGroupIds)->select())->toArray();
        Tree::instance()->init($groupList);
        $groupdata = [];
        if ($this->auth->isSuperAdmin()) {
            $result = Tree::instance()->getTreeList(Tree::instance()->getTreeArray(0));
            foreach ($result as $k => $v) {
                $groupdata[$v['id']] = $v['name'];
            }
        } else {
            $result = [];
            $groups = $this->auth->getGroups();
            foreach ($groups as $m => $n) {
                $childlist = Tree::instance()->getTreeList(Tree::instance()->getTreeArray($n['id']));
                $temp = [];
                foreach ($childlist as $k => $v) {
                    $temp[$v['id']] = $v['name'];
                }
                $result[__($n['name'])] = $temp;
            }
            $groupdata = $result;
        }
        $this->view->assign('groupdata', $groupdata);

	}


    /**
     * 员工列表
     * @return string
     */
	public function index() {
		$this->request->filter(['strip_tags']);
		if ($this->request->isAjax()) {
			list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            $wheres=[];
            $wheres['id']=['in',StaffModel::getMyStaffIds()];
			$list = $this->model->with(['parent','admin','staffrole'])
                ->where($where)->order($sort, $order)->paginate($limit);
			$row  = $list->items();

			$result = array("total" => $list->total(), "rows" => $row);

			return json($result);
		}

		return $this->view->fetch();
	}


    /**
     * 添加员工
     * @return string
     */
	public function add() {
        if ($this->request->isAjax()) {
            $data = $this->request->post('row/a');
            $mobile=$data['mobile'];
            $count = StaffModel::where(['mobile'=>$mobile])->count();
            if($count > 0){
                $this->error('员工手机号已存在');
            }

            $newSalt = substr(md5(uniqid(true)), 0, 6);
            $newPassword = md5(md5($data['password']) . $newSalt);
            $data['salt'] = $newSalt;
            $data['password'] = $newPassword;
            $data['status'] = 1;
            if(empty($data['img'])){
                $data['img'] = '/assets/img/avatar.png'; //设置新管理员默认头像。
            }
            Db::startTrans();
            try {
                $group = $this->request->post("group/a");
                //过滤不允许的组别,避免越权
                $group = array_intersect($this->childrenGroupIds, $group);
                if (!$group) {
                    exception(__('The parent group exceeds permission limit'));
                }
                $data['group_ids']=implode(',',$group);
                $result =  $this->model->save($data);
                Db::commit();
            }catch (Exception $e){
                Db::rollback();
                $this->error($e->getMessage());
            }

            if (!$result) {
                $this->error('提交失败');
            }
            $this->success('提交成功');
        }

		$staffname = StaffModel::where([])->column('id,name');
		$staffs    = ['' => '无'];
		foreach ($staffname as $id => $name) {
			$staffs[$id] = $name;
		}
        $this->view->assign('roles',StaffRole::where([])->column('name','id'));
		$this->view->assign('staffs', $staffs);
		return $this->view->fetch();
	}


    /**
     * 修改员工
     * @param null $ids
     * @return string
     */
	public function edit($ids = null) {
		$map['id'] = $ids;
		if ($this->request->isAjax()) {
			$data   = $this->request->post('row/a');


            if($data['password']){
                $newSalt = substr(md5(uniqid(true)), 0, 6);
                $newPassword = md5(md5($data['password']) . $newSalt);
                $data['salt'] = $newSalt;
                $data['password'] = $newPassword;
            }else{
                unset($data['password']);
            }
            $mobile=$data['mobile'];
            $count = StaffModel::where(['mobile'=>$mobile,'id'=>['neq',$ids]])->count();
            if($count > 0){
                $this->error('员工手机号已存在');
            }
            if(empty($data['img'])){
                $data['img'] = '/assets/img/avatar.png'; //设置新管理员默认头像。
            }
            Db::startTrans();
            try {
                $group = $this->request->post("group/a",[]);
                //过滤不允许的组别,避免越权
                $group = array_intersect($this->childrenGroupIds, $group);
                if (!$group) {
                    exception(__('The parent group exceeds permission limit'));
                }
                $data['group_ids']=implode(',',$group);
                $data['id']=$map['id'];
                $result=$this->model->save($data,$map);
                Db::commit();
            }catch (Exception $e){
                Db::rollback();
                $this->error($e->getMessage());
            }

			if (!$result) {
				$this->error('修改失败');
			}
			$this->success('修改成功');
		}
		$data = StaffModel::where($map)->find();
		$this->view->assign("row", $data);

		$staffname = StaffModel::where([])->column('id,name');
		$staffs    = ['' => '无'];
		foreach ($staffname as $id => $name) {
			$staffs[$id] = $name;
		}
        $this->view->assign('roles',StaffRole::where([])->column('name','id'));
		$this->view->assign('staffs', $staffs);

		return $this->view->fetch();
	}

    /**
     * 删除员工
     */
	public function del($ids = null) {
		if ($this->request->isAjax()) {
			$map['id'] = array('in', $ids);
            $findinfo = StaffModel::where($map)->select();
            foreach($findinfo as $k=>$v){
                if($v['admin_id'] ==1){
                    $this->error('管理员不可删除');
                }
            }
			$result    = StaffModel::destroy($map);

			if (!$result) {
				$this->error('删除失败');
			}
			$cids = Customer::where(['owner_staff_id' => $ids])->column('id');
			try {
				foreach ($cids as $id) {
					Customer::moveSeas($id);
				}
			} catch (Exception $e) {
                $this->error($e->getMessage());
			}

			$this->success('删除成功');
		}

		return $this->view->fetch();
	}


    /**
     * 更新状态禁用账号
     */
	public function update_status() {
		$id     = input('ids');
		$status = input('status', 2, 'intval');

		$staff = $this->model->where(['id' => $id])->find();
		if (empty($staff)) {
			$this->error('员工不存在');
		}
		if ($this->model->isUpdate(true)->save(['id' => $id, 'status' => $status])) {
			$this->success('操作成功');
		}
		$this->error('操作失败');
	}

    /**
     * 获取员工角色
     */
    public function getstaffrole(){
        $model=new StaffRole();
        $result = $model->where([])->field('id,name')->select();
        $searchlist = [];
        foreach ($result as $key => $value) {
            $searchlist[] = ['id' => $value['id'], 'name' => $value['name']];
        }
        $data = ['searchlist' => $searchlist];
        $this->success('', null, $data);
    }

    /**
     * 获取管理员账户
     */
    public function admin_username()
    {
        $params = input('name', '');
        $where['username'] = array('like', '%' . $params . '%');
        $list = Admin::where($where)->field('id,username as name')->select();
        $data['list'] = $list;
        return json_encode($data);
    }

    /**
     * 获取管理员邮箱
     */
    public function admin_email()
    {
        $params = input('name', '');
        $where['email'] = array('like', '%' . $params . '%');
        $list = Admin::where($where)->field('id,email as name')->select();
        $data['list'] = $list;
        return json_encode($data);
    }
}