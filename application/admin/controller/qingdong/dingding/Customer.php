<?php

namespace app\admin\controller\qingdong\dingding;

use addons\qingdong\model\DingCustomer;
use app\common\controller\Backend;
use think\Db;
use think\Exception;
use addons\qingdong\model\DingStaff;
use addons\qingdong\model\Customer as CustomerModel;
/**
 * 钉钉同步客户
 */
class Customer extends  Backend {

    public function _initialize() {
        parent::_initialize();
        $this->model      = new DingCustomer();

    }

    /**
     * 客户列表
     */
    public function index() {
        $this->request->filter(['strip_tags']);
        if ($this->request->isAjax()) {
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            $list = $this->model->with(['ownerstaff'])->where(['ding_customer.type'=>0])->where($where)->order($sort, $order)->paginate($limit);
            $row  = $list->items();

            $result = array("total" => $list->total(), "rows" => $row);

            return json($result);
        }

        return $this->view->fetch();
    }

    /**
     * 删除客户
     */
    public function del($ids = null) {
        if ($this->request->isAjax()) {
            $map['id'] = array('in', $ids);
            $result    = $this->model->where($map)->delete();
            if (!$result) {
                $this->error('删除失败');
            }
            $this->success('删除成功');
        }

        return $this->view->fetch();
    }

    /**
     * 同步到CRM客户
     */
    public function batch()
    {
        $info = $this->model->where(array('type'=>0,'status' => 0))->select();
        if (!$info) {
            $this->error('无数据可同步');
        }
        $success=0;
        $error_info=[];
        foreach ($info as $k => $v) {

            Db::startTrans();
            try {
                $staff = DingStaff::where(array('user_id' => $v['owner_staff_id']))->find();
                if (!$staff['staff_id']) {
                    throw new Exception('请先同步所属员工');
                }

                $customerData = array(
                    'name' => filter_Emoji($v['name']),
                    'source' => $v['source'],
                    'address' => $v['address'],
                    'address_detail' => $v['address_detail'],
                    'remarks' => $v['remark'],
                    'create_staff_id' => $staff['staff_id'],
                    'owner_staff_id' => $staff['staff_id'],
                );
                $customerinfo = CustomerModel::create($customerData);
                $idinfo = CustomerModel::getLastInsID();
                //更新状态
                $dingStatus = $this->model->where(array('id' => $v['id']))->update(array('status' => 1, 'customer_id' => $idinfo));
                if (!$customerinfo) {
                    throw new Exception('创建客户失败');
                }
                if (!$dingStatus) {
                    throw new Exception('修改表状态失败');
                }
                $success++;
                Db::commit();
            } catch (Exception $e) {
                Db::rollback();
                $error_info[] = '员工【' . $v['name'] . '】' . $e->getMessage();
            }

        }

        $this->success('同步成功【'.$success.'】条，同步失败【'.count($error_info).'】条；失败原因:'.implode('<br>',$error_info));
    }

}