<?php

namespace app\admin\controller\qingdong\dingding;

use addons\qingdong\model\DingContacts;
use app\common\controller\Backend;
use think\Db;
use think\Exception;
use addons\qingdong\model\DingStaff;
use addons\qingdong\model\DingCustomer;
use addons\qingdong\model\Contacts as ContactsModel;
use addons\qingdong\model\Customer;

/**
 * 钉钉同步联系人
 */
class Contacts extends  Backend {

    public function _initialize() {
        parent::_initialize();
        $this->model      = new DingContacts();

    }

    /**
     * 联系人列表
     * @return string|\think\response\Json
     */
    public function index() {
        $this->request->filter(['strip_tags']);
        if ($this->request->isAjax()) {
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            $list = $this->model->with(['ownerstaff','customer'])->where(['ding_contacts.type'=>0])->where($where)->order($sort, $order)->paginate($limit);
            $row  = $list->items();
            $result = array("total" => $list->total(), "rows" => $row);

            return json($result);
        }

        return $this->view->fetch();
    }


    /**
     * 删除联系人
     */
    public function del($ids = null) {
        if ($this->request->isAjax()) {
            $map['id'] = array('in', $ids);
            $result    = $this->model->where($map)->delete();
            if (!$result) {
                $this->error('删除失败');
            }
            $this->success('删除成功');
        }

        return $this->view->fetch();
    }
    /**
     * 同步到CRM联系人
     */
    public function batch()
    {
        $info = $this->model->where(array('status' => 0))->select();
        if (!$info) {
            $this->error('无数据可同步');
        }
        Db::startTrans();
        try{
            foreach($info as $k=>$v){
                $customer = DingCustomer::where(array('id'=>$v['customer_id']))->find();
                if(!$customer['customer_id']){
                    throw new Exception('请先同步客户');
                }
                $cusData = Customer::where(array('id'=>$customer['customer_id']))->find();
                if(!$cusData){
                    throw new Exception('客户不存在,请重新同步客户');
                }
                $contactData = array(
                    'customer_id' => $customer['customer_id'],
                    'name' => $v['name'],
                    'post' => $v['post'],
                    'email' => $v['email'],
                    'mobile' => $v['mobile'],
                    'remarks' => $v['remarks'],
                    'create_staff_id' => DingStaff::where(array('user_id'=>$v['create_staff_id']))->value('staff_id'),
                    'owner_staff_id' => DingStaff::where(array('user_id'=>$v['owner_staff_id']))->value('staff_id'),
                );
                $contactinfo = ContactsModel::create($contactData);
                //更新状态
                $dingStatus = $this->model->where(array('id'=>$v['id']))->update(array('status'=>1));
                if(!$contactinfo || !$dingStatus){
                    throw new Exception('同步失败');
                }

            }
            Db::commit();
        }catch (Exception $e){
            Db::rollback();
            $this->error($e->getMessage());
        }

        $this->success('同步成功');
    }

}