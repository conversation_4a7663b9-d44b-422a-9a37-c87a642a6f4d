<?php

namespace app\admin\controller\qingdong\dingding;

use addons\qingdong\model\DingRecord;
use app\common\controller\Backend;
use think\Db;
use think\Exception;
use addons\qingdong\model\DingStaff;
use addons\qingdong\model\DingCustomer;
use addons\qingdong\model\Record as RecordModel;
use addons\qingdong\model\Customer;
/**
 * 跟进记录
 */
class Record extends  Backend {

    public function _initialize() {
        parent::_initialize();
        $this->model      = new DingRecord();

    }
    /**
     * 跟进记录列表
     */
    public function index() {
        $this->request->filter(['strip_tags']);
        if ($this->request->isAjax()) {
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            $list = $this->model->with(['ownerstaff','customer'])->where($where)->order($sort, $order)->paginate($limit);
            $row  = $list->items();

            $result = array("total" => $list->total(), "rows" => $row);

            return json($result);
        }

        return $this->view->fetch();
    }

    /**
     * 删除跟进记录
     */
    public function del($ids = null) {
        if ($this->request->isAjax()) {
            $map['id'] = array('in', $ids);
            $result    = $this->model->where($map)->delete();
            if (!$result) {
                $this->error('删除失败');
            }
            $this->success('删除成功');
        }

        return $this->view->fetch();
    }
    /**
     * 同步到CRM
     */
    public function batch()
    {
        $info = $this->model->where(array('status' => 0))->select();
        if (!$info) {
            $this->error('无数据可同步');
        }
        Db::startTrans();
        try{
            foreach($info as $k=>$v){
                $customer = DingCustomer::where(array('id'=>$v['customer_id']))->find();
                if(!$customer['customer_id']){
                    throw new Exception('请先同步客户');
                }
                $cusData = Customer::where(array('id'=>$customer['customer_id']))->find();
                if(!$cusData){
                    throw new Exception('客户不存在,请重新同步客户');
                }
                $contactData = array(
                    'relation_id' => $customer['customer_id'],
                    'relation_type' => 1,
                    'follow_type' => '其他',
                    'content' => $v['content'],
                    'follow_time' => date('Y-m-d H:i:s',$v['createtime']),
                    'create_staff_id' => DingStaff::where(array('user_id'=>$v['create_staff_id']))->value('staff_id'),
                );
                $contactinfo = RecordModel::create($contactData);
                //更新状态
                $dingStatus = $this->model->where(array('id'=>$v['id']))->update(array('status'=>1));
                if(!$contactinfo || !$dingStatus){
                    throw new Exception('同步失败');
                }

            }
            Db::commit();
        }catch (Exception $e){
            Db::rollback();
            $this->error($e->getMessage());
        }

        $this->success('同步成功');
    }

}