<?php

namespace app\admin\controller\qingdong\weixin;

use addons\qingdong\model\DingStaff;
use app\common\controller\Backend;
use addons\qingdong\model\StaffDepartment;
use addons\qingdong\model\Staff as StaffModel;
use think\Db;
use think\Exception;

/**
 * 员工同步
 */
class Staff extends  Backend {

    public function _initialize() {
        parent::_initialize();
        $this->model      = new DingStaff();

    }

    /**
     * 员工列表
     */
    public function index() {
        $this->request->filter(['strip_tags']);
        if ($this->request->isAjax()) {
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            $wheres['ding_staff.type'] = 1;
            $list = $this->model->with(['department'])->where($where)->where($wheres)->order($sort, $order)->paginate($limit);
            $row  = $list->items();

            $result = array("total" => $list->total(), "rows" => $row);

            return json($result);
        }

        return $this->view->fetch();
    }

    /**
     * 删除员工
     */
    public function del($ids = null) {
        if ($this->request->isAjax()) {
            $map['id'] = array('in', $ids);
            $result    = DingStaff::where($map)->delete();
            if (!$result) {
                $this->error('删除失败');
            }
            $this->success('删除成功');
        }

        return $this->view->fetch();
    }
    /**
     * 同步到CRM
     */
    public function batch()
    {
        $info = $this->model->where(array('type' => 1, 'status' => 0))->select();
        if (!$info) {
            $this->error('无数据可同步');
        }
            $success=0;
            $error_info=[];
            foreach ($info as $k => $v) {
                Db::startTrans();
                try {
                    $mobiles = $v['mobile'];
                    if (!$mobiles || empty($mobiles)) {
                        //手机号不存在的员工，请到员工管理中修改本企业员工手机号
                        throw new Exception('手机号不存在无法同步员工');
                    }
                    $department = StaffDepartment::where(array('id' => $v['dept_id']))->find();
                    if (!$department['role_id']) {
                        throw new Exception('请先同步部门');
                    }
                    $staffData = array(
                        'mobile' => $mobiles,
                        'name' => $v['name'],
                        'nickname' => $v['name'],
                        'post' => $department['name'],
                        'password' => '38d7e44335dae29b37ceab504602d17b',
                        'salt' => 'a2d622',
                        'img' => '',
                        'email' => $mobiles . '@163.com',
                        'group_ids' => $department['role_id']
                    );
                    $staffinfo = StaffModel::create($staffData);
                    $ids = StaffModel::getLastInsID();
                    //更新状态
                    $weiStatus = $this->model->where(array('id' => $v['id']))->update(array('status' => 1, 'staff_id' => $ids));
                    if (!$staffinfo) {
                        throw new Exception('创建员工账号失败');
                    }
                    if (!$weiStatus) {
                        throw new Exception('修改表状态失败');
                    }
                    $success++;
                    Db::commit();
                } catch (Exception $e) {
                    Db::rollback();
                    $error_info[]='员工【'.$v['name'].'】'.$e->getMessage();
                }

            }

        $this->success('同步成功【'.$success.'】条，同步失败【'.count($error_info).'】条；失败原因:'.implode('<br>',$error_info));
    }

}