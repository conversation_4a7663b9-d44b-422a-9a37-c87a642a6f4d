<?php

namespace app\admin\controller\qingdong\notice;
use app\common\controller\Backend;
use addons\qingdong\model\Notice as NoticeModel;
use think\DB;
use fast\Tree;

/**
 * 通知列表
 */
class Notice extends Backend {
	public function _initialize() {
		parent::_initialize();
		$this->model = new NoticeModel();
	}


    /**
     * 通知列表
     */
	public function index() {
		$this->request->filter(['strip_tags']);
		if ($this->request->isAjax()) {
			list($where, $sort, $order, $offset, $limit) = $this->buildparams();
			$list   = $this->model->where($where)->order($sort, $order)->paginate($limit);
			$row    = $list->items();
			$result = array("total" => $list->total(), "rows" => $row);

			return json($result);
		}

		return $this->view->fetch();
	}

    /**
     * 添加
     */
	public function add() {
		if ($this->request->isAjax()) {
			$data             = $this->request->post('row/a');
			$result           = $this->model->save($data);
			if (!$result) {
				$this->error('提交失败');
			}
			$this->success('提交成功');
		}

		return $this->view->fetch();
	}


    /**
     * 修改
     */
	public function edit($ids = null) {
		$map['id'] = $ids;
		if ($this->request->isAjax()) {
			$data   = $this->request->post('row/a');
			$result = $this->model->save($data, $map);

			if (!$result) {
				$this->error('修改失败');
			}
			$this->success('修改成功');
		}
		$data = NoticeModel::where($map)->find();
		$this->view->assign("row", $data);

		return $this->view->fetch();
	}


    /**
     * 删除通知
     */
	public function del($ids = null) {
		if ($this->request->isAjax()) {
			$map['id'] = array('in', $ids);
			$result    = NoticeModel::destroy($map);
			if (!$result) {
				$this->error('删除失败');
			}
			$this->success('删除成功');
		}

		return $this->view->fetch();
	}
}