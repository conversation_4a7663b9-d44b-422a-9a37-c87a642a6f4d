<?php

namespace app\admin\controller\qingdong;


use addons\qingdong\model\Staff;
use app\admin\controller\qingdong\Base;
use addons\qingdong\model\Contract;
use addons\qingdong\model\Customer;
use addons\qingdong\model\Field;
use addons\qingdong\model\Receivables;
use addons\qingdong\model\Record;
use addons\qingdong\model\Leads;
use addons\qingdong\model\ExamineRecord;

use think\Config;

/**
 * 控制台
 * @icon fa fa-dashboard
 * @remark 用于展示当前系统中的统计数据、统计报表及重要实时数据
 */
class Dashboard extends Base {
    /**
     * 查看
     */
    public function index() {

        //团队统计
        $time      = strtotime(date('Y-m', strtotime('-1 year')));
        $auth = Staff::getMyStaffIds();
        $staff_id = implode(',',$auth);  //权限
        $sources   = Field::getField('客户来源');
        $customers = Customer::where([
            'owner_staff_id'=>['in',$staff_id],
            'createtime' => [
                'egt',
                $time
            ]
        ])->field("FROM_UNIXTIME(createtime,'%Y-%m') as ctime,source,count(*) as c")->group('source,ctime')->select();
        $select    = [];
        foreach ($customers as $v) {
            $select[$v['source']][$v['ctime']] = $v['c'];
        }
        $toMonthTime = strtotime(date('Y-m'));
        $sourcedata  = ['客户来源' => ['客户来源']];
        for ($time; $time <= $toMonthTime; $time = strtotime('+1 month', $time)) {
            $sourcedata['客户来源'][] = date('Y-m', $time);
            foreach ($sources as $v) {
                if (empty($sourcedata[$v])) {
                    $sourcedata[$v][] = $v;
                }
                $sourcedata[$v][] = intval($select[$v][date('Y-m', $time)] ?? 0);
            }
        }

        //合同金额统计
        $time      = strtotime(date('Y-m', strtotime('-1 year')));

        $contractNums=Contract::where(['order_staff_id'=>['in',$staff_id],'check_status' => 2,'createtime' => ['egt',$time]])->field("FROM_UNIXTIME(createtime,'%Y-%m') as ctime,count(*) as c")->group('ctime')->select();
        $contractNums=modify_array($contractNums,'ctime','c');
        $contract=Contract::where(['order_staff_id'=>['in',$staff_id],'check_status' => 2,'createtime' => ['egt',$time]])->field("FROM_UNIXTIME(createtime,'%Y-%m') as ctime,sum(money) as s")->group('ctime')->select();
        $contract=modify_array($contract,'ctime','s');
        //回款
        $receivables=Receivables::where(['owner_staff_id'=>['in',$staff_id],'check_status'=>2,'createtime' => ['egt',$time]])->field("FROM_UNIXTIME(createtime,'%Y-%m') as ctime,sum(money) as s")->group("ctime")->select();
        $receivables=modify_array($receivables,'ctime','s');
        $contactdata=[];
        for ($time; $time <= $toMonthTime; $time = strtotime('+1 month', $time)) {
            $contactdata['date'][]        = date('Y-m', $time);
            $contactdata['num'][]         = $contractNums[date('Y-m', $time)]??0;
            $contactdata['contract'][]    = $contract[date('Y-m', $time)]??0;
            $contactdata['receivables'][] = $receivables[date('Y-m', $time)]??0;
        }

        $where1['owner_staff_id'] =array('in',$staff_id);
        $start = strtotime(date('Y-m-d 00:00:00'));
        $end = strtotime(date('Y-m-d 23:59:59'));
        $where2['createtime']= array(array('egt',$start),array('elt',$end));
        //今日新增客户
         $todaycontomer = Customer::where(['owner_staff_id'=>['in',$staff_id]])->where($where2)->count();
        //今日跟进记录
         $todayrecord=Record::where(['create_staff_id'=>$where1['owner_staff_id']])->where($where2)->count();
        //今日新增线索
         $todaylead = Leads::where($where1)->where($where2)->count();
        //今日新增公海
        $todaysea = Customer::where('owner_staff_id is null or owner_staff_id =0')->where($where2)->count();
        //今日新增合同
        $todaycontract = Contract::where($where1)->where($where2)->count();
        //今日新增回款
        $todayreceivables = Receivables::where($where1)->where($where2)->sum('money');
        //今日已审批
        $todayexamine = ExamineRecord::where(array('check_staff_id'=>['in',$staff_id],'status'=>1))->where($where2)->count();
        //今日待审批
        $todaywork =ExamineRecord::where(array('check_staff_id'=>['in',$staff_id],'status'=>0))->where($where2)->count();


        //个人统计
        $Persontime      = strtotime(date('Y-m', strtotime('-1 year')));
        $Personauth = Staff::info();
        $Personstaff_id = $Personauth->id;  //权限
        $Personcustomers = Customer::where([
            'owner_staff_id'=>$Personstaff_id,
            'createtime' => [
                'egt',
                $Persontime
            ]
        ])->field("FROM_UNIXTIME(createtime,'%Y-%m') as ctime,source,count(*) as c")->group('source,ctime')->select();
        $Personselect    = [];
        foreach ($Personcustomers as $v) {
            $Personselect[$v['source']][$v['ctime']] = $v['c'];
        }
        $PersontoMonthTime = strtotime(date('Y-m'));
        $Personsourcedata  = ['客户来源' => ['客户来源']];
        for ($Persontime; $Persontime <= $PersontoMonthTime; $Persontime = strtotime('+1 month', $Persontime)) {
            $Personsourcedata['客户来源'][] = date('Y-m', $Persontime);
            foreach ($sources as $v) {
                if (empty($Personsourcedata[$v])) {
                    $Personsourcedata[$v][] = $v;
                }
                $Personsourcedata[$v][] = intval($Personselect[$v][date('Y-m', $Persontime)] ?? 0);
            }
        }

        //合同金额统计
        $Persontime      = strtotime(date('Y-m', strtotime('-1 year')));
        $PersoncontractNums=Contract::where(['order_staff_id'=>$Personstaff_id,'check_status' => 2,'createtime' => ['egt',$Persontime]])->field("FROM_UNIXTIME(createtime,'%Y-%m') as ctime,count(*) as c")->group('ctime')->select();
        $PersoncontractNums=modify_array($PersoncontractNums,'ctime','c');
        $Personcontract=Contract::where(['order_staff_id'=>$Personstaff_id,'check_status' => 2,'createtime' => ['egt',$Persontime]])->field("FROM_UNIXTIME(createtime,'%Y-%m') as ctime,sum(money) as s")->group('ctime')->select();
        $Personcontract=modify_array($Personcontract,'ctime','s');
        //回款
        $Personreceivables=Receivables::where(['owner_staff_id'=>$Personstaff_id,'check_status'=>2,'createtime' => ['egt',$Persontime]])->field("FROM_UNIXTIME(createtime,'%Y-%m') as ctime,sum(money) as s")->group("ctime")->select();
        $Personreceivables=modify_array($Personreceivables,'ctime','s');
        $Personcontactdata=[];
        for ($Persontime; $Persontime <= $PersontoMonthTime; $Persontime = strtotime('+1 month', $Persontime)) {
            $Personcontactdata['date'][]        = date('Y-m', $Persontime);
            $Personcontactdata['num'][]         = $PersoncontractNums[date('Y-m', $Persontime)]??0;
            $Personcontactdata['contract'][]    = $Personcontract[date('Y-m', $Persontime)]??0;
            $Personcontactdata['receivables'][] = $Personreceivables[date('Y-m', $Persontime)]??0;
        }
        $Personwhere1['owner_staff_id'] =$Personstaff_id;
        $Personwhere2['createtime']= array(array('egt',$start),array('elt',$end));
        //今日新增客户
        $Persontodaycontomer = Customer::where(['owner_staff_id'=>$Personstaff_id])->where($Personwhere2)->count();
        //今日跟进记录
        $Persontodayrecord=Record::where(['create_staff_id'=>$Personstaff_id])->where($Personwhere2)->count();
        //今日新增线索
        $Persontodaylead = Leads::where($Personwhere1)->where($Personwhere2)->count();
        //今日新增公海
        $Persontodaysea = Customer::where('owner_staff_id is null or owner_staff_id =0')->where($Personwhere2)->count();
        //今日新增合同
        $Persontodaycontract = Contract::where($Personwhere1)->where($Personwhere2)->count();
        //今日新增回款
        $Persontodayreceivables = Receivables::where($Personwhere1)->where($Personwhere2)->sum('money');
        //今日已审批
        $Persontodayexamine = ExamineRecord::where(array('check_staff_id'=>$Personstaff_id,'status'=>1))->where($Personwhere2)->count();
        //今日待审批
        $Persontodaywork =ExamineRecord::where(array('check_staff_id'=>$Personstaff_id,'status'=>0))->where($Personwhere2)->count();

        $this->view->assign([
            'sourcedata'       => array_values($sourcedata),
            'contactdata'       => $contactdata,
            'todaycontomer'    =>$todaycontomer,
            'todayrecord'    =>$todayrecord,
            'todaylead'    =>$todaylead,
            'todaysea'    =>$todaysea,
            'todaycontract'    =>$todaycontract,
            'todayreceivables'    =>$todayreceivables,
            'todayexamine'    =>$todayexamine,
            'todaywork'    =>$todaywork,
            'Personsourcedata'       => array_values($Personsourcedata),
            'Personcontactdata'       => $Personcontactdata,
            'Persontodaycontomer'    =>$Persontodaycontomer,
            'Persontodayrecord'    =>$Persontodayrecord,
            'Persontodaylead'    =>$Persontodaylead,
            'Persontodaysea'    =>$Persontodaysea,
            'Persontodaycontract'    =>$Persontodaycontract,
            'Persontodayreceivables'    =>$Persontodayreceivables,
            'Persontodayexamine'    =>$Persontodayexamine,
            'Persontodaywork'    =>$Persontodaywork,
        ]);

        return $this->view->fetch();
    }
}
