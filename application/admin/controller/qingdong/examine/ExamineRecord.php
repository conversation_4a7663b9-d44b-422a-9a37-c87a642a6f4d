<?php

namespace app\admin\controller\qingdong\examine;

use addons\qingdong\model\Flow;
use addons\qingdong\model\Message;
use addons\qingdong\model\Staff;
use addons\qingdong\model\ExamineRecord as ExamineRecordModel;
use addons\qingdong\model\BusinessStatus;
use addons\qingdong\model\Business;
use app\admin\controller\qingdong\Base;
use think\Db;
use think\Exception;
use think\Log;

/**
 * 审批记录
 */
class ExamineRecord extends Base {
	protected $noNeedLogin = [];
	protected $noNeedRight = [];

    /**
     * 审核
     */
	public function examine() {
		$relation_type = input('relation_type');
		$relation_id   = input('relation_id');
		$content       = input('content','');
		$status        = input('status');
        $record = ExamineRecordModel::where([
            'relation_type' => $relation_type,
            'relation_id' => $relation_id,
            'status' => 0,
            'check_staff_id' => $this->_staff->id
        ])->find();
        if (empty($record)) {
            $this->error('没有待审核数据');
        }
        if ($this->request->isAjax()) {
            $staff = Staff::info();
            Db::startTrans();
            try {
                $model = new ExamineRecordModel();
                if ($model->isUpdate(true)->save([
                        'status' => $status,
                        'content' => $content,
                        'check_time' => time()
                    ], ['id' => $record['id']]) == false) {
                    throw new Exception('修改失败');
                }
                switch ($relation_type) {
                    case $model::CONSUME_TYPE://费用
                        $rowModel = new \addons\qingdong\model\Consume();
                        $row = $rowModel->where(['id' => $relation_id])->find();
                        break;
                    case $model::CONTRACT_TYPE://合同
                        $rowModel = new \addons\qingdong\model\Contract();
                        $row = $rowModel->where(['id' => $relation_id])->find();
                        break;
                    case $model::RECEIVABLES_TYPE://回款
                        $rowModel = new \addons\qingdong\model\Receivables();
                        $row = $rowModel->where(['id' => $relation_id])->find();
                        break;
                    case $model::ACHIEVEMENT_TYPE://业绩目标
                        $rowModel = new \addons\qingdong\model\AchievementRecords();
                        $row = $rowModel->where(['id' => $relation_id])->find()->toArray();
                        break;
                    case $model::APPROVAL_TYPE://审批
                        $rowModel = new \addons\qingdong\model\Approval();
                        $row = $rowModel->where(['id' => $relation_id])->find()->toArray();
                        $row['staff_id'] = $row['create_staff_id'];
                        break;
                    default:
                        throw new Exception('参数错误');
                }
                if ($message = Message::where(['relation_type' => 'examine', 'relation_id' => $record['id'], 'to_staff_id' => $this->auth->id])->find()) {
                    Message::where(['id' => $message['id']])->update(['status' => 1, 'read_time' => time()]);
                }
                $check_staff_ids = explode(',', trim($row['check_staff_ids'], ','));
                $check_staff_ids[] = $staff->id;
                if ($status == 1) {//审核通过
                    $flow = Flow::getstepdetail($relation_type, $relation_id);
                    //给下一审核人发送通知
                    $result = Flow::sendStepRecord($flow, $relation_type, $relation_id, $check_staff_ids, $staff->id);

                    //已完成审核
                    if ($result['status'] == true) {
                        $rowModel->save([
                            'check_status' => 2,
                            'check_staff_ids' => implode(',', $check_staff_ids),
                            'order_id' => $result['order_id']
                        ], ['id' => $relation_id]);
                        switch ($relation_type) {
                            case $model::CONTRACT_TYPE://合同
                                \addons\qingdong\model\Customer::where(['id' => $row['customer_id']])->update(['contract_status' => 1]);
                               //合同签署成功后商机变为赢单
                               if($row['business_id']){
                                    BusinessStatus::create(array('business_id'=>$row['business_id'],'type'=>4,'remark'=>'合同签署'));
                                   Business::where(array('id'=>$row['business_id']))->update(array('contract_status'=>1,'updatetime'=>time()));
                               }
                                break;
                            case $model::ACHIEVEMENT_TYPE://业绩目标
                                $m = new \addons\qingdong\model\Achievement();
                                $m->where(['type' => $row['type'],'status'=>$row['status'], 'obj_id' => $row['obj_id']])->delete();
                                unset($row['id']);
                                unset($row['createtime']);
                                unset($row['updatetime']);
                                unset($row['deletetime']);
                                $m->allowField(true)->save($row);
                                $row['staff_id'] = $row['obj_id'];
                                break;
                            case $model::RECEIVABLES_TYPE://回款
                                $m = new \addons\qingdong\model\Contract();
                                $contract = $m->where(['id' => $row['contract_id']])->with(['receivables'])->find();
                                if ($contract['receivables']['repayment_money'] >= $contract['money']) {
                                    $m->save(['contract_status' => 1], ['id' => $row['contract_id']]);//已完成
                                }
                                break;
                        }

                        Message::addMessage(Message::EXAMINE_ADOPT_TYPE, $record['id'], $row['owner_staff_id'] ?? $row['staff_id'], $staff->id);

                        //删除 或签的待审批通知
                        Message::setRead(Message::EXAMINE_TYPE, $record['id']);
                        ExamineRecordModel::where([
                            'relation_type' => $relation_type,
                            'relation_id' => $relation_id,
                            'status' => 0,
                        ])->update(['status' => 3, 'check_time' => time()]);
                    } else {
                        $rowModel->save([
                            'check_staff_ids' => implode(',', $check_staff_ids),
                            'order_id' => $result['order_id']
                        ], ['id' => $relation_id]);
                    }
                } else {
                    //审核未通过
                    $rowModel->save(['check_status' => 3, 'check_staff_ids' => ''], ['id' => $relation_id]);
                    Message::addMessage(Message::EXAMINE_REFUSE_TYPE, $record['id'], $row['owner_staff_id'] ?? $row['staff_id'], $staff->id, '');

                    //删除待审批通知
                    $ids = ExamineRecordModel::where([
                        'relation_type' => $relation_type,
                        'relation_id' => $relation_id,
                        'status' => 0,
                    ])->column('id');
                    Message::where(['relation_type' => Message::EXAMINE_TYPE, 'relation_id' => ['in', $ids], 'status' => 0])->update(['read_time' => time(), 'status' => 1]);

                    ExamineRecordModel::where([
                        'relation_type' => $relation_type,
                        'relation_id' => $relation_id,
                        'status' => 0,
                    ])->update(['status' => 3, 'check_time' => time()]);
                }


                Db::commit();
            } catch (Exception $e) {
                Db::rollback();
                Log::record($e->getMessage());
                $this->error($e->getMessage());
            }

            $this->success('审核成功');
        }


        $this->assign('relation_type',$relation_type);
        $this->assign('relation_id',$relation_id);
        $this->assign('status',$status);
        return $this->view->fetch();
	}

}
