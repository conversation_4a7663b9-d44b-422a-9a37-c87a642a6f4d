<?php

namespace app\admin\controller\qingdong\examine;
use addons\qingdong\model\Staff;
use app\admin\controller\qingdong\Base;
use app\common\controller\Backend;
use addons\qingdong\model\ExamineRecord;
use addons\qingdong\model\FormApproval;
use think\DB;

/**
 * 审核信息
 */
class Examine extends Base {
    public function _initialize() {
        parent::_initialize();
        $this->model = new ExamineRecord();
    }

    /**
     * 审核合同
     */
    public function index() {
        $this->request->filter(['strip_tags']);

        if ($this->request->isAjax()) {
            $this->relationSearch = true;
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            $status = input('status',0);
            $wheres['check_staff_id'] = $this->_staff->id;
            $wheres['relation_type'] = 'contract';
            if($status == 1){
                $wheres['status'] = array('in','1,2');
            }else{
                $wheres['status']=0;
            }
            $list   = $this->model->where($where)->where($wheres)->with([
                'checkStaff',
                'contract' => ['ownerStaff']
            ])->order($sort, $order)->paginate($limit);
            $row    = $list->items();

            $result = array("total" => $list->total(), "rows" => $row);

            return json($result);
        }

        return $this->view->fetch();
    }

    /**
     * 审核费用
     */
    public function consume() {
        $this->request->filter(['strip_tags']);
        if ($this->request->isAjax()) {
            $this->relationSearch = true;
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            $status = input('status',0);
            $wheres['check_staff_id'] = $this->_staff->id;
            $wheres['relation_type'] = 'consume';
            if($status == 1){
                $wheres['status'] = array('in','1,2');
            }else{
                $wheres['status']=0;
            }

            $list   = $this->model->where($where)->where($wheres)->with(['checkStaff','consume'=>['staff']])->order($sort, $order)->paginate($limit);
            $row    = $list->items();
            $result = array("total" => $list->total(), "rows" => $row);

            return json($result);
        }

        return $this->view->fetch();
    }

    /**
     * 审核回款
     */
    public function receivables() {
        $this->request->filter(['strip_tags']);
        $receivables_id = input('receivables_id','','trim');
        if ($this->request->isAjax()) {
            $this->relationSearch = true;
            $status = input('status',0);
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            $wheres['check_staff_id'] = $this->_staff->id;
            $wheres['relation_type'] = 'receivables';
            if($status == 1){
                $wheres['status'] = array('in','1,2');
            }else{
                $wheres['status']=0;
            }
            if(isset($receivables_id) && $receivables_id){
                $wheres['relation_id'] = $receivables_id;
                $wheres['status'] = array('neq',0);
            }
            $list   = $this->model->where($where)->where($wheres)
                ->with(['checkStaff','receivables'=>['createStaff']])->order($sort, $order)->paginate($limit);
            $row    = $list->items();
            $result = array("total" => $list->total(), "rows" => $row);

            return json($result);
        }

        return $this->view->fetch();
    }
    /**
     * 审核业绩目标
     */
    public function achievement() {
        $this->request->filter(['strip_tags']);
        if ($this->request->isAjax()) {
            $this->relationSearch = true;
            $status = input('status',0);
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            $wheres['check_staff_id'] = $this->_staff->id;
            $wheres['relation_type'] = ExamineRecord::ACHIEVEMENT_TYPE;
            if($status == 1){
                $wheres['status'] = array('in','1,2');
            }else{
                $wheres['status']=0;
            }
            $list   = $this->model->where($where)->where($wheres)->with(['checkStaff','achievement'=>['createStaff']])->order($sort, $order)->paginate($limit);
            $row    = $list->items();
            $result = array("total" => $list->total(), "rows" => $row);

            return json($result);
        }

        return $this->view->fetch();
    }
    /**
     * 审核办公审批
     */
    public function work() {
        $this->request->filter(['strip_tags']);
        if ($this->request->isAjax()) {
            $this->relationSearch = true;
            $status = input('status',0);
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            $wheres['check_staff_id'] = $this->_staff->id;
            $wheres['relation_type'] = 'approval';
            if($status == 1){
                $wheres['status'] = array('in','1,2');
            }else{
                $wheres['status']=0;
            }

            $list   = $this->model->where($where)->where($wheres)
                ->with(['checkStaff','approval'=>['createStaff']])->order($sort, $order)->paginate($limit);
            foreach($list as $k=>$v){
                $list[$k]['approval_name'] = FormApproval::where(array('id'=>$v['approval']['formapproval_id']))->value('name');
            }
            $row    = $list->items();
            $result = array("total" => $list->total(), "rows" => $row);

            return json($result);
        }

        return $this->view->fetch();
    }

}