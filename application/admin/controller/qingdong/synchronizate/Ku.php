<?php

namespace app\admin\controller\qingdong\synchronizate;

use app\admin\controller\qingdong\Base;



/**
 * 同步进销存
 */
class Ku extends Base {
    protected $relationSearch = true;
    /**
     * @var \addons\qingdong\model\AdminConfig
     */
    protected $model       = null;


    public function _initialize() {
        parent::_initialize();
        $this->model = new \addons\qingdong\model\AdminConfig;
    }


    /**
     * 查看
     */
    public function index() {
        //设置过滤方法
        if ($this->request->isAjax()) {
            $row = $this->request->post('row/a', '', 'trim');
            $this->model::setConfig('status', $row['status'] ?? 0, $this->model::TYPE_KU);
            $this->model::setConfig('url', $row['url'] ?? 0, $this->model::TYPE_KU);
            $this->model::setConfig('account', $row['account'] ?? 0, $this->model::TYPE_KU);
            $this->model::setConfig('key', $row['key'] ?? 0, $this->model::TYPE_KU);
            $this->success('设置成功');
        }
        $ku = $this->model->where(['type'=>$this->model::TYPE_KU])->column('field,value');
        $ku['status'] = isset($ku['status']) ? $ku['status'] : 0;
        $ku['url'] = isset($ku['url']) ? $ku['url'] : '';
        $ku['account'] = isset($ku['account']) ? $ku['account'] : '';
        $ku['key'] = isset($ku['key']) ? $ku['key'] : '';
        $this->assign('ku',$ku);
        return $this->view->fetch();
    }

}
