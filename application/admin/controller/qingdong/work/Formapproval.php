<?php

namespace app\admin\controller\qingdong\work;

use app\common\controller\Backend;
use addons\qingdong\model\Form;
use addons\qingdong\model\Staff;
use addons\qingdong\model\FormApproval as FormApprovalModel;
use think\Db;
use think\Exception;
use addons\qingdong\library\Pingyin;

/**
 * 审批字段管理
 */
class Formapproval extends Backend {
	protected $relationSearch = true;

    /**
     * @var \addons\qingdong\model\FormApproval
     */
	protected $model = null;


	public function _initialize() {
		parent::_initialize();
		$this->model = new FormApprovalModel();
	}


	/**
	 * 查看
	 */
	public function index() {
		//设置过滤方法
		$this->request->filter(['strip_tags', 'trim']);
		if ($this->request->isAjax()) {

			$forms = $this->model->where([])->select();
			$forms = collection($forms)->toArray();


			$result = array("total" => count($forms), "rows" => $forms);

			return json($result);
		}

		return $this->view->fetch();
	}

    /**
     * 添加审批流程
     */
	public function add() {
		if ($this->request->isAjax()) {
			$data             = $this->request->post('row/a');
			if(!$data['name']){
                $this->error('审批流程标题不能为空');
            }
            $pingyin = new Pingyin();
			$english = $pingyin->get_all_py($data['name']);
			$formFind = Form::where(array('type'=>$english))->find();
			if($formFind){
                $english = $english.genRandomString(5);
            }
			//表单
            $formRes = array(
                'name'=>$data['name'],
                'type'=>$english,
            );
			$resForm = Form::create($formRes);
            $insetId = Form::getLastInsID();
            $data['form_id'] = $insetId;

			$result           = $this->model->allowField(true)->save($data);
			if (!$result || !$resForm) {
				$this->error('提交失败');
			}
			$this->success('提交成功');
		}
		$staffname = Staff::where(['status' => 1])->column('id,name');
		$staffs    = ['' => '无'];
		foreach ($staffname as $id => $name) {
			$staffs[$id] = $name;
		}
		$this->view->assign('staffs', $staffs);

		return $this->view->fetch();
	}


    /**
     * 修改审批流程
     */
	public function edit($ids = null) {
        $row=$this->model->get($ids);
        if ($this->request->isAjax()) {
            $data             = $this->request->post('row/a');
            $result           = $this->model->allowField(true)->save($data,['id'=>$ids]);
            if($row['form_id']){
                Form::where(array('id'=>$row['form_id']))->update(array('name'=>$data['name']));
            }
            if (!$result) {
                $this->error('提交失败');
            }
            $this->success('提交成功');
        }
        $staffname = Staff::where(['status' => 1])->column('id,name');
        $staffs    = ['' => '无'];
        foreach ($staffname as $id => $name) {
            $staffs[$id] = $name;
        }
        $this->view->assign('staffs', $staffs);
        $this->view->assign('row', $row);
        return $this->view->fetch();
	}
    /**
     * 删除审批流程
     */
    public function del($ids = null) {
        if ($this->request->isAjax()) {
            $map['id'] = $ids;
            $row=$this->model->get($ids);
            if(!$row){
                $this->error('数据不存在');
            }
            Form::destroy(array('id'=>$row['form_id']));
            $result    = $this->model->destroy($map);
            if (!$result) {
                $this->error('删除失败');
            }
            $this->success('删除成功');
        }

        return $this->view->fetch();
    }

    /**
     * 获取员工列表
     */
    public function get_staff()
    {

        $pageSize = input('pageSize');
        $pageNumber = input('pageNumber');
        $where = [];
        if ($keyValue = $this->request->request("keyValue")) {
            $where['id'] = ['in',explode(',',$keyValue)];
        }
        $name = input('name');
        if (!empty($name)) {
            $where['name'] = ['like', '%' . $name . '%'];
        }
        $customer = Staff::where($where)->field('id,name')->order('id desc')->paginate($pageSize, false, ['page' => $pageNumber]);
        return json(['list' => $customer->items(), 'total' => $customer->total()]);
    }
}
