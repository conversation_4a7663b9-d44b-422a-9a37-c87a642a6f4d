<?php

namespace app\admin\controller\qingdong\contract;
use addons\qingdong\model\Contract;
use addons\qingdong\model\Field;
use addons\qingdong\model\File;
use addons\qingdong\model\Staff;
use app\admin\controller\qingdong\Base;
use addons\qingdong\model\Record as RecordModel;
use think\DB;
use think\Exception;

/**
 * 跟进记录
 * 操作文档：https://doc.fastadmin.net/qingdong
 * 软件介绍：https://www.fastadmin.net/store/qingdong.html
 * 售后微信：qingdong_crm
 */
class Record extends Base {
    public function _initialize() {
        parent::_initialize();
        $this->model = new RecordModel();
    }


    /**
     * 跟进列表
     */
    public function index() {
        $this->request->filter(['strip_tags']);
        $need = input('need','');
        if ($this->request->isAjax()) {
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            //0:全部 1：我负责的 2：下属负责的 3：今日待跟进 4:今日已跟进
            $type = input('type',0);
            switch($type){
                case 1:
                    $staff = Staff::info();
                    $wheres['create_staff_id'] =  $staff->id;
                    break;
                case 2:
                    $wheres['create_staff_id'] =  array('in',Staff::getLowerStaffId());
                    break;
                case 3:
                    $start = date('Y-m-d 00:00:00');
                    $end = date('Y-m-d 23:59:59');
                    $record = collection(RecordModel::where(array('relation_type'=>3,'next_time'=>array(array('egt',$start),array('elt',$end))))->field("id,relation_id")->select())->toArray();
                    $relationId = [];
                    foreach($record as $k=>$v){
                        $whereRe['id'] = array('gt',$v['id']);
                        $whereRe['relation_id'] = $v['relation_id'];
                        $recordData = RecordModel::where($whereRe)->count();
                        if($recordData == 0){
                            $relationId[] = $v['id'];
                        }

                    }
                    $wheres['id'] =  array('in',$relationId);
                    $staff = Staff::info();
                    $wheres['create_staff_id'] =  $staff->id;
                    break;
                case 4:
                    $start = date('Y-m-d 00:00:00');
                    $end = date('Y-m-d 23:59:59');
                    $record = collection(RecordModel::where(array('relation_type'=>3,'next_time'=>array(array('egt',$start),array('elt',$end))))->field("id,relation_id")->select())->toArray();
                    $relationId = [];
                    foreach($record as $k=>$v){
                        $whereRe['id'] = array('gt',$v['id']);
                        $whereRe['relation_id'] = $v['relation_id'];
                        $recordData = RecordModel::where($whereRe)->count();
                        if($recordData >=1){
                            $relationId[] = $v['id'];
                        }

                    }
                    $wheres['id'] =  array('in',$relationId);
                    $staff = Staff::info();
                    $wheres['create_staff_id'] =  $staff->id;
                    break;
                default:
                    $wheres['create_staff_id'] =  array('in',Staff::getMyStaffIds());
                    break;

            }
            $wheres['relation_type'] = 3;
            if(isset($need) && $need =='contract'){
                $staff = Staff::info();
                $wheres['create_staff_id'] =  $staff->id;
                $wheres['status'] = 0;
                $wheres['follow_type'] = ['neq', '其它'];
                $wheres['next_time'] = array(array('egt',date('Y-m-d 00:00:00')),array('lt',date('Y-m-d 23:59:59')));
                $infolist = $this->model->where($wheres)->column('relation_id');
                if($infolist){
                    $whereExit['relation_id'] = array('in',$infolist);
                    $whereExit['next_time'] = array('gt',date('Y-m-d 23:59:59'));
                    $recordIds = $this->model->where($whereExit)->column('id');
                    if($recordIds){
                        $wheres['id'] = array('in',$recordIds);
                    }
                }
            }
            $list   = $this->model->where($where)->where($wheres)->with(['staff','contract'])->order($sort, $order)->paginate($limit);
            $row    = $list->items();
            $result = array("total" => $list->total(), "rows" => $row);

            return json($result);
        }

        return $this->view->fetch();
    }

    /**
     * 添加跟进
     */
    public function add($ids = null) {
        $relation_type=input('relation_type',3);
        if ($this->request->isPost()) {
            $params = $this->request->post('row/a');
            // 表单验证
            if (($result = $this->qingdongValidate($params, 'Record', 'create')) !== true) {
                $this->error($result);
            }
            if(!empty($params['files'])){
                $params['files']=File::getId($params['files']);
            }
            Db::startTrans();
            try {
                $result = $this->model::createRecord($params);
                Db::commit();
            } catch (Exception $e) {
                Db::rollback();
                $this->error($e->getMessage());
            }
            if ($result) {
                $this->success('创建跟进记录成功');
            }
            $this->error('创建失败');
        }

        $staff=Staff::where([])->column('name','id');
        if($ids){
            $contracts=Contract::where(['id'=>$ids])->column('id,name');
        }else{
            $contracts=Contract::where(['owner_staff_id'=>['in',Staff::getMyStaffIds()]])->column('id,name');
        }
        $follow= Field::getField('客户状态');
        $this->assign('ids', $ids);
        $this->assign('relation_type', $relation_type);
        $this->assign('contracts', $contracts);
        $this->assign('staff', $staff);
        $this->assign('follow', $follow);
        return $this->view->fetch();
    }

}