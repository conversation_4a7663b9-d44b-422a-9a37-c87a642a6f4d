define(['jquery', 'bootstrap', 'backend', 'form', 'table'], function ($, undefined, Backend, Form, Table) {
    function get_need_number(){
        $.post("qingdong/customer/need/get_need_number",function (data){
            top.window.Backend.api.sidebar({
                'qingdong/customer/need/index':[data.data.number, 'red', 'badge'],
                'qingdong/examine/examine/index':[data.data.contract, 'red', 'badge'], //合同
                'qingdong/examine/examine/consume':[data.data.consume, 'red', 'badge'],
                'qingdong/examine/examine/receivables':[data.data.receivables, 'red', 'badge'],
                'qingdong/examine/examine/achievement':[data.data.achievement, 'red', 'badge'],
                'qingdong/examine/examine/work':[data.data.approval, 'red', 'badge'],
                'qingdong/canlendar/canlendar':[data.data.eventOne, 'red', 'badge'],
            });
        },'json');
    }
    get_need_number();
    setInterval(function (){
        get_need_number();
    },50000);
    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'qingdong/canlendar/canlendar/index?need='+Fast.api.query("need"),
                    add_url: 'qingdong/canlendar/canlendar/add',
                    edit_url: 'qingdong/canlendar/canlendar/edit',
                    del_url: 'qingdong/canlendar/canlendar/del',
                    table: 'canlendar'
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                sortName: 'id',
                columns: [
                    [
                        {field: 'state', checkbox: true},
                        {field: 'title', title: __('标题'), operate:'like'},
                        {field: 'start_time', title: __('开始时间'),operate:false},
                        {field: 'end_time', title: __('结束时间'),operate:false},
                        {field: 'status', title: __('状态'), formatter: Table.api.formatter.status, searchList: {0: __('未开始'), 1: __('执行中'), 2: __('已结束'), 3: __('已取消')}},
                        {field: 'level', title: __('紧要程度'), formatter: Table.api.formatter.status, searchList: {1: __('重要'), 2: __('紧急'), 3: __('普通'), 4: __('重要且紧急')}},
                        {field: 'remark', title: __('备注'),operate:false},
                        {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', formatter: Table.api.formatter.datetime},
                        {
                            field: 'operate',
                            title: __('Operate'),
                            table: table,
                            events: Table.api.events.operate,
                            formatter: Table.api.formatter.operate
                        }
                    ]
                ],
                //启用普通表单搜索
                commonSearch: true,
                searchFormVisible: true,
                onLoadSuccess:function(){
                    // 这里就是数据渲染结束后的回调函数
                    $('.btn-editone').html('编辑');
                    $('.fa-pencil').remove();
                    $('.btn-delone').html('删除');
                    $('.fa-trash').remove();
                    $('.btn-editone').removeClass('btn-success')
                    $('.btn-editone').removeClass('btn')
                    $('.btn-delone').removeClass('btn')
                    $('.btn-delone').removeClass('btn-danger')
                }
            });
            // 为表格绑定事件
            Table.api.bindevent(table);
            $('.search').hide();
            $('.btn-import').hide();

        },
        add: function () {
            $('.relation_type').change(function(){
                var type = $(this).val();
                var url = '';
                if(type == 1){
                    url = 'qingdong/canlendar/canlendar/customer';
                }else if(type == 2){
                    url = 'qingdong/canlendar/canlendar/contacts';
                }else if(type == 3){
                    url = 'qingdong/canlendar/canlendar/contract';
                }else{
                    url = 'qingdong/canlendar/canlendar/leads';
                }
                $.ajax({url:url,success:function(ret) {
                        if (ret.code == 1) {
                            var data = ret.data;
                            var length = data.length;
                            var html = '';
                            for (var i = 0; i < length; i++) {
                                html += '<option value="' + data[i].id + '">' + data[i].name + '</option>';
                            }
                            $('#c-type').html(html);
                            $('select').selectpicker('refresh');
                        }
                    }
                });

            });
            Controller.api.bindevent();

        },

        edit: function () {
            $('.relation_type').change(function(){
                var type = $(this).val();
                var url = '';
                if(type == 1){
                    url = 'qingdong/canlendar/canlendar/customer';
                }else if(type == 2){
                    url = 'qingdong/canlendar/canlendar/contacts';
                }else if(type == 3){
                    url = 'qingdong/canlendar/canlendar/contract';
                }else{
                    url = 'qingdong/canlendar/canlendar/leads';
                }
                $.ajax({url:url,success:function(ret) {
                        if (ret.code == 1) {
                            var data = ret.data;
                            var length = data.length;
                            var html = '';
                            for (var i = 0; i < length; i++) {
                                html += '<option value="' + data[i].id + '">' + data[i].name + '</option>';
                            }
                            $('#c-type').html(html);
                            $('select').selectpicker('refresh');
                        }
                    }
                });

            });
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            },
            formatter: {
                thumb: function (value, row, index) {
                    var style = row.storage == 'upyun' ? '!/fwfh/120x90' : '';
                    return '<img src="' + row.image + '" data-tips-image alt="" title="封面图" style="max-height:90px;max-width:120px">';

                },

            }
        }

    };
    return Controller;
});