define(['jquery', 'bootstrap', 'backend', 'form', 'table'], function ($, undefined, Backend, Form, Table) {

    var Controller = {
        index : function () {
            $("input:radio[name='row[status]']").change(function (){
                var auto=$("input:radio[name='row[status]']:checked").val();
                if(auto == 1){
                    $('.auto').show();
                }else{
                    $('.auto').hide();
                }

            }).trigger('change');



            Form.api.bindevent($("form[role=form]"));
        },


    };
    return Controller;
});