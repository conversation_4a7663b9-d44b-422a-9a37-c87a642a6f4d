define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'qingdong/synchronizate/fauser/index',
                    table: 'fauser',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'user.id',
                columns: [
                    [
                        {checkbox: true,formatter: function (value, row, index) {
                                if (row.user_status == 0) {
                                    return {
                                        disabled: false,
                                    };
                                } else {
                                    return {
                                        disabled: true,
                                    }

                                }
                            }},
                        {field: 'username', title: __('Username'), operate: 'LIKE'},
                        {field: 'nickname', title: __('Nickname'), operate: 'LIKE'},
                        {field: 'email', title: __('Email'), operate: 'LIKE'},
                        {field: 'mobile', title: __('手机号'), operate: 'LIKE'},
                        {field: 'avatar', title: __('头像'), events: Table.api.events.image, formatter: Table.api.formatter.image, operate: false},
                        {field: 'level', title: __('级别'), operate: 'BETWEEN', sortable: true},
                        {field: 'user_status', title: __('状态'), formatter: Table.api.formatter.status, searchList: {0: __('未同步'), 1: __('已同步')}, operate: false},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.buttons,
                            buttons: [
                                {
                                    name: 'detail',
                                    text: __('同步'),
                                    title: __('同步'),
                                    classname: 'records btn-dialog',
                                    extend:'data-area=["90%","90%"]',
                                    url: 'qingdong/synchronizate/fauser/edit',
                                    visible: function (row) {
                                        if(row.user_status == 1){
                                            return false;
                                        }
                                        //返回true时按钮显示,返回false隐藏
                                        return true;
                                    }
                                }
                            ]
                        }                    ]
                ]
            });
            //批量操作
            $(document).on("click", ".batch", function () {
                var ids = Table.api.selectedids(table);
                var page = table.bootstrapTable('getData');
                var all = table.bootstrapTable('getOptions').totalRows;
                Layer.confirm("请选择同步的选项", {
                    title   : '批量同步',
                    btn     : ["选中项(" + ids.length + "条)", "全部(" + all + "条)"],
                    success : function (layero, index) {
                        $(".layui-layer-btn a", layero).addClass("layui-layer-btn0");
                    }
                    , yes   : function (index, layero) {
                        if (ids.length == 0) {
                            Layer.alert('数据为空');
                            return true;
                        }
                        Fast.api.open('qingdong/synchronizate/fauser/batch?type=1&id='+ids.join(","), '批量同步', {
                            callback:function(value){
                            },
                        area:['80%', '80%']
                        });
                        Layer.close(index);
                        return true;
                    }
                    ,
                    btn2    : function (index, layero) {

                        Fast.api.open('qingdong/synchronizate/fauser/batch?type=2&id='+ids.join(","), '批量同步', {
                            callback:function(value){
                            },
                            area:['80%', '80%']
                        });
                        Layer.close(index);
                        return true;
                    }

                })
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        edit: function () {
            $(document).on('change', "input[name='row[types]']", function() {
                var type = $(this).val();
                $('#type').val(type)
                var params = {type:type};
                if(type == 1){
                    $('.qd_th').html('线索字段名称');
                }else{
                    $('.qd_th').html('客户字段名称');
                }

                //来源
                $.ajax({url:"qingdong/synchronizate/fauser/source",data:params,success:function(result){
                        if(result.code == 1){
                            var data = result.data;
                            var length = data.length;
                           var html='';
                           for(var i=0;i<length;i++){
                               html +='<option value="'+data[i]+'">'+data[i]+'</option>';
                           }

                           $('select.sources').html(html);
                           $(".selectpicker").selectpicker('refresh');
                        }


                }});
                var parent = $('#num').val();

                $.ajax({url:"qingdong/synchronizate/fauser/fieldinfo",data:params,success:function(result){
                    if(result.code == 1){
                        var data = result.data;
                        var length = data.length;
                        var html ='<option value=""  style="text-align: center;text-align-last: center;direction: rtl;">无</option>';
                        for(var i=0;i<length;i++){
                            html +='<option data-id="'+parent+'" value="'+data[i].id+'">'+data[i].config.label+'</option>';
                        }
                        for(var i=0;i<20;i++){
                            $('select.customerinfo_'+i).html(html);
                            $(".selectpicker").selectpicker('refresh');
                        }

                    }
                }});

            });
            $(document).on('click', ".qd_append", function() {
                var num = $('#num').val();
                var parent = parseInt(num)+parseInt(1);
                $('#num').val(parent);

                var info = '<tr class="qd_tr_'+parent+'" style="">\n' +
                    '            <td style="text-align: center; vertical-align: middle;text-align-last: center; ">\n' +
                    '            <select name="" data-id="'+parent+'"   class="form-control userinfo_'+parent+' selectpicker userinfos" >\n' +
                    '            </select>\n' +
                    '            </td>\n' +
                    '            <td style="text-align: center; vertical-align: middle;text-align-last: center; ">\n' +
                    '            <select name="" data-id="'+parent+'"  class="form-control customerinfo_'+parent+' selectpicker customerinfos" >\n' +
                    '            </select>\n' +
                    '            </td>\n' +
                    '            <td style="text-align: center; vertical-align: middle;text-align-last: center; ">\n' +
                    '                <button type="button" data-id="'+parent+'" class="btn btn-danger qd_del">删除</button>\n' +
                    '            </td>\n' +
                    '        </tr>';

                $('.qd_tr').append(info);
                var type = $("input[name='row[type]']").val();
                var params = {type:type};
                //自定义客户线索
                $.ajax({url:"qingdong/synchronizate/fauser/fieldinfo",data:params,success:function(result){
                        if(result.code == 1){
                            var data = result.data;
                            var length = data.length;
                             var html ='<option value=""  style="text-align: center;text-align-last: center;direction: rtl;">无</option>';
                            for(var i=0;i<length;i++){
                                html +='<option data-id="'+parent+'" value="'+data[i].id+'">'+data[i].config.label+'</option>';
                            }
                            $('select.customerinfo_'+parent).html(html);
                            $(".selectpicker").selectpicker('refresh');
                        }
                    }});
                //自定义会员
                $.ajax({url:"qingdong/synchronizate/fauser/fielduser",data:'',success:function(result){
                        if(result.code == 1){
                            var datas = result.data;
                            var lengths = datas.length;
                            var userhtml ='<option value=""  style="text-align: center;text-align-last: center;direction: rtl;">无</option>';
                            for(var i=0;i<lengths;i++){
                                userhtml +='<option data-id="'+parent+'" value="'+datas[i].key+'">'+datas[i].value+'</option>';
                            }
                            $('select.userinfo_'+parent).html(userhtml);
                            $(".selectpicker").selectpicker('refresh');
                        }
                    }});

            });
            $(document).on('click', ".qd_del", function() {
                var num = $(this).data('id');
                $('.qd_tr_'+num).remove()
            });

            $(document).on('change', ".customerinfos", function() {
                var username = $(this).val();
                var num = $(this).data('id');
                if(username){
                    $(this).attr('name','rows['+username+'][customer][]');
                    $('.userinfo_'+num).attr('name','rows['+username+'][user][]');
                }
            });
            Controller.api.bindevent();
        },
        batch: function () {
            $(document).on('change', "input[name='row[types]']", function() {
                var type = $(this).val();
                $('#type').val(type)
                var params = {type:type};
                if(type == 1){
                    $('.qd_th').html('线索字段名称');
                }else{
                    $('.qd_th').html('客户字段名称');
                }

                //来源
                $.ajax({url:"qingdong/synchronizate/fauser/source",data:params,success:function(result){
                        if(result.code == 1){
                            var data = result.data;
                            var length = data.length;
                            var html='';
                            for(var i=0;i<length;i++){
                                html +='<option value="'+data[i]+'">'+data[i]+'</option>';
                            }

                            $('select.sources').html(html);
                            $(".selectpicker").selectpicker('refresh');
                        }


                    }});
                var parent = $('#num').val();

                $.ajax({url:"qingdong/synchronizate/fauser/fieldinfo",data:params,success:function(result){
                        if(result.code == 1){
                            var data = result.data;
                            var length = data.length;
                            var html ='<option value=""  style="text-align: center;text-align-last: center;direction: rtl;">无</option>';
                            for(var i=0;i<length;i++){
                                html +='<option data-id="'+parent+'" value="'+data[i].id+'">'+data[i].config.label+'</option>';
                            }
                            for(var i=0;i<20;i++){
                                $('select.customerinfo_'+i).html(html);
                                $(".selectpicker").selectpicker('refresh');
                            }

                        }
                    }});

            });
            $(document).on('click', ".qd_append", function() {
                var num = $('#num').val();
                var parent = parseInt(num)+parseInt(1);
                $('#num').val(parent);

                var info = '<tr class="qd_tr_'+parent+'" style="">\n' +
                    '            <td style="text-align: center; vertical-align: middle;text-align-last: center; ">\n' +
                    '            <select name="" data-id="'+parent+'"   class="form-control userinfo_'+parent+' selectpicker userinfos" >\n' +
                    '            </select>\n' +
                    '            </td>\n' +
                    '            <td style="text-align: center; vertical-align: middle;text-align-last: center; ">\n' +
                    '            <select name="" data-id="'+parent+'"  class="form-control customerinfo_'+parent+' selectpicker customerinfos" >\n' +
                    '            </select>\n' +
                    '            </td>\n' +
                    '            <td style="text-align: center; vertical-align: middle;text-align-last: center; ">\n' +
                    '                <button type="button" data-id="'+parent+'" class="btn btn-danger qd_del">删除</button>\n' +
                    '            </td>\n' +
                    '        </tr>';

                $('.qd_tr').append(info);
                var type = $("input[name='row[type]']").val();
                var params = {type:type};
                //自定义客户线索
                $.ajax({url:"qingdong/synchronizate/fauser/fieldinfo",data:params,success:function(result){
                        if(result.code == 1){
                            var data = result.data;
                            var length = data.length;
                            var html ='<option value=""  style="text-align: center;text-align-last: center;direction: rtl;">无</option>';
                            for(var i=0;i<length;i++){
                                html +='<option data-id="'+parent+'" value="'+data[i].id+'">'+data[i].config.label+'</option>';
                            }
                            $('select.customerinfo_'+parent).html(html);
                            $(".selectpicker").selectpicker('refresh');
                        }
                    }});
                //自定义会员
                $.ajax({url:"qingdong/synchronizate/fauser/fielduser",data:'',success:function(result){
                        if(result.code == 1){
                            var datas = result.data;
                            var lengths = datas.length;
                            var userhtml ='<option value=""  style="text-align: center;text-align-last: center;direction: rtl;">无</option>';
                            for(var i=0;i<lengths;i++){
                                userhtml +='<option data-id="'+parent+'" value="'+datas[i].key+'">'+datas[i].value+'</option>';
                            }
                            $('select.userinfo_'+parent).html(userhtml);
                            $(".selectpicker").selectpicker('refresh');
                        }
                    }});

            });
            $(document).on('click', ".qd_del", function() {
                var num = $(this).data('id');
                $('.qd_tr_'+num).remove()
            });

            $(document).on('change', ".customerinfos", function() {
                var username = $(this).val();
                var num = $(this).data('id');
                if(username){
                    $(this).attr('name','rows['+username+'][customer][]');
                    $('.userinfo_'+num).attr('name','rows['+username+'][user][]');
                }
            });
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});