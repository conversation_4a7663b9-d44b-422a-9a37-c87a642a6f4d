define(['jquery', 'bootstrap', 'backend', 'form', 'table'], function ($, undefined, Backend, Form, Table) {

    var Controller = {
        index: function () {

            // 初始化表格参数配置
            Table.api.init();

            //绑定事件
            $('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
                var panel = $($(this).attr("href"));
                if (panel.length > 0) {
                    Controller.table[panel.attr("id")].call(this);
                    $(this).on('click', function (e) {
                        $($(this).attr("href")).find(".btn-refresh").trigger("click");
                    });
                }
                //移除绑定的事件
                $(this).unbind('shown.bs.tab');
            });

            //必须默认触发shown.bs.tab事件
            $('ul.nav-tabs li.active a[data-toggle="tab"]').trigger("shown.bs.tab");

        },
        tableinfo:{
            url          : '',
            toolbar: '',
            pk           : 'id',
            sortName     : 'id',
            fixedColumns : true,
            fixedNumber  : 2,
            fixedRightNumber  : 1,
            columns: [
                [
                    {field: 'state', checkbox: true},
                    {field: 'create_staff.name', title: '姓名', operate: false},
                    {field: 'title', title: __('补卡原因'), operate: false},
                    {field: 'remark', title: __('补卡事由'), operate: false},
                    {
                        field: 'check_status',
                        title: '审批状态',
                        formatter: Table.api.formatter.status,
                        searchList: {0: '待审核', 1: '审核中', 2: '审核通过', 3: "审核未通过", 4: "已撤销"}
                    },
                    {
                        field: 'type',
                        title: __('补卡类型'),
                        formatter: Table.api.formatter.status,
                        searchList: {0: '补卡', 1: '早退补卡', 2: '迟到补卡'}
                    },
                    {
                        field: 'files',
                        title: __('附件'),
                        events: Table.api.events.image,
                        formatter: Table.api.formatter.images,
                        operate: false
                    },

                    {
                        field: 'createtime',
                        title: '打卡时间',
                        operate: 'RANGE',
                        addclass: 'datetimerange',
                        formatter: Table.api.formatter.datetime
                    },

                ]
            ],
            pagination        : true,
            search            : false,
            commonSearch      : true,
            searchFormVisible : true,
            //显示导出按钮
            showExport: true,
            onLoadSuccess:function(){
                // 这里就是数据渲染结束后的回调函数
                $(".btn-add").data("area",["80%","80%"]);
                $(".btn-edit").data("area",["80%","80%"]);
            }
        },
        table: {
            first: function () {
                // 初始化表格参数配置
                Table.api.init({
                    extend: {
                        index_url: 'qingdong/attendance/card/index',
                        table: 'card'
                    }
                });
                var table = $("#table");
                Controller.tableinfo.url=location.href+'&type=0';
                Controller.tableinfo.toolbar='#toolbar';
                Controller.tableinfo.columns[0][Controller.tableinfo.columns[0].length-1].table=table;
                // 初始化表格
                table.bootstrapTable(Controller.tableinfo);

                // 为表格绑定事件
                Table.api.bindevent(table);
            },
            second: function () {
                // 初始化表格参数配置
                Table.api.init({
                    extend: {
                        index_url: 'qingdong/attendance/card/index',
                        table: 'card'
                    }
                });
                var table = $("#table1");
                Controller.tableinfo.url=location.href+'&type=1';
                Controller.tableinfo.toolbar='#toolbar1';
                Controller.tableinfo.columns[0][Controller.tableinfo.columns[0].length-1].table=table;
                // 初始化表格
                table.bootstrapTable(Controller.tableinfo);

                // 为表格绑定事件
                Table.api.bindevent(table);
            },
            third: function () {
                // 初始化表格参数配置
                Table.api.init({
                    extend: {
                        index_url: 'qingdong/attendance/card/index',
                        table: 'card'
                    }
                });
                var table = $("#table2");
                Controller.tableinfo.url=location.href+'&type=2';
                Controller.tableinfo.toolbar='#toolbar2 ';
                Controller.tableinfo.columns[0][Controller.tableinfo.columns[0].length-1].table=table;
                // 初始化表格
                table.bootstrapTable(Controller.tableinfo);

                // 为表格绑定事件
                Table.api.bindevent(table);
            },
        },
        add: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            },
        }

    };
    return Controller;
});