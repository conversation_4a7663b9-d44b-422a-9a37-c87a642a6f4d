define(['jquery', 'bootstrap', 'backend', 'form', 'table'], function ($, undefined, Backend, Form, Table) {

    var Controller = {
        index: function () {

            // 初始化表格参数配置
            Table.api.init();

            //绑定事件
            $('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
                var panel = $($(this).attr("href"));
                if (panel.length > 0) {
                    Controller.table[panel.attr("id")].call(this);
                    $(this).on('click', function (e) {
                        $($(this).attr("href")).find(".btn-refresh").trigger("click");
                    });
                }
                //移除绑定的事件
                $(this).unbind('shown.bs.tab');
            });

            //必须默认触发shown.bs.tab事件
            $('ul.nav-tabs li.active a[data-toggle="tab"]').trigger("shown.bs.tab");

        },
        tableinfo:{
            url          : '',
            toolbar: '',
            pk           : 'id',
            sortName     : 'id',
            fixedColumns : true,
            fixedNumber  : 2,
            fixedRightNumber  : 1,
            columns: [
                [
                    {field: 'state', checkbox: true},
                    {field: 'create_staff.name', title: __('员工姓名'), operate: false},
                    {field: 'type', title: __('请假类型'), formatter: Table.api.formatter.status,
                        searchList: {'年假':'年假', '事假':'事假','病假':'病假','调休假':'调休假','婚假':'婚假','产假':'产假','陪产假':'陪产假','其他':'其他'}},
                    {field: 'start_time', title: '开始时间', operate: false},
                    {field: 'end_time', title: __('结束时间'), operate: false},
                    {field: 'hour', title: __('请假时长(小时)'), operate: false},
                    {field: 'remarks', title: __('备注'), operate: false},
                    {field: 'files', title: __('附件'), events: Table.api.events.image, formatter: Table.api.formatter.images, operate: false},

                    {field: 'createtime', title: '提交时间', operate:'RANGE', addclass:'datetimerange', formatter: Table.api.formatter.datetime},

                ]
            ],
            pagination        : true,
            search            : false,
            commonSearch      : true,
            searchFormVisible : true,
            //显示导出按钮
            showExport: false,
            onLoadSuccess:function(){
                // 这里就是数据渲染结束后的回调函数
                $(".btn-add").data("area",["80%","80%"]);
                $(".btn-edit").data("area",["80%","80%"]);
            }
        },
        table: {
            first: function () {
                // 初始化表格参数配置
                Table.api.init({
                    extend: {
                        index_url: 'qingdong/attendance/leave/index',
                        table: 'leave'
                    }
                });
                var table = $("#table");
                Controller.tableinfo.url=location.href+'&type=0';
                Controller.tableinfo.toolbar='#toolbar';
                Controller.tableinfo.columns[0][Controller.tableinfo.columns[0].length-1].table=table;
                // 初始化表格
                table.bootstrapTable(Controller.tableinfo);

                // 为表格绑定事件
                Table.api.bindevent(table);
            },
            second: function () {
                // 初始化表格参数配置
                Table.api.init({
                    extend: {
                        index_url: 'qingdong/attendance/leave/index',
                        table: 'leave'
                    }
                });
                var table = $("#table1");
                Controller.tableinfo.url=location.href+'&type=1';
                Controller.tableinfo.toolbar='#toolbar1';
                Controller.tableinfo.columns[0][Controller.tableinfo.columns[0].length-1].table=table;
                // 初始化表格
                table.bootstrapTable(Controller.tableinfo);

                // 为表格绑定事件
                Table.api.bindevent(table);
            },
            third: function () {
                // 初始化表格参数配置
                Table.api.init({
                    extend: {
                        index_url: 'qingdong/attendance/leave/index',
                        table: 'leave'
                    }
                });
                var table = $("#table2");
                Controller.tableinfo.url=location.href+'&type=2';
                Controller.tableinfo.toolbar='#toolbar2 ';
                Controller.tableinfo.columns[0][Controller.tableinfo.columns[0].length-1].table=table;
                // 初始化表格
                table.bootstrapTable(Controller.tableinfo);

                // 为表格绑定事件
                Table.api.bindevent(table);
            },
        },
        add: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            },
        }

    };
    return Controller;
});