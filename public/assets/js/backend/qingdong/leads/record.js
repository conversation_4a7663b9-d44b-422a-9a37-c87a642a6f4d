define(['jquery', 'bootstrap', 'backend', 'form', 'table'], function ($, undefined, Backend, Form, Table) {

    var Controller = {
        index: function () {
// 初始化表格参数配置
            Table.api.init();

            //绑定事件
            $('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
                var panel = $($(this).attr("href"));
                if (panel.length > 0) {
                    Controller.table[panel.attr("id")].call(this);
                    $(this).on('click', function (e) {
                        $($(this).attr("href")).find(".btn-refresh").trigger("click");
                    });
                }
                //移除绑定的事件
                $(this).unbind('shown.bs.tab');
            });

            //必须默认触发shown.bs.tab事件
            $('ul.nav-tabs li.active a[data-toggle="tab"]').trigger("shown.bs.tab");

        },
        table: {
            first: function () {
                // 初始化表格参数配置
                Table.api.init({
                    extend: {
                        index_url: 'qingdong/leads/record/index?type=0&need='+Fast.api.query("need"),
                        add_url: 'qingdong/leads/record/add',
                        customer_url  : 'qingdong/customer/leads/detail',
                        table: 'record'
                    }
                });

                var table = $("#table");

                // 初始化表格
                table.bootstrapTable({
                    url: $.fn.bootstrapTable.defaults.extend.index_url,
                    toolbar: '#toolbar',
                    sortName: 'id',
                    columns: [
                        [
                            {field: 'state', checkbox: true},
                            {
                                field : 'leads.name', title : '线索名称', fixedColumns : true, formatter : function (value, row, index) {
                                    if(row.leads){
                                        return "<a href='javascript:void(0);' data-id='" + row.leads.id + "' class='show-leads'>" + row.leads.name + "</a>";
                                    }else{
                                        return '';
                                    }

                                },operate:false
                            },
                            {field: 'follow_type', title: __('跟进类型')},
                            {field: 'follow_time', title: __('跟进时间')},
                            {field: 'follow', title: __('跟进状态')},
                            {field: 'content', title: __('跟进内容')},
                            {field: 'next_time', title: __('下次跟进时间')},
                            {field: 'staff.name', title: __('创建人'),operate:false},
                            {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', formatter: Table.api.formatter.datetime},
                            {
                                field     : 'operate',
                                title     : __('Operate'),
                                table     : table,
                                events    : Table.api.events.operate,
                                formatter : Table.api.formatter.operate,
                                buttons   : [
                                    {
                                        name      : '详情',
                                        text      : __('详情'),
                                        classname : 'records btn-dialog',
                                        url       : 'qingdong/customer/record/detail',
                                        callback: function (data) {
                                            $('.btn-success').trigger('click');
                                        },

                                    },
                                    {
                                        name      : '评论',
                                        text      : __('评论'),
                                        classname : 'records btn-dialog',
                                        url       : 'qingdong/customer/comment/add',
                                        callback: function (data) {
                                            $('.btn-success').trigger('click');
                                        },

                                    }
                                ]
                            }
                        ]
                    ],
                    //启用普通表单搜索
                    commonSearch: true,
                    searchFormVisible: true,
                    onLoadSuccess:function(){
                        // 这里就是数据渲染结束后的回调函数
                        $(".btn-add").data("area",["80%","80%"]);
                        $(".btn-edit").data("area",["80%","80%"]);
                    }
                });
                $(document).on('click', '.show-leads', function (data) {
                    var area = [$(window).width() > 1200 ? '1200px' : '95%', $(window).height() > 800 ? '800px' : '95%'];
                    var options = {
                        shadeClose : false,
                        shade      : [0.3, '#393D49'],
                        area       : area,
                        callback   : function (value) {
                            //在回调函数里可以调用你的业务代码实现前端的各种逻辑和效果
                        }
                    };
                    Fast.api.open($.fn.bootstrapTable.defaults.extend.customer_url + "?ids=" + $(this).data('id'), '线索详情', options);
                });
                // 为表格绑定事件
                Table.api.bindevent(table);
                $('.search').hide();
                $('.btn-import').hide();

            },
            second: function () {
                // 初始化表格参数配置
                Table.api.init({
                    extend: {
                        add_url: 'qingdong/leads/record/add',
                        customer_url  : 'qingdong/customer/leads/detail',
                        table: 'record'
                    }
                });
                var table = $("#table1");
                // 初始化表格
                table.bootstrapTable({
                    url: 'qingdong/leads/record/index?type=1&need='+Fast.api.query("need"),
                    toolbar: '#toolbar1',
                    sortName: 'id',
                    columns: [
                        [
                            {field: 'state', checkbox: true},
                            {
                                field : 'leads.name', title : '线索名称', fixedColumns : true, formatter : function (value, row, index) {
                                    if(row.leads){
                                        return "<a href='javascript:void(0);' data-id='" + row.leads.id + "' class='show-leads'>" + row.leads.name + "</a>";
                                    }else{
                                        return '';
                                    }

                                },operate:false
                            },
                            {field: 'follow_type', title: __('跟进类型')},
                            {field: 'follow_time', title: __('跟进时间')},
                            {field: 'follow', title: __('跟进状态')},
                            {field: 'content', title: __('跟进内容')},
                            {field: 'next_time', title: __('下次跟进时间')},
                            {field: 'staff.name', title: __('创建人'),operate:false},
                            {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', formatter: Table.api.formatter.datetime},
                            {
                                field     : 'operate',
                                title     : __('Operate'),
                                table     : table,
                                events    : Table.api.events.operate,
                                formatter : Table.api.formatter.operate,
                                buttons   : [
                                    {
                                        name      : '详情',
                                        text      : __('详情'),
                                        classname : 'records btn-dialog',
                                        url       : 'qingdong/customer/record/detail',
                                        callback: function (data) {
                                            $('.btn-success').trigger('click');
                                        },

                                    },
                                    {
                                        name      : '评论',
                                        text      : __('评论'),
                                        classname : 'records btn-dialog',
                                        url       : 'qingdong/customer/comment/add',
                                        callback: function (data) {
                                            $('.btn-success').trigger('click');
                                        },

                                    }
                                ]
                            }
                        ]
                    ],
                    //启用普通表单搜索
                    commonSearch: true,
                    searchFormVisible: true,
                    onLoadSuccess:function(){
                        // 这里就是数据渲染结束后的回调函数
                        $(".btn-add").data("area",["80%","80%"]);
                        $(".btn-edit").data("area",["80%","80%"]);
                    }
                });
                // 为表格绑定事件
                Table.api.bindevent(table);
                $('.search').hide();
                $('.btn-import').hide();

            },
            third: function () {
                // 初始化表格参数配置
                Table.api.init({
                    extend: {
                        add_url: 'qingdong/leads/record/add',
                        customer_url  : 'qingdong/customer/leads/detail',
                        table: 'record'
                    }
                });
                var table = $("#table2");
                // 初始化表格
                table.bootstrapTable({
                    url: 'qingdong/leads/record/index?type=2&need='+Fast.api.query("need"),
                    toolbar: '#toolbar2',
                    sortName: 'id',
                    columns: [
                        [
                            {field: 'state', checkbox: true},
                            {
                                field : 'leads.name', title : '所属客户', fixedColumns : true, formatter : function (value, row, index) {
                                    if(row.leads){
                                        return "<a href='javascript:void(0);' data-id='" + row.leads.id + "' class='show-leads'>" + row.leads.name + "</a>";
                                    }else{
                                        return '';
                                    }

                                },operate:false
                            },
                            {field: 'follow_type', title: __('跟进类型')},
                            {field: 'follow_time', title: __('跟进时间')},
                            {field: 'follow', title: __('跟进状态')},
                            {field: 'content', title: __('跟进内容')},
                            {field: 'next_time', title: __('下次跟进时间')},
                            {field: 'staff.name', title: __('创建人'),operate:false},
                            {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', formatter: Table.api.formatter.datetime},
                            {
                                field     : 'operate',
                                title     : __('Operate'),
                                table     : table,
                                events    : Table.api.events.operate,
                                formatter : Table.api.formatter.operate,
                                buttons   : [
                                    {
                                        name      : '详情',
                                        text      : __('详情'),
                                        classname : 'records btn-dialog',
                                        url       : 'qingdong/customer/record/detail',
                                        callback: function (data) {
                                            $('.btn-success').trigger('click');
                                        },

                                    },
                                    {
                                        name      : '评论',
                                        text      : __('评论'),
                                        classname : 'records btn-dialog',
                                        url       : 'qingdong/customer/comment/add',
                                        callback: function (data) {
                                            $('.btn-success').trigger('click');
                                        },

                                    }
                                ]
                            }
                        ]
                    ],
                    //启用普通表单搜索
                    commonSearch: true,
                    searchFormVisible: true,
                    onLoadSuccess:function(){
                        // 这里就是数据渲染结束后的回调函数
                        $(".btn-add").data("area",["80%","80%"]);
                        $(".btn-edit").data("area",["80%","80%"]);
                    }
                });
                // 为表格绑定事件
                Table.api.bindevent(table);
                $('.search').hide();
                $('.btn-import').hide();

            },
            four: function () {
                // 初始化表格参数配置
                Table.api.init({
                    extend: {
                        add_url: 'qingdong/leads/record/add',
                        customer_url  : 'qingdong/customer/leads/detail',
                        table: 'record'
                    }
                });
                var table = $("#table3");
                // 初始化表格
                table.bootstrapTable({
                    url: 'qingdong/leads/record/index?type=3&need='+Fast.api.query("need"),
                    toolbar: '#toolbar3',
                    sortName: 'id',
                    columns: [
                        [
                            {field: 'state', checkbox: true},
                            {
                                field : 'leads.name', title : '线索名称', fixedColumns : true, formatter : function (value, row, index) {

                                    if(row.leads){
                                        return "<a href='javascript:void(0);' data-id='" + row.leads.id + "' class='show-leads'>" + row.leads.name + "</a>";
                                    }else{
                                        return '';
                                    }

                                },operate:false
                            },
                            {field: 'follow_type', title: __('跟进类型')},
                            {field: 'follow_time', title: __('跟进时间')},
                            {field: 'follow', title: __('跟进状态')},
                            {field: 'content', title: __('跟进内容')},
                            {field: 'next_time', title: __('下次跟进时间')},
                            {field: 'staff.name', title: __('创建人'),operate:false},
                            {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', formatter: Table.api.formatter.datetime},
                            {
                                field     : 'operate',
                                title     : __('Operate'),
                                table     : table,
                                events    : Table.api.events.operate,
                                formatter : Table.api.formatter.operate,
                                buttons   : [
                                    {
                                        name      : '详情',
                                        text      : __('详情'),
                                        classname : 'records btn-dialog',
                                        url       : 'qingdong/customer/record/detail',
                                        callback: function (data) {
                                            $('.btn-success').trigger('click');
                                        },

                                    },
                                    {
                                        name      : '评论',
                                        text      : __('评论'),
                                        classname : 'records btn-dialog',
                                        url       : 'qingdong/customer/comment/add',
                                        callback: function (data) {
                                            $('.btn-success').trigger('click');
                                        },

                                    }
                                ]
                            }
                        ]
                    ],
                    //启用普通表单搜索
                    commonSearch: true,
                    searchFormVisible: true,
                    onLoadSuccess:function(){
                        // 这里就是数据渲染结束后的回调函数
                        $(".btn-add").data("area",["80%","80%"]);
                        $(".btn-edit").data("area",["80%","80%"]);
                    }
                });
                // 为表格绑定事件
                Table.api.bindevent(table);
                $('.search').hide();
                $('.btn-import').hide();

            },
            five: function () {
                // 初始化表格参数配置
                Table.api.init({
                    extend: {
                        add_url: 'qingdong/leads/record/add',
                        customer_url  : 'qingdong/customer/leads/detail',
                        table: 'record'
                    }
                });
                var table = $("#table4");
                // 初始化表格
                table.bootstrapTable({
                    url: 'qingdong/leads/record/index?type=4&need='+Fast.api.query("need"),
                    toolbar: '#toolbar4',
                    sortName: 'id',
                    columns: [
                        [
                            {field: 'state', checkbox: true},
                            {
                                field : 'leads.name', title : '线索名称', fixedColumns : true, formatter : function (value, row, index) {
                                    if(row.leads){
                                        return "<a href='javascript:void(0);' data-id='" + row.leads.id + "' class='show-leads'>" + row.leads.name + "</a>";
                                    }else{
                                        return '';
                                    }

                                },operate:false
                            },
                            {field: 'follow_type', title: __('跟进类型')},
                            {field: 'follow_time', title: __('跟进时间')},
                            {field: 'follow', title: __('跟进状态')},
                            {field: 'content', title: __('跟进内容')},
                            {field: 'next_time', title: __('下次跟进时间')},
                            {field: 'staff.name', title: __('创建人'),operate:false},
                            {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', formatter: Table.api.formatter.datetime},
                            {
                                field     : 'operate',
                                title     : __('Operate'),
                                table     : table,
                                events    : Table.api.events.operate,
                                formatter : Table.api.formatter.operate,
                                buttons   : [
                                    {
                                        name      : '详情',
                                        text      : __('详情'),
                                        classname : 'records btn-dialog',
                                        url       : 'qingdong/customer/record/detail',
                                        callback: function (data) {
                                            $('.btn-success').trigger('click');
                                        },

                                    },
                                    {
                                        name      : '评论',
                                        text      : __('评论'),
                                        classname : 'records btn-dialog',
                                        url       : 'qingdong/customer/comment/add',
                                        callback: function (data) {
                                            $('.btn-success').trigger('click');
                                        },

                                    }
                                ]
                            }
                        ]
                    ],
                    //启用普通表单搜索
                    commonSearch: true,
                    searchFormVisible: true,
                    onLoadSuccess:function(){
                        // 这里就是数据渲染结束后的回调函数
                        $(".btn-add").data("area",["80%","80%"]);
                        $(".btn-edit").data("area",["80%","80%"]);
                    }
                });
                // 为表格绑定事件
                Table.api.bindevent(table);
                $('.search').hide();
                $('.btn-import').hide();

            },
        },
        add: function () {
            Controller.api.bindevent();

        },

        edit: function () {
            Controller.api.bindevent();
        },
        detail: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            },
            formatter: {

            }
        }

    };
    return Controller;
});