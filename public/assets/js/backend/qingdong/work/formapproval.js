define(['jquery', 'bootstrap', 'backend', 'form', 'table'], function ($, undefined, Backend, Form, Table) {

	var Controller = {
		index     : function () {
			// 初始化表格参数配置
			Table.api.init({
				extend : {
					index_url : 'qingdong/work/formapproval/index',
					add_url   : 'qingdong/work/formapproval/add',
					edit_url  : 'qingdong/work/formapproval/edit',
					del_url  : 'qingdong/work/formapproval/del',
					table     : 'formapproval'
				}
			});

			var table = $("#table");

			// 初始化表格
			table.bootstrapTable({
				url               : $.fn.bootstrapTable.defaults.extend.index_url,
				sortName          : 'id',
				pageSize          : 1000,
				columns           : [
					[
						{field : 'name', title : __('模块'), width : 200},
						{field : 'img', title : __('图标'),  formatter: Table.api.formatter.image, operate: false},
						{field : 'desc', title : __('备注'), width : 600},
						{field : 'updatetime', title : '更新时间'},
						{
							field     : 'operate',
							title     : __('Operate'),
							table     : table,
							events    : Table.api.events.operate,
							formatter : Table.api.formatter.operate,
							buttons   : [
								{
									text      : "设计表单",
									title     : __('设计表单'),
									extend    : 'data-area=\'["100%","100%"]\'',
									classname : 'btn-xs btn-dialog',
									url: function (row) {
										return 'qingdong/general/form/edit/#/?id=' + row.form_id;
									}
								}
							],
						}
					]
				],
				pageList          : ['ALL'],
				//启用普通表单搜索
				commonSearch      : false,
				searchFormVisible : false,
				onLoadSuccess:function(){
					// 这里就是数据渲染结束后的回调函数
					$('.btn-editone').html('编辑');
					$('.fa-pencil').remove();
					$('.btn-delone').html('删除');
					$('.fa-trash').remove();
					$('.btn-editone').removeClass('btn-success')
					$('.btn-editone').removeClass('btn')
					$('.btn-delone').removeClass('btn')
					$('.btn-delone').removeClass('btn-danger')
				}
			});
			// 为表格绑定事件
			Table.api.bindevent(table);
			$('.search').hide();
			$('.btn-import').hide();

		},
		add       : function () {
			Controller.api.bindevent();
		},
		edit      : function () {
			Controller.api.bindevent();
		},
		edit_form : function () {
			Controller.api.bindevent();
		},
		api       : {
			bindevent : function () {
				$(document).on("fa.event.appendfieldlist", '.fieldlist', function(){
					//通用的表单组件事件绑定和组件渲染
					Form.events.selectpage($("form"));
					Form.events.datetimepicker($("form"));
				});
				$(document).on('change','.stafftype',function (){
					var stafftype=$(this).val();
					if(stafftype == 1 || stafftype == 2){
						$(this).parent('td').next('td').find('div').show();
					}else{
						$(this).parent('td').next('td').find('div').hide();
					}
				}).on('change','[name="row[status]"]',function (){
					var status=$(this).val();
					console.log(status)
					if(status == 1){
						$('.status_text').show();
					}else{
						$('.status_text').hide();
					}
				})
				Form.api.bindevent($("form[role=form]"));
			},
			formatter : {
				thumb : function (value, row, index) {

					return '<img src="' + row.img + '" data-tips-image alt="" title="封面图" style="max-height:90px;max-width:120px">';

				},

			}
		}

	};
	return Controller;
});