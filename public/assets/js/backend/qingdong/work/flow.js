define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'qingdong/work/flow/index' + location.search,
                    add_url: 'qingdong/work/flow/add',
                    edit_url: 'qingdong/work/flow/edit',
                    multi_url: 'qingdong/work/flow/multi',
                    import_url: 'qingdong/work/flow/import',
                    recyclebin_url: 'qingdong/work/flow/recyclebin',
                    table: 'qingdong_flow',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                columns: [
                    [
                        {checkbox: true},
                        {field: 'name', title: __('审批流名称'), operate: false},
                        {field: 'status', title: __('流程类型'),operate: false,formatter:Table.api.formatter.status, searchList: {0:'授权审批人', 1: '固定审批'}},
                        {field: 'relation_type', title: __('关联对象'), operate: false,formatter:Table.api.formatter.status,
                            searchList: {'contract':'合同', 'receivables': '回款', 'consume': '费用', 'achievement': '业绩目标', 'card': '补卡', 'leave': '请假'}},
                        {field: 'groups_text', title: __('适用范围'), operate: false},
                        {field: 'remark', title: __('备注'), operate: false},
                        {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'last_modified', title: __('最后修改时间'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'last_admin_text', title: __('最后修改人'), operate:false},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ],
                //启用普通表单搜索
                search:false,
                commonSearch: false,
                searchFormVisible: true,
                onLoadSuccess:function(){
                    // 这里就是数据渲染结束后的回调函数
                    $('.btn-editone').html('编辑');
                    $('.fa-pencil').remove();
                    $('.btn-delone').html('删除');
                    $('.fa-trash').remove();
                    $('.btn-editone').removeClass('btn-success')
                    $('.btn-editone').removeClass('btn')
                    $('.btn-delone').removeClass('btn')
                    $('.btn-delone').removeClass('btn-danger')
                }
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        recyclebin: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    'dragsort_url': ''
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: 'qingdong/work/recyclebin' + location.search,
                pk: 'id',
                sortName: 'id',
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'name', title: __('Name'), align: 'left'},
                        {
                            field: 'deletetime',
                            title: __('Deletetime'),
                            operate: 'RANGE',
                            addclass: 'datetimerange',
                            formatter: Table.api.formatter.datetime
                        },
                        {
                            field: 'operate',
                            width: '130px',
                            title: __('Operate'),
                            table: table,
                            events: Table.api.events.operate,
                            buttons: [
                                {
                                    name: 'Restore',
                                    text: __('Restore'),
                                    classname: 'btn btn-xs btn-info btn-ajax btn-restoreit',
                                    icon: 'fa fa-rotate-left',
                                    url: 'qingdong/work/restore',
                                    refresh: true
                                },
                                {
                                    name: 'Destroy',
                                    text: __('Destroy'),
                                    classname: 'btn btn-xs btn-danger btn-ajax btn-destroyit',
                                    icon: 'fa fa-times',
                                    url: 'qingdong/work/destroy',
                                    refresh: true
                                }
                            ],
                            formatter: Table.api.formatter.operate
                        }
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },

        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                $(document).on("fa.event.appendfieldlist", '.fieldlist', function(){
                    //通用的表单组件事件绑定和组件渲染
                    Form.events.selectpage($("form"));
                    Form.events.datetimepicker($("form"));
                });
                $(document).on('change','.stafftype',function (){
                    var stafftype=$(this).val();
                    if(stafftype == 1 || stafftype == 2){
                        $(this).parent('td').next('td').find('div').show();
                    }else{
                        $(this).parent('td').next('td').find('div').hide();
                    }
                }).on('change','[name="row[status]"]',function (){
                    var status=$(this).val();
                    console.log(status)
                    if(status == 1){
                        $('.status_text').show();
                    }else{
                        $('.status_text').hide();
                    }
                })
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});