define(['jquery', 'bootstrap', 'backend', 'form', 'table'], function ($, undefined, Backend, Form, Table) {
    var Controller = {
        index: function () {

            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'qingdong/work/approval/index',
                    add_url: 'qingdong/work/approval/add',
                    table: 'approval'
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                sortName: 'id',
                columns: [
                    [
                        {field: 'state', checkbox: true},
                        {field: 'formapproval.name', title: __('类型'), operate:false},
                        {field: 'create_staff.name', title: __('创建人'), operate:false},
                        {field: 'check_status', title: __('状态'),formatter:Table.api.formatter.status,
                            searchList: {'0':'待审核', 1:'审核中',2:'审核通过',3:'审核未通过',4:'撤销',}},
                        {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', formatter: Table.api.formatter.datetime},
                        {
                            field     : 'operate',
                            title     : __('Operate'),
                            table     : table,
                            events    : Table.api.events.operate,
                            formatter : Table.api.formatter.operate,
                            buttons   : [
                                {
                                    name: 'detail',
                                    text: __('详情'),
                                    classname: 'records btn-dialog',
                                    url: 'qingdong/work/approval/detail',
                                    extend: "data-area='[\"80%\", \"80%\"]'",
                                    callback: function (data) {
                                        Layer.alert("接收到回传数据：" + JSON.stringify(data), {title: "回传数据"});
                                    }
                                }

                            ]
                        }
                    ]
                ],
                //启用普通表单搜索
                commonSearch: true,
                searchFormVisible: true,
                onLoadSuccess:function(){
                    // 这里就是数据渲染结束后的回调函数
                    $(".btn-add").data("area",["80%","80%"]);
                    $(".btn-edit").data("area",["80%","80%"]);
                }
            });
            // 为表格绑定事件
            Table.api.bindevent(table);
            $('.search').hide();
            $('.btn-import').hide();
            $('#myPopover').popover();
        },
        add: function () {
            Controller.api.bindevent();

        },

        edit: function () {
            Controller.api.bindevent();
        },
        detail: function () {
            $('.stafflist').popover({
                placement : 'bottom',
                trigger : 'hover'
            });
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"),function (data){
                    Fast.api.close(data);
                });
            },
            formatter: {
                thumb: function (value, row, index) {
                    var style = row.storage == 'upyun' ? '!/fwfh/120x90' : '';
                    return '<img src="' + row.image + '" data-tips-image alt="" title="封面图" style="max-height:90px;max-width:120px">';

                },

            }
        }

    };
    return Controller;
});