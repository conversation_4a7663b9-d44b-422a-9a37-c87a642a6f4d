define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {
    var Controller = {
        report: function () {
            Table.api.init({});
            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                sortName: 'id',
                //启用普通表单搜索
                search:false,
                escape:false,
                pagination:false,
                commonSearch: true,
                searchFormVisible: true,
            });
            // 为表格绑定事件
            Table.api.bindevent(table);

            Form.events.selectpage($("form"));
            Form.events.daterangepicker($("form"));
        },record: function () {
            Table.api.init({});
            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                sortName: 'id',
                //启用普通表单搜索
                search:false,
                escape:false,
                pagination:false,
                commonSearch: true,
                searchFormVisible: true,
            });
            // 为表格绑定事件
            Table.api.bindevent(table);

            Form.events.selectpage($("form"));
            Form.events.daterangepicker($("form"));
        },record_list: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: location.href,
                    table: 'record'
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                toolbar: '#toolbar',
                sortName: 'id',
                columns: [
                    [
                        {field: 'state', checkbox: true},
                        {
                            field : 'customer.name', title : '所属客户', fixedColumns : true, formatter : function (value, row, index) {
                                if(row.customer){
                                    return "<a href='javascript:void(0);' data-id='" + row.customer.id + "' class='show-customer'>" + row.customer.name + "</a>";
                                }else{
                                    return '';
                                }

                            },operate:false
                        },
                        {field: 'follow_type', title: __('跟进类型'),operate:false},
                        {field: 'follow_time', title: __('跟进时间'),operate:false},
                        {field: 'follow', title: __('跟进状态'),operate:false},
                        {field: 'content', title: __('跟进内容'),operate:false},
                        {field: 'next_time', title: __('下次跟进时间'),operate:false},
                        {field: 'staff.name', title: __('创建人'),operate:false},
                        {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', formatter: Table.api.formatter.datetime},
                        {
                            field     : 'operate',
                            title     : __('Operate'),
                            table     : table,
                            events    : Table.api.events.operate,
                            formatter : Table.api.formatter.operate,
                            buttons   : [
                                {
                                    name      : '详情',
                                    text      : __('详情'),
                                    classname : 'records btn-dialog',
                                    url       : 'qingdong/customer/record/detail',
                                    callback: function (data) {
                                        $('.btn-success').trigger('click');
                                    },

                                },
                                {
                                    name      : '评论',
                                    text      : __('评论'),
                                    classname : 'records btn-dialog',
                                    url       : 'qingdong/customer/comment/add',
                                    callback: function (data) {
                                        $('.btn-success').trigger('click');
                                    },

                                }
                            ]
                        }
                    ]
                ],
                search:false,
                //启用普通表单搜索
                commonSearch: true,
                searchFormVisible: true,
                onLoadSuccess:function(){
                    // 这里就是数据渲染结束后的回调函数
                    $(".btn-add").data("area",["80%","80%"]);
                    $(".btn-edit").data("area",["80%","80%"]);
                }
            });
            $(document).on('click', '.show-customer', function (data) {
                var area = [$(window).width() > 1200 ? '1200px' : '95%', $(window).height() > 800 ? '800px' : '95%'];
                var options = {
                    shadeClose : false,
                    shade      : [0.3, '#393D49'],
                    area       : area,
                    callback   : function (value) {
                        //在回调函数里可以调用你的业务代码实现前端的各种逻辑和效果
                    }
                };
                Fast.api.open($.fn.bootstrapTable.defaults.extend.customer_url + "?ids=" + $(this).data('id'), '客户详情', options);
            });
            // 为表格绑定事件
            Table.api.bindevent(table);
        },
    };
    return Controller;
});