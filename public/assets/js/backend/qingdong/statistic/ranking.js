define(['jquery', 'bootstrap', 'backend', 'addtabs', 'table', 'echarts', 'echarts-theme', 'template', 'form'],
    function ($, undefined, Backend, Datatable, Table, Echarts, undefined, Template,Form) {

    var Controller = {
        //合同金额分析
        index: function () {
            var myChart2=Echarts.init(document.getElementById('echart'), 'walden');
            var option2 = {

                legend: {
                    data: ['合同金额']
                },

                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                xAxis: [
                    {
                        type: 'value',

                    }
                ],
                yAxis: [
                    {
                        type: 'category',
                        data: Orderdata.customerdata.name,

                    }

                ],
                series: [
                    {
                        name: '合同金额',
                        type: 'bar',
                        barWidth:30,
                        data: Orderdata.customerdata.num
                    }
                ]
            };

            // 使用刚指定的配置项和数据显示图表。
            myChart2.setOption(option2);

            Form.events.selectpage($("form"));
            Form.events.daterangepicker($("form"));

        },
        contract: function () {
            var myChart2=Echarts.init(document.getElementById('echart'), 'walden');
            var option2 = {

                legend: {
                    data: ['回款金额']
                },

                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                xAxis: [
                    {
                        type: 'value',

                    }
                ],
                yAxis: [
                    {
                        type: 'category',
                        data: Orderdata.customerdata.name,

                    }

                ],
                series: [
                    {
                        name: '回款金额',
                        type: 'bar',
                        barWidth:30,
                        data: Orderdata.customerdata.num
                    }
                ]
            };

            // 使用刚指定的配置项和数据显示图表。
            myChart2.setOption(option2);

            Form.events.selectpage($("form"));
            Form.events.daterangepicker($("form"));

        },
        customer: function () {
            var myChart2=Echarts.init(document.getElementById('echart'), 'walden');
            var option2 = {

                legend: {
                    data: ['新增客户数']
                },

                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                xAxis: [
                    {
                        type: 'value',

                    }
                ],
                yAxis: [
                    {
                        type: 'category',
                        data: Orderdata.customerdata.name,

                    }

                ],
                series: [
                    {
                        name: '新增客户数',
                        type: 'bar',
                        barWidth:30,
                        data: Orderdata.customerdata.num
                    }
                ]
            };

            // 使用刚指定的配置项和数据显示图表。
            myChart2.setOption(option2);

            Form.events.selectpage($("form"));
            Form.events.daterangepicker($("form"));

        },
        record: function () {
            var myChart2=Echarts.init(document.getElementById('echart'), 'walden');
            var option2 = {

                legend: {
                    data: ['跟进次数']
                },

                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                xAxis: [
                    {
                        type: 'value',

                    }
                ],
                yAxis: [
                    {
                        type: 'category',
                        data: Orderdata.customerdata.name,

                    }

                ],
                series: [
                    {
                        name: '跟进次数',
                        type: 'bar',
                        barWidth:30,
                        data: Orderdata.customerdata.num
                    }
                ]
            };

            // 使用刚指定的配置项和数据显示图表。
            myChart2.setOption(option2);

            Form.events.selectpage($("form"));
            Form.events.daterangepicker($("form"));

        },
    };

    return Controller;
});
