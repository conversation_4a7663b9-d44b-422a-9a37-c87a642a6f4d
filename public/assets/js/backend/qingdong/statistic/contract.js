define(['jquery', 'bootstrap', 'backend', 'addtabs', 'table', 'echarts', 'echarts-theme', 'template','form'],
    function ($, undefined, Backend, Datatable, Table, Echarts, undefined, Template,Form) {

    var Controller = {
        //合同数量统计分析
        newadd: function () {
            var myChart2=Echarts.init(document.getElementById('echart'), 'walden');
            var option2 = {
                title: {
                    text: Orderdata.data.title
                },
                color:['#c23531'],
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                toolbox: {
                    show: true,
                    feature: {
                        dataZoom: {
                            yAxisIndex: 'none'
                        },
                        dataView: { readOnly: false },
                        magicType: { type: ['line', 'bar'] },
                        restore: {},
                        saveAsImage: {}
                    }
                },
                legend: {
                    data: [ '2021年度','2022年度']
                },
                xAxis: {
                    type: 'category',
                    boundaryGap: false,
                    data: Orderdata.data.date,
                },
                yAxis: {
                    type: 'value'
                },
                series: [
                    {
                        name: '2021年度',
                        type: 'line',
                        stack: 'Total',
                        data:  Orderdata.data.last_number
                    },
                    {
                        name: '2022年度',
                        type: 'line',
                        stack: 'Total',
                        data: Orderdata.data.number
                    },
                ]
            };

            // 使用刚指定的配置项和数据显示图表。
            myChart2.setOption(option2);

            Form.events.selectpage($("form"));
            Form.events.daterangepicker($("form"));
        },
        //合同金额统计分析
        money: function () {
            var myChart2=Echarts.init(document.getElementById('echart'), 'walden');
            var option2 = {
                title: {
                    text: Orderdata.data.title
                },
                color:['#c23531'],
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                toolbox: {
                    show: true,
                    feature: {
                        dataZoom: {
                            yAxisIndex: 'none'
                        },
                        dataView: { readOnly: false },
                        magicType: { type: ['line', 'bar'] },
                        restore: {},
                        saveAsImage: {}
                    }
                },
                legend: {
                    data: [ '2021年度','2022年度']
                },
                xAxis: {
                    type: 'category',
                    boundaryGap: false,
                    data: Orderdata.data.date,
                },
                yAxis: {
                    type: 'value'
                },
                series: [
                    {
                        name: '2021年度',
                        type: 'line',
                        stack: 'Total',
                        data:  Orderdata.data.last_number
                    },
                    {
                        name: '2022年度',
                        type: 'line',
                        stack: 'Total',
                        data: Orderdata.data.number
                    },
                ]
            };

            // 使用刚指定的配置项和数据显示图表。
            myChart2.setOption(option2);

            Form.events.selectpage($("form"));
            Form.events.daterangepicker($("form"));
        },
        //回款金额统计分析
        receivables: function () {
            var myChart2=Echarts.init(document.getElementById('echart'), 'walden');
            var option2 = {
                title: {
                    text: Orderdata.data.title
                },
                color:['#c23531'],
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                toolbox: {
                    show: true,
                    feature: {
                        dataZoom: {
                            yAxisIndex: 'none'
                        },
                        dataView: { readOnly: false },
                        magicType: { type: ['line', 'bar'] },
                        restore: {},
                        saveAsImage: {}
                    }
                },
                legend: {
                    data: [ '2021年度','2022年度']
                },
                xAxis: {
                    type: 'category',
                    boundaryGap: false,
                    data: Orderdata.data.date,
                },
                yAxis: {
                    type: 'value'
                },
                series: [
                    {
                        name: '2021年度',
                        type: 'line',
                        stack: 'Total',
                        data:  Orderdata.data.last_number
                    },
                    {
                        name: '2022年度',
                        type: 'line',
                        stack: 'Total',
                        data: Orderdata.data.number
                    },
                ]
            };

            // 使用刚指定的配置项和数据显示图表。
            myChart2.setOption(option2);

            Form.events.selectpage($("form"));
            Form.events.daterangepicker($("form"));
        },
    };

    return Controller;
});
