define(['jquery', 'bootstrap', 'backend', 'addtabs', 'table', 'echarts', 'echarts-theme', 'template','form'],
    function ($, undefined, Backend, Datatable, Table, Echarts, undefined, Template,Form) {

    var Controller = {
        //业绩目标分析
        index: function () {
            var myChart2=Echarts.init(document.getElementById('echart'), 'walden');
            var option2 = {

                color:['#c23531'],
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                legend: {
                    data: ['目标合计', '金额']
                },
                xAxis: [
                    {
                        type: 'category',
                        data: Orderdata.staffdata.month,

                    }
                ],
                yAxis: [
                    {
                        type: 'value',
                        name: '目标合计',
                        axisLabel: {
                            formatter: '{value} 元',
                        }
                    },
                    {
                        type: 'value',
                        name: '金额',
                        axisLabel: {
                            formatter: '{value} 元',
                        }

                    }
                ],
                series: [
                    {
                        name: '目标合计',
                        type: 'bar',
                        data: Orderdata.staffdata.achievement
                    },

                    {
                        name: '金额',
                        type: 'bar',

                        data: Orderdata.staffdata.money
                    }
                ]
            };

            // 使用刚指定的配置项和数据显示图表。
            myChart2.setOption(option2);

            Form.events.selectpage($("form"));
            Form.events.datetimepicker($("form"));

        },
    };

    return Controller;
});
