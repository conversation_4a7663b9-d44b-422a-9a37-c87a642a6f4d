define(['jquery', 'bootstrap', 'backend', 'addtabs', 'table', 'echarts', 'echarts-theme', 'template','form'],
    function ($, undefined, Backend, Datatable, Table, Echarts, undefined, Template,Form) {

    var Controller = {
        //客户总量分析
        index: function () {


            var myChart2=Echarts.init(document.getElementById('echart'), 'walden');
            var option2 = {

                color:['#c23531'],

                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                legend: {
                    data: ['新增客户数', '成交客户数']
                },
                xAxis: [
                    {
                        type: 'category',
                        data: Orderdata.customerdata.date,

                    }
                ],
                yAxis: [
                    {
                        type: 'value',
                        name: '数量',
                        axisLabel: {
                            formatter: '{value} 个',
                        }
                    },
                    {
                        type: 'value',


                    }
                ],
                series: [
                    {
                        name: '新增客户数',
                        type: 'bar',
                        data: Orderdata.customerdata.num
                    },

                    {
                        name: '成交客户数',
                        type: 'bar',

                        data: Orderdata.customerdata.use
                    }
                ]
            };

            // 使用刚指定的配置项和数据显示图表。
            myChart2.setOption(option2);

            Form.events.selectpage($("form"));
            Form.events.daterangepicker($("form"));
        },
        //客户跟进次数分析
        record: function () {
            var myChart2=Echarts.init(document.getElementById('echart'), 'walden');
            var option2 = {

                color:['#c23531'],

                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                legend: {
                    data: ['跟进客户数', '跟进次数']
                },
                xAxis: [
                    {
                        type: 'category',
                        data: Orderdata.customerdata.date,

                    }
                ],
                yAxis: [
                    {
                        type: 'value',
                        name: '跟进客户数',
                        axisLabel: {
                            formatter: '{value} 个',
                        }
                    },
                    {
                        type: 'value',
                        name: '跟进次数',
                        axisLabel: {
                            formatter: '{value} 次',
                        }

                    }
                ],
                series: [
                    {
                        name: '跟进客户数',
                        type: 'bar',
                        data: Orderdata.customerdata.num
                    },

                    {
                        name: '跟进次数',
                        type: 'bar',

                        data: Orderdata.customerdata.use
                    }
                ]
            };

            // 使用刚指定的配置项和数据显示图表。
            myChart2.setOption(option2);

            Form.events.selectpage($("form"));
            Form.events.daterangepicker($("form"));

        },
        //客户跟进方式分析
        recordtype:function(){
            var series=[];
            $.each(Orderdata.sourcedata,function(index){
                if(index != 0){
                    series.push({type: 'line', smooth: true, seriesLayoutBy: 'row', emphasis: {focus: 'series'}});
                }

            });
            var length=Orderdata.sourcedata[0].length;
            length=length-1;
            series.push({
                type: 'pie',
                id: 'pie',
                radius: '40%',
                center: ['50%', '30%'],
                emphasis: {focus: 'data'},
                label: {
                    formatter: '{b}: {@'+Orderdata.sourcedata[0][length]+'} ({d}%)'
                },
                encode: {
                    itemName: Orderdata.sourcedata[0][0],
                    value:  Orderdata.sourcedata[0][length],
                    tooltip:  Orderdata.sourcedata[0][length]
                }
            });
            // 基于准备好的dom，初始化echarts实例
            var myChart = Echarts.init(document.getElementById('echart'), 'walden');

            // 指定图表的配置项和数据
            var  option = {
                legend: {},
                tooltip: {
                    trigger: 'axis',
                    showContent: false
                },
                dataset: {
                    source: Orderdata.sourcedata
                },
                xAxis: {type: 'category'},
                yAxis: {gridIndex: 0},
                grid: {top: '65%',height:'32%'},
                series:series
            };

            myChart.on('updateAxisPointer', function (event) {
                var xAxisInfo = event.axesInfo[0];
                if (xAxisInfo) {
                    var dimension = xAxisInfo.value + 1;
                    myChart.setOption({
                        series: {
                            id: 'pie',
                            label: {
                                formatter: '{b}: {@[' + dimension + ']} ({d}%)'
                            },
                            encode: {
                                value: dimension,
                                tooltip: dimension
                            }
                        }
                    });
                }
            });

            // 使用刚指定的配置项和数据显示图表。
            myChart.setOption(option);

            Form.events.selectpage($("form"));
            Form.events.daterangepicker($("form"));
        },
        //客户自定义分析
        customize:function(){
            var myChart2=Echarts.init(document.getElementById('echart'), 'walden');
            console.log(Orderdata.customerdata)
            if(Orderdata.customerdata.type == 'bar'){
                var option2 = {

                    color:['#7cb5ec'],
                    toolbox: {
                        show: true,
                        feature: {
                            magicType: {type: [ 'line','bar']},
                            saveAsImage: {}
                        },
                    },
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            type: 'shadow'
                        }
                    },
                    legend: {
                        data: ['跟进客户数', '跟进次数']
                    },
                    xAxis: [
                        {
                            type: 'category',
                            data: Orderdata.customerdata.x,
                            axisLabel: {
                                interval:0,
                                rotate:40
                            },
                        }
                    ],
                    yAxis: [
                        {
                            type: 'value',
                            name: '客户总数',
                            axisLabel: {
                                formatter: '{value} 个',
                            }
                        }
                    ],
                    series: [
                        {
                            name: '客户总数',
                            type: 'bar',
                            data: Orderdata.customerdata.y,
                            barMaxWidth:40
                        }
                    ]
                };
            }else{
                var option2 = {
                    title: {
                        text: Orderdata.customerdata.title,
                        left: 'center'
                    },
                    toolbox: {
                        show: true,
                        feature: {
                            saveAsImage: {}
                        },
                    },
                    tooltip: {
                        trigger: 'item'
                    },
                    legend: {
                        orient: 'vertical',
                        left: 'left'
                    },
                    series: [
                        {
                            name: Orderdata.customerdata.title,
                            type: 'pie',
                            radius: '50%',
                            data: Orderdata.customerdata.data,
                            emphasis: {
                                itemStyle: {
                                    shadowBlur: 10,
                                    shadowOffsetX: 0,
                                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                                }
                            }
                        }
                    ]
                };
            }

            // 使用刚指定的配置项和数据显示图表。
            myChart2.setOption(option2);

            Form.events.selectpage($("form"));
            Form.events.daterangepicker($("form"));
        },
    };

    return Controller;
});
