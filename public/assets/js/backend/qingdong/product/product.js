define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

	var Controller = {
		index  : function () {
			// 初始化表格参数配置
			Table.api.init({
				extend : {
					index_url  : 'qingdong/product/product/index',
					detail_url : 'qingdong/product/product/detail',
					del_url    : 'qingdong/product/product/del',
					table      : 'product',
				}
			});

			var table = $("#table");
			var q = {};
			location.search.replace(/([^?&=]+)=([^&]+)/g,(_,k,v)=>q[k]=v);
			var isselect=false;
			var pselect = true;
			if (q.isselect == 1) {
				isselect= true;
				pselect = false;
			}

			// 初始化表格
			table.bootstrapTable({
				url          : $.fn.bootstrapTable.defaults.extend.index_url,
				pk           : 'id',
				sortName     : 'id',
				fixedColumns : true,
				fixedNumber  : 2,
				columns      : [
					[
						{checkbox : true},
						{
							field : 'type_id', title : '产品分类', operate : '=', searchList:$.getJSON("qingdong/product/product/get_type?type=search")
						},
						{
							field : 'name', title : '产品名称', operate : 'like', fixedColumns : true, formatter : function (value, row, index) {
								return "<a href='javascript:void(0);' class='btn-dialog' " +
									"data-url='qingdong/product/product/detail/ids/"+row.id+"' data-shade='0.3' data-title='产品详情' >" + value + "</a>";
							}
						},
						{field : 'num', title : '产品编码', operate : 'like'},
						{field: 'img', title: '产品图片', operate: false, events: Table.api.events.image, formatter: Table.api.formatter.image},
						{field : 'unit', title : '产品单位', operate : false},
						{field : 'price', title : '零售价', operate : false},
						{field : 'wholesale', title : '批发价', operate : false},
						{field: 'cost_price', title: __('采购价'), operate: 'LIKE'},

						{field : 'status', title : '状态',formatter: Table.api.formatter.status, searchList: { '下架': __('下架'),'上架': __('上架')}},
						{
							field: 'operate2', title: __('Operate'),visible:isselect, operate : false,events: {
								'click .btn-chooseone': function (e, value, row, index) {
									Fast.api.close(row);
								},
							}, formatter: function () {
								return '<a href="javascript:;" class="btn btn-danger btn-chooseone btn-xs"><i class="fa fa-check"></i> ' + __('Choose') + '</a>';
							}
						},
						{
							field: 'operate', title: __('Operate'),table: table,visible:pselect,
							events: Table.api.events.operate, formatter: Table.api.formatter.buttons,
							buttons: [

								{
									name: 'edit',
									text: __('编辑'),
									title: __('编辑'),
									extend:'data-area=["80%","80%"]',
									classname: 'records btn-dialog',
									url: 'qingdong/product/product/edit',
								},
								{
									name: 'detail',
									text: __('详情'),
									title: __('详情'),
									extend:'data-area=["80%","80%"]',
									classname: 'records btn-dialog',
									url: 'qingdong/product/product/detail',
								},

							]
						},
					]
				],
				search:false,
				//启用普通表单搜索
				commonSearch      : true,
				searchFormVisible:true,
				showSearch         : true,
				pageSize           : 10
			});


			// 为表格绑定事件
			Table.api.bindevent(table);
		},
		add    : function () {
			Controller.api.bindevent();
		},
		add_part    : function () {
			Controller.api.bindevent();
		},
		edit   : function () {
			Controller.api.bindevent();
		},
		import   : function () {
			Controller.api.bindevent();
		},
		recyclebin: function () {
			// 初始化表格参数配置
			Table.api.init({
				extend: {
					'dragsort_url': ''
				}
			});

			var table = $("#table");
			// 初始化表格
			table.bootstrapTable({
				url: 'qingdong/product/product/recyclebin' + location.search,
				pk: 'id',
				sortName: 'id',
				columns: [
					[
						{checkbox: true},
						{field: 'id', title: __('Id')},
						{field: 'name', title: __('Name'), align: 'left'},
						{
							field: 'deletetime',
							title: __('Deletetime'),
							operate: 'RANGE',
							addclass: 'datetimerange',
							formatter: Table.api.formatter.datetime
						},
						{
							field: 'operate',
							width: '130px',
							title: __('Operate'),
							table: table,
							events: Table.api.events.operate,
							buttons: [
								{
									name: 'Restore',
									text: __('Restore'),
									classname: 'btn btn-xs btn-info btn-ajax btn-restoreit',
									icon: 'fa fa-rotate-left',
									url: 'qingdong/product/product/restore',
									refresh: true
								},
								{
									name: 'Destroy',
									text: __('Destroy'),
									classname: 'btn btn-xs btn-danger btn-ajax btn-destroyit',
									icon: 'fa fa-times',
									url: 'qingdong/product/product/destroy',
									refresh: true
								}
							],
							formatter: Table.api.formatter.operate
						}
					]
				]
			});
			// 为表格绑定事件
			Table.api.bindevent(table);
		},
		edit_part   : function () {
			Controller.api.bindevent();
		},
		tabledetail: {

			basic: function () {

			},


		},
		detail : function () {
			Table.api.init({});
			// 初始化表格参数配置
			//绑定事件
			$('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
				var panel = $($(this).attr("href"));
				if (panel.length > 0) {
					Controller.tabledetail[panel.attr("id")].call(this);
					$(this).on('click', function (e) {
						$($(this).attr("href")).find(".btn-refresh").trigger("click");
					});
				}
				//移除绑定的事件
				$(this).unbind('shown.bs.tab');
			});

			//必须默认触发shown.bs.tab事件
			$('ul.nav-tabs li.active a[data-toggle="tab"]').trigger("shown.bs.tab");

			//编辑
			$(document).on('click', ".btn-del", function () {//删除
				var id = $('#ids').val();
				Layer.confirm('确定删除当前产品吗？', {
					btn   : ['确定', '取消'],
					title : '提示',
				}, function (index, layero) {
					Fast.api.ajax("qingdong/product/product/del?ids=" + id, function (data, ret) {
						if (ret.code == 1) {
							Layer.close(index);
							parent.location.reload();
						}
					}, function (data, ret) {
					});

				});
			});

			Controller.api.bindevent();
		},
		api    : {
			bindevent : function () {
				Form.api.bindevent($("form[role=form]"), function(data, ret){
					//这里是表单提交处理成功后的回调函数，接收来自php的返回数据
					Fast.api.close(data);//这里是重点
				});
			}
		}
	};
	return Controller;
});