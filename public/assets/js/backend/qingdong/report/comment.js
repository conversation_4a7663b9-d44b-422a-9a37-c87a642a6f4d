define(['jquery', 'bootstrap', 'backend', 'form', 'table'], function ($, undefined, Backend, Form, Table) {

    var Controller = {
        index: function () {
// 初始化表格参数配置
            Table.api.init();

            //绑定事件
            $('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
                var panel = $($(this).attr("href"));
                if (panel.length > 0) {
                    Controller.table[panel.attr("id")].call(this);
                    $(this).on('click', function (e) {
                        $($(this).attr("href")).find(".btn-refresh").trigger("click");
                    });
                }
                //移除绑定的事件
                $(this).unbind('shown.bs.tab');
            });

            //必须默认触发shown.bs.tab事件
            $('ul.nav-tabs li.active a[data-toggle="tab"]').trigger("shown.bs.tab");

        },
        table: {
            first: function () {
                var table = $("#table");
                // 初始化表格
                table.bootstrapTable({
                    url: 'qingdong/report/comment/index?type=0',
                    toolbar: '#toolbar',
                    sortName: 'id',
                    columns: [
                        [
                            {field: 'state', checkbox: true},
                            {
                                field : 'daily.type', title : '报告类型', fixedColumns : true, formatter : function (value, row, index) {

                                    if(row.daily){
                                        return "<a href='javascript:void(0);' data-id='" + row.daily.id + "' class='show-record'>" +  row.daily.type + "</a>";
                                    }else{
                                        return '';
                                    }

                                },operate:false
                            },
                            {field: 'staff.name', title: __('员工'),operate:false},
                            {field: 'content', title: __('内容'),operate:false},
                            {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', formatter: Table.api.formatter.datetime},

                        ]
                    ],
                    //启用普通表单搜索
                    commonSearch: true,
                    searchFormVisible: false,
                });
                $(document).on('click', '.show-record', function (data) {
                    var area = [$(window).width() > 1200 ? '1200px' : '95%', $(window).height() > 800 ? '800px' : '95%'];
                    var options = {
                        shadeClose : false,
                        shade      : [0.3, '#393D49'],
                        area       : area,
                        callback   : function (value) {
                            //在回调函数里可以调用你的业务代码实现前端的各种逻辑和效果
                        }
                    };
                    Fast.api.open("qingdong/report/daily/detail?ids=" + $(this).data('id'), '报告详情', options);
                });
                // 为表格绑定事件
                Table.api.bindevent(table);
                $('.search').hide();
                $('.btn-import').hide();
            },
            second: function () {
                var table = $("#table1");
                // 初始化表格
                table.bootstrapTable({
                    url: 'qingdong/report/comment/index?type=1',
                    toolbar: '#toolbar1',
                    sortName: 'id',
                    columns: [
                        [
                            {field: 'state', checkbox: true},
                            {
                                field : 'daily.type', title : '报告类型', fixedColumns : true, formatter : function (value, row, index) {

                                    if(row.daily){
                                        return "<a href='javascript:void(0);' data-id='" + row.daily.id + "' class='show-record'>" +  row.daily.type + "</a>";
                                    }else{
                                        return '';
                                    }

                                },operate:false
                            },
                            {field: 'staff.name', title: __('员工'),operate:false},
                            {field: 'content', title: __('内容'),operate:false},
                            {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', formatter: Table.api.formatter.datetime},

                        ]
                    ],
                    //启用普通表单搜索
                    commonSearch: true,
                    searchFormVisible: false,
                });

                // 为表格绑定事件
                Table.api.bindevent(table);
                $('.search').hide();
                $('.btn-import').hide();
            },
            third: function () {
                var table = $("#table2");
                // 初始化表格
                table.bootstrapTable({
                    url: 'qingdong/report/comment/index?type=2',
                    toolbar: '#toolbar2',
                    sortName: 'id',
                    columns: [
                        [
                            {field: 'state', checkbox: true},
                            {
                                field : 'daily.type', title : '报告类型', fixedColumns : true, formatter : function (value, row, index) {

                                    if(row.daily){
                                        return "<a href='javascript:void(0);' data-id='" + row.daily.id + "' class='show-record'>" +  row.daily.type + "</a>";
                                    }else{
                                        return '';
                                    }

                                },operate:false
                            },
                            {field: 'staff.name', title: __('员工'),operate:false},
                            {field: 'content', title: __('内容'),operate:false},
                            {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', formatter: Table.api.formatter.datetime},

                        ]
                    ],
                    //启用普通表单搜索
                    commonSearch: true,
                    searchFormVisible: false,
                });
                $(document).on('click', '.show-record', function (data) {
                    var area = [$(window).width() > 1200 ? '1200px' : '95%', $(window).height() > 800 ? '800px' : '95%'];
                    var options = {
                        shadeClose : false,
                        shade      : [0.3, '#393D49'],
                        area       : area,
                        callback   : function (value) {
                            //在回调函数里可以调用你的业务代码实现前端的各种逻辑和效果
                        }
                    };
                    Fast.api.open("qingdong/report/daily/detail?ids=" + $(this).data('id'), '报告详情', options);
                });
                // 为表格绑定事件
                Table.api.bindevent(table);
                $('.search').hide();
                $('.btn-import').hide();
            },
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            },
            formatter: {

            }
        }

    };
    return Controller;
});