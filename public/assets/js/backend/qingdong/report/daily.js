define(['jquery', 'bootstrap', 'backend', 'form', 'table'], function ($, undefined, Backend, Form, Table) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init();

            //绑定事件
            $('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
                var panel = $($(this).attr("href"));
                if (panel.length > 0) {
                    Controller.table[panel.attr("id")].call(this);
                    $(this).on('click', function (e) {
                        $($(this).attr("href")).find(".btn-refresh").trigger("click");
                    });
                }
                //移除绑定的事件
                $(this).unbind('shown.bs.tab');
            });

            //必须默认触发shown.bs.tab事件
            $('ul.nav-tabs li.active a[data-toggle="tab"]').trigger("shown.bs.tab");

        },
        table: {
            first: function () {
                // 初始化表格参数配置
                Table.api.init({
                    extend: {
                        add_url: 'qingdong/report/daily/add',
                        table: 'daily'
                    }
                });

                var table = $("#table");

                // 初始化表格
                table.bootstrapTable({
                    url: 'qingdong/report/daily/index?type=0',
                    toolbar: '#toolbar',
                    sortName: 'id',
                    columns: [
                        [
                            {field: 'state', checkbox: true},
                            {field: 'type', title: __('类型'),formatter:Table.api.formatter.status,
                                searchList: {'日报':'日报', '周报':'周报','月报':'月报','季报':'季报','年报':'年报',}},
                            {field: 'staff.name', title: __('创建人'),operate:false},
                            {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', formatter: Table.api.formatter.datetime},
                            {
                                field     : 'operate',
                                title     : __('Operate'),
                                table     : table,
                                events    : Table.api.events.operate,
                                formatter : Table.api.formatter.operate,
                                buttons   : [
                                    {
                                        name: 'detail',
                                        text: __('详情'),
                                        classname: 'records btn-dialog',
                                        url: 'qingdong/report/daily/detail',
                                        extend: "data-area='[\"80%\", \"80%\"]'",
                                        callback: function (data) {
                                            Layer.alert("接收到回传数据：" + JSON.stringify(data), {title: "回传数据"});
                                        }
                                    },
                                    {
                                        name      : '评论',
                                        text      : __('评论'),
                                        classname : 'records btn-dialog',
                                        url       : 'qingdong/report/daily/comment',
                                        callback: function (data) {

                                        },

                                    }

                                ]
                            }
                        ]
                    ],
                    //启用普通表单搜索
                    commonSearch: true,
                    searchFormVisible: true,
                });
                // 为表格绑定事件
                Table.api.bindevent(table);
                $('.search').hide();
                $('.btn-import').hide();
            },
            second: function () {
                // 初始化表格参数配置
                Table.api.init({
                    extend: {
                        add_url: 'qingdong/report/daily/add',
                        table: 'daily'
                    }
                });

                var table = $("#table1");

                // 初始化表格
                table.bootstrapTable({
                    url: 'qingdong/report/daily/index?type=1',
                    toolbar: '#toolbar1',
                    sortName: 'id',
                    columns: [
                        [
                            {field: 'state', checkbox: true},
                            {field: 'type', title: __('类型'),formatter:Table.api.formatter.status,
                                searchList: {'日报':'日报', '周报':'周报','月报':'月报','季报':'季报','年报':'年报',}},
                            {field: 'staff.name', title: __('创建人'),operate:false},
                            {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', formatter: Table.api.formatter.datetime},
                            {
                                field     : 'operate',
                                title     : __('Operate'),
                                table     : table,
                                events    : Table.api.events.operate,
                                formatter : Table.api.formatter.operate,
                                buttons   : [
                                    {
                                        name: 'detail',
                                        text: __('详情'),
                                        classname: 'records btn-dialog',
                                        url: 'qingdong/report/daily/detail',
                                        extend: "data-area='[\"80%\", \"80%\"]'",
                                        callback: function (data) {
                                            Layer.alert("接收到回传数据：" + JSON.stringify(data), {title: "回传数据"});
                                        }
                                    },
                                    {
                                        name      : '评论',
                                        text      : __('评论'),
                                        classname : 'records btn-dialog',
                                        url       : 'qingdong/report/daily/comment',
                                        callback: function (data) {

                                        },

                                    }

                                ]
                            }
                        ]
                    ],
                    //启用普通表单搜索
                    commonSearch: true,
                    searchFormVisible: true,
                });
                // 为表格绑定事件
                Table.api.bindevent(table);
                $('.search').hide();
                $('.btn-import').hide();
            },
            third: function () {
                // 初始化表格参数配置
                Table.api.init({
                    extend: {
                        add_url: 'qingdong/report/daily/add',
                        table: 'daily'
                    }
                });

                var table = $("#table2");

                // 初始化表格
                table.bootstrapTable({
                    url: 'qingdong/report/daily/index?type=2',
                    toolbar: '#toolbar2',
                    sortName: 'id',
                    columns: [
                        [
                            {field: 'state', checkbox: true},
                            {field: 'type', title: __('类型'),formatter:Table.api.formatter.status,
                                searchList: {'日报':'日报', '周报':'周报','月报':'月报','季报':'季报','年报':'年报',}},
                            {field: 'staff.name', title: __('创建人'),operate:false},
                            {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', formatter: Table.api.formatter.datetime},
                            {
                                field     : 'operate',
                                title     : __('Operate'),
                                table     : table,
                                events    : Table.api.events.operate,
                                formatter : Table.api.formatter.operate,
                                buttons   : [
                                    {
                                        name: 'detail',
                                        text: __('详情'),
                                        classname: 'records btn-dialog',
                                        url: 'qingdong/report/daily/detail',
                                        extend: "data-area='[\"80%\", \"80%\"]'",
                                        callback: function (data) {
                                            Layer.alert("接收到回传数据：" + JSON.stringify(data), {title: "回传数据"});
                                        }
                                    },
                                    {
                                        name      : '评论',
                                        text      : __('评论'),
                                        classname : 'records btn-dialog',
                                        url       : 'qingdong/report/daily/comment',
                                        callback: function (data) {

                                        },

                                    }

                                ]
                            }
                        ]
                    ],
                    //启用普通表单搜索
                    commonSearch: true,
                    searchFormVisible: true,
                });
                // 为表格绑定事件
                Table.api.bindevent(table);
                $('.search').hide();
                $('.btn-import').hide();
            },
        },
        add: function () {
            Controller.api.bindevent();

        },
        comment: function () {
            Controller.api.bindevent();

        },
        edit: function () {
            Controller.api.bindevent();
        },
        detail: function () {
            Table.api.init({});
            //跟进记录
            var table6 = $("#comment");
            // 初始化表格
            table6.bootstrapTable({
                url          : 'qingdong/report/daily/comment_list?relation_id='+Config.idinfo.id,
                pk           : 'id',
                sortName     : 'id',
                operate      : false,
                commonSearch : false,
                search       : false,
                visible      : false,
                showColumns  : false,
                showExport   : false,
                showToggle   : false,
                columns      : [
                    [
                        {field: 'staff.name', title: __('评论人')},
                        {field: 'content', title: __('评论内容'),cellStyle:function (value,row,index,field) {
                                return {
                                    css: {
                                        "min-width": "150px",
                                        "white-space": "nowrap",
                                        "text-overflow": "ellipsis",
                                        "overflow": "hidden",
                                        "max-width":"300px",
                                        'cursor':"pointer"
                                    }
                                };
                            },formatter:function (value,row,index,field) {
                                var span=document.createElement('span');
                                span.setAttribute('title',value);
                                span.innerHTML = value;
                                return span.outerHTML;
                            }
                        },
                        {field: 'createtime', title: __('评论时间')},

                    ]
                ]
            });
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            },
            formatter: {

            }
        }

    };
    return Controller;
});