define(['jquery', 'bootstrap', 'backend', 'form', 'table'], function ($, undefined, Backend, Form, Table) {

    var Controller = {
        index : function () {
            // 初始化表格参数配置
            Table.api.init({
                extend : {
                    index_url : 'qingdong/general/seastype/index',
                    add_url   : 'qingdong/general/seastype/add',
                    edit_url   : 'qingdong/general/seastype/edit',
                    del_url   : 'qingdong/general/seastype/del',
                    table     : 'seastype'
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url               : $.fn.bootstrapTable.defaults.extend.index_url,
                sortName          : 'id',
                sortOrder:'asc',
                columns           : [
                    [
                        // {checkbox: true},
                        {field : 'name', title : __('公海名称'),  formatter:function (value, row, index) {
                                return value.toString().replace(/(&|&amp;)nbsp;/g, '&nbsp;');
                            }
                        },
                        {field : 'remarks', title : __('备注')},
                        {field: 'createtime', title: __('创建时间'), formatter: Table.api.formatter.datetime, operate: 'RANGE', addclass: 'datetimerange', sortable: true},
                        {
                            field     : 'operate',
                            title     : __('Operate'),
                            table     : table,
                            events    : Table.api.events.operate,
                            buttons:[
                            ], formatter: function (value, row, index) {
                                if (row.id == 1) {
                                    return '';
                                }
                                return Table.api.formatter.operate.call(this, value, row, index);
                            }
                        }
                    ]
                ],
                search:false,
                //启用普通表单搜索
                commonSearch      : false,
                searchFormVisible : false,
                onLoadSuccess:function(){
                    // 这里就是数据渲染结束后的回调函数
                    $('.btn-delone').html('删除').removeClass('btn-danger').removeClass('btn');
                    $('.btn-editone').html('编辑').removeClass('btn-success').removeClass('btn');
                }
            });

            // 为表格绑定事件
            Table.api.bindevent(table);

        },

        add : function () {

            Controller.api.bindevent();
        },
        edit : function () {
            Controller.api.bindevent();
        },
        api        : {
            bindevent : function () {
                Form.api.bindevent($("form[role=form]"));
            },
            formatter: {
                title: function (value, row, index) {
                    value = value.toString().replace(/(&|&amp;)nbsp;/g, '&nbsp;');
                    return  value;
                },

            },
        }

    };
    return Controller;
});