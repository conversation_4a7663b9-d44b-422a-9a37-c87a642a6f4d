
define(['jquery', 'bootstrap', 'backend', 'form', 'table'], function ($, undefined, Backend, Form, Table) {

	var Controller = {
		index : function () {
			// 初始化表格参数配置
			Table.api.init({
				extend : {
					index_url : 'qingdong/general/notice_template/index',
					table     : 'field'
				}
			});

			var table = $("#table");

			// 初始化表格
			table.bootstrapTable({
				url               : $.fn.bootstrapTable.defaults.extend.index_url,
				sortName          : 'id',
				pageSize:1000,
				columns           : [
					[
						{field : 'name', title : __('模块'), width : 200},
						{
							field     : '微信公众号',
							title     : "微信公众号",
							table     : table,
							events    : Table.api.events.operate,
							formatter : Table.api.formatter.operate,
							buttons: [
								{
									text      : "修改模板",
									title     : __('修改模板'),
									classname: 'records btn-dialog',
									url: 'qingdong/general/notice_template/edit?id={ids}',
									visible: function (row) {
										if(row.data && row.data.template_id){
											return true;
										}
										//返回true时按钮显示,返回false隐藏
										return false;
									}
								},
								{
									text      : "未设置",
									title     : __('未设置'),
									classname: 'records btn-dialog',
									url: 'qingdong/general/notice_template/edit?id={ids}',
									visible: function (row) {
										if(row.data && row.data.template_id){
											return false;
										}
										//返回true时按钮显示,返回false隐藏
										return true;
									}
								},
							],
						},
						{
							field     : '企业微信',
							title     : "企业微信",
							table     : table,
							events    : Table.api.events.operate,
							formatter : Table.api.formatter.operate,
							buttons: [
								{
									text      : "修改模板",
									title     : __('修改模板'),
									classname: 'records btn-dialog',
									url: 'qingdong/general/notice_template/edit_enterprise?id={ids}',
									visible: function (row) {
										if(row.enterprise_data && row.enterprise_data.first){
											return true;
										}
										//返回true时按钮显示,返回false隐藏
										return false;
									}
								},
								{
									text      : "未设置",
									title     : __('未设置'),
									classname: 'records btn-dialog',
									url: 'qingdong/general/notice_template/edit_enterprise?id={ids}',
									visible: function (row) {
										if(row.enterprise_data && row.enterprise_data.first){
											return false;
										}
										//返回true时按钮显示,返回false隐藏
										return true;
									}
								},
							],
						},
					]
				],
				pageList:['ALL'],
				//启用普通表单搜索
				commonSearch      : false,
				searchFormVisible : false,
			});
			// 为表格绑定事件
			Table.api.bindevent(table);
			$('.search').hide();
			$('.btn-import').hide();

		},
		edit : function () {
			$(document).on('change','.keyword_color',function (){
				var color = $(this).val();
				$(this).next('.input-color').css('background-color',color);
				$(this).parent().parent().find('input').css('color',color);
			}).on('click','.variable_name',function (){
				var input_1=$(this).parents('.input-group').find('input');
				console.log(input_1.val())
				input_1.val(input_1.val()+$(this).data('value'));
			});
			$('.keyword_color').trigger('change');

			Controller.api.bindevent();
		},
		edit_enterprise : function () {
			$(document).on('change','.keyword_color',function (){
				var color = $(this).val();
				$(this).next('.input-color').css('background-color',color);
				$(this).parent().parent().find('input').css('color',color);
			}).on('click','.variable_name',function (){
				var input_1=$(this).parents('.input-group').find('input');
				console.log(input_1.val())
				input_1.val(input_1.val()+$(this).data('value'));
			});
			$('.keyword_color').trigger('change');

			Controller.api.bindevent();
		},
		api  : {
			bindevent : function () {
				Form.api.bindevent($("form[role=form]"));
			},
			formatter : {
				thumb : function (value, row, index) {
					var style = row.storage == 'upyun' ? '!/fwfh/120x90' : '';
					return '<img src="' + row.image + '" data-tips-image alt="" title="封面图" style="max-height:90px;max-width:120px">';

				},

			}
		}

	};
	return Controller;
});