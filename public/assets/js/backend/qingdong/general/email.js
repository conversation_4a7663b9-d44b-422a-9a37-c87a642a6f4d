define(['jquery', 'bootstrap', 'backend', 'form', 'table'], function ($, undefined, Backend, Form, Table) {

	var Controller = {
		index : function () {
			// 初始化表格参数配置
			Table.api.init({
				extend : {
					index_url : 'qingdong/general/email/index',
					add_url : 'qingdong/general/email/add',
					edit_url : 'qingdong/general/email/edit',
					del_url : 'qingdong/general/email/del',
					table     : 'email'
				}
			});

			var table = $("#table");

			// 初始化表格
			table.bootstrapTable({
				url               : $.fn.bootstrapTable.defaults.extend.index_url,
				sortName          : 'id',
				pageSize:1000,
				columns           : [
					[
						{field : 'name', title : __('名称'), width : 200},
						{field: 'createtime', title: __('创建时间'), formatter: Table.api.formatter.datetime, operate: false},

						{field: 'updatetime', title: __('更新时间'), formatter: Table.api.formatter.datetime, operate: 'RANGE', addclass: 'datetimerange', sortable: true},
						{field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
					]
				],

				pageList:['ALL'],
				//启用普通表单搜索
				commonSearch      : false,
				searchFormVisible : false,
				onLoadSuccess:function(){
					// 这里就是数据渲染结束后的回调函数
					$('.btn-editone').html('编辑');
					$('.fa-pencil').remove();
					$('.btn-delone').html('删除');
					$('.fa-trash').remove();
					$('.btn-editone').removeClass('btn-success')
					$('.btn-editone').removeClass('btn')
					$('.btn-delone').removeClass('btn')
					$('.btn-delone').removeClass('btn-danger')
				}
			});
			// 为表格绑定事件
			Table.api.bindevent(table);
			$('.search').hide();
			$('.btn-import').hide();

		},
		batch_send_email    : function () {

			$(document).on('change','#c-template_id',function (){
				var templater_id=$(this).val();
				var url=location.pathname + location.search;
				var content=$("#c-content").val();
				if (content){
					Layer.confirm(
						__('邮件已经存在内容，确定要覆盖吗？'),
						{icon: 3, title: __('Warning'), shadeClose: true},
						function (index) {
							location=url+"&templater_id="+templater_id
							layer.close(index);
							return;
						}
					);
				}else{
					location=url+"&templater_id="+templater_id;
					return;
				}

			});
			Controller.api.bindevent();
		},
		send_email    : function () {

			$(document).on('change','#c-template_id',function (){
				var templater_id=$(this).val();
				var url=location.pathname + location.search;
				var content=$("#c-content").val();
				if (content){
					Layer.confirm(
						__('邮件已经存在内容，确定要覆盖吗？'),
						{icon: 3, title: __('Warning'), shadeClose: true},
						function (index) {
							location=url+"&templater_id="+templater_id
							layer.close(index);
							return;
						}
					);
				}else{
					location=url+"&templater_id="+templater_id;
					return;
				}

			});
			Controller.api.bindevent();
		},
		add : function () {
			$(document).on('click','.variable_name',function (){
				var c_content=$('#c-content');
				var html=c_content.val();
				c_content.val(html+$(this).data('value'));
			});
			Controller.api.bindevent();
		},
		edit : function () {

			$(document).on('click','.variable_name',function (){
				var c_content=$('#c-content');
				var html=c_content.val();
				c_content.val(html+$(this).data('value'));
			});
			Controller.api.bindevent();
		},
		api  : {
			bindevent : function () {
				Form.api.bindevent($("form[role=form]"));
			},
			formatter : {
				thumb : function (value, row, index) {
					var style = row.storage == 'upyun' ? '!/fwfh/120x90' : '';
					return '<img src="' + row.image + '" data-tips-image alt="" title="封面图" style="max-height:90px;max-width:120px">';

				},

			}
		}

	};
	return Controller;
});