define(['jquery', 'bootstrap', 'backend', 'form', 'table'], function ($, undefined, Backend, Form, Table) {

	var Controller = {
		index : function () {
			$("input:radio[name='row[auto]']").change(function (){
				var auto=$("input:radio[name='row[auto]']:checked").val();
				if(auto == 1){
					$('.auto').show();
				}else{
					$('.auto').hide();
				}

			}).trigger('change');

			$("input:checkbox[name='row[genjing]']").change(function (){
				var auto=$("input:checkbox[name='row[genjing]']:checked").val();
				if(auto == 1){
					$('.genjing').show();
				}else{
					$('.genjing').hide();
				}

			}).trigger('change');
			$("input:checkbox[name='row[chengjiao]']").change(function (){
				var auto=$("input:checkbox[name='row[chengjiao]']:checked").val();
				if(auto == 1){
					$('.chengjiao').show();
				}else{
					$('.chengjiao').hide();
				}

			}).trigger('change');
			$("input:radio[name='row[leadauto]']").change(function (){
				var auto=$("input:radio[name='row[leadauto]']:checked").val();
				if(auto == 1){
					$('.leadauto').show();
				}else{
					$('.leadauto').hide();
				}

			}).trigger('change');

			Form.api.bindevent($("form[role=form]"));
		},
		wechat : function () {

			Form.api.bindevent($("form[role=form]"));
		}

	};
	return Controller;
});