define(['jquery', 'bootstrap', 'backend', 'form', 'table'], function ($, undefined, Backend, Form, Table) {

	var Controller = {
		index : function () {
			// 初始化表格参数配置
			Table.api.init({
				extend : {
					index_url : 'qingdong/general/form/index',
					table     : 'field'
				}
			});

			var table = $("#table");

			// 初始化表格
			table.bootstrapTable({
				url               : $.fn.bootstrapTable.defaults.extend.index_url,
				sortName          : 'id',
				pageSize:1000,
				columns           : [
					[
						{field : 'name', title : __('模块'), width : 200},
						{field : 'updatetime', title : '更新时间'},
						{
							field     : 'operate',
							title     : __('Operate'),
							table     : table,
							events    : Table.api.events.operate,
							formatter : Table.api.formatter.operate,
							buttons: [
								{
									text      : "设计表单",
									title     : __('设计表单'),
									extend:'data-area=\'["100%","100%"]\'',
									classname: 'records btn-dialog',
									url: 'qingdong/general/form/edit/#/?id={ids}'
								}
							],
						}
					]
				],
				pageList:['ALL'],
				//启用普通表单搜索
				commonSearch      : false,
				searchFormVisible : false,
			});
			// 为表格绑定事件
			Table.api.bindevent(table);
			$('.search').hide();
			$('.btn-import').hide();

		},
		edit : function () {
			Controller.api.bindevent();
		},
		api  : {
			bindevent : function () {
				Form.api.bindevent($("form[role=form]"));
			},
			formatter : {
				thumb : function (value, row, index) {
					var style = row.storage == 'upyun' ? '!/fwfh/120x90' : '';
					return '<img src="' + row.image + '" data-tips-image alt="" title="封面图" style="max-height:90px;max-width:120px">';

				},

			}
		}

	};
	return Controller;
});