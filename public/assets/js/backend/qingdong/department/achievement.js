define(['jquery', 'bootstrap', 'backend', 'form', 'table'], function ($, undefined, Backend, Form, Table) {

	var Controller = {
		index : function () {
			// 初始化表格参数配置
			Table.api.init({
				extend : {
					index_url : 'qingdong/department/achievement/index',
					add_url : 'qingdong/department/achievement/add',
					table     : 'achievement'
				}
			});

			var table = $("#table");

			// 初始化表格
			table.bootstrapTable({
				url                : $.fn.bootstrapTable.defaults.extend.index_url,
				sortName           : 'id',
				fixedColumns : true,
				// fixedNumber  : 2,
				fixedRightNumber  : 1,
				columns            : [
					[
						{checkbox : true,rowspan:2},
						{field : 'staff.name', title : __('姓名'),rowspan:2},
						{field : 'status', title : __('考核目标'),rowspan:2, formatter : Table.api.formatter.status, searchList :{1: '合同金额', 2: '回款金额'}},
						{field : 'all', title : __('全年'),colspan:3},
						{field : 'one',colspan:3, title : __('第一季度')},
						{field : 'two',colspan:3, title : __('第二季度')},
						{field : 'three',colspan:3, title : __('第三季度')},
						{field : 'four',colspan:3, title : __('第四季度')},

						{field : 'january',colspan:3, title : __('一月')},
						{field : 'february',colspan:3, title : __('二月')},
						{field : 'march',colspan:3, title : __('三月')},
						{field : 'april',colspan:3, title : __('四月')},
						{field : 'may',colspan:3, title : __('五月')},
						{field : 'june',colspan:3, title : __('六月')},
						{field : 'july',colspan:3, title : __('七月')},
						{field : 'august',colspan:3, title : __('八月')},
						{field : 'september',colspan:3, title : __('九月')},
						{field : 'october',colspan:3, title : __('十月')},
						{field : 'november',colspan:3, title : __('十一月')},
						{field : 'december',colspan:3, title : __('十二月')},
						{
							field     : 'operate',
							title     : __('Operate'),
							rowspan:2,
							table     : table,
							events    : Table.api.events.operate,
							formatter : Table.api.formatter.operate,
							buttons   : [
								{
									name      : 'save',
									text      : __('修改目标'),
									classname : 'records btn-dialog',
									extend: 'data-area=["90%","90%"]',
									url       : 'qingdong/department/achievement/edit',
									callback: function (data) {
										$('.btn-success').trigger('click');
									},

								},
								{
									name      : 'delete',
									text      : __('删除'),
									classname : 'records btn-ajax',
									confirm: '确认要删除当前目标?',
									refresh:true,
									extend: 'data-area=["90%","90%"]',
									url       : 'qingdong/department/achievement/del',
									callback: function (data) {
										$('.btn-success').trigger('click');
									},

								}
							]
						}
					],
					[

						{field : 'year_money', title : __('已完成'),cellStyle:{css: {"border": "0"}}},
						{field : 'yeartarget', title : __('目标'),cellStyle:{css: {"border": "0"}}},
						{field : 'year_ratio', title : __('完成率'),cellStyle:{css: {"border": "0",'border-right':'1px solid #eae8e8','color':'red'}}},

						{field : 'one_money', title : __('已完成'),cellStyle:{css: {"border": "0"}}},
						{field : 'one', title : __('目标'),cellStyle:{css: {"border": "0"}}},
						{field : 'one_ratio', title : __('完成率'),cellStyle:{css: {"border": "0",'border-right':'1px solid #eae8e8','color':'red'}}},

						{field : 'two_money', title : __('已完成'),cellStyle:{css: {"border": "0"}}},
						{field : 'two', title : __('目标'),cellStyle:{css: {"border": "0"}}},
						{field : 'two_ratio', title : __('完成率'),cellStyle:{css: {"border": "0",'border-right':'1px solid #eae8e8','color':'red'}}},

						{field : 'three_money', title : __('已完成'),cellStyle:{css: {"border": "0"}}},
						{field : 'three', title : __('目标'),cellStyle:{css: {"border": "0"}}},
						{field : 'three_ratio', title : __('完成率'),cellStyle:{css: {"border": "0",'border-right':'1px solid #eae8e8','color':'red'}}},

						{field : 'four_money', title : __('已完成'),cellStyle:{css: {"border": "0"}}},
						{field : 'four', title : __('目标'),cellStyle:{css: {"border": "0"}}},
						{field : 'four_ratio', title : __('完成率'),cellStyle:{css: {"border": "0",'border-right':'1px solid #eae8e8','color':'red'}}},

						{field : 'january_money', title : __('已完成'),cellStyle:{css: {"border": "0"}}},
						{field : 'january', title : __('目标'),cellStyle:{css: {"border": "0"}}},
						{field : 'january_ratio', title : __('完成率'),cellStyle:{css: {"border": "0",'border-right':'1px solid #eae8e8','color':'red'}}},

						{field : 'february_money', title : __('已完成'),cellStyle:{css: {"border": "0"}}},
						{field : 'february', title : __('目标'),cellStyle:{css: {"border": "0"}}},
						{field : 'february_ratio', title : __('完成率'),cellStyle:{css: {"border": "0",'border-right':'1px solid #eae8e8','color':'red'}}},

						{field : 'march_money', title : __('已完成'),cellStyle:{css: {"border": "0"}}},
						{field : 'march', title : __('目标'),cellStyle:{css: {"border": "0"}}},
						{field : 'march_ratio', title : __('完成率'),cellStyle:{css: {"border": "0",'border-right':'1px solid #eae8e8','color':'red'}}},

						{field : 'april_money', title : __('已完成'),cellStyle:{css: {"border": "0"}}},
						{field : 'april', title : __('目标'),cellStyle:{css: {"border": "0"}}},
						{field : 'april_ratio', title : __('完成率'),cellStyle:{css: {"border": "0",'border-right':'1px solid #eae8e8','color':'red'}}},

						{field : 'may_money', title : __('已完成'),cellStyle:{css: {"border": "0"}}},
						{field : 'may', title : __('目标'),cellStyle:{css: {"border": "0"}}},
						{field : 'may_ratio', title : __('完成率'),cellStyle:{css: {"border": "0",'border-right':'1px solid #eae8e8','color':'red'}}},

						{field : 'june_money', title : __('已完成'),cellStyle:{css: {"border": "0"}}},
						{field : 'june', title : __('目标'),cellStyle:{css: {"border": "0"}}},
						{field : 'june_ratio', title : __('完成率'),cellStyle:{css: {"border": "0",'border-right':'1px solid #eae8e8','color':'red'}}},

						{field : 'july_money', title : __('已完成'),cellStyle:{css: {"border": "0"}}},
						{field : 'july', title : __('目标'),cellStyle:{css: {"border": "0"}}},
						{field : 'july_ratio', title : __('完成率'),cellStyle:{css: {"border": "0",'border-right':'1px solid #eae8e8','color':'red'}}},

						{field : 'august_money', title : __('已完成'),cellStyle:{css: {"border": "0"}}},
						{field : 'august', title : __('目标'),cellStyle:{css: {"border": "0"}}},
						{field : 'august_ratio', title : __('完成率'),cellStyle:{css: {"border": "0",'border-right':'1px solid #eae8e8','color':'red'}}},

						{field : 'september_money', title : __('已完成'),cellStyle:{css: {"border": "0"}}},
						{field : 'september', title : __('目标'),cellStyle:{css: {"border": "0"}}},
						{field : 'september_ratio', title : __('完成率'),cellStyle:{css: {"border": "0",'border-right':'1px solid #eae8e8','color':'red'}}},

						{field : 'october_money', title : __('已完成'),cellStyle:{css: {"border": "0"}}},
						{field : 'october', title : __('目标'),cellStyle:{css: {"border": "0"}}},
						{field : 'october_ratio', title : __('完成率'),cellStyle:{css: {"border": "0",'border-right':'1px solid #eae8e8','color':'red'}}},

						{field : 'november_money', title : __('已完成'),cellStyle:{css: {"border": "0"}}},
						{field : 'november', title : __('目标'),cellStyle:{css: {"border": "0"}}},
						{field : 'november_ratio', title : __('完成率'),cellStyle:{css: {"border": "0",'border-right':'1px solid #eae8e8','color':'red'}}},

						{field : 'december_money', title : __('已完成'),cellStyle:{css: {"border": "0"}}},
						{field : 'december', title : __('目标'),cellStyle:{css: {"border": "0"}}},
						{field : 'december_ratio', title : __('完成率'),cellStyle:{css: {"border": "0",'border-right':'1px solid #eae8e8','color':'red'}}},

					]
				],
				templateView       : false,
				clickToSelect      : false,
				search             : false,
				showColumns        : false,
				showToggle         : false,
				showExport         : false,
				showSearch         : false,
				commonSearch       : true,
				searchFormVisible  : true,
				searchFormTemplate : 'searchformtpl',
				pageSize           : 50
			});

			// 为表格绑定事件
			Table.api.bindevent(table);

		},
		personal : function () {
			// 初始化表格参数配置
			Table.api.init({
				extend : {
					index_url : 'qingdong/department/achievement/personal',
					table     : 'achievement'
				}
			});

			var table = $("#table");

			// 初始化表格
			table.bootstrapTable({
				url                : $.fn.bootstrapTable.defaults.extend.index_url,
				sortName           : 'id',
				fixedColumns : true,
				fixedNumber  : 2,
				fixedRightNumber  : 1,
				columns            : [
					[
						{checkbox : true},
						{field : 'year', title : __('年份')},
						{field : 'status', title : __('目标'), formatter : Table.api.formatter.status, searchList :{1: '合同金额', 2: '回款金额'}},
						{field : 'all', title : __('全年')},
						{field : 'one', title : __('第一季度')},
						{field : 'january', title : __('一月')},
						{field : 'february', title : __('二月')},
						{field : 'march', title : __('三月')},
						{field : 'two', title : __('第二季度')},
						{field : 'april', title : __('四月')},
						{field : 'may', title : __('五月')},
						{field : 'june', title : __('六月')},
						{field : 'three', title : __('第三季度')},
						{field : 'july', title : __('七月')},
						{field : 'august', title : __('八月')},
						{field : 'september', title : __('九月')},
						{field : 'four', title : __('第四季度')},
						{field : 'october', title : __('十月')},
						{field : 'november', title : __('十一月')},
						{field : 'december', title : __('十二月')},
						{
							field     : 'operate',
							title     : __('Operate'),
							table     : table,
							events    : Table.api.events.operate,
							formatter : Table.api.formatter.operate,
							buttons   : [
								{
									name      : '设置目标',
									text      : __('设置目标'),
									classname : 'btn btn-info btn-xs btn-dialog',
									url       : 'qingdong/department/achievement/edit_personal/status/{status}/year/{year}',
									callback: function (data) {
										$('.btn-success').trigger('click');
									},

								}
							]
						}
					]
				],
				templateView       : false,
				clickToSelect      : false,
				search             : false,
				showColumns        : false,
				showToggle         : false,
				showExport         : false,
				showSearch         : false,
				commonSearch       : true,
				searchFormVisible  : true,
				searchFormTemplate : 'searchformtpl',
				pageSize           : 50
			});

			// 为表格绑定事件
			Table.api.bindevent(table);

		},

		records  : function () {
			// 初始化表格参数配置
			Table.api.init({
				extend : {
					index_url : 'qingdong/department/achievement/records',
					table     : 'achievement'
				}
			});

			var table = $("#table");

			// 初始化表格
			table.bootstrapTable({
				url                : $.fn.bootstrapTable.defaults.extend.index_url,
				sortName           : 'id',
				fixedColumns : true,
				columns            : [
					[
						{checkbox : true},
						{field : 'year', title : __('年份')},
						{field : 'check_status', title : __('审核状态'), formatter : Table.api.formatter.status,
							searchList :{0: '待审核', 1: '审核中',2:'审核通过',3:'审核未通过',4:'撤销'}},
						{field : 'staff.name', title : __('提交人')},
						{field : 'check_staff', title : __('审核人')},
						{field : 'createtime', title : __('提交时间')},
						{field : 'status', title : __('目标类型'), formatter : Table.api.formatter.status, searchList :{1: '合同金额', 2: '回款金额'}},
						{field : 'yeartarget', title : __('年目标')},
						{field : 'january', title : __('一月')},
						{field : 'february', title : __('二月')},
						{field : 'march', title : __('三月')},
						{field : 'april', title : __('四月')},
						{field : 'may', title : __('五月')},
						{field : 'june', title : __('六月')},
						{field : 'july', title : __('七月')},
						{field : 'august', title : __('八月')},
						{field : 'september', title : __('九月')},
						{field : 'october', title : __('十月')},
						{field : 'november', title : __('十一月')},
						{field : 'december', title : __('十二月')},

					]
				],
				templateView       : false,
				clickToSelect      : false,
				search             : false,
				showColumns        : false,
				showToggle         : false,
				showExport         : false,
				showSearch         : false,
				commonSearch       : true,
				pageSize           : 50
			});

			// 为表格绑定事件
			Table.api.bindevent(table);
		},
		add  : function () {

			Controller.api.bindevent();
		},
		edit : function () {

			Controller.api.bindevent();
		},
		edit_personal : function () {

			$('.month').on('change',function () {
				var january = parseFloat($('#january').val()),
					february = parseFloat($('#february').val()),
					march = parseFloat($('#march').val()),
					april = parseFloat($('#april').val()),
					may = parseFloat($('#may').val()),
					june = parseFloat($('#june').val()),
					july = parseFloat($('#july').val()),
					august = parseFloat($('#august').val()),
					september = parseFloat($('#september').val()),
					october = parseFloat($('#october').val()),
					november = parseFloat($('#november').val()),
					december = parseFloat($('#december').val());
				$('#all').html((january + february + march + april + may + june + july + august + september + october + november + december).toFixed(2))
				$('#yeartarget').val((january + february + march + april + may + june + july + august + september + october + november + december).toFixed(2))
				$('#one').html((january + february + march).toFixed(2));
				$('#two').html((april + may + june).toFixed(2));
				$('#three').html((july + august + september).toFixed(2));
				$('#four').html((october + november + december).toFixed(2));
				$(this).val(parseFloat($(this).val()));
			}).trigger('change');

			Controller.api.bindevent();
		},
		api  : {
			bindevent : function () {
				$('.average_month').on('click',function (){
					var yeartarget=$('#yeartarget').val();
					var average=Math.floor(parseFloat(yeartarget/12) * 100) / 100
					$('.month').val(average).trigger('change');
				});
				$('.month').on('change',function () {
					var january = parseFloat($('#january').val()),
						february = parseFloat($('#february').val()),
						march = parseFloat($('#march').val()),
						april = parseFloat($('#april').val()),
						may = parseFloat($('#may').val()),
						june = parseFloat($('#june').val()),
						july = parseFloat($('#july').val()),
						august = parseFloat($('#august').val()),
						september = parseFloat($('#september').val()),
						october = parseFloat($('#october').val()),
						november = parseFloat($('#november').val()),
						december = parseFloat($('#december').val());
					$('#all').html((january + february + march + april + may + june + july + august + september + october + november + december).toFixed(2))
					$('#yeartarget').val((january + february + march + april + may + june + july + august + september + october + november + december).toFixed(2))
					$('#one').html((january + february + march).toFixed(2));
					$('#two').html((april + may + june).toFixed(2));
					$('#three').html((july + august + september).toFixed(2));
					$('#four').html((october + november + december).toFixed(2));
					$(this).val(parseFloat($(this).val()));
				}).trigger('change');


				Form.api.bindevent($("form[role=form]"), function(data, ret){
					//这里是表单提交处理成功后的回调函数，接收来自php的返回数据
					Fast.api.close(data);//这里是重点

				}, function(data, ret){

				});
			},
			formatter : {},
		}

	};
	return Controller;
});