define(['jquery', 'bootstrap', 'backend', 'form', 'table'], function ($, undefined, Backend, Form, Table) {

	var Controller = {
		index : function () {

			// 初始化表格参数配置
			Table.api.init({
				extend : {
					index_url : 'qingdong/department/staff/index',
					add_url   : 'qingdong/department/staff/add',
					edit_url  : 'qingdong/department/staff/edit',
					del_url   : 'qingdong/department/staff/del',
					table     : 'staff'
				}
			});

			var table = $("#table");

			// 初始化表格
			table.bootstrapTable({
				url               : $.fn.bootstrapTable.defaults.extend.index_url,
				sortName          : 'id',
				fixedColumns : true,
				fixedRightNumber:1,
				columns           : [
					[
						{checkbox : true},
						{field : 'name', title : __('姓名')},
						{field : 'img', title : __('头像'), events : Table.api.events.image, formatter : Table.api.formatter.image, operate : false},
						{field : 'num', title : __('员工编号')},
						{field : 'email', title : __('邮箱')},
						{field : 'mobile', title : __('手机号码')},
						{field : 'sex', title : __('性别'),operate:false, formatter : Table.api.formatter.status, searchList : {0 : __('未知'),1 : __('男'), 2 : __('女')}},
						{field : 'post', title : __('岗位'),operate:false},
						{field : 'staffrole.name', title : __('角色'), operate : false},
						{field : 'role', title : __('角色'), visible: false, searchList : $.getJSON("qingdong/department/staff/getstaffrole")},
						{field : 'parent_name', title : __('直属上级'),operate:false},
						{field : 'admin.username', title : __('绑定后台账号'),operate:false},
						{field : 'status', title : __('状态'), formatter : Table.api.formatter.status, searchList : {0 : __('审核中'), 1 : __('正常'),2:'禁用'}},
						{field : 'createtime', title : __('创建时间'), formatter : Table.api.formatter.datetime, operate : 'RANGE', addclass : 'datetimerange', sortable : true},
						{
							field     : 'operate',
							title     : __('Operate'),
							table     : table,
							events    : Table.api.events.operate,
							formatter : Table.api.formatter.operate,
							buttons   : [
								{
								    name: '禁用',
								    text: __('禁用'),
								    classname: 'btn-xs  btn-ajax',
								    url: 'qingdong/department/staff/update_status/status/2',
								    refresh: true,
									confirm: '确认要禁用当前员工吗',
									hidden:function (row) {
										if(row.status == 1){
											return false;
										}
										return true;
									}
								},
								{
									name: '启用',
									text: __('启用'),
									classname: 'btn-xs btn-ajax',
									url: 'qingdong/department/staff/update_status/status/1',
									refresh: true,
									confirm: '确认要启用当前员工吗',
									hidden:function (row) {
										if(row.status == 1){
											return true;
										}
										return false;
									}
								},

							]
						}
					]
				],
				search            : false,
				//启用普通表单搜索
				commonSearch      : true,
				searchFormVisible : false,
				onLoadSuccess:function(){
					// 这里就是数据渲染结束后的回调函数
					$('.btn-editone').html('编辑');
					$('.fa-pencil').remove();
					$('.btn-delone').html('删除');
					$('.fa-trash').remove();
					$('.btn-editone').removeClass('btn-success')
					$('.btn-editone').removeClass('btn')
					$('.btn-delone').removeClass('btn')
					$('.btn-delone').removeClass('btn-danger')
				}
			});

			// 为表格绑定事件
			Table.api.bindevent(table);

		},

		add           : function () {

			Controller.api.bindevent();
		},
		edit          : function () {
			Controller.api.bindevent();
		},
		staff_examine : function () {
			// 初始化表格参数配置
			Table.api.init({
				extend : {
					index_url : 'qingdong/department/staff/staff_examine',
					table     : 'staff_examine'
				}
			});

			var table = $("#table");

			// 初始化表格
			table.bootstrapTable({
				url               : $.fn.bootstrapTable.defaults.extend.index_url,
				sortName          : 'id',
				columns           : [
					[
						{checkbox : true},
						{field : 'nickname', title : __('昵称'), operate : false},
						{field : 'name', title : __('姓名')},
						{field : 'img', title : __('头像'), events : Table.api.events.image, formatter : Table.api.formatter.image, operate : false},
						{field : 'num', title : __('员工编号'), operate : false},
						{field : 'email', title : __('邮箱'), operate : false},
						{field : 'mobile', title : __('手机号码'), operate : false},
						{field : 'sex', title : __('性别'), formatter : Table.api.formatter.status, searchList : {0 : __('未知'),1 : __('男'), 2 : __('女')}},
						{field : 'post', title : __('岗位'), operate : false},
						{field : 'createtime', title : __('创建时间'), formatter : Table.api.formatter.datetime, operate : 'RANGE', addclass : 'datetimerange', sortable : true},
						{
							field     : 'operate',
							title     : __('Operate'),
							table     : table,
							events    : Table.api.events.operate,
							formatter : Table.api.formatter.operate,
							buttons   : [
								{
									name      : '审核通过',
									text      : __('审核通过'),
									classname : 'btn btn-info btn-xs btn-detail btn-dialog',
									url       : 'qingdong/department/staff/examine',
									refresh   : true
								},
								{
									name      : '审核拒绝',
									text      : __('审核拒绝'),
									classname : 'btn btn-xs btn-danger btn-ajax',
									confirm: '确认审核拒绝',
									url       : 'qingdong/department/staff/del',
									refresh   : true
								}
							]
						}
					]
				],
				search            : false,
				//启用普通表单搜索
				commonSearch      : false,
				searchFormVisible : false,
			});

			// 为表格绑定事件
			Table.api.bindevent(table);

		},
		examine          : function () {
			Controller.api.bindevent();
		},
		api           : {
			bindevent : function () {
				$(document).on('change','[name="row[role]"]',function (data) {
					if($('[name="row[role]"]').val() == 2){
						$('#rules').show();
					}else{
						$('#rules').hide();
					}
				});
				$('[name="row[role]"]').trigger('change');
				Form.api.bindevent($("form[role=form]"));
			},

			formatter : {},
		}

	};
	return Controller;
});