define(['jquery', 'bootstrap', 'backend', 'form', 'table'], function ($, undefined, Backend, Form, Table) {

    var Controller = {
        index : function () {
            // 初始化表格参数配置
            Table.api.init({
                extend : {
                    index_url : 'qingdong/department/department/index',
                    add_url   : 'qingdong/department/department/add',
                    edit_url   : 'qingdong/department/department/edit',
                    del_url   : 'qingdong/department/department/del',
                    table     : 'department'
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url               : $.fn.bootstrapTable.defaults.extend.index_url,
                sortName          : 'id',
                columns           : [
                    [
                        {checkbox: true},
                        {field : 'name', title : __('部门名称'), align: 'left', formatter:function (value, row, index) {
                                return value.toString().replace(/(&|&amp;)nbsp;/g, '&nbsp;');
                            }
                        },
                        {field: 'createtime', title: __('创建时间'), formatter: Table.api.formatter.datetime, operate: 'RANGE', addclass: 'datetimerange', sortable: true},
                        {
                            field     : 'operate',
                            title     : __('Operate'),
                            table     : table,
                            events    : Table.api.events.operate,
                            formatter : Table.api.formatter.operate,
                            buttons:[
                            ]
                        }
                    ]
                ],
                search:false,
                //启用普通表单搜索
                commonSearch      : false,
                searchFormVisible : false,
            });

            // 为表格绑定事件
            Table.api.bindevent(table);

        },

        add : function () {

            Controller.api.bindevent();
        },
        edit : function () {
            Controller.api.bindevent();
        },
        api        : {
            bindevent : function () {
                Form.api.bindevent($("form[role=form]"));
            },
            formatter: {
                title: function (value, row, index) {
                    value = value.toString().replace(/(&|&amp;)nbsp;/g, '&nbsp;');
                    return  value;
                },

            },
        }

    };
    return Controller;
});