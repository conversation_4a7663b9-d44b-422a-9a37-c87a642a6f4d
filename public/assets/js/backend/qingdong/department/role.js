define(['jquery', 'bootstrap', 'backend', 'form', 'table', 'jstree'], function ($, undefined, Backend, Form, Table) {
    //读取选中的条目
    $.jstree.core.prototype.get_all_checked = function (full) {
        var obj = this.get_selected(), i, j;
        for (i = 0, j = obj.length; i < j; i++) {
            obj = obj.concat(this.get_node(obj[i]).parents);
        }
        obj = $.grep(obj, function (v, i, a) {
            return v != '#';
        });
        obj = obj.filter(function (itm, i, a) {
            return i == a.indexOf(itm);
        });
        return full ? $.map(obj, $.proxy(function (i) {
            return this.get_node(i);
        }, this)) : obj;
    };
    var Controller = {
        index : function () {
            // 初始化表格参数配置
            Table.api.init({
                extend : {
                    index_url : 'qingdong/department/role/index',
                    add_url   : 'qingdong/department/role/add',
                    edit_url   : 'qingdong/department/role/edit',
                    del_url   : 'qingdong/department/role/del',
                    table     : 'role'
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url               : $.fn.bootstrapTable.defaults.extend.index_url,
                sortName          : 'id',
                columns           : [
                    [
                        {checkbox: true},
                        {field : 'name', title : __('角色名称'),formatter:function (value, row, index) {
                                return value.toString().replace(/(&|&amp;)nbsp;/g, '&nbsp;');
                            }
                        },
                        {field: 'createtime', title: __('创建时间'), formatter: Table.api.formatter.datetime, operate: 'RANGE', addclass: 'datetimerange', sortable: true},
                        {
                            field     : 'operate',
                            title     : __('Operate'),
                            table     : table,
                            events    : Table.api.events.operate,
                            formatter : Table.api.formatter.operate,
                            buttons:[
                                {
                                    name: '权限配置',
                                    text: __('权限配置'),
                                    classname: 'btn-xs btn-dialog',
                                    url: 'qingdong/department/role/rule',

                                },
                            ]
                        }
                    ]
                ],
                search:false,
                //启用普通表单搜索
                commonSearch      : false,
                searchFormVisible : false,
                onLoadSuccess:function(){
                    // 这里就是数据渲染结束后的回调函数
                    $('.btn-delone').html('删除').removeClass('btn-danger').removeClass('btn');
                    $('.btn-editone').html('编辑').removeClass('btn-success')
                        .removeClass('btn').data("area",["300px","200px"]);
                }
            });

            // 为表格绑定事件
            Table.api.bindevent(table);

        },

        add : function () {

            Controller.api.bindevent();
        },
        edit : function () {
            Controller.api.bindevent();
        },
        rule:function(){
            //渲染权限节点树
            var ids=$('#ids').val()
            console.log(ids)
            Controller.api.set_roletree(ids);
        },
        api        : {
            bindevent : function () {
                Form.api.bindevent($("form[role=form]"));
            },
            formatter: {
                title: function (value, row, index) {
                    value = value.toString().replace(/(&|&amp;)nbsp;/g, '&nbsp;');
                    return  value;
                },

            },
            rendertree: function (content) {
                $("#treeview")
                    .on('redraw.jstree', function (e) {
                        $(".layer-footer").attr("domrefresh", Math.random());
                    })
                    .jstree({
                        "themes": {"stripes": true},
                        "checkbox": {
                            "keep_selected_style": false,
                        },
                        "types": {
                            "root": {
                                "icon": false,
                            },
                            "menu": {
                                "icon": false,
                            },
                            "file": {
                                "icon": false,
                            }
                        },
                        "plugins": ["checkbox", "types"],
                        "core": {
                            'check_callback': true,
                            'dblclick_toggle': false,
                            "data": content
                        }
                    });
            },
            set_roletree:function(ids){
                $.post("qingdong/department/role/roletree", {ids:ids},function (ret){
                    if (ret.hasOwnProperty("code")) {
                        var data = ret.hasOwnProperty("data") && ret.data != "" ? ret.data : "";
                        if (ret.code === 1) {
                            var roletree=data;
                            //销毁已有的节点树
                            $("#treeview").jstree("destroy");
                            Controller.api.rendertree(roletree);

                        } else {
                            Backend.api.toastr.error(ret.msg);
                        }
                    }
                },'json');

                Form.api.bindevent($("form[role=form]"), null, null, function () {
                    var array=new Array();
                    if ($("#treeview").length > 0) {
                        var r = $("#treeview").jstree("get_selected");
                        if(r.length != 0){
                            array.push(r.join(','));
                        }
                    }
                    $("input[name='row[rules]']").val(array.join(','));
                    return true;
                });
            },

        }

    };
    return Controller;
});