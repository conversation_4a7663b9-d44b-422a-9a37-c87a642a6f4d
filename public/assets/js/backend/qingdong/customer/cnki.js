define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index  : function () {
            // 初始化表格参数配置
            Table.api.init({
                extend : {
                    index_url  : 'qingdong/customer/cnki/index',
                    detail_url : 'qingdong/customer/customer/detail',
                    table      : 'qingdong_customer',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url          : $.fn.bootstrapTable.defaults.extend.index_url,
                pk           : 'id',
                sortName     : 'id',
                fixedColumns : true,
                fixedNumber  : 2,
                fixedRightNumber  : 1,
                search:false,
                searchFormVisible:true,
                columns      : [
                    [
                        {checkbox : true},
                        {
                            field : 'name', title : '客户名称', fixedColumns : true, formatter : function (value, row, index) {

                                return "<a href='javascript:void(0);' data-id='" + row.id + "' class='show-detail'>" + value + "</a>";
                            },operate:'like'
                        },
                        {field : 'subname', title : '助记名称',operate:'like'},
                        {field : 'contract_status', title : '成交状态',operate:'=',searchList:{0:'未成交',1:'已成交'},formatter:Table.api.formatter.status},
                        {field : 'industry', title : '客户所属',operate:'like'},
                        {field : 'follow', title : '客户状态',operate:'like'},
                        {field : 'source', title : '客户来源',operate:'like'},
                        {field : 'level', title : '客户星级',operate:'=',searchList:{0:'无',1:'1星',2:'2星',3:'3星',4:'4星',5:'5星'},formatter:Table.api.formatter.status},
                        {field : 'next_time', title : '下次联系时间',operate:false},
                        {field : 'last_time', title : '最后跟进时间',operate:false},
                        {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', formatter: Table.api.formatter.datetime},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.buttons,
                            buttons: [
                                {
                                    name: 'detail',
                                    text: __('合并客户'),
                                    title: __('合并客户'),
                                    classname: 'records btn-dialog',
                                    extend:'data-area=["90%","90%"]',
                                    url: 'qingdong/customer/cnki/edit',
                                    visible: function (row) {
                                        //返回true时按钮显示,返回false隐藏
                                        return true;
                                    }
                                },

                            ]
                        }
                    ]
                ]
            });
            $(document).on('click', '.show-detail', function (data) {
                var area = [$(window).width() > 1200 ? '1200px' : '95%', $(window).height() > 800 ? '800px' : '95%'];
                var options = {
                    shadeClose : false,
                    shade      : [0.3, '#393D49'],
                    area       : area,
                    callback   : function (value) {
                        //在回调函数里可以调用你的业务代码实现前端的各种逻辑和效果
                        console.log(value);
                    }
                };
                Fast.api.open($.fn.bootstrapTable.defaults.extend.detail_url + "?ids=" + $(this).data('id'), '客户详情', options);
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add    : function () {
            Controller.api.bindevent();
        },
        edit    : function () {
           $('.tongbu').change(function(){
               var number =  $(this).val();
               var name = $(this).data('name');
               var zucontent = $('.zu_'+name).html();
               var fucontent = $('.fu_'+name).html();
               var content = zucontent+fucontent;
               if(number == 1){
                   $('#c-'+name).val($.trim(zucontent));
                   $('#c-'+name).selectpicker('val', zucontent.replace(/\s+/g,''));
               }else if(number == 2){
                   $('#c-'+name).val(fucontent.replace(/\s+/g,''));
                   $('#c-'+name).selectpicker('val', fucontent.replace(/\s+/g,''));
               }
               else{
                   $('#c-'+name).val(content.replace(/\s+/g,''));
               }

           });
            Controller.api.bindevent();
        },

        api    : {
            bindevent : function () {
                Form.api.bindevent($("form[role=form]"), function(data, ret){
                    //这里是表单提交处理成功后的回调函数，接收来自php的返回数据
                    Fast.api.close(data);//这里是重点
                });
            }
        }
    };
    return Controller;
});