define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index  : function () {
            // 初始化表格参数配置
            Table.api.init();

            //绑定事件
            $('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
                var panel = $($(this).attr("href"));
                if (panel.length > 0) {
                    Controller.table[panel.attr("id")].call(this);
                    $(this).on('click', function (e) {
                        $($(this).attr("href")).find(".btn-refresh").trigger("click");
                    });
                }
                //移除绑定的事件
                $(this).unbind('shown.bs.tab');
            });

            //必须默认触发shown.bs.tab事件
            $('ul.nav-tabs li.active a[data-toggle="tab"]').trigger("shown.bs.tab");


            $(document).on('click', '.show-detail', function (data) {
                var area = [$(window).width() > 1200 ? '1200px' : '95%', $(window).height() > 800 ? '800px' : '95%'];
                var options = {
                    shadeClose : false,
                    shade      : [0.3, '#393D49'],
                    area       : area,
                    callback   : function (value) {
                        //在回调函数里可以调用你的业务代码实现前端的各种逻辑和效果
                        console.log(value);
                    }
                };
                Fast.api.open($.fn.bootstrapTable.defaults.extend.detail_url + "?ids=" + $(this).data('id'), '商机', options);
            });
            $(document).on('click', '.show-customer', function (data) {
                var area = [$(window).width() > 1200 ? '1200px' : '95%', $(window).height() > 800 ? '800px' : '95%'];
                var options = {
                    shadeClose : false,
                    shade      : [0.3, '#393D49'],
                    area       : area,
                    callback   : function (value) {
                        //在回调函数里可以调用你的业务代码实现前端的各种逻辑和效果
                        console.log(value);
                    }
                };
                Fast.api.open("qingdong/customer/customer/detail?ids=" + $(this).data('id'), '客户详情', options);
            });

            //商机转移
            $(document).on('click','.btn-transfer',function () {
                var tab_id=$('.active.in').attr('id');
                var table;
                switch (tab_id){
                    case 'first'://全部
                        table=$('#table');
                        break;
                    case 'second'://我负责的
                        table=$('#table1');
                        break;
                    case 'third'://下属负责的
                        table=$('#table2');
                        break;
                    case 'four'://今日待跟进
                        table=$('#table3');
                        break;
                    case 'five'://今日已跟进
                        table=$('#table4');
                        break;
                    case 'six'://从未跟进的
                        table=$('#table5');
                        break;
                }
                //在templateView的模式下不能调用table.bootstrapTable('getSelections')来获取选中的ID,只能通过下面的Table.api.selectedids来获取
                if(Table.api.selectedids(table).length == 0){
                    layer.alert('请选择要筛选的商机！');
                    return false;
                }
                var ids=JSON.stringify(Table.api.selectedids(table));
                Fast.api.open("qingdong/customer/business/batch_change?ids="+ids, "批量转移商机",{
                    shadeClose: false,
                    shade: false,
                    maxmin: false,
                    moveOut: false,
                    scrollbars:false,
                    'area':[
                        $(window).width() > 800 ? '600px' : '400px',
                        $(window).height() > 600 ? '500px' : '400px'
                    ],
                    callback:function(value){
                        Form.events.plupload("#plupload-local");
                        //   在这里可以接收弹出层中使用`Fast.api.close(data)`进行回传数据
                    },
                });
            }).on("click", ".btn-export", function () {
                var tab_id=$('.active.in').attr('id');
                var table,type;
                switch (tab_id){
                    case 'first'://全部
                        table=$('#table');
                        type=0;
                        break;
                    case 'second'://我负责的
                        table=$('#table1');
                        type=1;
                        break;
                    case 'third'://下属负责的
                        table=$('#table2');
                        type=2;
                        break;
                    case 'four'://今日待跟进
                        table=$('#table3');
                        type=3;
                        break;
                    case 'five'://今日已跟进
                        table=$('#table4');
                        type=4;
                        break;
                    case 'six'://从未跟进的
                        table=$('#table5');
                        type=5;
                        break;
                }

                var ids = Table.api.selectedids(table);
                var page = table.bootstrapTable('getData');
                var all = table.bootstrapTable('getOptions').totalRows;
                var search = $("form.form-commonsearch").serialize();
                var sort = table.bootstrapTable('getOptions').sortName+' '+table.bootstrapTable('getOptions').sortOrder;
                var filter = search.filter;
                var op = search.op;
                Layer.confirm("请选择导出的选项", {
                    title   : '导出数据',
                    btn     : ["选中项(" + ids.length + "条)", "本页(" + page.length + "条)", "全部(" + all + "条)"],
                    success : function (layero, index) {
                        $(".layui-layer-btn a", layero).addClass("layui-layer-btn0");
                    }, yes   : function (index, layero) {
                        if (ids.length == 0) {
                            Layer.alert('数据为空');
                            return true;
                        }
                        $.post('qingdong/customer/business/export', {sort:sort,ids : ids.join(","), filter:filter,op:op}, function (res) {
                            Layer.alert(res.msg);
                            if(res.code ==1){
                                window.location.href = res.data.filePath;
                                Layer.close(index);
                                table.bootstrapTable('refresh');
                            }
                        }, 'json');
                        return false;
                    },
                    btn2    : function (index, layero) {
                        if (page.length == 0) {
                            Layer.alert('数据为空');
                            return true;
                        }
                        var ids = [];
                        $.each(page, function (i, j) {
                            ids.push(j.id);
                        });
                        $.post('qingdong/customer/business/export', {sort:sort,ids : ids.join(","), filter:filter,op:op}, function (res) {
                            Layer.alert(res.msg);
                            if(res.code ==1){
                                window.location.href = res.data.filePath;
                                Layer.close(index);
                                table.bootstrapTable('refresh');
                            }
                        }, 'json');
                        return false;
                    },
                    btn3    : function (index, layero) {
                        if (page.length == 0) {
                            Layer.alert('数据为空');
                            return true;
                        }
                        $.post('qingdong/customer/business/export', {sort:sort,ids : ids.join(","), type : type, filter:filter,op:op}, function (res) {
                            Layer.alert(res.msg);
                            if(res.code ==1){
                                window.location.href = res.data.filePath;
                                Layer.close(index);
                                table.bootstrapTable('refresh');
                            }
                        }, 'json');
                        return false;
                    }
                })
            }).on('click','.btn-imports',function () {
                Fast.api.open("qingdong/customer/business/import", "商机导入",{
                    shadeClose: false,
                    shade: false,
                    maxmin: false,
                    moveOut: false,
                    scrollbars:false,
                    'area':[
                        $(window).width() > 800 ? '600px' : '400px',
                        $(window).height() > 600 ? '500px' : '400px'
                    ],
                    callback:function(value){
                        Form.events.plupload("#plupload-local");

                        //   在这里可以接收弹出层中使用`Fast.api.close(data)`进行回传数据
                    },
                });

            }).on("click", ".btn-export-product", function () {
                var tab_id=$('.active.in').attr('id');
                var table,type;
                switch (tab_id){
                    case 'first'://全部
                        table=$('#table');
                        type=0;
                        break;
                    case 'second'://我负责的
                        table=$('#table1');
                        type=1;
                        break;
                    case 'third'://下属负责的
                        table=$('#table2');
                        type=2;
                        break;
                    case 'four'://今日待跟进
                        table=$('#table3');
                        type=3;
                        break;
                    case 'five'://今日已跟进
                        table=$('#table4');
                        type=4;
                        break;
                    case 'six'://从未跟进的
                        table=$('#table5');
                        type=5;
                        break;
                }
                var ids = Table.api.selectedids(table);
                var page = table.bootstrapTable('getData');
                var options = table.bootstrapTable('getOptions');
                var params=options.queryParams({});
                var all = options.totalRows;
                var sort = options.sortName+' '+options.sortOrder;
                var filter = params.filter;
                var op = params.op;
                Layer.confirm("请选择导出的选项", {
                    title   : '导出产品明细数据',
                    btn     : ["选中项(" + ids.length + "条)", "本页(" + page.length + "条)", "全部(" + all + "条)"],
                    success : function (layero, index) {
                        $(".layui-layer-btn a", layero).addClass("layui-layer-btn0");
                    }, yes   : function (index, layero) {
                        if (ids.length == 0) {
                            Layer.alert('数据为空');
                            return true;
                        }
                        $.post('qingdong/customer/business/export_product', {sort:sort,ids : ids.join(","), isall : 1}, function (res) {
                            Layer.alert(res.msg);
                            if(res.code == 1){
                                window.location.href = res.data.filePath;
                                Layer.close(index);
                            }
                            table.bootstrapTable('refresh');
                        }, 'json');
                        return false;
                    },
                    btn2    : function (index, layero) {
                        if (page.length == 0) {
                            Layer.alert('数据为空');
                            return true;
                        }
                        var ids = [];
                        $.each(page, function (i, j) {
                            ids.push(j.id);
                        });
                        $.post('qingdong/customer/business/export_product', {sort:sort,ids : ids.join(","), isall : 2}, function (res) {
                            Layer.alert(res.msg);
                            if(res.code == 1){
                                window.location.href = res.data.filePath;
                                Layer.close(index);
                            }
                            table.bootstrapTable('refresh');
                        }, 'json');
                        return false;
                    },
                    btn3    : function (index, layero) {
                        if (page.length == 0) {
                            Layer.alert('数据为空');
                            return true;
                        }
                        $.post('qingdong/customer/business/export_product?filter='+filter+'&op='+op, {sort:sort,ids : ids.join(","), isall : 3,type:type}, function (res) {
                            Layer.alert(res.msg);
                            if(res.code == 1){
                                window.location.href = res.data.filePath;
                                Layer.close(index);
                            }
                            table.bootstrapTable('refresh');
                        }, 'json');
                        return false;
                    }
                })
            }).on('click','.btn-imports-product',function () {
                Fast.api.open("qingdong/customer/business/import_product", "商机导入产品明细",{
                    shadeClose: false,
                    shade: false,
                    maxmin: false,
                    moveOut: false,
                    scrollbars:false,
                    'area':[
                        $(window).width() > 800 ? '600px' : '400px',
                        $(window).height() > 600 ? '500px' : '400px'
                    ],
                    callback:function(value){
                        Form.events.plupload("#plupload-local");

                        //   在这里可以接收弹出层中使用`Fast.api.close(data)`进行回传数据
                    },
                });

            });
        },
        tableinfo:{
            url          : '',
            toolbar: '#toolbar',
            pk           : 'id',
            sortName     : 'id',
            fixedColumns : true,
            fixedNumber  : 2,
            fixedRightNumber  : 1,
            columns      : [
                [
                    {checkbox : true},
                    {field : 'customer', title : '客户名称', formatter : function (value, row, index) {
                            if(row.customer){
                                return "<a href='javascript:void(0);' data-id='" + row.customer_id + "' class='show-customer'>" + row.customer.name + "</a>";
                            }
                            return '';
                        },operate:false},
                    {
                        field : 'name', title : '项目名称', fixedColumns : true, formatter : function (value, row, index) {
                            if(value ==''){
                                value = '无';
                            }
                            return "<a href='javascript:void(0);' data-id='" + row.id + "' class='show-detail'>" + value + "</a>";
                        }
                    },
                    {field : 'money', title : '项目类型'},
                    {field : 'money', title : '销售负责人'},
                    {field : 'money', title : '预计合同金额(万元)'},
                    {field : 'customer_id', title : '客户名称', visible: false, addClass: "selectpage", extend: "data-source='qingdong/customer/customer/index' data-field='name'"},
                    {field : 'money', title : '商机金额'},
                    {field : 'expect_time', title : '预计签订时间',operate: 'RANGE',autocomplete:false, addclass: 'datetimerange'},
                    {field : 'next_time', title : '下次联系时间',operate: 'RANGE',autocomplete:false, addclass: 'datetimerange'},
                    {field : 'owner_staff.name', title : '负责人',operate:false},
                    {field : 'owner_staff_id', title : '负责人', visible: false, addClass: "selectpage", extend: "data-source='qingdong/department/staff/index' data-field='name'"},
                    {field : 'type', title : '商机阶段', formatter : Table.api.formatter.status,operate:false, visible: false,
                    searchList : {0 : '初期沟通', 1 : '立项跟踪', 2 : '方案/报价', 3 : '谈判审核', 4 : '赢单', 5 : '输单', 6 : '无效'}},
                    {field : 'updatetime', title : '最后跟进时间',operate:false},
                    {
                        field: 'operate2', title: __('Operate'), events: {
                            'click .btn-chooseone': function (e, value, row, index) {
                                Fast.api.close({id: row.id, name: row.name});
                            },
                        }, formatter: function () {
                            return '<a href="javascript:;" class="btn btn-danger btn-chooseone btn-xs"><i class="fa fa-check"></i> ' + __('Choose') + '</a>';
                        }
                    },
                    {field: 'createtime', title: "创建时间", operate:'RANGE',autocomplete:false, addclass:'datetimerange', formatter: Table.api.formatter.datetime},
                    {field: 'operate', title: __('Operate'),  events: Table.api.events.operate, formatter: Table.api.formatter.buttons,
                        buttons: [
                            {
                                name: 'detail',
                                text: __('推进商机'),
                                title: __('推进商机'),
                                extend:'data-area=["80%","80%"]',
                                classname: 'records btn-dialog',
                                url: 'qingdong/customer/business/batch_status',
                                visible: function (row) {
                                    //返回true时按钮显示,返回false隐藏
                                    return true;
                                }
                            },
                            {
                                name: 'detail',
                                text: __('跟进'),
                                title: __('跟进'),
                                extend:'data-area=["80%","80%"]',
                                classname: 'records btn-dialog',
                                url: 'qingdong/customer/business/record',
                                visible: function (row) {
                                    //返回true时按钮显示,返回false隐藏
                                    return true;
                                }
                            },
                            {
                                name: 'detail',
                                text: __('编辑'),
                                title: __('编辑'),
                                extend:'data-area=["80%","80%"]',
                                classname: 'records btn-dialog',
                                url: 'qingdong/customer/business/edit',
                                visible: function (row) {
                                    //返回true时按钮显示,返回false隐藏
                                    return true;
                                }
                            }
                        ]
                    }

                ]
            ],
            pagination        : true,
            search            : false,
            commonSearch      : true,
            searchFormVisible : true,
            //显示导出按钮
            showExport: false,
            onLoadSuccess:function(){
                // 这里就是数据渲染结束后的回调函数
                $(".btn-add").data("area",["80%","80%"]);
                $(".btn-edit").data("area",["80%","80%"]);
            }
        },
        table: {
            first: function () {
                // 初始化表格参数配置
                Table.api.init({
                    extend : {
                        add_url    : 'qingdong/customer/business/add',
                        detail_url : 'qingdong/customer/business/detail',
                        table      : 'business',
                    }
                });
                if(Config.fields !='[]'){
                    if (Config.fields && Object.keys(Config.fields).length > 0) {
                        var fields = JSON.parse(Config.fields);
                        var start = Controller.tableinfo.columns[0].length-2;
                        for (var i = 0; i < fields.length; i++) {
                            if (fields[i].hasOwnProperty('formatter'))
                                fields[i].formatter = eval(fields[i].formatter);
                            if (fields[i].hasOwnProperty('events'))
                                fields[i].events = eval(fields[i].events);
                            if (fields[i].hasOwnProperty('searchList'))
                                fields[i].searchList = JSON.parse(fields[i].searchList);
                            Controller.tableinfo.columns[0].splice(start + i, 0, fields[i]);
                        }
                    }

                }
                const q = {};
                location.search.replace(/([^?&=]+)=([^&]+)/g,function(_,k,v){q[k]=v});
                var table = $("#table");
                Controller.tableinfo.url=location.href+'&type=0';
                Controller.tableinfo.toolbar='#toolbar';
                Controller.tableinfo.columns[0][Controller.tableinfo.columns[0].length-1].table=table;
                if (q.isselect == 1) {
                    Controller.tableinfo.columns[0][Controller.tableinfo.columns[0].length - 1].visible = false;
                } else {
                    Controller.tableinfo.columns[0][Controller.tableinfo.columns[0].length - 2].visible = false;
                }
                // 初始化表格
                table.bootstrapTable(Controller.tableinfo);

                // 为表格绑定事件
                Table.api.bindevent(table);
            },
            second: function () {
                // 初始化表格参数配置
                Table.api.init({
                    extend : {
                        add_url    : 'qingdong/customer/business/add',
                        detail_url : 'qingdong/customer/business/detail',
                        table      : 'business',
                    }
                });

                var table = $("#table1");
                Controller.tableinfo.url=location.href+'&type=1';
                Controller.tableinfo.toolbar='#toolbar1';
                Controller.tableinfo.columns[0][Controller.tableinfo.columns[0].length-1].table=table;
                // 初始化表格
                table.bootstrapTable(Controller.tableinfo);

                // 为表格绑定事件
                Table.api.bindevent(table);
            },
            third: function () {
                // 初始化表格参数配置
                Table.api.init({
                    extend : {
                        add_url    : 'qingdong/customer/business/add',
                        detail_url : 'qingdong/customer/business/detail',
                        table      : 'business',
                    }
                });


                var table = $("#table2");

                Controller.tableinfo.url=location.href+'&type=2';
                Controller.tableinfo.toolbar='#toolbar2';
                Controller.tableinfo.columns[0][Controller.tableinfo.columns[0].length-1].table=table;
                table.bootstrapTable(Controller.tableinfo);

                // 为表格绑定事件
                Table.api.bindevent(table);
            },
            four: function () {
                // 初始化表格参数配置
                Table.api.init({
                    extend : {
                        add_url    : 'qingdong/customer/business/add',
                        detail_url : 'qingdong/customer/business/detail',
                        table      : 'business',
                    }
                });
                var table = $("#table3");

                Controller.tableinfo.url=location.href+'&type=3';
                Controller.tableinfo.toolbar='#toolbar3';
                Controller.tableinfo.columns[0][Controller.tableinfo.columns[0].length-1].table=table;

                table.bootstrapTable(Controller.tableinfo);

                // 为表格绑定事件
                Table.api.bindevent(table);
            },
            five: function () {
                // 初始化表格参数配置
                Table.api.init({
                    extend : {
                        add_url    : 'qingdong/customer/business/add',
                        detail_url : 'qingdong/customer/business/detail',
                        table      : 'business',
                    }
                });

                var table = $("#table4");

                Controller.tableinfo.url=location.href+'&type=4';
                Controller.tableinfo.toolbar='#toolbar4';
                Controller.tableinfo.columns[0][Controller.tableinfo.columns[0].length-1].table=table;
                // 初始化表格
                table.bootstrapTable(Controller.tableinfo);

                // 为表格绑定事件
                Table.api.bindevent(table);
            },
            six: function () {
                // 初始化表格参数配置
                Table.api.init({
                    extend : {
                        add_url    : 'qingdong/customer/business/add',
                        detail_url : 'qingdong/customer/business/detail',
                        table      : 'business',
                    }
                });

                var table = $("#table5");


                Controller.tableinfo.url=location.href+'&type=5';
                Controller.tableinfo.toolbar='#toolbar5';
                Controller.tableinfo.columns[0][Controller.tableinfo.columns[0].length-1].table=table;
                // 初始化表格
                table.bootstrapTable(Controller.tableinfo);
                // 为表格绑定事件
                Table.api.bindevent(table);
            },
        },
        add    : function () {
            Controller.api.bindevent();
        },
        edit    : function () {
            Controller.api.bindevent();
        },
        import  : function () {
            Form.api.bindevent($("form[role=form]"));
        },
        import_product  : function () {
            Form.api.bindevent($("form[role=form]"));
        },
        batch_change : function () {
            Controller.api.bindevent();
        },
        record    : function () {
            Controller.api.bindevent();
        },
        batch_status    : function () {
            Controller.api.bindevent();
        },
        tabledetail    : {
            record: function () {

                //跟进记录
                var table1 = $("#records");
                // 初始化表格
                table1.bootstrapTable({
                    url          : 'qingdong/customer/record/index?busniess_id='+Config.idinfo.id,
                    pk           : 'id',
                    sortName     : 'id',
                    toolbar: "#toolbar_records",
                    operate      : false,
                    commonSearch : false,
                    search       : false,
                    visible      : false,
                    showColumns  : false,
                    showExport   : false,
                    showToggle   : false,
                    columns      : [
                        [
                            {field: 'follow_type', title: __('跟进类型')},
                            {field: 'follow_time', title: __('跟进时间')},
                            {field: 'follow', title: __('跟进状态')},
                            {field: 'content', title: __('跟进内容'),cellStyle:function (value,row,index,field) {
                                    return {
                                        css: {
                                            "min-width": "150px",
                                            "white-space": "nowrap",
                                            "text-overflow": "ellipsis",
                                            "overflow": "hidden",
                                            "max-width":"300px",
                                            'cursor':"pointer"
                                        }
                                    };
                                },formatter:function (value,row,index,field) {
                                    var span=document.createElement('span');
                                    span.setAttribute('title',value);
                                    span.innerHTML = value;
                                    return span.outerHTML;
                                }
                            },
                            {field: 'next_time', title: __('下次跟进时间')},
                            {
                                field     : 'operate',
                                title     : __('Operate'),
                                table     : table1,
                                events    : Table.api.events.operate,
                                formatter : Table.api.formatter.operate,
                                buttons   : [
                                    {
                                        name      : '详情',
                                        text      : __('详情'),
                                        classname : 'records btn-dialog',
                                        url       : 'qingdong/customer/record/detail',
                                        callback: function (data) {
                                            $('.btn-success').trigger('click');
                                        },

                                    },
                                    {
                                        name      : '评论',
                                        text      : __('评论'),
                                        classname : 'records btn-dialog',
                                        url       : 'qingdong/customer/comment/add',
                                        callback: function (data) {
                                            $('.btn-success').trigger('click');
                                        },

                                    }
                                ]
                            }
                        ]
                    ]
                });
                Table.api.bindevent(table1);
            },
            secord: function () {
                //商机产品
                var table=$('#product');
                table.bootstrapTable({
                    url          : 'qingdong/customer/business/product?business_id=' +Config.idinfo.id,
                    pk           : 'business_product.id',
                    sortName     : 'business_product.id',
                    toolbar: "#toolbar_secord",
                    operate      : false,
                    commonSearch : false,
                    search       : false,
                    visible      : false,
                    showColumns  : false,
                    showExport   : false,
                    showToggle   : false,
                    columns      : [
                        [
                            {
                                field : 'product.name', title : '产品名称', operate : 'like', fixedColumns : true, formatter : function (value, row, index) {
                                    if(row.product){
                                        return "<a href='javascript:void(0);' data-id='" + row.product.id + "' class='show-detail_product'>" + row.product.name + "</a>";
                                    }
                                }
                            },
                            {field : 'product.num', title : '产品编码', operate : 'like'},
                            {field: 'product.img', title: '产品图片', operate: false, events: Table.api.events.image, formatter: Table.api.formatter.image},
                            {field : 'product.unit', title : '产品单位', operate : false},
                            {field : 'price', title : '零售价', operate : false},
                            {field : 'wholesale', title : '批发价', operate : false},
                            {field : 'number', title : '数量', operate : false},

                        ]
                    ]
                });
                $(document).on('click', '.show-detail_product', function (data) {
                    var area = [$(window).width() > 1200 ? '1200px' : '95%', $(window).height() > 800 ? '800px' : '95%'];
                    var options = {
                        shadeClose : false,
                        shade      : [0.3, '#393D49'],
                        area       : area,
                        callback   : function (value) {
                            //在回调函数里可以调用你的业务代码实现前端的各种逻辑和效果
                            console.log(value);
                        }
                    };
                    Fast.api.open("qingdong/product/product/detail?ids=" + $(this).data('id'), '产品详情', options);
                });
                Table.api.bindevent(table);

            },
            //合同
            third: function () {
                var table1 = $("#contract");
                // 初始化表格
                table1.bootstrapTable({
                    url          : 'qingdong/customer/business/get_contract?business_id=' +Config.idinfo.id,
                    pk           : 'id',
                    sortName     : 'id',
                    toolbar:"#toolbar_third",
                    operate      : false,
                    commonSearch : false,
                    search       : false,
                    visible      : false,
                    showColumns  : false,
                    showExport   : false,
                    showToggle   : false,
                    columns      : [
                        [
                            {
                                field : 'num', title : '合同编号', fixedColumns : true, formatter : function (value, row, index) {
                                    if(value ==''){
                                        value = '无';
                                    }
                                    return "<a href='javascript:void(0);' data-area=[\"90%\",\"90%\"] class='btn-dialog' " +
                                        "data-url='qingdong/customer/contract/detail/ids/"+row.id+"'  data-title='合同详情' >" + value + "</a>";
                                },operate:'like'
                            },
                            {field : 'name', title : '合同名称'},
                            {field : 'customer.name', title : '客户名称'},
                            {field : 'money', title : '合同金额'},
                            {field : 'start_time', title : '开始日期'},
                            {field : 'end_time', title : '结束日期'},
                            {field : 'check_status', title : '状态', formatter : Table.api.formatter.status,
                                searchList : {0 : '待审核', 1 : '审核中', 2 : '审核通过', 3 : '审核未通过', 4 : '撤销', 5 : '草稿(未提交)'}},
                        ]
                    ]
                });

                Table.api.bindevent(table1);
            },
            //推进历史
            four: function () {
                var table1 = $("#history");
                // 初始化表格
                table1.bootstrapTable({
                    url          : 'qingdong/customer/business/get_business_status?business_id=' +Config.idinfo.id,
                    pk           : 'id',
                    sortName     : 'id',
                    toolbar:"#toolbar_four",
                    operate      : false,
                    commonSearch : false,
                    search       : false,
                    visible      : false,
                    showColumns  : false,
                    showExport   : false,
                    showToggle   : false,
                    columns      : [
                        [

                            {field : 'type', title : '商机阶段', formatter : Table.api.formatter.status,
                                searchList : {0 : '初期沟通', 1 : '立项跟踪', 2 : '方案/报价', 3 : '谈判审核', 4 : '赢单', 5 : '输单', 6 : '无效'}},

                            {field : 'remark', title : '备注'},
                            {field : 'money', title : '图片', formatter: Controller.api.formatter.thumb},
                            {field: 'createtime', title: __('推进时间'), operate:'RANGE', addclass:'datetimerange', formatter: Table.api.formatter.datetime},

                        ]
                    ]
                });

                Table.api.bindevent(table1);
            },
        },
        detail : function () {
            // 初始化表格参数配置
            Table.api.init({});
            $('.stafflist').popover({
                placement : 'bottom',
                trigger : 'hover'
            });
            //绑定事件
            $('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
                var panel = $($(this).attr("href"));
                if (panel.length > 0) {
                    Controller.tabledetail[panel.attr("id")].call(this);
                    $(this).on('click', function (e) {
                        $($(this).attr("href")).find(".btn-refresh").trigger("click");
                    });
                }
                //移除绑定的事件
                $(this).unbind('shown.bs.tab');
            });

            //必须默认触发shown.bs.tab事件
            $('ul.nav-tabs li.active a[data-toggle="tab"]').trigger("shown.bs.tab");


            //编辑
            $(document).on('click','.btn-edit',function () {
                var id=$('#ids').val();
                Fast.api.open("qingdong/customer/business/edit?ids="+id, "商机编辑",{
                    shadeClose: false,
                    shade: false,
                    maxmin: false,
                    moveOut: false,
                    scrollbars:false,
                    callback:function(value){
                        location.reload();
                    },
                });

            });
            //推进
            $(document).on('click','.btn-status',function () {
                var id=$('#ids').val();
                Fast.api.open("qingdong/customer/business/batch_status?ids="+id, "推进商机",{
                    shadeClose: false,
                    shade: false,
                    maxmin: false,
                    moveOut: false,
                    scrollbars:false,
                    callback:function(value){
                        location.reload();
                    },
                });

            });
            //删除
            $("#dels").on('click',function(){
                var id=$('#ids').val();
                Layer.confirm('确定删除商机吗？', {
                    btn: ['确定','取消']
                },function(index, layero){
                    Fast.api.ajax("qingdong/customer/business/delete?ids="+id,function(data,ret){
                        if(ret.code == 1){
                            Layer.close(index);
                            parent.location.reload();
                        }
                    },function(data,ret){
                    });

                });
            });
            //商机转移
            $(document).on('click','.btn-transfer',function () {
                var ids=$(this).data('id');
                if(!ids){
                    layer.alert('商机不存在！');
                    return false;
                }
                Fast.api.open("qingdong/customer/business/batch_change?ids="+ids, "转移商机",{
                    shadeClose: false,
                    shade: false,
                    maxmin: false,
                    moveOut: false,
                    scrollbars:false,
                    'area':[
                        $(window).width() > 800 ? '600px' : '400px',
                        $(window).height() > 600 ? '500px' : '400px'
                    ],
                    callback:function(value){
                        Form.events.plupload("#plupload-local");

                        //   在这里可以接收弹出层中使用`Fast.api.close(data)`进行回传数据
                    },
                });
            });
            Controller.api.bindevent();
        },
        api    : {
            bindevent : function () {
                $(document).on("fa.event.appendfieldlist", '.fieldlist', function(){
                    //通用的表单组件事件绑定和组件渲染
                    Form.events.selectpage($("form"));
                    Form.events.datetimepicker($("form"));
                });
                $(".selectpage").data("params", function (obj) {
                    var customer_id=$('#customer').val()
                    //obj为SelectPage对象
                    return {ids: customer_id};
                });
                function set_money(){
                    var $price = $('.price');
                    var $number = $('.number');
                    var price=0;
                    $.each($price, function (index, item) {
                        var p = $(item).val(), n = $number.eq(index).val();
                        p = parseFloat(p);
                        n = parseInt(n);
                        price += p * n;
                    });
                    $('#c-money').val(price);
                }
                $(document).on("change", ".product", function(){
                    var $this=$(this);
                    var id=$this.next('input').val();
                    Fast.api.ajax("qingdong/customer/contract/get_product_price?id="+id,function(data,ret){
                        $this.parent().parent().next('span').find('input').val(data.price);
                        set_money();
                        $(".fieldlist input:eq(2)").trigger("change");

                        return false;
                    });

                }).on("change", ".price", function(){
                    set_money();
                }).on("change", ".number", function(){
                    set_money();
                }).on("click", ".select-customer", function(){
                    var $this=$(this);
                    top.Fast.api.open($(this).data("url") , __('Select'), {callback: function (data) {
                            $this.html(data.name);
                            $('[name="'+$this.data('name')+'"]').val(data.id);
                        },area:["100%", "100%"]});
                }).on("click", ".select-product", function(){
                    var $this=$(this);
                    top.Fast.api.open($(this).data("url"), __('Select'), {callback: function (data) {
                          console.log('sssss');
                           console.log(data);
                            $this.html(data.name);
                            $('[name="'+$this.data('name')+'"]').val(data.id);
                            $this.parent().parent().next('td').find('input').val(data.price);
                            $this.parent().parent().parent().find('.price2').val(data.wholesale);
                            if(data.goods){
                                $this.parent().parent().parent().find('.goods').html(data.goods.name);
                            }
                            $this.parent().parent().parent().find('.type').html(data.type);


                            set_money();
                            $(".fieldlist input:eq(2)").trigger("change");
                        },area:["100%", "100%"]});
                });
                Form.events.selectpage($("form"));

                $(".selectpage").data("params", function (obj) {
                    var customer_id=$('#customer').val()
                    if(!customer_id){
                        layer.msg('请先选择客户');
                        return false;
                    }
                    //obj为SelectPage对象
                    return {ids: customer_id};
                });

                Form.api.bindevent($("form[role=form]"), function(data, ret){
                    //这里是表单提交处理成功后的回调函数，接收来自php的返回数据
                    Fast.api.close(data);//这里是重点
                });
            },
            formatter : {
                thumb : function (value, row, index) {
                    var length = row.file.length;
                    var html='';
                    for(var i=0;i<length;i++){
                        html += '<a href="' + row.file[i]['file_path'] + '" target="_blank"><img src="' + row.file[i]['file_path'] + '" data-tips-image alt="" title="商机图" style="max-height:90px;max-width:120px;margin-right:10px;"></a>';

                    }
                    return html;
                },

            }
        }
    };
    return Controller;
});