define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

	var Controller = {
		index  : function () {
			// 初始化表格参数配置
			Table.api.init({
				extend : {
					index_url  : 'qingdong/customer/product/index',
					add_url    : 'qingdong/customer/product/add',
					detail_url : 'qingdong/customer/product/detail',
					del_url    : 'qingdong/customer/product/del',
					table      : 'customer',
				}
			});

			var table = $("#table");

			// 初始化表格
			table.bootstrapTable({
				url          : $.fn.bootstrapTable.defaults.extend.index_url,
				pk           : 'id',
				sortName     : 'id',
				fixedColumns : true,
				fixedNumber  : 2,
				columns      : [
					[
						{checkbox : true},
						//						{field : 'id', title : __('Id'), sortable : true},
						{
							field : 'name', title : '产品名称', fixedColumns : true, formatter : function (value, row, index) {

								return "<a href='javascript:void(0);' data-id='" + row.id + "' class='show-detail'>" + value + "</a>";
							}
						},
						{field : 'num', title : '产品编码'},
						{field : 'price', title : '标准价格'},
						{field : 'description', title : '产品描述'},

					]
				]
			});

			$(document).on('click', '.show-detail', function (data) {
				var area = [$(window).width() > 1200 ? '1200px' : '95%', $(window).height() > 800 ? '800px' : '95%'];
				var options = {
					shadeClose : false,
					shade      : [0.3, '#393D49'],
					area       : area,
					callback   : function (value) {
						//在回调函数里可以调用你的业务代码实现前端的各种逻辑和效果
						console.log(value);
					}
				};
				Fast.api.open($.fn.bootstrapTable.defaults.extend.detail_url + "?ids=" + $(this).data('id'), '客户详情', options);
			});

			// 为表格绑定事件
			Table.api.bindevent(table);
		},
		add    : function () {
			Controller.api.bindevent();
		},
		edit   : function () {


			Controller.api.bindevent();
		},
		detail : function () {
			//编辑
			$(document).on('click', '.btn-edit', function () {
				var id = $('#ids').val();
				Fast.api.open("qingdong/customer/product/edit?ids=" + id, "产品编辑", {
					shadeClose : false,
					shade      : false,
					maxmin     : false,
					moveOut    : false,
					scrollbars : false,
					callback   : function () {
						location.reload()
					}
				});

			}).on('click', ".btn-del", function () {//删除
				var id = $('#ids').val();
				Layer.confirm('确定删除当前产品吗？', {
					btn   : ['确定', '取消'],
					title : '提示',
				}, function (index, layero) {
					Fast.api.ajax("qingdong/customer/product/del?ids=" + id, function (data, ret) {
						if (ret.code == 1) {
							Layer.close(index);
							parent.location.reload();
						}
					}, function (data, ret) {
					});

				});
			});
			Controller.api.bindevent();
		},
		api    : {
			bindevent : function () {
				Form.api.bindevent($("form[role=form]"));
			}
		}
	};
	return Controller;
});