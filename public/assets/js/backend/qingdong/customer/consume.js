define(['jquery', 'bootstrap', 'backend', 'form', 'table'], function ($, undefined, Backend, Form, Table) {

    var Controller = {
        index: function () {
            Table.api.init();

            //绑定事件
            $('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
                var panel = $($(this).attr("href"));
                if (panel.length > 0) {
                    Controller.table[panel.attr("id")].call(this);
                    $(this).on('click', function (e) {
                        $($(this).attr("href")).find(".btn-refresh").trigger("click");
                    });
                }
                //移除绑定的事件
                $(this).unbind('shown.bs.tab');
            });

            //必须默认触发shown.bs.tab事件
            $('ul.nav-tabs li.active a[data-toggle="tab"]').trigger("shown.bs.tab");

            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'qingdong/customer/consume/index?type=0',
                    add_url: 'qingdong/customer/consume/add',
                    edit_url: 'qingdong/customer/consume/edit',
                    del_url: 'qingdong/customer/consume/del',
                    detail_url : 'qingdong/customer/consume/detail',
                    customer_url : 'qingdong/customer/customer/detail',
                    table: 'consume'
                }
            });

            $(document).on('click', '.show-customer', function (data) {
                var area = [$(window).width() > 1200 ? '1200px' : '95%', $(window).height() > 800 ? '800px' : '95%'];
                var options = {
                    shadeClose : false,
                    shade      : [0.3, '#393D49'],
                    area       : area,
                    callback   : function (value) {
                        //在回调函数里可以调用你的业务代码实现前端的各种逻辑和效果
                        console.log(value);
                    }
                };
                Fast.api.open($.fn.bootstrapTable.defaults.extend.customer_url + "?ids=" + $(this).data('id'), '客户详情', options);
            });

        },

        tableinfo:{
            url: '',
            pk: 'id',
            toolbar: '#toolbar',
            sortName: 'id',
            fixedRightNumber:1,
            fixedColumns:true,
            columns: [
                [
                    {field: 'state', checkbox: true},
                    {field: 'consume_type', title: __('消费方式'), operate:false, formatter : function (value, row, index) {

                            return "<a href='javascript:void(0);' class='btn-dialog'  data-shade=\"0.3\"\n" +
                                "data-url=\""+ $.fn.bootstrapTable.defaults.extend.detail_url + "?ids=" + row.id+
                                "\" data-title=\"费用详情\" ' >" + value + "</a>";
                        }},
                    {field: 'consume_time', title: __('消费日期'), operate: '=', addclass: 'datetimepicker',data:"data-date-format=\" YYYY-MM-DD\""},

                    {field: 'customer', title: __('关联客户'), operate:false, formatter : function (value, row, index) {
                            if(row.customer){
                                return "<a href='javascript:void(0);' data-id='" + row.customer.id + "' class='show-customer'>" + row.customer.name + "</a>";
                            }else{
                                return '';
                            }
                        }},
                    {field : 'customer_id', title : '关联客户', visible: false, addClass: "selectpage", extend: "data-source='qingdong/customer/customer/index' data-field='name'"},

                    {field: 'money', title: __('消费金额')},
                    {field: 'check_status', title: __('审批状态'), formatter: Table.api.formatter.status,  searchList: {0: __('待审核'), 1: __('审核中'), 2: __('审核通过'), 3: __('审核拒绝'), 4: __('撤销'), 5: __('草稿')},custom: {0: 'gray', 1: 'gray',2: 'success', 3: 'danger', 4: 'danger', 5: 'danger'}},
                    {field: 'remark', title: __('备注')},
                    {field: 'file_text', title: __('附件'), formatter: function (value, row, index) {
                            var length = row.file_text.length;
                            var html='';
                            for(var i=0;i<length;i++){
                                html += '<a href="' + row.file_text[i] + '" target="_blank"><img src="' + row.file_text[i] + '" data-tips-image alt="" title="附件图" style="max-height:90px;max-width:120px;margin-right:10px;"></a>';

                            }
                            return html;
                        }, operate : false},
                    {field: 'staff.name', title: __('创建人'),operate:false},
                    {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', formatter: Table.api.formatter.datetime},

                ]
            ],
            //启用普通表单搜索
            search:false,
            commonSearch: true,
            searchFormVisible: true,
        },
        table: {
            first: function () {
                var table = $("#table");

                Controller.tableinfo.url=location.href+'&type=0';
                Controller.tableinfo.toolbar='#toolbar';
                Controller.tableinfo.columns[0][Controller.tableinfo.columns[0].length-1].table=table;

                // 初始化表格
                table.bootstrapTable(Controller.tableinfo);

                // 为表格绑定事件
                Table.api.bindevent(table);
            },
            second: function () {

                // 初始化表格参数配置
                Table.api.init({
                    extend: {
                        index_url: 'qingdong/customer/consume/index?type=0',
                        add_url: 'qingdong/customer/consume/add',
                        edit_url: 'qingdong/customer/consume/edit',
                        del_url: 'qingdong/customer/consume/del',
                        detail_url : 'qingdong/customer/consume/detail',
                        customer_url : 'qingdong/customer/customer/detail',
                        table: 'consume'
                    }
                });
                var table = $("#table1");

                Controller.tableinfo.url=location.href+'&type=1';
                Controller.tableinfo.toolbar='#toolbar1';
                Controller.tableinfo.columns[0][Controller.tableinfo.columns[0].length-1].table=table;

                // 初始化表格
                table.bootstrapTable(Controller.tableinfo);
                // 为表格绑定事件
                Table.api.bindevent(table);
            },
            third: function () {
                // 初始化表格参数配置
                Table.api.init({
                    extend: {
                        index_url: 'qingdong/customer/consume/index?type=0',
                        add_url: 'qingdong/customer/consume/add',
                        edit_url: 'qingdong/customer/consume/edit',
                        del_url: 'qingdong/customer/consume/del',
                        detail_url : 'qingdong/customer/consume/detail',
                        customer_url : 'qingdong/customer/customer/detail',
                        table: 'consume'
                    }
                });

                var table = $("#table2");

                Controller.tableinfo.url=location.href+'&type=2';
                Controller.tableinfo.toolbar='#toolbar2';
                Controller.tableinfo.columns[0][Controller.tableinfo.columns[0].length-1].table=table;

                // 初始化表格
                table.bootstrapTable(Controller.tableinfo);
                // 为表格绑定事件
                Table.api.bindevent(table);
            },
        },
        add    : function () {
            Controller.api.bindevent();
        },
        edit    : function () {
            Controller.api.bindevent();
        },
        detail : function () {
            $('.stafflist').popover({
                placement : 'bottom',
                trigger : 'hover'
            });
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                $(document).on("click", ".select-customer", function(){
                    var $this=$(this);
                    top.Fast.api.open($(this).data("url") , __('Select'), {callback: function (data) {
                            $this.html(data.name);
                            $('[name="'+$this.data('name')+'"]').val(data.id);
                        },area:["100%", "100%"]});
                })

                Form.api.bindevent($("form[role=form]"));
            },
            formatter: {}
        },

    };
    return Controller;
});