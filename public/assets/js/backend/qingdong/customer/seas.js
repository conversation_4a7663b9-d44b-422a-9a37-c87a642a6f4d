define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

	var Controller = {
		index  : function () {
			// 初始化表格参数配置
			Table.api.init({
				extend : {
					index_url  : 'qingdong/customer/seas/index',
					detail_url : 'qingdong/customer/customer/detail',
					table      : 'customer',
				}
			});

			var table = $("#table");

			// 初始化表格
			table.bootstrapTable({
				url          : $.fn.bootstrapTable.defaults.extend.index_url,
				pk           : 'id',
				sortName     : 'id',
				fixedColumns : true,
				fixedNumber  : 2,
				fixedRightNumber  : 1,
				search:false,
				searchFormVisible:true,
				columns      : [
					[
						{checkbox : true},
						{
							field : 'name', title : '客户名称', fixedColumns : true, formatter : function (value, row, index) {

								return "<a href='javascript:void(0);' data-id='" + row.id + "' class='show-detail'>" + value + "</a>";
							},operate:'like'
						},
						{field : 'subname', title : '助记名称',operate:'like'},
						{field : 'industry', title : '客户所属',operate:'like'},
						{field : 'source', title : '客户来源',operate:'like'},
						{field : 'next_time', title : '下次联系时间',operate:false},
						{field : 'last_time', title : '最后跟进时间',operate:false},
						{field : 'remarks', title : '备注信息',operate:false},
						{field : 'address', title : '省市区',operate:false},
						{field : 'address_detail', title : '详细地址',operate:false},
						{field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.buttons,
							buttons: [
								{
									name: 'seas',
									text: __('领取'),
									title: __('领取'),
									classname: 'records btn-ajax',
									url: 'qingdong/customer/customer/receive',
									confirm: '确定要领取当前客户吗?',
									refresh:true,
									error: function (data, ret) {
										console.log(data, ret);
										Layer.alert(ret.msg);
										return false;
									}
								},
							]
						}
					]
				]
			});

			$(document).on('click', '.show-detail', function (data) {
				var area = [$(window).width() > 1200 ? '1200px' : '95%', $(window).height() > 800 ? '800px' : '95%'];
				var options = {
					shadeClose : false,
					shade      : [0.3, '#393D49'],
					area       : area,
					callback   : function (value) {
						//在回调函数里可以调用你的业务代码实现前端的各种逻辑和效果
						console.log(value);
					}
				};
				Fast.api.open($.fn.bootstrapTable.defaults.extend.detail_url + "?ids=" + $(this).data('id'), '客户详情', options);
			});
			$(document).on('click','.btn-transfer',function () {
				//在templateView的模式下不能调用table.bootstrapTable('getSelections')来获取选中的ID,只能通过下面的Table.api.selectedids来获取
				if(Table.api.selectedids(table).length == 0){
					layer.alert('请选择要筛选的客户！');
					return false;
				}
				var ids=JSON.stringify(Table.api.selectedids(table));
				Fast.api.open("qingdong/customer/customer/batch_change?ids="+ids, "批量转移客户",{
					shadeClose: false,
					shade: false,
					maxmin: false,
					moveOut: false,
					scrollbars:false,
					'area':[
						$(window).width() > 800 ? '600px' : '400px',
						$(window).height() > 600 ? '500px' : '400px'
					],
					callback:function(value){
						Form.events.plupload("#plupload-local");

						//   在这里可以接收弹出层中使用`Fast.api.close(data)`进行回传数据
					},
				});
			});
			$(document).on('click','.btn-imports',function () {
				Fast.api.open("qingdong/customer/customer/import", "公海导入",{
					shadeClose: false,
					shade: false,
					maxmin: false,
					moveOut: false,
					scrollbars:false,
					'area':[
						$(window).width() > 800 ? '600px' : '400px',
						$(window).height() > 600 ? '500px' : '400px'
					],
					callback:function(value){
						Form.events.plupload("#plupload-local");

						//   在这里可以接收弹出层中使用`Fast.api.close(data)`进行回传数据
					},
				});

			});
			// 为表格绑定事件
			Table.api.bindevent(table);
		},
		add    : function () {
			Controller.api.bindevent();
		},
		api    : {
			bindevent : function () {
				Form.api.bindevent($("form[role=form]"));
			}
		}
	};
	return Controller;
});