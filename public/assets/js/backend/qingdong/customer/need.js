define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {
    function get_need_number(){
        $.post("qingdong/customer/need/get_need_number",function (data){
            top.window.Backend.api.sidebar({
                'qingdong/customer/need/index':[data.data.number, 'red', 'badge'],
                'qingdong/examine/examine/index':[data.data.examine, 'red', 'badge'], //合同
                'qingdong/examine/examine/consume':[data.data.consume, 'red', 'badge'],
                'qingdong/examine/examine/receivables':[data.data.receivables, 'red', 'badge'],
                'qingdong/examine/examine/achievement':[data.data.achievement, 'red', 'badge'],
                'qingdong/examine/examine/work':[data.data.approval, 'red', 'badge'],
                'qingdong/canlendar/canlendar':[data.data.eventOne, 'red', 'badge'],
            });
        },'json');
    }
    get_need_number();
    setInterval(function (){
        get_need_number();
    },50000);
    var Controller = {
        index  : function () {

        },


        api    : {
            bindevent : function () {
                Form.api.bindevent($("form[role=form]"), function(data, ret){
                    //这里是表单提交处理成功后的回调函数，接收来自php的返回数据
                    Fast.api.close(data);//这里是重点
                });
            }
        }
    };
    return Controller;
});