define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

	var Controller = {
		index  : function () {
			// 初始化表格参数配置
			Table.api.init();

			//绑定事件
			$('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
				var panel = $($(this).attr("href"));
				if (panel.length > 0) {
					Controller.table[panel.attr("id")].call(this);
					$(this).on('click', function (e) {
						$($(this).attr("href")).find(".btn-refresh").trigger("click");
					});
				}
				//移除绑定的事件
				$(this).unbind('shown.bs.tab');
			});

			//必须默认触发shown.bs.tab事件
			$('ul.nav-tabs li.active a[data-toggle="tab"]').trigger("shown.bs.tab");

			$(document).on('click', '.show-detail', function (data) {
				var area = [$(window).width() > 1200 ? '90%' : '95%', $(window).height() > 800 ? '90%' : '95%'];
				var options = {
					shadeClose : false,
					shade      : [0.3, '#393D49'],
					area       : area,
					callback   : function (value) {
						//在回调函数里可以调用你的业务代码实现前端的各种逻辑和效果
						console.log(value);
					}
				};
				Fast.api.open($.fn.bootstrapTable.defaults.extend.detail_url + "?ids=" + $(this).data('id'), '客户详情', options);
			});
			//导入
			$(document).on('click','.btn-imports',function () {
				Fast.api.open("qingdong/customer/customer/import", "客户导入",{
					shadeClose: false,
					shade: false,
					maxmin: false,
					moveOut: false,
					scrollbars:false,
					'area':[
						$(window).width() > 800 ? '600px' : '400px',
						$(window).height() > 600 ? '500px' : '400px'
					],
					callback:function(value){
						Form.events.plupload("#plupload-local");

						//   在这里可以接收弹出层中使用`Fast.api.close(data)`进行回传数据
					},
				});

			}).on('click','.btn-transfer',function () {
				var tab_id=$('.active.in').attr('id');
				var table;
				switch (tab_id){
					case 'first'://全部
						table=$('#table');
						break;
					case 'second'://我负责的
						table=$('#table1');
						break;
					case 'third'://下属负责的
						table=$('#table2');
						break;
					case 'four'://今日待跟进
						table=$('#table3');
						break;
					case 'five'://今日已跟进
						table=$('#table4');
						break;
					case 'six'://从未跟进的
						table=$('#table5');
						break;
				}
				//在templateView的模式下不能调用table.bootstrapTable('getSelections')来获取选中的ID,只能通过下面的Table.api.selectedids来获取
				if(Table.api.selectedids(table).length == 0){
					layer.alert('请选择要筛选的客户！');
					return false;
				}
				var ids=JSON.stringify(Table.api.selectedids(table));
				Fast.api.open("qingdong/customer/customer/batch_change?ids="+ids, "批量转移客户",{
					shadeClose: false,
					shade: false,
					maxmin: false,
					moveOut: false,
					scrollbars:false,
					'area':[
						$(window).width() > 800 ? '600px' : '400px',
						$(window).height() > 600 ? '500px' : '400px'
					],
					callback:function(value){
						Form.events.plupload("#plupload-local");

						//   在这里可以接收弹出层中使用`Fast.api.close(data)`进行回传数据
					},
				});
			}).on('click','.btn-send-email',function () {
				var tab_id=$('.active.in').attr('id');
				var table;
				switch (tab_id){
					case 'first'://全部
						table=$('#table');
						break;
					case 'second'://我负责的
						table=$('#table1');
						break;
					case 'third'://下属负责的
						table=$('#table2');
						break;
					case 'four'://今日待跟进
						table=$('#table3');
						break;
					case 'five'://今日已跟进
						table=$('#table4');
						break;
					case 'six'://从未跟进的
						table=$('#table5');
						break;
				}
				//在templateView的模式下不能调用table.bootstrapTable('getSelections')来获取选中的ID,只能通过下面的Table.api.selectedids来获取
				if(Table.api.selectedids(table).length == 0){
					layer.alert('请选择要选中的客户！');
					return false;
				}
				var $this=$(this);
				var ids=JSON.stringify(Table.api.selectedids(table));
				var url=$this.data('url'),title=$this.data('title'),types=$this.data('types');
				Fast.api.open(url+"?ids="+ids+"&type="+types, title,{
					shadeClose: false,
					shade: false,
					maxmin: false,
					moveOut: false,
					scrollbars:false,
					'area':[
						$(window).width() > 800 ? '600px' : '400px',
						$(window).height() > 600 ? '500px' : '400px'
					],
					callback:function(value){
						Form.events.plupload("#plupload-local");
						//   在这里可以接收弹出层中使用`Fast.api.close(data)`进行回传数据
					},
				});
			}).on('click','.btn-send-sms',function () {
				var tab_id=$('.active.in').attr('id');
				var table;
				switch (tab_id){
					case 'first'://全部
						table=$('#table');
						break;
					case 'second'://我负责的
						table=$('#table1');
						break;
					case 'third'://下属负责的
						table=$('#table2');
						break;
					case 'four'://今日待跟进
						table=$('#table3');
						break;
					case 'five'://今日已跟进
						table=$('#table4');
						break;
					case 'six'://从未跟进的
						table=$('#table5');
						break;
				}
				//在templateView的模式下不能调用table.bootstrapTable('getSelections')来获取选中的ID,只能通过下面的Table.api.selectedids来获取
				if(Table.api.selectedids(table).length == 0){
					layer.alert('请选择要选中的客户！');
					return false;
				}
				var $this=$(this);
				var ids=JSON.stringify(Table.api.selectedids(table));
				var url=$this.data('url'),title=$this.data('title'),types=$this.data('types');
				Fast.api.open(url+"?ids="+ids+"&type="+types, title,{
					shadeClose: false,
					shade: false,
					maxmin: false,
					moveOut: false,
					scrollbars:false,
					'area':[
						$(window).width() > 800 ? '600px' : '400px',
						$(window).height() > 600 ? '500px' : '400px'
					],
					callback:function(value){
						Form.events.plupload("#plupload-local");
						//   在这里可以接收弹出层中使用`Fast.api.close(data)`进行回传数据
					},
				});
			}).on("click", ".btn-export", function () {
				var tab_id=$('.active.in').attr('id');
				var table,type;
				switch (tab_id){
					case 'first'://全部
						table=$('#table');
						type=0;
						break;
					case 'second'://我负责的
						table=$('#table1');
						type=1;
						break;
					case 'third'://下属负责的
						table=$('#table2');
						type=2;
						break;
					case 'four'://今日待跟进
						table=$('#table3');
						type=3;
						break;
					case 'five'://今日已跟进
						table=$('#table4');
						type=4;
						break;
					case 'six'://从未跟进的
						table=$('#table5');
						type=5;
						break;
				}
				var ids = Table.api.selectedids(table);
				var page = table.bootstrapTable('getData');

				var options = table.bootstrapTable('getOptions');
				var all = options.totalRows;
				var params=options.queryParams({});
				var sort = options.sortName+' '+options.sortOrder;
				var filter = params.filter;
				var op = params.op;
				Layer.confirm("请选择导出的选项", {
					title   : '导出数据',
					btn     : ["选中项(" + ids.length + "条)", "本页(" + page.length + "条)", "全部(" + all + "条)"],
					success : function (layero, index) {
						$(".layui-layer-btn a", layero).addClass("layui-layer-btn0");
					},
					yes   : function (index, layero) {
						if (ids.length == 0) {
							Layer.alert('数据为空');
							return true;
						}
						$.post('qingdong/customer/customer/export', {sort:sort,ids : ids.join(","), isall : 1}, function (res) {
							Layer.alert(res.msg);
							if(res.code == 1){
								window.location.href = res.data.filePath;
								Layer.close(index);
							}
							table.bootstrapTable('refresh');
						}, 'json');
						return false;
					},
					btn2    : function (index, layero) {
						if (page.length == 0) {
							Layer.alert('数据为空');
							return true;
						}
						var ids = [];
						$.each(page, function (i, j) {
							ids.push(j.id);
						});
						$.post('qingdong/customer/customer/export', {sort:sort,ids : ids.join(","), isall : 2}, function (res) {
							Layer.alert(res.msg);
							if(res.code == 1){
								window.location.href = res.data.filePath;
								Layer.close(index);
							}
							table.bootstrapTable('refresh');
						}, 'json');
						return false;
					},
					btn3    : function (index, layero) {
						if (page.length == 0) {
							Layer.alert('数据为空');
							return true;
						}

						$.post('qingdong/customer/customer/export?filter='+filter+'&op='+op, {sort:sort,ids : ids.join(","), isall : 3,type:type}, function (res) {
							Layer.alert(res.msg);
							if(res.code == 1){
								window.location.href = res.data.filePath;
								Layer.close(index);
							}
							table.bootstrapTable('refresh');
						}, 'json');
						return false;
					}
				})
			});
		},
		tableinfo:{
			url          : '',
			toolbar: '#toolbar',
			pk           : 'id',
			sortName     : 'id',
			fixedColumns : true,
			fixedNumber  : 2,
			fixedRightNumber  : 1,
			search:false,
			searchFormVisible:true,
			columns      : [
				[
					{checkbox : true},
					{
						field : 'name', title : '客户名称', fixedColumns : true, formatter : function (value, row) {
							if(!value){
								value = '无'
							}
							return "<a href='javascript:void(0);' data-id='" + row.id + "' class='show-detail'>" + value + "</a>";
						},operate:'like',searchable:true
					},
					{field : 'subname', title : '助记名称',operate:'like'},
					{field : 'owner_staff.name', title : '归属员工',operate:false},
					{field : 'owner_staff_id', title : '归属员工',visible:false,operate:'=',searchList:$.getJSON("qingdong/customer/customer/stafflist")},
					{field : 'contract_status', title : '成交状态',operate:'=',searchList:{0:'未成交',1:'已成交'},formatter:Table.api.formatter.status},
					{field : 'industry', title : '客户所属',operate:'like'},
					{field : 'follow', title : '客户状态',operate:'like'},
					{field : 'source', title : '客户来源',operate:'like'},
					{field : 'level', title : '客户星级',operate:'=',searchList:{0:'无',1:'1星',2:'2星',3:'3星',4:'4星',5:'5星'},formatter:Table.api.formatter.status},

					{field : 'next_time', title : '下次联系时间',operate:false},
					{field : 'last_time', title : '最后跟进时间',operate:false},
					{field : 'receivetime', title : '领取时间', operate:'RANGE', addclass:'datetimerange', formatter: Table.api.formatter.datetime},

					{field: 'createtime', title: __('Createtime'),autocomplete:false, operate:'RANGE', addclass:'datetimerange', formatter: Table.api.formatter.datetime},

					{
						field: 'operate2', title: __('Operate'), events: {
							'click .btn-chooseone': function (e, value, row, index) {
								Fast.api.close({id: row.id, name: row.name,address:row.address_detail});
							},
						}, formatter: function () {
							return '<a href="javascript:;" class="btn btn-danger btn-chooseone btn-xs"><i class="fa fa-check"></i> ' + __('Choose') + '</a>';
						}
					},
					{field: 'operate', title: __('Operate'),
						events: Table.api.events.operate, formatter: Table.api.formatter.buttons,
                        buttons: [

                            {
                                name: 'edit',
                                text: __('编辑'),
                                title: __('编辑'),
                                extend:'data-area=["80%","80%"]',
                                classname: 'records btn-dialog',
                                url: 'qingdong/customer/customer/edit',
                                visible: function (row) {
                                    if(row.operation =='read'){
                                        return false;
                                    }
                                    //返回true时按钮显示,返回false隐藏
                                    return true;
                                }
                            },
                            {
                                name: 'detail',
                                text: __('跟进'),
                                title: __('跟进'),
                                extend:'data-area=["80%","80%"]',
                                classname: ' records btn-dialog',
                                url: 'qingdong/customer/record/add/relation_type/1',
                                visible: function (row) {
                                    //返回true时按钮显示,返回false隐藏
                                    if(row.operation =='read'){
                                        return false;
                                    }
                                    return true;
                                }
                            },
                            {
                                name: 'seas',
                                text: __('公海'),
                                title: __('公海'),
                                classname: 'seas btn-ajax',
                                url: 'qingdong/customer/customer/seas',
                                visible: function (row) {
                                    //返回true时按钮显示,返回false隐藏
                                    if(row.operation_team=='update' || row.operation =='read'){
                                        return false;
                                    }
                                    return true;
                                },
                                confirm: '确定将当前客户放入公海吗?',
                                refresh:true,
                                error: function (data, ret) {
                                    console.log(data, ret);
                                    Layer.alert(ret.msg);
                                    return false;
                                },

                            },

                        ]
                    },

				]
			],
			onLoadSuccess:function(){
				// 这里就是数据渲染结束后的回调函数
				$(".btn-add").data("area",["80%","80%"]);
				$(".btn-edit").data("area",["80%","80%"]);
			}
		},
		table: {
			first : function() {
				// 初始化表格参数配置
				Table.api.init({
					extend : {
						add_url    : 'qingdong/customer/customer/add',
						detail_url : 'qingdong/customer/customer/detail',
						table      : 'customer',
					}
				});
				if(Config.fields !='[]'){
					if (Config.fields && Object.keys(Config.fields).length > 0) {
						var fields = JSON.parse(Config.fields);
						var start = Controller.tableinfo.columns[0].length-2;
						for (var i = 0; i < fields.length; i++) {
							if (fields[i].hasOwnProperty('formatter'))
								fields[i].formatter = eval(fields[i].formatter);
							if (fields[i].hasOwnProperty('events'))
								fields[i].events = eval(fields[i].events);
							if (fields[i].hasOwnProperty('searchList'))
								fields[i].searchList = JSON.parse(fields[i].searchList);
							Controller.tableinfo.columns[0].splice(start + i, 0, fields[i]);
						}
					}

				}
				let q = {}
				location.search.replace(/([^?&=]+)=([^&]+)/g,function(_,k,v){q[k]=v});
				var table = $("#table");
				Controller.tableinfo.url=location.href+'&type=0';
				Controller.tableinfo.toolbar='#toolbar';
				Controller.tableinfo.columns[0][Controller.tableinfo.columns[0].length-1].table=table;
				if (q.isselect == 1) {
					Controller.tableinfo.columns[0][Controller.tableinfo.columns[0].length - 1].visible = false;
				} else {
					Controller.tableinfo.columns[0][Controller.tableinfo.columns[0].length - 2].visible = false;
				}
				// 初始化表格
				table.bootstrapTable(Controller.tableinfo);

				// 为表格绑定事件
				Table.api.bindevent(table);
			},
			second : function() {
				// 初始化表格参数配置
				Table.api.init({
					extend : {
						add_url    : 'qingdong/customer/customer/add',
						table      : 'customer',
					}
				});
				var table = $("#table1");
				Controller.tableinfo.url=location.href+'&type=1';
				Controller.tableinfo.toolbar='#toolbar1';
				Controller.tableinfo.columns[0][Controller.tableinfo.columns[0].length-1].table=table;
				// 初始化表格
				table.bootstrapTable(Controller.tableinfo);
				// 为表格绑定事件
				Table.api.bindevent(table);
			},
			third : function() {
				// 初始化表格参数配置
				Table.api.init({
					extend : {
						add_url    : 'qingdong/customer/customer/add',
						table      : 'customer',
					}
				});
				var table = $("#table2");
				Controller.tableinfo.url=location.href+'&type=2';
				Controller.tableinfo.toolbar='#toolbar2';
				Controller.tableinfo.columns[0][Controller.tableinfo.columns[0].length-1].table=table;
				// 初始化表格
				table.bootstrapTable(Controller.tableinfo);
				// 为表格绑定事件
				Table.api.bindevent(table);
			},
			four : function() {
				// 初始化表格参数配置
				Table.api.init({
					extend : {
						add_url    : 'qingdong/customer/customer/add',
						table      : 'customer',
					}
				});
				var table = $("#table3");
				Controller.tableinfo.url=location.href+'&type=3';
				Controller.tableinfo.toolbar='#toolbar3';
				Controller.tableinfo.columns[0][Controller.tableinfo.columns[0].length-1].table=table;
				// 初始化表格
				table.bootstrapTable(Controller.tableinfo);
				// 为表格绑定事件
				Table.api.bindevent(table);
			},
			five : function() {
				// 初始化表格参数配置
				Table.api.init({
					extend : {
						add_url    : 'qingdong/customer/customer/add',
						table      : 'customer',
					}
				});
				var table = $("#table4");
				Controller.tableinfo.url=location.href+'&type=4';
				Controller.tableinfo.toolbar='#toolbar4';
				Controller.tableinfo.columns[0][Controller.tableinfo.columns[0].length-1].table=table;
				// 初始化表格
				table.bootstrapTable(Controller.tableinfo);
				// 为表格绑定事件
				Table.api.bindevent(table);
			},
			six : function() {
				// 初始化表格参数配置
				Table.api.init({
					extend : {
						add_url    : 'qingdong/customer/customer/add',
						table      : 'customer',
					}
				});
				var table = $("#table5");

				Controller.tableinfo.url=location.href+'&type=5';
				Controller.tableinfo.toolbar='#toolbar5';
				Controller.tableinfo.columns[0][Controller.tableinfo.columns[0].length-1].table=table;
				// 初始化表格
				table.bootstrapTable(Controller.tableinfo);
				// 为表格绑定事件
				Table.api.bindevent(table);
			},

		},
		add    : function () {
			Controller.api.bindevent();
		},
		editteam    : function () {
			Controller.api.bindevent();
		},
		delteam    : function () {
			Controller.api.bindevent();
		},
		map    : function () {
			window.addEventListener('message', function (event) {
				var loc = event.data;
				$('#lng').val(loc.latlng.lng)
				$('#lat').val(loc.latlng.lat)
				$('#address').val(loc.poiaddress)
				$('#address_detail').val(loc.poiname)
				console.log(loc)
			}, false);
			Form.api.bindevent($("form[role=form]"), function(data, ret){
				//这里是表单提交处理成功后的回调函数，接收来自php的返回数据
				Fast.api.close(data);//这里是重点

			}, function(data, ret){

			},function (){
				var lng=$('#lng').val();
				var lat=$('#lat').val();
				var address=$('#address').val();
				var address_detail=$('#address_detail').val();
				if(!address){
					Layer.alert('请选择地理位置！');
					return false;
				}
				Fast.api.close({lng:lng,lat:lat,address:address,address_detail:address_detail});
				return false;
			});
		},
		change    : function () {
			Controller.api.bindevent();
		},
		batch_change    : function () {
			Controller.api.bindevent();
		},
		add_consume    : function () {
			Controller.api.bindevent();
		},

		edit    : function () {
			Controller.api.bindevent();
		},
		get_sign_detail    : function () {
			Controller.api.bindevent();
		},
		addteam    : function () {
			Controller.api.bindevent();
		},
		import  : function () {
			$('[name="staff_id"]').on('change',function (){
				var staff_id=$(this).val();
				if(staff_id == 0){
					$('#seas_type').show();
				}else{
					$('#seas_type').hide();
				}
			})
			Form.api.bindevent($("form[role=form]"), function (data, ret) {
				//这里是表单提交处理成功后的回调函数，接收来自php的返回数据
				Fast.api.close(data);//这里是重点
			}, function (data, ret) {
				Toastr.error(ret.msg);
				return false;
			});
		},
		words  : function () {
			$('.copy').on('click',function (){
				var copyhtml=$(this).prev().html();
				let copyInput = document.createElement('input');//创建input元素
				document.body.appendChild(copyInput);//向页面底部追加输入框
				copyInput.setAttribute('value', copyhtml);//添加属性，将url赋值给input元素的value属性
				copyInput.select();//选择input元素
				document.execCommand("Copy");//执行复制命令
				Layer.msg('复制成功！');
				copyInput.remove();//删除动态创建的节点

			})

			$("[name='is_template']").on('click',function (){
				var ischecked=$(this).is(':checked');
				if(ischecked == true){
					$('#template_name').show();
				}else{
					$('#template_name').hide();
				}
			})
			$("[name='template_id']").on('change',function (){
				var url=$(this).find(':selected').data('url');
				$('#file').val(url)
			})
			Form.api.bindevent($("form[role=form]"), function (data, ret) {
				Fast.api.close(data);//这里是重点
			}, function (data, ret) {
				Toastr.error(ret.msg);
				return false;
			});
		},
		tabledetail:{

			record: function () {

				//跟进记录
				var table6 = $("#records");
				// 初始化表格
				table6.bootstrapTable({
					url          : 'qingdong/customer/record/index?customer_id='+Config.idinfo.id,
					pk           : 'id',
					sortName     : 'id',
					toolbar:"#toolbar_records",
					operate      : false,
					commonSearch : false,
					search       : false,
					visible      : false,
					showColumns  : false,
					showExport   : false,
					showToggle   : false,
					columns      : [
						[
							{field: 'follow_type', title: __('跟进类型')},
							{field: 'follow_time', title: __('跟进时间')},
							{field: 'follow', title: __('跟进状态')},
							{field: 'content', title: __('跟进内容'),cellStyle:function (value,row,index,field) {
									return {
										css: {
											"min-width": "150px",
											"white-space": "nowrap",
											"text-overflow": "ellipsis",
											"overflow": "hidden",
											"max-width":"300px",
											'cursor':"pointer"
										}
									};
								},formatter:function (value,row,index,field) {
									var span=document.createElement('span');
									span.setAttribute('title',value);
									span.innerHTML = value;
									return span.outerHTML;
								}
							},
							{field: 'next_time', title: __('下次跟进时间')},
							{
								field     : 'operate',
								title     : __('Operate'),
								table     : table6,
								events    : Table.api.events.operate,
								formatter : Table.api.formatter.operate,
								buttons   : [
									{
										name      : '详情',
										text      : __('详情'),
										classname : 'records btn-dialog',
										url       : 'qingdong/customer/record/detail',
										callback: function (data) {
											$('.btn-success').trigger('click');
										},

									},
									{
										name      : '评论',
										text      : __('评论'),
										classname : 'records btn-dialog',
										url       : 'qingdong/customer/comment/add',
										callback: function (data) {
											$('.btn-success').trigger('click');
										},
										visible: function (row) {
											if(Config.idinfo.operation_team =='update'){
												return false;
											}
											//返回true时按钮显示,返回false隐藏
											return true;
										},

									}
								]
							}
						]
					]
				});
				// 为表格1绑定事件
				Table.api.bindevent(table6);
			},
			basic: function () {

			},
			//联系人
			contacts: function () {
				var table = $("#table-contacts");
				// 初始化表格
				table.bootstrapTable({
					url          : 'qingdong/customer/customer/get_contacts' + location.search,
					pk           : 'id',
					sortName     : 'id',
					toolbar:"#toolbar_contacts",
					operate      : false,
					commonSearch : false,
					search       : false,
					visible      : false,
					showColumns  : false,
					showExport   : false,
					showToggle   : false,
					columns      : [
						[
							{
								field : 'name', title : '姓名', fixedColumns : true, formatter : function (value, row, index) {
									if(value ==''){
										value = '无';
									}
									if(Config.idinfo.operation_team =='update' || Config.idinfo.operation =='read'){
										return value;
									}
									return "<a href='javascript:void(0);' class='btn-dialog' " +
										"data-url='qingdong/customer/contacts/detail/ids/"+row.id+"' data-title='联系人详情' >" + value + "</a>";
								},operate:'like'
							},
							{field : 'post', title : '职务'},
							{field : 'role', title : '角色'},
							{field : 'email', title : '邮箱'},
							{field : 'mobile', title : '手机号'},
							{field : 'remarks', title : '备注信息'},
						]
					]
				});
				Table.api.bindevent(table);
			},
			//费用
			settings5: function () {
				var table5 = $("#consume");
				// 初始化表格
				table5.bootstrapTable({
					url          : 'qingdong/customer/customer/get_consume' + location.search,
					pk           : 'id',
					sortName     : 'id',
					toolbar:"#toolbar_consume",
					operate      : false,
					commonSearch : false,
					search       : false,
					visible      : false,
					showColumns  : false,
					showExport   : false,
					showToggle   : false,
					columns      : [
						[
							{
								field : 'consume_time', title : '消费日期', fixedColumns : true, formatter : function (value, row, index) {
									if(value ==''){
										value = '无';
									}
									if(Config.idinfo.operation_team =='update' || Config.idinfo.operation =='read'){
										return value;
									}
									return "<a href='javascript:void(0);' class='btn-dialog' " +
										"data-url='qingdong/customer/consume/detail/ids/"+row.id+"' data-title='费用详情' >" + value + "</a>";
								},operate:'like'
							},
							{field : 'consume_type', title : '消费方式'},
							{field : 'money', title : '消费金额'},
							{field : 'money', title : '消费人'},
							{field : 'follow_staff.name', title : '审核人'},
							{field : 'check_status', title : '状态', formatter : Table.api.formatter.status,
								searchList : {0 : '待审核', 1 : '审核中', 2 : '审核通过', 3 : '审核未通过', 4 : '撤销', 5 : '草稿(未提交)'}},
							{field: 'operate', title: __('Operate'), table: table5, events: Table.api.events.operate}
						]
					]
				});
				Table.api.bindevent(table5);
			},
			//合同
			settings1: function () {
				var table1 = $("#contract");
				// 初始化表格
				table1.bootstrapTable({
					url          : 'qingdong/customer/customer/get_contract' + location.search,
					pk           : 'id',
					sortName     : 'id',
					toolbar:"#toolbar_contract",
					operate      : false,
					commonSearch : false,
					search       : false,
					visible      : false,
					showColumns  : false,
					showExport   : false,
					showToggle   : false,
					columns      : [
						[
							{
								field : 'num', title : '合同编号', fixedColumns : true, formatter : function (value, row, index) {
									if(value ==''){
										value = '无';
									}
									if(Config.idinfo.operation_team =='update' || Config.idinfo.operation =='read'){
										return value;
									}
									return "<a href='javascript:void(0);' class='btn-dialog' " +
										"data-url='qingdong/customer/contract/detail/ids/"+row.id+"' data-title='合同详情' >" + value + "</a>";
								},operate:'like'
							},
							{field : 'name', title : '合同名称'},
							{field : 'customer.name', title : '客户名称'},
							{field : 'money', title : '合同金额'},
							{field : 'start_time', title : '开始日期'},
							{field : 'end_time', title : '结束日期'},
							{field : 'check_status', title : '状态', formatter : Table.api.formatter.status,
								searchList : {0 : '待审核', 1 : '审核中', 2 : '审核通过', 3 : '审核未通过', 4 : '撤销', 5 : '草稿(未提交)'}},
						]
					]
				});

				Table.api.bindevent(table1);
			},
			//回款计划
			settings2: function () {
				//回款计划
				var table_plan = $("#plan");
				table_plan.bootstrapTable({
					url          : 'qingdong/customer/customer/get_receivables_plan' + location.search,
					pk           : 'id',
					sortName     : 'id',
					toolbar:"#toolbar_receivablesplan",
					operate      : false,
					commonSearch : false,
					search       : false,
					visible      : false,
					showColumns  : false,
					showExport   : false,
					showToggle   : false,
					columns      : [
						[
							{field : 'num', title : '期数'},
							{field : 'customer.name', title : '客户名称'},
							{
								field : 'contract.num', title : '合同编号', fixedColumns : true, formatter : function (value, row, index) {
									if(value ==''){
										value = '无';
									}
									if(Config.idinfo.operation_team =='update' || Config.idinfo.operation =='read'){
										return value;
									}
									return "<a href='javascript:void(0);' class='btn-dialog' " +
										"data-url='qingdong/customer/contract/detail/ids/"+row.contract.id+"' data-title='合同详情' >" + value + "</a>";
								},operate:'like'
							},
							{field : 'money', title : '计划回款金额'},
							{field : 'return_date', title : '计划回款日期'},
							{field : 'return_type', title : '计划回款方式'},
							{field : 'remind', title : '提前几日提醒'},
							{field : 'remarks', title : '备注'},
						]
					]
				});
				Table.api.bindevent(table_plan);
				//回款记录
				var table_receivables = $("#receivables");
				table_receivables.bootstrapTable({
					url          : 'qingdong/customer/customer/get_receivables' + location.search,
					pk           : 'id',
					sortName     : 'id',
					toolbar:"#toolbar_receivables",
					operate      : false,
					commonSearch : false,
					search       : false,
					visible      : false,
					showColumns  : false,
					showExport   : false,
					showToggle   : false,
					columns      : [
						[
							{
								field : 'number', title : '回款编号', fixedColumns : true, formatter : function (value, row, index) {
									if(value ==''){
										value = '无';
									}
									if(Config.idinfo.operation_team =='update' || Config.idinfo.operation =='read'){
										return value;
									}
									return "<a href='javascript:void(0);' class='btn-dialog' " +
										"data-url='qingdong/customer/receivables/detail/ids/"+row.id+"' data-title='回款详情' >" + value + "</a>";
								},operate:'like'
							},
							{
								field : 'contract.name', title : '合同名称', fixedColumns : true, formatter : function (value, row, index) {
									if(value ==''){
										value = '无';
									}
									if(Config.idinfo.operation_team =='update' || Config.idinfo.operation =='read'){
										return value;
									}
									return "<a href='javascript:void(0);' class='btn-dialog' " +
										"data-url='qingdong/customer/contract/detail/ids/"+row.contract.id+"' data-title='合同详情' >" + value + "</a>";
								},operate:'like'
							},
							{field : 'money', title : '合同金额'},
							{field : 'money', title : '回款金额'},
							{field : 'plan.num', title : '期数'},
							{field : 'owner_staff.name', title : '负责人'},
							{
								field      : 'check_status', title : '状态', formatter : Table.api.formatter.status,
								searchList : {0 : '待审核', 1 : '审核中', 2 : '审核通过', 3 : '审核未通过', 4 : '撤销', 5 : '草稿(未提交)'}
							},
							{field : 'return_time', title : '回款日期'},
						]
					]
				});
				Table.api.bindevent(table_receivables);

			},
			//相关团队
			team: function () {
				var tableteam = $("#table_team");
				tableteam.bootstrapTable({
					url          : 'qingdong/customer/customer/getteam' + location.search,
					pk           : 'id',
					sortName     : 'id',
					toolbar:"#toolbar_team",
					operate      : false,
					commonSearch : false,
					search       : false,
					visible      : false,
					showColumns  : false,
					showExport   : false,
					showToggle   : false,
					columns      : [
						[
							{field : 'img', title : '头像', formatter: Table.api.formatter.image},
							{field : 'name', title : '员工姓名'},
							{field : 'post', title : '员工角色'},
							{field : 'roles', title : '团队角色'},
							{field : 'is_edit', title : '权限'},
							{field: 'operate', title: __('Operate'), table: tableteam, events: Table.api.events.operate, formatter: Table.api.formatter.buttons,
								buttons: [
									{
										text: __('修改'),
										title: __('修改'),
										extend:'data-area=["400px","300px"]',
										classname: 'btn-dialog',
										url: 'qingdong/customer/customer/editteam/customer_id/{customer_id}',
										visible: function (row) {
											if(row.roles == '负责人'){
												return false;
											}
											if(Config.idinfo.operation_team =='update' || Config.idinfo.operation =='read'){
												return false;
											}
											//返回true时按钮显示,返回false隐藏
											return true;
										}
									},
									{
										text: __('删除'),
										title: __('删除'),
										classname: 'btn-ajax',
										refresh:true,
										confirm: '确认发送',
										extend:'data-area=["80%","80%"]',
										url: 'qingdong/customer/customer/delteam/customer_id/{customer_id}/staff_id/{id}',
										visible: function (row) {
											if(row.roles == '负责人'){
												return false;
											}
											if(Config.idinfo.operation_team =='update'){
												return false;
											}
											//返回true时按钮显示,返回false隐藏
											return true;
										},
									},
								]
							}
						]
					]
				});
				// 为表格绑定事件
				Table.api.bindevent(tableteam);
			},
			//附件
			settings3: function () {
				var tablefiles = $("#files");
				tablefiles.bootstrapTable({
					url          : 'qingdong/customer/customer/get_file' + location.search,
					pk           : 'id',
					sortName     : 'id',
					toolbar:"#toolbar_files",
					operate      : false,
					commonSearch : false,
					search       : false,
					visible      : false,
					showColumns  : false,
					showExport   : false,
					showToggle   : false,
					columns      : [
						[
							{field : 'name', title : '附件名称'},
							{field : 'size', title : '附件大小'},
							{
								field : 'file_path', title : '文件预览', events : Table.api.events.image, formatter : Table.api.formatter.image, operate : false
							},
							{
								field : 'file_path', title : '下载',formatter : function (value, row, index) {
									if(value ==''){
										value = '无';
									}
									return "<a href='"+value+"' download='' >点击下载</a>";
								}
							},

						]
					]
				});
				// 为表格绑定事件
				Table.api.bindevent(tablefiles);
			},
			//操作日志
			settings4: function () {
				var table7 = $("#operation_log");
				// 初始化表格
				table7.bootstrapTable({
					url          : 'qingdong/customer/log/index?customer_id='+Config.idinfo.id,
					pk           : 'id',
					sortName     : 'id',
					toolbar:"#toolbar_log",
					operate      : false,
					commonSearch : false,
					search       : false,
					visible      : false,
					showColumns  : false,
					showExport   : false,
					showToggle   : false,
					columns      : [
						[
							{field: 'staff.name', title: __('员工')},
							{field: 'content', title: __('内容'),cellStyle:function (value,row,index,field) {
									return {
										css: {
											"min-width": "150px",
											"white-space": "nowrap",
											"text-overflow": "ellipsis",
											"overflow": "hidden",
											"max-width":"300px",
											'cursor':"pointer"
										}
									};
								},formatter:function (value,row,index,field) {
									var span=document.createElement('span');
									span.setAttribute('title',value);
									span.innerHTML = value;
									return span.outerHTML;
								}
							},
							{field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', formatter: Table.api.formatter.datetime},

						]
					]
				});
				Table.api.bindevent(table7);
			},
			//签到记录
			settings6: function () {
				var table8 = $("#sign");
				table8.bootstrapTable({
					url: 'qingdong/customer/customer/get_sign?customer_id=' + Config.idinfo.id,
					pk: 'id',
					sortName: 'id',
					toolbar: "#toolbar_sign",
					operate: false,
					commonSearch: false,
					search: false,
					visible: false,
					showColumns: false,
					showExport: false,
					showToggle: false,
					columns: [
						[
							{field: 'location', title: '地理位置', formatter: Controller.api.formatter.map, operate: false},

							{
								field: 'createtime',
								title: __('创建时间'),
								operate: 'RANGE',
								addclass: 'datetimerange',
								formatter: Table.api.formatter.datetime
							},

							{
								field: 'operate',
								title: __('Operate'),
								table: table8,
								events: Table.api.events.operate,
								formatter: Table.api.formatter.operate,
								buttons: [
									{
										name: 'detail',
										text: __('签到详情'),
										title: __('签到详情'),
										classname: 'records btn-dialog',
										extend: 'data-area=["90%","90%"]',
										url: 'qingdong/customer/customer/get_sign_detail',
										callback: function (data) {
											$('.btn-success').trigger('click');
										},

									}
								]
							}
						]
					]
				});
				Table.api.bindevent(table8);
			},
			//商机
			settings7: function () {
				var table = $("#business");
				// 初始化表格
				table.bootstrapTable({
					url          : 'qingdong/customer/customer/get_business?customer_id=' + Config.idinfo.id,
					pk           : 'id',
					sortName     : 'id',
					toolbar:"#toolbar_business",
					operate      : false,
					commonSearch : false,
					search       : false,
					visible      : false,
					showColumns  : false,
					showExport   : false,
					showToggle   : false,
					columns      : [
						[
							{
								field : 'name', title : '商机名称', fixedColumns : true, formatter : function (value, row, index) {
									if(value ==''){
										value = '无';
									}
									if(Config.idinfo.operation =='read'){
										return value;
									}
									return "<a href='javascript:void(0);' data-id='" + row.id + "' class='show-business-detail'>" + value + "</a>";
								}
							},
							{field : 'money', title : '商机金额'},
							{field : 'expect_time', title : '预计成交时间',operate: 'RANGE', addclass: 'datetimerange'},
							{field : 'next_time', title : '下次联系时间',operate: 'RANGE', addclass: 'datetimerange'},
							{field : 'type', title : '商机阶段', formatter : Table.api.formatter.status,
								searchList : {0 : '初期沟通', 1 : '立项跟踪', 2 : '方案/报价', 3 : '谈判审核', 4 : '赢单', 5 : '输单', 6 : '无效'}},
							{field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', formatter: Table.api.formatter.datetime},

						]
					]
				});
				Table.api.bindevent(table);
			},
		},
		detail : function (){
			// 初始化表格参数配置
			Table.api.init({});
			//绑定事件
			$('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
				var panel = $($(this).attr("href"));
				if (panel.length > 0) {
					Controller.tabledetail[panel.attr("id")].call(this);
					$(this).on('click', function (e) {
						$($(this).attr("href")).find(".btn-refresh").trigger("click");
					});
				}
				//移除绑定的事件
				$(this).unbind('shown.bs.tab');
			});

			//必须默认触发shown.bs.tab事件
			$('ul.nav-tabs li.active a[data-toggle="tab"]').trigger("shown.bs.tab");
			$(document).on('click', '.show-business-detail', function (data) {
				var area = [$(window).width() > 1200 ? '1200px' : '95%', $(window).height() > 800 ? '800px' : '95%'];
				var options = {
					shadeClose : false,
					shade      : [0.3, '#393D49'],
					area       : area,
					callback   : function (value) {
						//在回调函数里可以调用你的业务代码实现前端的各种逻辑和效果
						console.log(value);
					}
				};
				Fast.api.open("qingdong/customer/business/detail?ids=" + $(this).data('id'), '商机', options);
			}).on('click','.btn-edit',function () {
				var id=$('#ids').val();
				Fast.api.open("qingdong/customer/customer/edit?ids="+id, "客户编辑",{
					shadeClose: false,
					shade: false,
					maxmin: false,
					moveOut: false,
					scrollbars:false,
					callback:function () {
						location.reload()
					}
				});

			}).on('click',".btn-del",function(){//删除
				var id=$('#ids').val();
				Layer.confirm('确定删除当前客户吗？', {
					btn: ['确定','取消'],
					title: '提示',
				},function(index, layero){
					Fast.api.ajax("qingdong/customer/customer/del?ids="+id,function(data,ret){
						if(ret.code == 1){
							Layer.close(index);
							parent.location.reload();
						}
					},function(data,ret){
					});

				});
			}).on('click',".btn-seas",function(){//放入公海
				var id=$('#ids').val();
				Layer.confirm('确定将当前客户放入公海吗？', {
					btn: ['确定','取消'],
					title: '提示',
				},function(index, layero){
					Fast.api.ajax("qingdong/customer/customer/seas?ids="+id,function(data,ret){
						if(ret.code == 1){
							Layer.close(index);
							parent.location.reload();
						}
					},function(data,ret){
					});

				});
			}).on('click',".btn-change",function(){//转移客户
				var id=$('#ids').val();
				Fast.api.open("qingdong/customer/customer/change?ids="+id, "转移客户",{
					shadeClose: false,
					shade: false,
					maxmin: false,
					moveOut: false,
					scrollbars:false,
					area:["400px","350px"],
					callback:function () {
						location.reload()
					}
				});
			}).on('click','.btn-send-email',function () {
				var $this=$(this);
				var ids=$('#ids').val();
				var url=$this.data('url'),title=$this.data('title'),types=$this.data('types');
				Fast.api.open(url+"?ids="+ids+"&type="+types, title,{
					shadeClose: false,
					shade: false,
					maxmin: false,
					moveOut: false,
					scrollbars:false,
					'area':[
						$(window).width() > 800 ? '600px' : '400px',
						$(window).height() > 600 ? '500px' : '400px'
					],
					callback:function(value){
						Form.events.plupload("#plupload-local");
						//   在这里可以接收弹出层中使用`Fast.api.close(data)`进行回传数据
					},
				});
			}).on('click','.btn-send-sms',function () {
				var $this=$(this);
				var ids=$('#ids').val();
				var url=$this.data('url'),title=$this.data('title'),types=$this.data('types');
				Fast.api.open(url+"?ids="+ids+"&type="+types, title,{
					shadeClose: false,
					shade: false,
					maxmin: false,
					moveOut: false,
					scrollbars:false,
					'area':[
						$(window).width() > 800 ? '600px' : '400px',
						$(window).height() > 600 ? '500px' : '400px'
					],
					callback:function(value){
						Form.events.plupload("#plupload-local");
						//   在这里可以接收弹出层中使用`Fast.api.close(data)`进行回传数据
					},
				});
			}).on('click', '.btn-word', function (data) {
				//导出word
				var area = [$(window).width() > 1200 ? '1200px' : '70%', $(window).height() > 800 ? '800px' : '80%'];
				var options = {
					shadeClose : false,
					shade      : [0.3, '#393D49'],
					area       : area,
					callback   : function (value) {
						//在回调函数里可以调用你的业务代码实现前端的各种逻辑和效果
						window.location.href = value.file;
					}
				};
				Fast.api.open("qingdong/customer/customer/words?ids=" + $(this).data('id'), '导出word', options);
			});
			Controller.api.bindevent();
		},

		api    : {
			bindevent : function () {
				$("#fachoose-map").on('click', function () {
					parent.Fast.api.open("qingdong/customer/customer/map",'地理位置选择', {
						area: ['500px', '600px'],
						callback: function (data) {
							console.log(data);
							$('#lng').val(data.lng)
							$('#lat').val(data.lat)
							$('#address').val(data.address)
							$('#address_text').val(data.address)
							$('#address_detail').val(data.address_detail)
						}
					});
					return false;
				});

				Form.api.bindevent($("form[role=form]"), function(data, ret){

					//这里是表单提交处理成功后的回调函数，接收来自php的返回数据
					Fast.api.close(data);//这里是重点
				});
			},
			formatter : {
				thumb : function (value, row, index) {
					var length = row.file_ids.length;
					var html='';
					for(var i=0;i<length;i++){
						html += '<a href="' + row.file_ids[i] + '" target="_blank"><img src="' + row.file_ids[i] + '" data-tips-image alt="" title="签到图" style="max-height:90px;max-width:120px;margin-right:10px;"></a>';

					}
					return html;
				},
				map:function(value,row,index){
					return '<a href="https://apis.map.qq.com/uri/v1/marker?marker=coord:'+row.lat+','+row.lng+';title:'+row.location+';addr:'+row.location+'&referer=crm" target="_blank" >'+row.location+'</a>';
				}

			}
		}
	};
	return Controller;
});