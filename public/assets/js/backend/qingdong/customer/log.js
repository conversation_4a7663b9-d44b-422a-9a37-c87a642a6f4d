define(['jquery', 'bootstrap', 'backend', 'form', 'table'], function ($, undefined, Backend, Form, Table) {

    var Controller = {
        index: function () {

            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'qingdong/customer/log/index',
                    table: 'log'
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                sortName: 'id',
                columns: [
                    [
                        {field: 'state', checkbox: true},
                        {field: 'staff.name', title: __('员工'),operate:false},
                        {field: 'content', title: __('跟进内容')},
                        {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', formatter: Table.api.formatter.datetime},

                    ]
                ],
                //启用普通表单搜索
                commonSearch: true,
                searchFormVisible: true,
                onLoadSuccess:function(){
                    // 这里就是数据渲染结束后的回调函数
                    $(".btn-add").data("area",["80%","80%"]);
                    $(".btn-edit").data("area",["80%","80%"]);
                }
            });
            // 为表格绑定事件
            Table.api.bindevent(table);
            $('.search').hide();
            $('.btn-import').hide();

        },
        add: function () {
            Controller.api.bindevent();

        },

        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            },
            formatter: {

            }
        }

    };
    return Controller;
});