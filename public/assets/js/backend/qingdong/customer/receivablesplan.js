define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            Table.api.init();

            //绑定事件
            $('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
                var panel = $($(this).attr("href"));
                if (panel.length > 0) {
                    Controller.table[panel.attr("id")].call(this);
                    $(this).on('click', function (e) {
                        $($(this).attr("href")).find(".btn-refresh").trigger("click");
                    });
                }
                //移除绑定的事件
                $(this).unbind('shown.bs.tab');
            });

            //必须默认触发shown.bs.tab事件
            $('ul.nav-tabs li.active a[data-toggle="tab"]').trigger("shown.bs.tab");

            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'qingdong/customer/receivablesplan/index?type=0',
                    add_url: 'qingdong/customer/receivablesplan/add',
                    edit_url: 'qingdong/customer/receivablesplan/edit',
                    del_url: 'qingdong/customer/receivablesplan/del',
                    multi_url: 'qingdong/customer/receivablesplan/multi',
                    import_url: 'qingdong/customer/receivablesplan/import',
                    customer_url : 'qingdong/customer/customer/detail',
                    contract_url : 'qingdong/customer/contract/detail',
                    table: 'qingdong_receivables_plan',
                }
            });
            $(document).on('click', '.show-customer', function (data) {
                var area = [$(window).width() > 1200 ? '1200px' : '95%', $(window).height() > 800 ? '800px' : '95%'];
                var options = {
                    shadeClose : false,
                    shade      : [0.3, '#393D49'],
                    area       : area,
                    callback   : function (value) {
                        //在回调函数里可以调用你的业务代码实现前端的各种逻辑和效果
                        console.log(value);
                    }
                };
                Fast.api.open($.fn.bootstrapTable.defaults.extend.customer_url + "?ids=" + $(this).data('id'), '客户详情', options);
            }).on('click', '.show-contract', function (data) {
                var area = [$(window).width() > 1200 ? '1200px' : '95%', $(window).height() > 800 ? '800px' : '95%'];
                var options = {
                    shadeClose : false,
                    shade      : [0.3, '#393D49'],
                    area       : area,
                    callback   : function (value) {
                        //在回调函数里可以调用你的业务代码实现前端的各种逻辑和效果
                        console.log(value);
                    }
                };
                Fast.api.open($.fn.bootstrapTable.defaults.extend.contract_url + "?ids=" + $(this).data('id'), '合同详情', options);
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        tableinfo:{
            url: '',
            pk: 'id',
            toolbar: '#toolbar',
            sortName: 'id',
            columns: [
                [
                    {checkbox: true},
                    {field: 'num', title: __('期数'), operate: 'LIKE'},
                    {field: 'status', title: __('回款状态'),formatter: Table.api.formatter.status, searchList:{0: __('进行中'), 1: __('已完成'), 2: __('已逾期')}},
                    {field: 'contract_id', title: __('合同'), operate:false, formatter : function (value, row, index) {
                            if(row.contract){
                                return "<a href='javascript:void(0);' data-id='" + row.contract.id + "' class='show-contract'>" + row.contract.name + "</a>";
                            }
                            return '';
                        }},
                    {field: 'customer_id', title: __('客户'), operate:false, formatter : function (value, row, index) {
                            if(row.customer){
                                return "<a href='javascript:void(0);' data-id='" + row.customer.id + "' class='show-customer'>" + row.customer.name + "</a>";
                            }
                            return '';
                        }},
                    {field: 'money', title: __('回款金额'), operate:'BETWEEN'},
                    {field: 'return_date', title: __('回款日期'), operate:'RANGE', addclass:'datetimerange', autocomplete:false},
                    {field: 'return_type', title: __('回款方式'), operate: 'LIKE'},
                    {field: 'remind', title: __('提前几天提醒')},
                    {field: 'remind_date', title: __('提醒日期'), operate:'RANGE', addclass:'datetimerange', autocomplete:false},
                    {field: 'createtime', title: __('创建时间'), operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                    {field: 'remarks', title: __('备注'), operate:false},
                    {field: 'operate', title: __('Operate'),  events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                ]
            ],
            //启用普通表单搜索
            search:false,
            commonSearch: true,
            searchFormVisible: true,
            onLoadSuccess:function(){
                // 这里就是数据渲染结束后的回调函数
                $('.btn-editone').html('编辑');
                $('.fa-pencil').remove();
                $('.btn-delone').html('删除');
                $('.fa-trash').remove();
                $('.btn-editone').removeClass('btn-success')
                $('.btn-editone').removeClass('btn')
                $('.btn-delone').removeClass('btn-danger')
                $('.btn-delone').removeClass('btn')
            }
        },
        table: {
            first: function () {

                // 初始化表格参数配置
                Table.api.init({});

                var table = $("#table");
                Controller.tableinfo.url=location.href+'&type=0';
                Controller.tableinfo.toolbar='#toolbar';
                Controller.tableinfo.columns[0][Controller.tableinfo.columns[0].length-1].table=table;

                // 初始化表格
                table.bootstrapTable(Controller.tableinfo);

                // 为表格绑定事件
                Table.api.bindevent(table);

            },
            second: function () {
                // 初始化表格参数配置
                Table.api.init({});

                var table = $("#table1");
                Controller.tableinfo.url=location.href+'&type=1';
                Controller.tableinfo.toolbar='#toolbar1';
                Controller.tableinfo.columns[0][Controller.tableinfo.columns[0].length-1].table=table;

                // 初始化表格
                table.bootstrapTable(Controller.tableinfo);

                // 为表格绑定事件
                Table.api.bindevent(table);
            },
            third: function () {
                // 初始化表格参数配置
                Table.api.init({});

                var table = $("#table2");
                Controller.tableinfo.url=location.href+'&type=2';
                Controller.tableinfo.toolbar='#toolbar2';
                Controller.tableinfo.columns[0][Controller.tableinfo.columns[0].length-1].table=table;

                // 初始化表格
                table.bootstrapTable(Controller.tableinfo);

                // 为表格绑定事件
                Table.api.bindevent(table);
            },
        },
        recyclebin: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    'dragsort_url': ''
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: 'qingdong/customer/receivablesplan/recyclebin' + location.search,
                pk: 'id',
                sortName: 'id',
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {
                            field: 'deletetime',
                            title: __('Deletetime'),
                            operate: 'RANGE',
                            addclass: 'datetimerange',
                            formatter: Table.api.formatter.datetime
                        },
                        {
                            field: 'operate',
                            width: '130px',
                            title: __('Operate'),
                            table: table,
                            events: Table.api.events.operate,
                            buttons: [
                                {
                                    name: 'Restore',
                                    text: __('Restore'),
                                    classname: 'btn btn-xs btn-info btn-ajax btn-restoreit',
                                    icon: 'fa fa-rotate-left',
                                    url: 'qingdong/customer/receivablesplan/restore',
                                    refresh: true
                                },
                                {
                                    name: 'Destroy',
                                    text: __('Destroy'),
                                    classname: 'btn btn-xs btn-danger btn-ajax btn-destroyit',
                                    icon: 'fa fa-times',
                                    url: 'qingdong/customer/receivablesplan/destroy',
                                    refresh: true
                                }
                            ],
                            formatter: Table.api.formatter.operate
                        }
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },

        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                $(document).on("click", ".select-customer", function(){
                    var $this=$(this);
                    top.Fast.api.open($(this).data("url") , __('Select'), {callback: function (data) {
                            $this.html(data.name);
                            $('[name="'+$this.data('name')+'"]').val(data.id);
                        },area:["100%", "100%"]});
                }).on("click", ".select-contract", function(){
                    var $this=$(this),customer_id=$('[name="row[customer_id]"]').val();
                    if(!customer_id){
                        Layer.msg('请先选择客户');
                        return false;
                    }
                    top.Fast.api.open($(this).data("url")+"&customer_id="+customer_id+"&check_status=2" , __('Select'), {callback: function (data) {
                            $this.html(data.name);
                            $('[name="'+$this.data('name')+'"]').val(data.id);
                        },area:["100%", "100%"]});
                });
                $("#c-contract_id").data("params", function (obj) {
                    var customer_id=$('#c-customer_id').val();
                    return {customer_id: customer_id};
                });

                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});