define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

	var Controller = {
		index  : function () {
			// 初始化表格参数配置
			Table.api.init({

			});

			//绑定事件
			$('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
				var panel = $($(this).attr("href"));
				if (panel.length > 0) {
					Controller.table[panel.attr("id")].call(this);
					$(this).on('click', function (e) {
						$($(this).attr("href")).find(".btn-refresh").trigger("click");
					});
				}
				//移除绑定的事件
				$(this).unbind('shown.bs.tab');
			});

			//必须默认触发shown.bs.tab事件
			$('ul.nav-tabs li.active a[data-toggle="tab"]').trigger("shown.bs.tab");
			$(document).on('click', '.show-detail', function (data) {
				var area = [$(window).width() > 1200 ? '1200px' : '95%', $(window).height() > 800 ? '800px' : '95%'];
				var options = {
					shadeClose : false,
					shade      : [0.3, '#393D49'],
					area       : area,
					callback   : function (value) {
						//在回调函数里可以调用你的业务代码实现前端的各种逻辑和效果
						console.log(value);
					}
				};
				Fast.api.open($.fn.bootstrapTable.defaults.extend.detail_url + "?ids=" + $(this).data('id'), '线索详情', options);
			});
			//导入
			$(document).on('click','.btn-imports',function () {
				Fast.api.open("qingdong/customer/leads/import", "线索导入",{
					shadeClose: false,
					shade: false,
					maxmin: false,
					moveOut: false,
					scrollbars:false,
					'area':[
						$(window).width() > 800 ? '600px' : '400px',
						$(window).height() > 600 ? '500px' : '400px'
					],
					callback:function(value){
						Form.events.plupload("#plupload-local");

						//   在这里可以接收弹出层中使用`Fast.api.close(data)`进行回传数据
					},
				});

			});
			//导出
			$(document).on("click", ".btn-export", function () {

				var tab_id=$('.active.in').attr('id');
				var table;
				switch (tab_id){
					case 'first'://全部
						table=$('#table');
						break;
					case 'second'://我负责的
						table=$('#table1');
						break;
					case 'third'://下属负责的
						table=$('#table2');
						break;
					case 'four'://今日待跟进
						table=$('#table3');
						break;
					case 'five'://今日已跟进
						table=$('#table4');
						break;
					case 'six'://从未跟进的
						table=$('#table5');
						break;
				}

				var ids = Table.api.selectedids(table);
				var page = table.bootstrapTable('getData');
				var options = table.bootstrapTable('getOptions');
				var all = options.totalRows;
				var params=options.queryParams({});
				var sort = options.sortName+' '+options.sortOrder;
				var filter = params.filter;
				var op = params.op;
				Layer.confirm("请选择导出的选项", {
					title   : '导出数据',
					btn     : ["选中项(" + ids.length + "条)", "本页(" + page.length + "条)", "全部(" + all + "条)"],
					success : function (layero, index) {
						$(".layui-layer-btn a", layero).addClass("layui-layer-btn0");
					}, yes   : function (index, layero) {
						if (ids.length == 0) {
							Layer.alert('数据为空');
							return true;
						}
						$.post('qingdong/customer/leads/export', {sort:sort,ids : ids.join(","), isall : 1}, function (res) {
							Layer.alert(res.msg);
							window.location.href = res.data.filePath;
							Layer.close(index);
							table.bootstrapTable('refresh');
						}, 'json');
						return false;
					},
					btn2    : function (index, layero) {
						if (page.length == 0) {
							Layer.alert('数据为空');
							return true;
						}
						var ids = [];
						$.each(page, function (i, j) {
							ids.push(j.id);
						});
						$.post('qingdong/customer/leads/export', {sort:sort,ids : ids.join(","), isall : 2}, function (res) {
							Layer.alert(res.msg);
							window.location.href = res.data.filePath;
							Layer.close(index);
							table.bootstrapTable('refresh');
						}, 'json');
						return false;
					},
					btn3    : function (index, layero) {
						if (page.length == 0) {
							Layer.alert('数据为空');
							return true;
						}
						$.post('qingdong/customer/leads/export?filter='+filter+'&op='+op, {sort:sort,ids : ids.join(","), isall : 3}, function (res) {
							Layer.alert(res.msg);
							window.location.href = res.data.filePath;
							Layer.close(index);
							table.bootstrapTable('refresh');
						}, 'json');
						return false;
					}
				})
			});

		},
		tableinfo:{
			toolbar: '#toolbar',
			pk: 'id',
			sortName: 'id',
			fixedColumns: true,
			fixedNumber: 2,
			fixedRightNumber: 1,
			search: false,
			searchFormVisible: true,
			columns: [
				[
					{checkbox: true},

					{
						field: 'name',
						title: '线索名称',
						fixedColumns: true,
						formatter: function (value, row, index) {
							if (value == '') {
								value = '无';
							}
							return "<a href='javascript:void(0);' data-id='" + row.id + "' class='show-detail'>" + value + "</a>";
						}, operate: "like"
					},
					{
						field: 'source', title: '线索来源', fixedColumns: true, operate: "like"
					},
					{field: 'owner_staff.name', title: '负责人', operate: false},
					{field: 'telephone', title: '电话', operate: "like"},
					{field: 'mobile', title: '手机', operate: "like"},
					{field: 'industry', title: '客户行业'},
					{field: 'level', title: '客户星级', operate: false},
					{field: 'address_detail', title: '地址', operate: false},
					{field: 'next_time', title: '下次联系时间', operate: 'RANGE',addclass: 'datetimerange',autocomplete:false,
						formatter: Table.api.formatter.datetime},
					{field: 'remarks', title: '备注信息', operate: false},
					{
						field: 'createtime',
						title: __('Createtime'),
						operate: 'RANGE',
						addclass: 'datetimerange',autocomplete:false,
						formatter: Table.api.formatter.datetime
					},
					{field: 'updatetime', title: '更新时间', operate: false},
					{
						field: 'operate2', title: __('Operate'),operate: false, events: {
							'click .btn-chooseone': function (e, value, row, index) {
								Fast.api.close({id: row.id, name: row.name});
							},
						}, formatter: function () {
							return '<a href="javascript:;" class="btn btn-danger btn-chooseone btn-xs"><i class="fa fa-check"></i> ' + __('Choose') + '</a>';
						}},
					{
						field: 'operate',
						title: __('Operate'),
						events: Table.api.events.operate,
						formatter: Table.api.formatter.buttons,
						buttons: [
							{
								name: 'edit',
								text: __('编辑'),
								title: __('编辑'),
								extend: 'data-area=["80%","80%"]',
								classname: 'records btn-dialog',
								url: 'qingdong/customer/leads/edit',
								visible: function (row) {
									//返回true时按钮显示,返回false隐藏
									return true;
								}
							},
							{
								name: 'detail',
								text: __('跟进'),
								title: __('跟进'),
								extend: 'data-area=["80%","80%"]',
								classname: 'records btn-dialog',
								url: 'qingdong/leads/record/add',
								visible: function (row) {
									//返回true时按钮显示,返回false隐藏
									return true;
								}
							}
						]
					}
				]
			],
			onLoadSuccess: function () {
				// 这里就是数据渲染结束后的回调函数
				$(".btn-add").data("area", ["80%", "80%"]);
				$(".btn-edit").data("area", ["80%", "80%"]);
			}
		},
		table: {
			first : function() {
				// 初始化表格参数配置
				Table.api.init({
					extend : {
						add_url    : 'qingdong/customer/leads/add',
						detail_url    : 'qingdong/customer/leads/detail',
						table      : 'lead',
					}
				});
				if(Config.fields !='[]'){
					if (Config.fields && Object.keys(Config.fields).length > 0) {
						var fields = JSON.parse(Config.fields);
						var start = Controller.tableinfo.columns[0].length-2;
						for (var i = 0; i < fields.length; i++) {
							if (fields[i].hasOwnProperty('formatter'))
								fields[i].formatter = eval(fields[i].formatter);
							if (fields[i].hasOwnProperty('events'))
								fields[i].events = eval(fields[i].events);
							if (fields[i].hasOwnProperty('searchList'))
								fields[i].searchList = JSON.parse(fields[i].searchList);
							Controller.tableinfo.columns[0].splice(start + i, 0, fields[i]);
						}
					}
				}


				const q = {};
				location.search.replace(/([^?&=]+)=([^&]+)/g,function(_,k,v){q[k]=v});
				var table = $("#table");
				Controller.tableinfo.url=location.href+'&type=0';
				Controller.tableinfo.toolbar='#toolbar';
				Controller.tableinfo.columns[0][Controller.tableinfo.columns[0].length-1].table=table;
				if (q.isselect == 1) {
					Controller.tableinfo.columns[0][Controller.tableinfo.columns[0].length - 1].visible = false;
				} else {
					Controller.tableinfo.columns[0][Controller.tableinfo.columns[0].length - 2].visible = false;
				}
				// 初始化表格
				table.bootstrapTable(Controller.tableinfo);

				// 为表格绑定事件
				Table.api.bindevent(table);
			},
			second  : function () {
				var table = $("#table1");
				Controller.tableinfo.url=location.href+'&type=1';
				Controller.tableinfo.toolbar='#toolbar1';
				Controller.tableinfo.columns[0][Controller.tableinfo.columns[0].length-1].table=table;

				// 初始化表格
				table.bootstrapTable(Controller.tableinfo);
				// 为表格绑定事件
				Table.api.bindevent(table);
			},
			third  : function () {

				var table = $("#table2");
				Controller.tableinfo.url=location.href+'&type=2';
				Controller.tableinfo.toolbar='#toolbar2';
				Controller.tableinfo.columns[0][Controller.tableinfo.columns[0].length-1].table=table;
				// 初始化表格
				table.bootstrapTable(Controller.tableinfo);
				// 为表格绑定事件
				Table.api.bindevent(table);
			},
			four  : function () {

				var table = $("#table3");
				Controller.tableinfo.url=location.href+'&type=3';
				Controller.tableinfo.toolbar='#toolbar3';
				Controller.tableinfo.columns[0][Controller.tableinfo.columns[0].length-1].table=table;

				// 初始化表格
				table.bootstrapTable(Controller.tableinfo);
				// 为表格绑定事件
				Table.api.bindevent(table);
			},
			five  : function () {
				var table = $("#table4");
				Controller.tableinfo.url=location.href+'&type=4';
				Controller.tableinfo.toolbar='#toolbar4';
				Controller.tableinfo.columns[0][Controller.tableinfo.columns[0].length-1].table=table;

				// 初始化表格
				table.bootstrapTable(Controller.tableinfo);
				// 为表格绑定事件
				Table.api.bindevent(table);
			},
			six  : function () {
				var table = $("#table5");
				Controller.tableinfo.url=location.href+'&type=5';
				Controller.tableinfo.toolbar='#toolbar5';
				Controller.tableinfo.columns[0][Controller.tableinfo.columns[0].length-1].table=table;

				// 初始化表格
				table.bootstrapTable(Controller.tableinfo);
				// 为表格绑定事件
				Table.api.bindevent(table);
			},
		},
		add    : function () {
			Controller.api.bindevent();
		},
		edit    : function () {
			Controller.api.bindevent();
		},
		detail : function () {
			// 初始化表格参数配置
			Table.api.init({});
			//跟进记录
			var table1 = $("#records");
			// 初始化表格
			table1.bootstrapTable({
				url          : 'qingdong/customer/record/index?leads_id='+Config.idinfo.id,
				pk           : 'id',
				sortName     : 'id',
				operate      : false,
				commonSearch : false,
				search       : false,
				visible      : false,
				showColumns  : false,
				showExport   : false,
				showToggle   : false,
				columns      : [
					[
						{field: 'follow_type', title: __('跟进类型')},
						{field: 'follow_time', title: __('跟进时间')},
						{field: 'follow', title: __('跟进状态')},
						{field: 'content', title: __('跟进内容'),cellStyle:function (value,row,index,field) {
								return {
									css: {
										"min-width": "150px",
										"white-space": "nowrap",
										"text-overflow": "ellipsis",
										"overflow": "hidden",
										"max-width":"300px",
										'cursor':"pointer"
									}
								};
							},formatter:function (value,row,index,field) {
								var span=document.createElement('span');
								span.setAttribute('title',value);
								span.innerHTML = value;
								return span.outerHTML;
							}
						},
						{field: 'next_time', title: __('下次跟进时间')},
						{
							field     : 'operate',
							title     : __('Operate'),
							table     : table1,
							events    : Table.api.events.operate,
							formatter : Table.api.formatter.operate,
							buttons   : [
								{
									name      : '详情',
									text      : __('详情'),
									classname : 'records btn-dialog',
									url       : 'qingdong/customer/record/detail',
									callback: function (data) {
										$('.btn-success').trigger('click');
									},

								}
							]
						}
					]
				]
			});
			//操作日志
			var table2 = $("#operation_log");
			// 初始化表格
			table2.bootstrapTable({
				url          : 'qingdong/customer/log/index?leads_id='+Config.idinfo.id,
				pk           : 'id',
				sortName     : 'id',
				operate      : false,
				commonSearch : false,
				search       : false,
				visible      : false,
				showColumns  : false,
				showExport   : false,
				showToggle   : false,
				columns      : [
					[
						{field: 'staff.name', title: __('员工')},
						{field: 'content', title: __('内容'),cellStyle:function (value,row,index,field) {
								return {
									css: {
										"min-width": "150px",
										"white-space": "nowrap",
										"text-overflow": "ellipsis",
										"overflow": "hidden",
										"max-width":"300px",
										'cursor':"pointer"
									}
								};
							},formatter:function (value,row,index,field) {
								var span=document.createElement('span');
								span.setAttribute('title',value);
								span.innerHTML = value;
								return span.outerHTML;
							}
						},
						{field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', formatter: Table.api.formatter.datetime},

					]
				]
			});
			//附件
			$('#files').bootstrapTable({
				url          : 'qingdong/customer/leads/get_file' + location.search,
				pk           : 'id',
				sortName     : 'id',
				operate      : false,
				commonSearch : false,
				search       : false,
				visible      : false,
				showColumns  : false,
				showExport   : false,
				showToggle   : false,
				columns      : [
					[
						{field : 'name', title : '附件名称'},
						{field : 'size', title : '附件大小'},
						{field : 'staff.name', title : '上传人'},
						{
							field : 'file_path', title : '文件预览', events : Table.api.events.image, formatter : Table.api.formatter.image, operate : false
						},
					]
				]
			});
			//编辑
			$(document).on('click','.btn-edit',function () {
				var id=$('#ids').val();
				Fast.api.open("qingdong/customer/leads/edit?ids="+id, "线索编辑",{
					shadeClose: false,
					shade: false,
					maxmin: false,
					moveOut: false,
					scrollbars:false,
					callback:function () {
						location.reload()
					}
				});

			}).on('click',".btn-del",function(){//删除
				var id=$('#ids').val();
				Layer.confirm('确定删除当前线索吗？', {
					btn: ['确定','取消'],
					title: '提示',
				},function(index, layero){
					Fast.api.ajax("qingdong/customer/leads/del?ids="+id,function(data,ret){
						if(ret.code == 1){
							Layer.close(index);
							parent.location.reload();
						}
					},function(data,ret){
					});

				});
			}).on('click',".btn-convert-customer",function(){//转为客户
				var id=$('#ids').val();
				Layer.confirm('确认要将当前线索转为客户？', {
					btn: ['确定','取消'],
					title: '提示',
				},function(index, layero){
					Fast.api.ajax("qingdong/customer/leads/convert_customer?ids="+id,function(data,ret){
						if(ret.code == 1){
							Layer.close(index);
							parent.location.reload();
						}
					},function(data,ret){
					});

				});
			}).on('click',".btn-convert-leadpool",function(){//放入线索池
				var id=$('#ids').val();
				Layer.confirm('确认要将当前线索放入线索池？', {
					btn: ['确定','取消'],
					title: '提示',
				},function(index, layero){
					Fast.api.ajax("qingdong/customer/leads/movepool?ids="+id,function(data,ret){
						if(ret.code == 1){
							Layer.close(index);
							parent.location.reload();
						}
					},function(data,ret){
					});

				});
			});

			Controller.api.bindevent();
		},
		import    : function () {
			Controller.api.bindevent();
		},
		transfer    : function () {
			Controller.api.bindevent();
		},
		api    : {
			bindevent : function () {
				Form.api.bindevent($("form[role=form]"), function(data, ret){
					//这里是表单提交处理成功后的回调函数，接收来自php的返回数据
					Fast.api.close(data);//这里是重点
				});
			}
		}
	};
	return Controller;
});