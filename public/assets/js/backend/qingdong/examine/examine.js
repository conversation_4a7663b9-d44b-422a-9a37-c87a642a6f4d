define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {
    $.post("qingdong/customer/need/get_need_number",function (data){
        top.window.Backend.api.sidebar({
            'qingdong/customer/need/index':[data.data.number, 'red', 'badge'],
            'qingdong/examine/examine/index':[data.data.examine, 'red', 'badge'], //合同
            'qingdong/examine/examine/consume':[data.data.consume, 'red', 'badge'],
            'qingdong/examine/examine/receivables':[data.data.receivables, 'red', 'badge'],
            'qingdong/examine/examine/achievement':[data.data.achievement, 'red', 'badge'],
            'qingdong/examine/examine/work':[data.data.approval, 'red', 'badge'],
            'qingdong/canlendar/canlendar':[data.data.eventOne, 'red', 'badge'],
        });
    },'json');

    var Controller = {

        index  : function () {
            // 初始化表格参数配置
            Table.api.init();

            //绑定事件
            $('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
                var panel = $($(this).attr("href"));
                if (panel.length > 0) {
                    Controller.table[panel.attr("id")].call(this);
                    $(this).on('click', function (e) {
                        $($(this).attr("href")).find(".btn-refresh").trigger("click");
                    });
                }
                //移除绑定的事件
                $(this).unbind('shown.bs.tab');
            });

            //必须默认触发shown.bs.tab事件
            $('ul.nav-tabs li.active a[data-toggle="tab"]').trigger("shown.bs.tab");


        },
        table: {
            first: function () {
                // 初始化表格参数配置
                Table.api.init({
                    extend: {
                        index_url: 'qingdong/examine/examine/index?status=0',
                        table: 'contact'
                    }
                });
                // 合同
                var table1 = $("#table");
                table1.bootstrapTable({
                    url: $.fn.bootstrapTable.defaults.extend.index_url,
                    toolbar: '#toolbar',
                    sortName: 'id',
                    search: false,
                    columns: [
                        [
                            {field: 'state', checkbox: true, },
                            {field : 'contract.owner_staff.name', title : __('提交人'), operate: false},

                            {
                                field : 'contract.name', title : '合同名称', fixedColumns : true, formatter : function (value, row, index) {
                                    if(row.contract){
                                        return "<a href='javascript:void(0);' data-id='" + row.contract.id + "' class='show-contract'>" + value + "</a>";
                                    }
                                    return '';
                                },operate:false
                            },
                            {field: 'contract.name', title: '合同名称', operate: false},
                            {field: 'check_staff.name', title: __('审批人'), operate: false},
                            {field: 'status', title: __('审批状态'), operate: false, formatter: Table.api.formatter.status,  searchList: {0: __('待审批'), 1: __('审批通过'), 2: __('审批拒绝'), 3: __('撤销')}},
                            {field: 'content', title: __('审批内容')},
                            {field: 'check_time', title: __('审批时间'), formatter: Table.api.formatter.datetime, operate: 'RANGE', addclass: 'datetimerange'},
                            {field: 'createtime', title: __('创建时间'), formatter: Table.api.formatter.datetime, operate: 'RANGE', addclass: 'datetimerange'},
                            {field: 'operate', title: __('Operate'), table: table1, events: Table.api.events.operate, formatter: Table.api.formatter.buttons,
                                buttons: [
                                    {
                                        name: 'success',
                                        text: __('通过'),
                                        title: __('通过'),
                                        classname: 'records btn-dialog',
                                        area:'',
                                        refresh:true,
                                        url: 'qingdong/examine/examine_record/examine?relation_type=contract&relation_id={relation_id}&status=1',
                                        visible: function (row) {
                                            //返回true时按钮显示,返回false隐藏
                                            return true;
                                        },
                                        success:function(data){
                                            $.post("qingdong/customer/need/get_need_number",function (data){
                                                top.window.Backend.api.sidebar({
                                                    'qingdong/customer/need/index':[data.data.number, 'red', 'badge'],
                                                    'qingdong/examine/examine/index':[data.data.examine, 'red', 'badge'], //合同
                                                    'qingdong/examine/examine/consume':[data.data.consume, 'red', 'badge'],
                                                    'qingdong/examine/examine/receivables':[data.data.receivables, 'red', 'badge'],
                                                    'qingdong/examine/examine/achievement':[data.data.achievement, 'red', 'badge'],
                                                    'qingdong/examine/examine/work':[data.data.approval, 'red', 'badge'],
                                                    'qingdong/canlendar/canlendar':[data.data.eventOne, 'red', 'badge'],
                                                });
                                            },'json');
                                            $(".btn-refresh").trigger("click");
                                        }
                                    },
                                    {
                                        name: 'error',
                                        text: __('拒绝'),
                                        title: __('拒绝'),
                                        classname: 'records btn-dialog',
                                        extend: ' data-area=\'\'',
                                        refresh:true,
                                        url: 'qingdong/examine/examine_record/examine?relation_type=contract&relation_id={relation_id}&status=2',
                                        error: function (data, ret) {
                                            console.log(data, ret);
                                            Layer.alert(ret.msg);
                                            return false;
                                        },
                                        success:function(data){
                                            $.post("qingdong/customer/need/get_need_number",function (data){
                                                top.window.Backend.api.sidebar({
                                                    'qingdong/customer/need/index':[data.data.number, 'red', 'badge'],
                                                    'qingdong/examine/examine/index':[data.data.examine, 'red', 'badge'], //合同
                                                    'qingdong/examine/examine/consume':[data.data.consume, 'red', 'badge'],
                                                    'qingdong/examine/examine/receivables':[data.data.receivables, 'red', 'badge'],
                                                    'qingdong/examine/examine/achievement':[data.data.achievement, 'red', 'badge'],
                                                    'qingdong/examine/examine/work':[data.data.approval, 'red', 'badge'],
                                                    'qingdong/canlendar/canlendar':[data.data.eventOne, 'red', 'badge'],
                                                });
                                            },'json');
                                            $(".btn-refresh").trigger("click");
                                        }
                                    },
                                ]
                            }
                        ]
                    ],
                    //启用普通表单搜索
                    commonSearch: true,
                    searchFormVisible: true,
                });
                $(document).on('click', '.show-contract', function (data) {
                    var area = [$(window).width() > 1200 ? '1200px' : '95%', $(window).height() > 800 ? '800px' : '95%'];
                    var options = {
                        shadeClose : false,
                        shade      : [0.3, '#393D49'],
                        area       : area,
                        end   : function (value) {
                            //在回调函数里可以调用你的业务代码实现前端的各种逻辑和效果
                           // $(".btn-refresh").trigger("click");
                        }
                    };
                    Fast.api.open("qingdong/customer/contract/detail?ids=" + $(this).data('id'), '合同', options);
                });
                // 为表格1绑定事件
                Table.api.bindevent(table1);

            },
            second: function () {
                // 初始化表格参数配置
                Table.api.init({
                    extend: {
                        index_url: 'qingdong/examine/examine/index?status=1',
                        table: 'contact'
                    }
                });
                // 合同
                var table1 = $("#table1");
                table1.bootstrapTable({
                    url: $.fn.bootstrapTable.defaults.extend.index_url,
                    toolbar: '#toolbar1',
                    sortName: 'id',
                    search: false,
                    columns: [
                        [
                            {field: 'state', checkbox: true, },
                            {field : 'contract.owner_staff.name', title : __('提交人'), operate: false},

                            {
                                field : 'contract.name', title : '合同名称', fixedColumns : true, formatter : function (value, row, index) {
                                    if(row.contract){
                                        return "<a href='javascript:void(0);' data-id='" + row.contract.id + "' class='show-contract'>" + value + "</a>";
                                    }
                                    return '';
                                },operate:false
                            },
                            {field: 'contract.name', title: '合同名称', operate: false},
                            {field: 'check_staff.name', title: __('审批人'), operate: false},
                            {field: 'status', title: __('审批状态'), operate: false, formatter: Table.api.formatter.status,  searchList: {0: __('待审批'), 1: __('审批通过'), 2: __('审批拒绝'), 3: __('撤销')}},
                            {field: 'content', title: __('审批内容')},
                            {field: 'check_time', title: __('审批时间'), formatter: Table.api.formatter.datetime, operate: 'RANGE', addclass: 'datetimerange'},
                            {field: 'createtime', title: __('创建时间'), formatter: Table.api.formatter.datetime, operate: 'RANGE', addclass: 'datetimerange'},

                        ]
                    ],
                    //启用普通表单搜索
                    commonSearch: true,
                    searchFormVisible: true,
                });

                // 为表格1绑定事件
                Table.api.bindevent(table1);
            },
            third: function () {
                // 初始化表格参数配置
                Table.api.init({
                    extend: {
                        index_url: 'qingdong/examine/examine/consume?status=0',
                        table: 'consume'
                    }
                });
                // 费用
                var table2 = $("#table");
                table2.bootstrapTable({
                    url: $.fn.bootstrapTable.defaults.extend.index_url,
                    toolbar: '#toolbar',
                    sortName: 'id',
                    search: false,
                    columns: [
                        [
                            {field : 'consume.staff.name', title : __('提交人'), operate: false},

                            {
                                field : 'consume.consume_type', title : '消费方式', fixedColumns : true, formatter : function (value, row, index) {
                                    if(row.consume){
                                        return "<a href='javascript:void(0);' data-id='" + row.relation_id + "' class='show-consume'>" + row.consume.consume_type + "</a>";
                                    }
                                    return '';
                                },operate:false
                            },

                            {field: 'consume.consume_type', title: '消费方式', operate: false},
                            {field: 'consume.consume_time', title: '消费日期', operate: false},
                            {field: 'consume.money', title: '消费金额', operate: false},
                            {field: 'check_staff.name', title: __('审批人'), operate: false},
                            {field: 'status', title: __('审批状态'), operate: false, formatter: Table.api.formatter.status,  searchList: {0: __('待审批'), 1: __('审批通过'), 2: __('审批拒绝'), 3: __('撤销')}},
                            {field: 'createtime', title: __('创建时间'), formatter: Table.api.formatter.datetime, operate: 'RANGE', addclass: 'datetimerange'},
                            {field: 'operate', title: __('Operate'), table: table2, events: Table.api.events.operate, formatter: Table.api.formatter.buttons,
                                buttons: [
                                    {
                                        name: 'success',
                                        text: __('通过'),
                                        title: __('通过'),
                                        classname: 'records btn-dialog',
                                        area:'',
                                        refresh:true,
                                        icon: 'fa fa-check',
                                        url: 'qingdong/examine/examine_record/examine?relation_type=consume&relation_id={relation_id}&status=1',
                                        visible: function (row) {
                                            //返回true时按钮显示,返回false隐藏
                                            return true;
                                        },
                                        success:function(data){
                                            $.post("qingdong/customer/need/get_need_number",function (data){
                                                top.window.Backend.api.sidebar({
                                                    'qingdong/customer/need/index':[data.data.number, 'red', 'badge'],
                                                    'qingdong/examine/examine/index':[data.data.examine, 'red', 'badge'], //合同
                                                    'qingdong/examine/examine/consume':[data.data.consume, 'red', 'badge'],
                                                    'qingdong/examine/examine/receivables':[data.data.receivables, 'red', 'badge'],
                                                    'qingdong/examine/examine/achievement':[data.data.achievement, 'red', 'badge'],
                                                    'qingdong/examine/examine/work':[data.data.approval, 'red', 'badge'],
                                                    'qingdong/canlendar/canlendar':[data.data.eventOne, 'red', 'badge'],
                                                });
                                            },'json');
                                            $(".btn-refresh").trigger("click");
                                        }
                                    },
                                    {
                                        name: 'error',
                                        text: __('拒绝'),
                                        title: __('拒绝'),
                                        classname: 'records btn-dialog',
                                        extend: ' data-area=\'\'',
                                        refresh:true,
                                        icon: 'fa fa-close',
                                        url: 'qingdong/examine/examine_record/examine?relation_type=consume&relation_id={relation_id}&status=2',
                                        error: function (data, ret) {
                                            console.log(data, ret);
                                            Layer.alert(ret.msg);
                                            return false;
                                        },
                                        success:function(data){
                                            $.post("qingdong/customer/need/get_need_number",function (data){
                                                top.window.Backend.api.sidebar({
                                                    'qingdong/customer/need/index':[data.data.number, 'red', 'badge'],
                                                    'qingdong/examine/examine/index':[data.data.examine, 'red', 'badge'], //合同
                                                    'qingdong/examine/examine/consume':[data.data.consume, 'red', 'badge'],
                                                    'qingdong/examine/examine/receivables':[data.data.receivables, 'red', 'badge'],
                                                    'qingdong/examine/examine/achievement':[data.data.achievement, 'red', 'badge'],
                                                    'qingdong/examine/examine/work':[data.data.approval, 'red', 'badge'],
                                                    'qingdong/canlendar/canlendar':[data.data.eventOne, 'red', 'badge'],
                                                });
                                            },'json');
                                            $(".btn-refresh").trigger("click");
                                        }
                                    },
                                ]
                            }

                        ]
                    ],
                    //启用普通表单搜索
                    commonSearch: true,
                    searchFormVisible: true,
                });

                $(document).on('click', '.show-consume', function (data) {
                    var area = [$(window).width() > 1200 ? '1200px' : '95%', $(window).height() > 800 ? '800px' : '95%'];
                    var options = {
                        shadeClose : false,
                        shade      : [0.3, '#393D49'],
                        area       : area,
                        end   : function (value) {
                            //在回调函数里可以调用你的业务代码实现前端的各种逻辑和效果
                            console.log(value);
                            $(".btn-refresh").trigger("click");
                        }
                    };
                    Fast.api.open("qingdong/customer/consume/detail?ids=" + $(this).data('id'), '费用详情', options);
                });
                // 为表格2绑定事件
                Table.api.bindevent(table2);

            },
            four: function () {
                // 初始化表格参数配置
                Table.api.init({
                    extend: {
                        index_url: 'qingdong/examine/examine/consume?status=1',
                        table: 'consume'
                    }
                });
                // 费用
                var table2 = $("#table1");
                table2.bootstrapTable({
                    url: $.fn.bootstrapTable.defaults.extend.index_url,
                    toolbar: '#toolbar1',
                    sortName: 'id',
                    search: false,
                    columns: [
                        [
                            {field : 'consume.staff.name', title : __('提交人'), operate: false},

                            {
                                field : 'consume.consume_type', title : '消费方式', fixedColumns : true, formatter : function (value, row, index) {
                                if(row.consume){
                                    return "<a href='javascript:void(0);' data-id='" + row.relation_id + "' class='show-consume'>" + row.consume.consume_type + "</a>";
                                }
                                return '';
                                },operate:false
                            },

                            {field: 'consume.consume_type', title: '消费方式', operate: false},
                            {field: 'consume.consume_time', title: '消费日期', operate: false},
                            {field: 'consume.money', title: '消费金额', operate: false},
                            {field: 'check_staff.name', title: __('审批人'), operate: false},
                            {field: 'status', title: __('审批状态'), operate: false, formatter: Table.api.formatter.status,  searchList: {0: __('待审批'), 1: __('审批通过'), 2: __('审批拒绝'), 3: __('撤销')}},
                            {field: 'createtime', title: __('创建时间'), formatter: Table.api.formatter.datetime, operate: 'RANGE', addclass: 'datetimerange'},

                        ]
                    ],
                    //启用普通表单搜索
                    commonSearch: true,
                    searchFormVisible: true,
                });

                // 为表格2绑定事件
                Table.api.bindevent(table2);
            },
            five: function () {
                // 初始化表格参数配置
                Table.api.init({
                    extend: {
                        index_url: 'qingdong/examine/examine/achievement?status=0',
                        table: 'achievement'
                    }
                });
                // 业绩目标
                var table4 = $("#table");
                table4.bootstrapTable({
                    url: $.fn.bootstrapTable.defaults.extend.index_url,
                    toolbar: '#toolbar',
                    sortName: 'id',
                    fixedColumns : true,
                    fixedRightNumber  : 1,
                    search: false,
                    columns: [
                        [
                            {field : 'achievement.create_staff.name', title : __('提交人'), operate: false},
                            {field: 'achievement.status', title: '业绩目标类型', operate: false, formatter : Table.api.formatter.status, searchList :{1: '合同金额', 2: '回款金额'}},
                            {field: 'achievement.year', title: '业绩目标年份', operate: false},
                            {field: 'achievement.yeartarget', title: '年目标', operate: false},
                            {field : 'achievement.january', title : __('一月'), operate: false},
                            {field : 'achievement.february', title : __('二月'), operate: false},
                            {field : 'achievement.march', title : __('三月'), operate: false},
                            {field : 'achievement.april', title : __('四月'), operate: false},
                            {field : 'achievement.may', title : __('五月'), operate: false},
                            {field : 'achievement.june', title : __('六月'), operate: false},
                            {field : 'achievement.july', title : __('七月'), operate: false},
                            {field : 'achievement.august', title : __('八月'), operate: false},
                            {field : 'achievement.september', title : __('九月'), operate: false},
                            {field : 'achievement.october', title : __('十月'), operate: false},
                            {field : 'achievement.november', title : __('十一月'), operate: false},
                            {field : 'achievement.december', title : __('十二月'), operate: false},
                            {field: 'check_staff.name', title: __('审批人'), operate: false},
                            {field: 'status', title: __('审批状态'), operate: false, formatter: Table.api.formatter.status,  searchList: {0: __('待审批'), 1: __('审批通过'), 2: __('审批拒绝'), 3: __('撤销')}},
                            {field: 'createtime', title: __('创建时间'), formatter: Table.api.formatter.datetime, operate: 'RANGE', addclass: 'datetimerange'},
                            {field: 'operate', title: __('Operate'),width:150, table: table4, events: Table.api.events.operate, formatter: Table.api.formatter.buttons,
                                buttons: [
                                    {
                                        name: 'success',
                                        text: __('通过'),
                                        title: __('通过'),
                                        classname: 'records btn-dialog',
                                        extend: ' data-area=\'\'',
                                        refresh:true,
                                        url: 'qingdong/examine/examine_record/examine?relation_type=achievement&relation_id={relation_id}&status=1',
                                        visible: function (row) {
                                            //返回true时按钮显示,返回false隐藏
                                            return true;
                                        },
                                        success:function(data){
                                            $.post("qingdong/customer/need/get_need_number",function (data){
                                                top.window.Backend.api.sidebar({
                                                    'qingdong/customer/need/index':[data.data.number, 'red', 'badge'],
                                                    'qingdong/examine/examine/index':[data.data.examine, 'red', 'badge'], //合同
                                                    'qingdong/examine/examine/consume':[data.data.consume, 'red', 'badge'],
                                                    'qingdong/examine/examine/receivables':[data.data.receivables, 'red', 'badge'],
                                                    'qingdong/examine/examine/achievement':[data.data.achievement, 'red', 'badge'],
                                                    'qingdong/examine/examine/work':[data.data.approval, 'red', 'badge'],
                                                    'qingdong/canlendar/canlendar':[data.data.eventOne, 'red', 'badge'],
                                                });
                                            },'json');
                                            $(".btn-refresh").trigger("click");
                                        }
                                    },
                                    {
                                        name: 'error',
                                        text: __('拒绝'),
                                        title: __('拒绝'),
                                        classname: 'records btn-dialog',
                                        extend: ' data-area=\'\'',
                                        refresh:true,
                                        url: 'qingdong/examine/examine_record/examine?relation_type=achievement&relation_id={relation_id}&status=2',
                                        error: function (data, ret) {
                                            console.log(data, ret);
                                            Layer.alert(ret.msg);
                                            return false;
                                        },
                                        success:function(data){
                                            $.post("qingdong/customer/need/get_need_number",function (data){
                                                top.window.Backend.api.sidebar({
                                                    'qingdong/customer/need/index':[data.data.number, 'red', 'badge'],
                                                    'qingdong/examine/examine/index':[data.data.examine, 'red', 'badge'], //合同
                                                    'qingdong/examine/examine/consume':[data.data.consume, 'red', 'badge'],
                                                    'qingdong/examine/examine/receivables':[data.data.receivables, 'red', 'badge'],
                                                    'qingdong/examine/examine/achievement':[data.data.achievement, 'red', 'badge'],
                                                    'qingdong/examine/examine/work':[data.data.approval, 'red', 'badge'],
                                                    'qingdong/canlendar/canlendar':[data.data.eventOne, 'red', 'badge'],
                                                });
                                            },'json');
                                            $(".btn-refresh").trigger("click");
                                        }
                                    },
                                ]
                            }
                        ]
                    ],
                    //启用普通表单搜索
                    commonSearch: true,
                    searchFormVisible: true,
                });
                // 为表格2绑定事件
                Table.api.bindevent(table4);
            },
            six: function () {
                // 初始化表格参数配置
                Table.api.init({
                    extend: {
                        index_url: 'qingdong/examine/examine/achievement?status=1',
                        table: 'achievement'
                    }
                });
                // 业绩目标
                var table4 = $("#table1");
                table4.bootstrapTable({
                    url: $.fn.bootstrapTable.defaults.extend.index_url,
                    toolbar: '#toolbar1',
                    sortName: 'id',
                    fixedColumns : true,
                    fixedRightNumber  : 1,
                    search: false,
                    columns: [
                        [
                            {field : 'achievement.create_staff.name', title : __('提交人'), operate: false},
                            {field: 'achievement.status', title: '业绩目标类型', operate: false, formatter : Table.api.formatter.status, searchList :{1: '合同金额', 2: '回款金额'}},
                            {field: 'achievement.year', title: '业绩目标年份', operate: false},
                            {field: 'achievement.yeartarget', title: '年目标', operate: false},
                            {field : 'achievement.january', title : __('一月'), operate: false},
                            {field : 'achievement.february', title : __('二月'), operate: false},
                            {field : 'achievement.march', title : __('三月'), operate: false},
                            {field : 'achievement.april', title : __('四月'), operate: false},
                            {field : 'achievement.may', title : __('五月'), operate: false},
                            {field : 'achievement.june', title : __('六月'), operate: false},
                            {field : 'achievement.july', title : __('七月'), operate: false},
                            {field : 'achievement.august', title : __('八月'), operate: false},
                            {field : 'achievement.september', title : __('九月'), operate: false},
                            {field : 'achievement.october', title : __('十月'), operate: false},
                            {field : 'achievement.november', title : __('十一月'), operate: false},
                            {field : 'achievement.december', title : __('十二月'), operate: false},
                            {field: 'check_staff.name', title: __('审批人'), operate: false},
                            {field: 'status', title: __('审批状态'), operate: false, formatter: Table.api.formatter.status,  searchList: {0: __('待审批'), 1: __('审批通过'), 2: __('审批拒绝'), 3: __('撤销')}},
                            {field: 'createtime', title: __('创建时间'), formatter: Table.api.formatter.datetime, operate: 'RANGE', addclass: 'datetimerange'},

                        ]
                    ],
                    //启用普通表单搜索
                    commonSearch: true,
                    searchFormVisible: true,
                });
                // 为表格2绑定事件
                Table.api.bindevent(table4);
            },
            seven: function () {
                // 初始化表格参数配置
                Table.api.init({
                    extend: {
                        index_url: 'qingdong/examine/examine/receivables?status=0',
                        table: 'receivables'
                    }
                });
                // 回款
                var table3 = $("#table");
                table3.bootstrapTable({
                    url: $.fn.bootstrapTable.defaults.extend.index_url,
                    toolbar: '#toolbar',
                    sortName: 'id',
                    search: false,
                    columns: [
                        [
                            {field : 'receivables.create_staff.name', title : __('提交人'), operate: false},

                            {
                                field : 'receivables.number', title : '回款编号', fixedColumns : true, formatter : function (value, row, index) {
                                    if(row.receivables){
                                        return "<a href='javascript:void(0);' data-id='" + row.relation_id + "' class='show-receivables'>" + row.receivables.number + "</a>";
                                    }
                                    return '';
                                },operate:false
                            },
                            {field: 'receivables.return_type', title: '回款方式', operate: false},
                            {field: 'receivables.return_time', title: '回款日期', operate: false},
                            {field: 'receivables.money', title: '回款金额', operate: false},
                            {field: 'check_staff.name', title: __('审批人'), operate: false},
                            {field: 'status', title: __('审批状态'), operate: false, formatter: Table.api.formatter.status,  searchList: {0: __('待审批'), 1: __('审批通过'), 2: __('审批拒绝'), 3: __('撤销')}},
                            {field: 'createtime', title: __('创建时间'), formatter: Table.api.formatter.datetime, operate: 'RANGE', addclass: 'datetimerange'},
                            {field: 'operate', title: __('Operate'), table: table3, events: Table.api.events.operate, formatter: Table.api.formatter.buttons,
                                buttons: [
                                    {
                                        name: 'success',
                                        text: __('通过'),
                                        title: __('通过'),
                                        classname: 'records btn-dialog',
                                        extend: ' data-area=\'\'',
                                        refresh:true,
                                        url: 'qingdong/examine/examine_record/examine?relation_type=receivables&relation_id={relation_id}&status=1',
                                        visible: function (row) {
                                            //返回true时按钮显示,返回false隐藏
                                            return true;
                                        },
                                        success:function(data){
                                            $.post("qingdong/customer/need/get_need_number",function (data){
                                                top.window.Backend.api.sidebar({
                                                    'qingdong/customer/need/index':[data.data.number, 'red', 'badge'],
                                                    'qingdong/examine/examine/index':[data.data.examine, 'red', 'badge'], //合同
                                                    'qingdong/examine/examine/consume':[data.data.consume, 'red', 'badge'],
                                                    'qingdong/examine/examine/receivables':[data.data.receivables, 'red', 'badge'],
                                                    'qingdong/examine/examine/achievement':[data.data.achievement, 'red', 'badge'],
                                                    'qingdong/examine/examine/work':[data.data.approval, 'red', 'badge'],
                                                    'qingdong/canlendar/canlendar':[data.data.eventOne, 'red', 'badge'],
                                                });
                                            },'json');
                                            $(".btn-refresh").trigger("click");
                                        }
                                    },
                                    {
                                        name: 'error',
                                        text: __('拒绝'),
                                        title: __('拒绝'),
                                        classname: 'records btn-dialog',
                                        extend: ' data-area=\'\'',
                                        refresh:true,
                                        url: 'qingdong/examine/examine_record/examine?relation_type=receivables&relation_id={relation_id}&status=2',
                                        error: function (data, ret) {
                                            console.log(data, ret);
                                            Layer.alert(ret.msg);
                                            return false;
                                        },
                                        success:function(data){
                                            $.post("qingdong/customer/need/get_need_number",function (data){
                                                top.window.Backend.api.sidebar({
                                                    'qingdong/customer/need/index':[data.data.number, 'red', 'badge'],
                                                    'qingdong/examine/examine/index':[data.data.examine, 'red', 'badge'], //合同
                                                    'qingdong/examine/examine/consume':[data.data.consume, 'red', 'badge'],
                                                    'qingdong/examine/examine/receivables':[data.data.receivables, 'red', 'badge'],
                                                    'qingdong/examine/examine/achievement':[data.data.achievement, 'red', 'badge'],
                                                    'qingdong/examine/examine/work':[data.data.approval, 'red', 'badge'],
                                                    'qingdong/canlendar/canlendar':[data.data.eventOne, 'red', 'badge'],
                                                });
                                            },'json');
                                            $(".btn-refresh").trigger("click");
                                        }
                                    },
                                ]
                            }
                        ]
                    ],
                    //启用普通表单搜索
                    commonSearch: true,
                    searchFormVisible: true,
                });
                $(document).on('click', '.show-receivables', function (data) {
                    var area = [$(window).width() > 1200 ? '1200px' : '95%', $(window).height() > 800 ? '800px' : '95%'];
                    var options = {
                        shadeClose : false,
                        shade      : [0.3, '#393D49'],
                        area       : area,
                        end   : function (value) {
                            //在回调函数里可以调用你的业务代码实现前端的各种逻辑和效果
                          //  $(".btn-refresh").trigger("click");
                        }
                    };
                    Fast.api.open("qingdong/customer/receivables/detail?ids=" + $(this).data('id'), '回款详情', options);
                });

                // 为表格2绑定事件
                Table.api.bindevent(table3);
            },
            eight: function () {
                // 初始化表格参数配置
                Table.api.init({
                    extend: {
                        index_url: 'qingdong/examine/examine/receivables?status=1',
                        table: 'receivables'
                    }
                });
                // 回款
                var table3 = $("#table1");
                table3.bootstrapTable({
                    url: $.fn.bootstrapTable.defaults.extend.index_url,
                    toolbar: '#toolbar1',
                    sortName: 'id',
                    search: false,
                    columns: [
                        [
                            {field : 'receivables.create_staff.name', title : __('提交人'), operate: false},

                            {
                                field : 'receivables.number', title : '回款编号', fixedColumns : true, formatter : function (value, row, index) {
                                    if(row.receivables){
                                        return "<a href='javascript:void(0);' data-id='" + row.relation_id + "' class='show-receivables'>" + row.receivables.number + "</a>";
                                    }
                                    return '';
                                },operate:false
                            },
                            {field: 'receivables.return_type', title: '回款方式', operate: false},
                            {field: 'receivables.return_time', title: '回款日期', operate: false},
                            {field: 'receivables.money', title: '回款金额', operate: false},
                            {field: 'check_staff.name', title: __('审批人'), operate: false},
                            {field: 'status', title: __('审批状态'), operate: false, formatter: Table.api.formatter.status,  searchList: {0: __('待审批'), 1: __('审批通过'), 2: __('审批拒绝'), 3: __('撤销')}},
                            {field: 'createtime', title: __('创建时间'), formatter: Table.api.formatter.datetime, operate: 'RANGE', addclass: 'datetimerange'},

                        ]
                    ],
                    //启用普通表单搜索
                    commonSearch: true,
                    searchFormVisible: true,
                });

                // 为表格2绑定事件
                Table.api.bindevent(table3);
            },
            nine: function () {
                // 初始化表格参数配置
                Table.api.init({
                    extend: {
                        index_url: 'qingdong/examine/examine/work?status=0',
                        table: 'work'
                    }
                });
                // 办公审批
                var table3 = $("#table");
                table3.bootstrapTable({
                    url: $.fn.bootstrapTable.defaults.extend.index_url,

                    toolbar: '#toolbar',
                    sortName: 'id',
                    search: false,
                    columns: [
                        [
                            {field: 'approval_name', title: __('审批类型'), operate: false},
                            {field : 'approval.create_staff.name', title : __('提交人'), operate: false},
                            {field: 'check_staff.name', title: __('审批人'), operate: false},
                            {field: 'status', title: __('审批状态'), operate: false, formatter: Table.api.formatter.status,  searchList: {0: __('待审批'), 1: __('审批通过'), 2: __('审批拒绝'), 3: __('撤销')}},
                            {field: 'createtime', title: __('创建时间'), formatter: Table.api.formatter.datetime, operate: 'RANGE', addclass: 'datetimerange'},
                            {field: 'operate', title: __('Operate'), table: table3, events: Table.api.events.operate, formatter: Table.api.formatter.buttons,
                                buttons: [
                                    {
                                        name: 'detail',
                                        text: __('详情'),
                                        classname: 'records btn-dialog',
                                        url: function (row) {
                                            return 'qingdong/work/approval/detail?ids=' + row.approval.id;
                                        },
                                        extend: "data-area='[\"80%\", \"80%\"]'",
                                        callback: function (data) {
                                            Layer.alert("接收到回传数据：" + JSON.stringify(data), {title: "回传数据"});
                                            $(".btn-refresh").trigger("click");
                                        }
                                    },
                                    {
                                        name: 'success',
                                        text: __('通过'),
                                        title: __('通过'),
                                        classname: 'records btn-dialog',
                                        extend: ' data-area=\'\'',
                                        refresh:true,
                                        url: 'qingdong/examine/examine_record/examine?relation_type=approval&relation_id={relation_id}&status=1',
                                        visible: function (row) {
                                            //返回true时按钮显示,返回false隐藏
                                            return true;
                                        },
                                        success:function(data){
                                            $.post("qingdong/customer/need/get_need_number",function (data){
                                                top.window.Backend.api.sidebar({
                                                    'qingdong/customer/need/index':[data.data.number, 'red', 'badge'],
                                                    'qingdong/examine/examine/index':[data.data.examine, 'red', 'badge'], //合同
                                                    'qingdong/examine/examine/consume':[data.data.consume, 'red', 'badge'],
                                                    'qingdong/examine/examine/receivables':[data.data.receivables, 'red', 'badge'],
                                                    'qingdong/examine/examine/achievement':[data.data.achievement, 'red', 'badge'],
                                                    'qingdong/examine/examine/work':[data.data.approval, 'red', 'badge'],
                                                    'qingdong/canlendar/canlendar':[data.data.eventOne, 'red', 'badge'],
                                                });
                                            },'json');
                                            $(".btn-refresh").trigger("click");
                                        }
                                    },
                                    {
                                        name: 'error',
                                        text: __('拒绝'),
                                        title: __('拒绝'),
                                        classname: 'records btn-dialog',
                                        extend: ' data-area=\'\'',
                                        refresh:true,
                                        url: 'qingdong/examine/examine_record/examine?relation_type=approval&relation_id={relation_id}&status=2',
                                        error: function (data, ret) {
                                            console.log(data, ret);
                                            Layer.alert(ret.msg);
                                            return false;
                                        },
                                        success:function(data){
                                            $.post("qingdong/customer/need/get_need_number",function (data){
                                                top.window.Backend.api.sidebar({
                                                    'qingdong/customer/need/index':[data.data.number, 'red', 'badge'],
                                                    'qingdong/examine/examine/index':[data.data.examine, 'red', 'badge'], //合同
                                                    'qingdong/examine/examine/consume':[data.data.consume, 'red', 'badge'],
                                                    'qingdong/examine/examine/receivables':[data.data.receivables, 'red', 'badge'],
                                                    'qingdong/examine/examine/achievement':[data.data.achievement, 'red', 'badge'],
                                                    'qingdong/examine/examine/work':[data.data.approval, 'red', 'badge'],
                                                    'qingdong/canlendar/canlendar':[data.data.eventOne, 'red', 'badge'],
                                                });
                                            },'json');
                                            $(".btn-refresh").trigger("click");
                                        }
                                    },
                                ]
                            }
                        ]
                    ],
                    //启用普通表单搜索
                    commonSearch: true,
                    searchFormVisible: true,
                });
                $(document).on('click', '.show-receivables', function (data) {
                    var area = [$(window).width() > 1200 ? '1200px' : '95%', $(window).height() > 800 ? '800px' : '95%'];
                    var options = {
                        shadeClose : false,
                        shade      : [0.3, '#393D49'],
                        area       : area,
                        end   : function (value) {
                            //在回调函数里可以调用你的业务代码实现前端的各种逻辑和效果
                          //  $(".btn-refresh").trigger("click");
                        }
                    };
                    Fast.api.open("qingdong/customer/receivables/detail?ids=" + $(this).data('id'), '回款详情', options);
                });

                // 为表格2绑定事件
                Table.api.bindevent(table3);
            },
            ten: function () {
                // 初始化表格参数配置
                Table.api.init({
                    extend: {
                        index_url: 'qingdong/examine/examine/work?status=1',
                        table: 'work'
                    }
                });
                // 办公审批
                var table3 = $("#table1");
                table3.bootstrapTable({
                    url: $.fn.bootstrapTable.defaults.extend.index_url,

                    toolbar: '#toolbar1',
                    sortName: 'id',
                    search: false,
                    columns: [
                        [
                            {field: 'approval_name', title: __('审批类型'), operate: false},
                            {field : 'approval.create_staff.name', title : __('提交人'), operate: false},
                            {field: 'check_staff.name', title: __('审批人'), operate: false},
                            {field: 'status', title: __('审批状态'), operate: false, formatter: Table.api.formatter.status,  searchList: {0: __('待审批'), 1: __('审批通过'), 2: __('审批拒绝'), 3: __('撤销')}},
                            {field: 'createtime', title: __('创建时间'), formatter: Table.api.formatter.datetime, operate: 'RANGE', addclass: 'datetimerange'},
                            {field: 'operate', title: __('Operate'), table: table3, events: Table.api.events.operate, formatter: Table.api.formatter.buttons,
                                buttons: [
                                    {
                                        name: 'detail',
                                        text: __('详情'),
                                        classname: 'records btn-dialog',
                                        url: function (row) {
                                            return 'qingdong/work/approval/detail?ids=' + row.approval.id;
                                        },
                                        extend: "data-area='[\"80%\", \"80%\"]'",
                                        callback: function (data) {
                                            Layer.alert("接收到回传数据：" + JSON.stringify(data), {title: "回传数据"});
                                            $(".btn-refresh").trigger("click");
                                        }
                                    },
                                    {
                                        name: 'success',
                                        text: __('通过'),
                                        title: __('通过'),
                                        classname: 'records btn-dialog',
                                        extend: ' data-area=\'\'',
                                        refresh:true,
                                        url: 'qingdong/examine/examine_record/examine?relation_type=approval&relation_id={relation_id}&status=1',
                                        visible: function (row) {
                                            //返回true时按钮显示,返回false隐藏
                                            return true;
                                        },
                                        success:function(data){
                                            $.post("qingdong/customer/need/get_need_number",function (data){
                                                top.window.Backend.api.sidebar({
                                                    'qingdong/customer/need/index':[data.data.number, 'red', 'badge'],
                                                    'qingdong/examine/examine/index':[data.data.examine, 'red', 'badge'], //合同
                                                    'qingdong/examine/examine/consume':[data.data.consume, 'red', 'badge'],
                                                    'qingdong/examine/examine/receivables':[data.data.receivables, 'red', 'badge'],
                                                    'qingdong/examine/examine/achievement':[data.data.achievement, 'red', 'badge'],
                                                    'qingdong/examine/examine/work':[data.data.approval, 'red', 'badge'],
                                                    'qingdong/canlendar/canlendar':[data.data.eventOne, 'red', 'badge'],
                                                });
                                            },'json');
                                            $(".btn-refresh").trigger("click");
                                        }
                                    },
                                    {
                                        name: 'error',
                                        text: __('拒绝'),
                                        title: __('拒绝'),
                                        classname: 'records btn-dialog',
                                        extend: ' data-area=\'\'',
                                        refresh:true,
                                        url: 'qingdong/examine/examine_record/examine?relation_type=approval&relation_id={relation_id}&status=2',
                                        error: function (data, ret) {
                                            console.log(data, ret);
                                            Layer.alert(ret.msg);
                                            return false;
                                        },
                                        success:function(data){
                                            $.post("qingdong/customer/need/get_need_number",function (data){
                                                top.window.Backend.api.sidebar({
                                                    'qingdong/customer/need/index':[data.data.number, 'red', 'badge'],
                                                    'qingdong/examine/examine/index':[data.data.examine, 'red', 'badge'], //合同
                                                    'qingdong/examine/examine/consume':[data.data.consume, 'red', 'badge'],
                                                    'qingdong/examine/examine/receivables':[data.data.receivables, 'red', 'badge'],
                                                    'qingdong/examine/examine/achievement':[data.data.achievement, 'red', 'badge'],
                                                    'qingdong/examine/examine/work':[data.data.approval, 'red', 'badge'],
                                                    'qingdong/canlendar/canlendar':[data.data.eventOne, 'red', 'badge'],
                                                });
                                            },'json');
                                            $(".btn-refresh").trigger("click");
                                        }
                                    },
                                ]
                            }
                        ]
                    ],
                    //启用普通表单搜索
                    commonSearch: true,
                    searchFormVisible: true,
                });
                // 为表格2绑定事件
                Table.api.bindevent(table3);
            },
        },
        consume  : function () {
            // 初始化表格参数配置
            Table.api.init();

            //绑定事件
            $('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
                var panel = $($(this).attr("href"));
                if (panel.length > 0) {
                    Controller.table[panel.attr("id")].call(this);
                    $(this).on('click', function (e) {
                        $($(this).attr("href")).find(".btn-refresh").trigger("click");
                    });
                }
                //移除绑定的事件
                $(this).unbind('shown.bs.tab');
            });

            //必须默认触发shown.bs.tab事件
            $('ul.nav-tabs li.active a[data-toggle="tab"]').trigger("shown.bs.tab");


        },
        receivables  : function () {
            // 初始化表格参数配置
            Table.api.init();

            //绑定事件
            $('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
                var panel = $($(this).attr("href"));
                if (panel.length > 0) {
                    Controller.table[panel.attr("id")].call(this);
                    $(this).on('click', function (e) {
                        $($(this).attr("href")).find(".btn-refresh").trigger("click");
                    });
                }
                //移除绑定的事件
                $(this).unbind('shown.bs.tab');
            });

            //必须默认触发shown.bs.tab事件
            $('ul.nav-tabs li.active a[data-toggle="tab"]').trigger("shown.bs.tab");


        },

        achievement  : function () {
            // 初始化表格参数配置
            Table.api.init();

            //绑定事件
            $('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
                var panel = $($(this).attr("href"));
                if (panel.length > 0) {
                    Controller.table[panel.attr("id")].call(this);
                    $(this).on('click', function (e) {
                        $($(this).attr("href")).find(".btn-refresh").trigger("click");
                    });
                }
                //移除绑定的事件
                $(this).unbind('shown.bs.tab');
            });

            //必须默认触发shown.bs.tab事件
            $('ul.nav-tabs li.active a[data-toggle="tab"]').trigger("shown.bs.tab");


        },
        work  : function () {
            // 初始化表格参数配置
            Table.api.init();

            //绑定事件
            $('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
                var panel = $($(this).attr("href"));
                if (panel.length > 0) {
                    Controller.table[panel.attr("id")].call(this);
                    $(this).on('click', function (e) {
                        $($(this).attr("href")).find(".btn-refresh").trigger("click");
                    });
                }
                //移除绑定的事件
                $(this).unbind('shown.bs.tab');
            });

            //必须默认触发shown.bs.tab事件
            $('ul.nav-tabs li.active a[data-toggle="tab"]').trigger("shown.bs.tab");


        },

        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            },
        }
    };
    return Controller;
});