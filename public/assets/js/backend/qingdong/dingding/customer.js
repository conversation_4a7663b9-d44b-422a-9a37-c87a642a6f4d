define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'qingdong/dingding/customer/index',
                    del_url   : 'qingdong/dingding/customer/del',
                    table: 'customer',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url               : $.fn.bootstrapTable.defaults.extend.index_url,
                sortName          : 'id',
                columns           : [
                    [
                        {checkbox: true},
                        {field : 'name', title : __('客户名称')},
                        {field : 'address', title : __('地址')},
                        {field : 'address_detail', title : __('详细地址'),operate:false},
                        {field : 'source', title : __('来源')},
                        {field : 'remark', title : __('备注'),operate:false},
                        {field : 'ownerstaff.name', title : __('负责人'),operate:false},
                        {field: 'status', title: __('状态'), formatter: Table.api.formatter.status, searchList: {0: __('未同步'), 1: __('已同步')}, operate: false},
                        {field: 'createtime', title: __('创建时间'), formatter: Table.api.formatter.datetime, operate: 'RANGE', addclass: 'datetimerange', sortable: true},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}

                    ]
                ],
                search:false,
                //启用普通表单搜索
                commonSearch      : true,
                searchFormVisible : false,
                onLoadSuccess:function(){
                    // 这里就是数据渲染结束后的回调函数
                    $('.btn-editone').html('编辑');
                    $('.fa-pencil').remove();
                    $('.btn-delone').html('删除');
                    $('.fa-trash').remove();
                    $('.btn-editone').removeClass('btn-success')
                    $('.btn-editone').removeClass('btn')
                    $('.btn-delone').removeClass('btn')
                    $('.btn-delone').removeClass('btn-danger')
                }
            });
            //获取钉钉数据
            $(document).on("click", ".batch", function () {
                var ids = Table.api.selectedids(table);
                var page = table.bootstrapTable('getData');
                var all = table.bootstrapTable('getOptions').totalRows;
                Layer.confirm("请确认是否同步", {
                    title   : '批量同步',
                    btn     : ["是","否"],
                    success : function (layero, index) {
                        $(".layui-layer-btn a", layero).addClass("layui-layer-btn0");
                    }
                    , yes   : function (index, layero) {
                        var dindex = layer.load();
                        $.post('qingdong/dingding/dingding/customer', {}, function (res) {
                            Layer.alert(res.msg);
                            Layer.close(index);
                            Layer.close(dindex);
                            table.bootstrapTable('refresh');
                        }, 'json');


                    }
                    ,
                    btn2    : function (index, layero) {
                        Layer.close(index);
                    }

                })
            });
            //同步到CRM中
            $(document).on("click", ".batchall", function () {
                var ids = Table.api.selectedids(table);
                var page = table.bootstrapTable('getData');
                var all = table.bootstrapTable('getOptions').totalRows;
                Layer.confirm("请确认是否同步", {
                    title   : '批量同步',
                    btn     : [ "是","否"],
                    success : function (layero, index) {
                        $(".layui-layer-btn a", layero).addClass("layui-layer-btn0");
                    }
                    , yes   : function (index, layero) {
                        var dindex = layer.load();
                        $.post('qingdong/dingding/customer/batch', {}, function (res) {
                            Layer.alert(res.msg);
                            Layer.close(index);
                            Layer.close(dindex);
                            table.bootstrapTable('refresh');
                        }, 'json');

                    }
                    ,
                    btn2    : function (index, layero) {
                        Layer.close(index);
                    }

                })
            });
            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        edit: function () {

            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            },
            formatter: {
                title: function (value, row, index) {
                    value = value.toString().replace(/(&|&amp;)nbsp;/g, '&nbsp;');
                    return  value;
                },

            },
        }
    };
    return Controller;
});