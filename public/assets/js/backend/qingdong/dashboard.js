define(['jquery', 'bootstrap', 'backend', 'addtabs', 'table', 'echarts', 'echarts-theme', 'template'], function ($, undefined, Backend, Datatable, Table, Echarts, undefined, Template) {
    var series=[];
    $.each(Orderdata.sourcedata,function(index){
        if(index != 0){
            series.push({type: 'line', smooth: true, seriesLayoutBy: 'row', emphasis: {focus: 'series'}});
        }

    });
    var length=Orderdata.sourcedata[0].length;
    length=length-1;
    series.push({
        type: 'pie',
        id: 'pie',
        radius: '30%',
        center: ['50%', '28%'],
        emphasis: {focus: 'data'},
        label: {
            formatter: '{b}: {@'+Orderdata.sourcedata[0][length]+'} ({d}%)'
        },
        encode: {
            itemName: Orderdata.sourcedata[0][0],
            value:  Orderdata.sourcedata[0][length],
            tooltip:  Orderdata.sourcedata[0][length]
        }
    });
    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init();

            //绑定事件
            $('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
                var panel = $($(this).attr("href"));
                if (panel.length > 0) {
                    Controller.table[panel.attr("id")].call(this);
                    $(this).on('click', function (e) {
                        $($(this).attr("href")).find(".btn-refresh").trigger("click");
                    });
                }
                //移除绑定的事件
                $(this).unbind('shown.bs.tab');
            });

            //必须默认触发shown.bs.tab事件
            $('ul.nav-tabs li.active a[data-toggle="tab"]').trigger("shown.bs.tab");

        },
        table: {
            one: function () {
                // 基于准备好的dom，初始化echarts实例
                var myChart = Echarts.init(document.getElementById('echart'), 'walden');

                // 指定图表的配置项和数据
                var  option = {
                    legend: {},
                    tooltip: {
                        trigger: 'axis',
                        showContent: false
                    },
                    dataset: {
                        source: Orderdata.Personsourcedata
                    },
                    xAxis: {type: 'category'},
                    yAxis: {gridIndex: 0},
                    grid: {top: '55%'},
                    series:series
                };

                myChart.on('updateAxisPointer', function (event) {
                    var xAxisInfo = event.axesInfo[0];
                    if (xAxisInfo) {
                        var dimension = xAxisInfo.value + 1;
                        myChart.setOption({
                            series: {
                                id: 'pie',
                                label: {
                                    formatter: '{b}: {@[' + dimension + ']} ({d}%)'
                                },
                                encode: {
                                    value: dimension,
                                    tooltip: dimension
                                }
                            }
                        });
                    }
                });

                // 使用刚指定的配置项和数据显示图表。
                myChart.setOption(option);

                var myChart2=Echarts.init(document.getElementById('echart2'), 'walden');
                var option2 = {
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            type: 'cross',
                            crossStyle: {
                                color: '#999'
                            }
                        }
                    },
                    toolbox: {
                        feature: {
                            dataView: {show: true, readOnly: false},
                            magicType: {show: true, type: ['line', 'bar']},
                            restore: {show: true},
                            saveAsImage: {show: true}
                        }
                    },
                    legend: {
                        data: ['合同金额', '合同份数']
                    },
                    xAxis: [
                        {
                            type: 'category',
                            data: Orderdata.personcontactdata.date,
                            axisPointer: {
                                type: 'shadow'
                            }
                        }
                    ],
                    yAxis: [
                        {
                            type: 'value',
                            name: '金额',

                            axisLabel: {
                                formatter: '{value} 元'
                            }
                        },
                        {
                            type: 'value',
                            name: '合同数',

                            axisLabel: {
                                formatter: '{value} 份'
                            }
                        }
                    ],
                    series: [
                        {
                            name: '合同金额',
                            type: 'bar',
                            data: Orderdata.personcontactdata.contract
                        },

                        {
                            name: '合同份数',
                            type: 'line',
                            yAxisIndex: 1,
                            data: Orderdata.personcontactdata.num
                        }
                    ]
                };

                // 使用刚指定的配置项和数据显示图表。
                myChart2.setOption(option2);

                var myChart3=Echarts.init(document.getElementById('echart3'), 'walden');
                var option3 = {
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            type: 'cross',
                            crossStyle: {
                                color: '#999'
                            }
                        }
                    },
                    toolbox: {
                        feature: {
                            dataView: {show: true, readOnly: false},
                            magicType: {show: true, type: ['line', 'bar']},
                            restore: {show: true},
                            saveAsImage: {show: true}
                        }
                    },
                    legend: {
                        data: ['回款金额']
                    },
                    xAxis: [
                        {
                            type: 'category',
                            data: Orderdata.personcontactdata.date,
                            axisPointer: {
                                type: 'shadow'
                            }
                        }
                    ],
                    yAxis: [
                        {
                            type: 'value',
                            name: '金额',

                            axisLabel: {
                                formatter: '{value} 元'
                            }
                        }
                    ],
                    series: [

                        {
                            name: '回款金额',
                            type: 'bar',
                            data: Orderdata.personcontactdata.receivables
                        }

                    ]
                };

                // 使用刚指定的配置项和数据显示图表。
                myChart3.setOption(option3);

                $(window).resize(function () {
                    myChart.resize();
                });

                $(document).on("click", ".btn-refresh", function () {
                    setTimeout(function () {
                        myChart.resize();
                    }, 0);
                });
            },
            two: function () {
                // 基于准备好的dom，初始化echarts实例
                var myChart = Echarts.init(document.getElementById('echart6'), 'walden');

                // 指定图表的配置项和数据
                var  option = {
                    legend: {},
                    tooltip: {
                        trigger: 'axis',
                        showContent: false
                    },
                    dataset: {
                        source: Orderdata.sourcedata
                    },
                    xAxis: {type: 'category'},
                    yAxis: {gridIndex: 0},
                    grid: {top: '55%'},
                    series:series
                };

                myChart.on('updateAxisPointer', function (event) {
                    var xAxisInfo = event.axesInfo[0];
                    if (xAxisInfo) {
                        var dimension = xAxisInfo.value + 1;
                        myChart.setOption({
                            series: {
                                id: 'pie',
                                label: {
                                    formatter: '{b}: {@[' + dimension + ']} ({d}%)'
                                },
                                encode: {
                                    value: dimension,
                                    tooltip: dimension
                                }
                            }
                        });
                    }
                });

                // 使用刚指定的配置项和数据显示图表。
                myChart.setOption(option);

                var myChart2=Echarts.init(document.getElementById('echart7'), 'walden');
                var option2 = {
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            type: 'cross',
                            crossStyle: {
                                color: '#999'
                            }
                        }
                    },
                    toolbox: {
                        feature: {
                            dataView: {show: true, readOnly: false},
                            magicType: {show: true, type: ['line', 'bar']},
                            restore: {show: true},
                            saveAsImage: {show: true}
                        }
                    },
                    legend: {
                        data: ['合同金额', '合同份数']
                    },
                    xAxis: [
                        {
                            type: 'category',
                            data: Orderdata.contactdata.date,
                            axisPointer: {
                                type: 'shadow'
                            }
                        }
                    ],
                    yAxis: [
                        {
                            type: 'value',
                            name: '金额',

                            axisLabel: {
                                formatter: '{value} 元'
                            }
                        },
                        {
                            type: 'value',
                            name: '合同数',

                            axisLabel: {
                                formatter: '{value} 份'
                            }
                        }
                    ],
                    series: [
                        {
                            name: '合同金额',
                            type: 'bar',
                            data: Orderdata.contactdata.contract
                        },

                        {
                            name: '合同份数',
                            type: 'line',
                            yAxisIndex: 1,
                            data: Orderdata.contactdata.num
                        }
                    ]
                };

                // 使用刚指定的配置项和数据显示图表。
                myChart2.setOption(option2);

                var myChart3=Echarts.init(document.getElementById('echart5'), 'walden');
                var option3 = {
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            type: 'cross',
                            crossStyle: {
                                color: '#999'
                            }
                        }
                    },
                    toolbox: {
                        feature: {
                            dataView: {show: true, readOnly: false},
                            magicType: {show: true, type: ['line', 'bar']},
                            restore: {show: true},
                            saveAsImage: {show: true}
                        }
                    },
                    legend: {
                        data: ['回款金额']
                    },
                    xAxis: [
                        {
                            type: 'category',
                            data: Orderdata.contactdata.date,
                            axisPointer: {
                                type: 'shadow'
                            }
                        }
                    ],
                    yAxis: [
                        {
                            type: 'value',
                            name: '金额',

                            axisLabel: {
                                formatter: '{value} 元'
                            }
                        }
                    ],
                    series: [

                        {
                            name: '回款金额',
                            type: 'bar',
                            data: Orderdata.contactdata.receivables
                        }

                    ]
                };

                // 使用刚指定的配置项和数据显示图表。
                myChart3.setOption(option3);

                $(window).resize(function () {
                    myChart.resize();
                });

                $(document).on("click", ".btn-refresh", function () {
                    setTimeout(function () {
                        myChart.resize();
                    }, 0);
                });
            },

        },
    };

    return Controller;
});
