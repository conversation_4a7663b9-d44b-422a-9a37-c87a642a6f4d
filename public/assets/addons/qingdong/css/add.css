.btn-success{
    background-color:#4E76FB !important;
}
.btn-imports{
    background-color:#F5BD00 !important;
}
.btn-transfer{
    background-color:#55D05F !important;
}
.btn-send-email{
    background-color:#E74C44 !important;
}
.btn-send-sms{
    background-color:#4E9FFB !important;
    margin-left:3px;
}
.btn-refresh{
    background-color:#4E76FB !important;
}
.records,.seas{
    color:#4E9FFB;
    font-weight: 500;
}
.pagination > .active > a, .pagination > .active > span, .pagination > .active > a:hover, .pagination > .active > span:hover, .pagination > .active > a:focus, .pagination > .active > span:focus{
    background-color:#4E76FB !important;
    border-color:#4E76FB !important;
}
.records{
    padding-right:10px;
}
.toolbar a{
    margin-right:6px;
}
.commonsearch-table{
    background-color:#fff;
    padding-top:20px;
    padding-bottom:10px;
}
.panel{
    background-color:#f1f4f6 !important;
}
.fixed-table-container{
    background-color:#fff;
}
tbody tr td{
    padding-top: 20px !important;
    padding-bottom: 20px !important;
}
.fixed-table-toolbar{
    padding-top: 10px;
    padding-bottom: 10px;
}
.toolbar a:hover{
    border:0px solid #fff !important;
}

.form-group label{
    font-weight:normal !important;
    margin-right:10px !important;
    min-width:55px !important;
    padding-top:18px !important;
}
.form-group .col-sm-6 .col-sm-10{
    width:100% !important;
}
.col-sm-12 textarea{
    width:88.5% !important;
}
.form-control{
    width:75% !important;
    margin-top:10px !important;
}

#c-next_time{
    width:67% !important;
}
.input-group{
    width:90% !important;
    float:right;
}
#c-image{
    width:100% !important;
}
.input-group-addon{
    padding-top: 8px !important;
    padding-left: 12px !important;
    padding-right: 12px !important;
    width: 0% !important;
}
.form-group-selects{
    height:56px;
}
.bootstrap-select{
    margin-top:0px !important;
}
