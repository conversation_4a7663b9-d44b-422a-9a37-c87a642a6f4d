
.records,.seas{
    color:#4E9FFB;
    font-weight: 500;
    font-size: 12px !important;
}
.pagination > .active > a, .pagination > .active > span, .pagination > .active > a:hover, .pagination > .active > span:hover, .pagination > .active > a:focus, .pagination > .active > span:focus{
    background-color:#4E76FB !important;
    border-color:#4E76FB !important;
}
.toolbar a{
    margin-right:6px;
}
.commonsearch-table{
    background-color:#fff;
    padding-top:20px;
    padding-bottom:10px;
}
.panel{
    background-color:#f1f4f6 !important;
}
.fixed-table-container{
    background-color:#fff;
}
tbody tr td{
    padding-top: 20px !important;
    padding-bottom: 20px !important;
}

.layui-layer-fast .layui-layer-title{
    background:#edf5ff !important;
    color:#4E76FB !important;
}
.layui-layer-fast .layui-layer-footer{
    background-color:#fff !important;
    text-align:center !important;
    padding-top:0px !important;
    padding-bottom:20px !important;
}
.layui-layer-fast .layui-layer-setwin > a:after{
    color:#4E76FB !important;
}
.layui-layer-footer .btn{
    margin-right:20px !important;
}
.layui-layer-fast .layui-layer-btn .layui-layer-btn0{
    background-color:#1e9fff !important;
    border-color:#1e9fff !important;
}
.btn-editone,.btn-delone{
    background-color:unset !important;
    color:#4E76FB !important;
}
.btn-editone:hover,.btn-delone:hover{
    background-color:unset !important;
    color:#4E76FB !important;
    border-color: unset !important;
    border: unset !important;
}
.tooltip{
    display: none !important;
}
.btn-editone, .btn-delone{
    color:#4E9FFB !important;
    font-weight: 500 !important;
}

