(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-b7723af0"],{"24ae":function(t,a,e){"use strict";e("3d10")},"3d10":function(t,a,e){var o=e("4748");"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var d=e("499e").default;d("4033f784",o,!0,{sourceMap:!1,shadowMode:!1})},4748:function(t,a,e){var o=e("24fb");a=o(!1),a.push([t.i,".box-center[data-v-467f097f],.box-left[data-v-467f097f],.box-right[data-v-467f097f]{height:calc(100vh - 64px);border:1px solid #f5f5f5;box-sizing:border-box}.box-left[data-v-467f097f]{border-left:0}.box-center[data-v-467f097f]{padding:50px;background:#efefef}.box-right[data-v-467f097f]{position:relative}.tips[data-v-467f097f]{color:#ccc}.deletebox[data-v-467f097f],.tips[data-v-467f097f]{width:100%;text-align:center}.deletebox[data-v-467f097f]{padding:30px 0;border-top:20px solid #efefef;color:#ed4014;position:absolute;bottom:0}.tabbox[data-v-467f097f]{padding:20px}.tabtitle[data-v-467f097f]{font-size:26px;color:#333;background:#f5f5f5;padding-left:20px;margin-bottom:20px}.ivu-tabs[data-v-467f097f]{overflow:visible}.tabbox[data-v-467f097f]::-webkit-scrollbar{display:none}",""]),t.exports=a},bb51:function(t,a,e){"use strict";e.r(a);var o=function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",[e("Header"),e("Row",{attrs:{type:"flex",justify:"space-between"}},[e("i-col",{attrs:{xs:24,sm:24,md:12,lg:8,"class-name":"box-left"}},[e("LeftMenu"),e("div",{staticClass:"tabbox"},[e("div",{staticClass:"tabtitle"},[t._v("字段配置")]),t.reload?e(t.curformdata.component?"qdsd-"+t.curformdata.component+"-edit":"",{tag:"component"}):t._e(),t.curformdata.component?t._e():e("h2",{staticClass:"tips"},[t._v("请选择控件")])],1)],1),e("i-col",{attrs:{xs:24,sm:24,md:12,lg:16,"class-name":"box-center"}},[e("MainConfig")],1)],1)],1)},d=[],s=e("9efd"),r={data(){return{deletelist:[],id:7,reload:!0}},computed:{curformdata(){return this.$store.state.curformdata}},watch:{curformdata(){this.reload=!1,this.$nextTick((function(){this.reload=!0}))}},methods:{adddelete(){this.$store.state.curformdata={}},getRow(){var t=this;t.$http.post(s["a"].design_data,{id:t.id,_ajax:1}).then(a=>{a=a.data,1==a.code?(JSON.parse(a.data.data)&&(t.$store.state.dataconfig=JSON.parse(a.data.data).data),t.$store.state.baseData=a.data):t.$Message.error(a.data.msg)}).catch(()=>{t.$Message.error("网络错误")})}},beforeRouteUpdate(t){t.query.id!=this.id&&(this.id=t.query.id,this.$store.state.id=t.query.id,this.getRow())},created(){},mounted(){this.id=this.$route.query.id,this.$store.state.id=this.$route.query.id,this.getRow()}},i=r,f=(e("24ae"),e("2877")),n=Object(f["a"])(i,o,d,!1,null,"467f097f",null);a["default"]=n.exports}}]);
//# sourceMappingURL=chunk-b7723af0.5cb4589f.js.map