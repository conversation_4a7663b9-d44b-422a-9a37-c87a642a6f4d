(function(t){function e(e){for(var o,i,n=e[0],l=e[1],c=e[2],d=0,f=[];d<n.length;d++)i=n[d],Object.prototype.hasOwnProperty.call(r,i)&&r[i]&&f.push(r[i][0]),r[i]=0;for(o in l)Object.prototype.hasOwnProperty.call(l,o)&&(t[o]=l[o]);u&&u(e);while(f.length)f.shift()();return s.push.apply(s,c||[]),a()}function a(){for(var t,e=0;e<s.length;e++){for(var a=s[e],o=!0,i=1;i<a.length;i++){var l=a[i];0!==r[l]&&(o=!1)}o&&(s.splice(e--,1),t=n(n.s=a[0]))}return t}var o={},r={app:0},s=[];function i(t){return n.p+"assets/addons/qingdong/js/"+({}[t]||t)+"."+{"chunk-ea4c6e30":"742aaccb"}[t]+".js"}function n(e){if(o[e])return o[e].exports;var a=o[e]={i:e,l:!1,exports:{}};return t[e].call(a.exports,a,a.exports,n),a.l=!0,a.exports}n.e=function(t){var e=[],a=r[t];if(0!==a)if(a)e.push(a[2]);else{var o=new Promise((function(e,o){a=r[t]=[e,o]}));e.push(a[2]=o);var s,l=document.createElement("script");l.charset="utf-8",l.timeout=120,n.nc&&l.setAttribute("nonce",n.nc),l.src=i(t);var c=new Error;s=function(e){l.onerror=l.onload=null,clearTimeout(d);var a=r[t];if(0!==a){if(a){var o=e&&("load"===e.type?"missing":e.type),s=e&&e.target&&e.target.src;c.message="Loading chunk "+t+" failed.\n("+o+": "+s+")",c.name="ChunkLoadError",c.type=o,c.request=s,a[1](c)}r[t]=void 0}};var d=setTimeout((function(){s({type:"timeout",target:l})}),12e4);l.onerror=l.onload=s,document.head.appendChild(l)}return Promise.all(e)},n.m=t,n.c=o,n.d=function(t,e,a){n.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:a})},n.r=function(t){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},n.t=function(t,e){if(1&e&&(t=n(t)),8&e)return t;if(4&e&&"object"===typeof t&&t&&t.__esModule)return t;var a=Object.create(null);if(n.r(a),Object.defineProperty(a,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var o in t)n.d(a,o,function(e){return t[e]}.bind(null,o));return a},n.n=function(t){var e=t&&t.__esModule?function(){return t["default"]}:function(){return t};return n.d(e,"a",e),e},n.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},n.p="/",n.oe=function(t){throw console.error(t),t};var l=window["webpackJsonp"]=window["webpackJsonp"]||[],c=l.push.bind(l);l.push=e,l=l.slice();for(var d=0;d<l.length;d++)e(l[d]);var u=c;s.push([0,"chunk-vendors"]),a()})({0:function(t,e,a){t.exports=a("56d7")},"00ba":function(t,e,a){"use strict";a("9af0")},"0195":function(t,e,a){"use strict";a("ad8b")},"0418":function(t,e,a){"use strict";a.r(e);var o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{attrs:{id:"header"}},[a("Row",[a("i-col",{attrs:{span:"8",offset:"1","class-name":"title"}},[t._v("标题"+t._s(t.baseData.name))]),a("i-col",{attrs:{span:"13","class-name":"action"}},[a("Button",{attrs:{type:"primary"},on:{click:t.save}},[t._v("保存")]),a("Button",{attrs:{type:"primary"},on:{click:t.screen}},[t._v(t._s(t.fullscreen?"退出全屏":"全屏"))])],1)],1)],1)},r=[],s=a("9efd"),i={components:{},data(){return{showbox:!1,fullscreen:!1}},computed:{theme(){return"Theme"+this.$store.state.formconfig.themeSelected},baseData(){return this.$store.state.baseData},formconfig(){return this.$store.state.formconfig},config(){return this.$store.state.dataconfig}},watch:{},methods:{screen(){let t=document.documentElement;this.fullscreen?document.exitFullscreen?document.exitFullscreen():document.webkitCancelFullScreen?document.webkitCancelFullScreen():document.mozCancelFullScreen?document.mozCancelFullScreen():document.msExitFullscreen&&document.msExitFullscreen():t.requestFullscreen?t.requestFullscreen():t.webkitRequestFullScreen?t.webkitRequestFullScreen():t.mozRequestFullScreen?t.mozRequestFullScreen():t.msRequestFullscreen&&t.msRequestFullscreen(),this.fullscreen=!this.fullscreen},save(){var t=this;console.log(t.$store.state.dataconfig,"提交的数据");let e=t.$store.state.dataconfig,a=!1,o=!1,r={};e.forEach(t=>{if("radio"==t.component||"checkbox"==t.component){let e=t.config.content;e.forEach(e=>{e.value||(a=!0,r=t)})}if("select"==t.component){let e=t.config.content;e.length<=0&&(o=!0,r=t)}}),console.log(a,r,"判断数据"),a||o?t.$Message.error("请完善"+r.config.label+"的选项设置"):t.$http.post(s["a"].design_save,{id:t.$store.state.id,data:JSON.stringify({data:t.$store.state.dataconfig}),_ajax:1}).then((function(e){1==e.data.code?t.$Message.success(e.data.msg):t.$Message.error(e.data.msg)})).catch(()=>{t.$Message.error("网络错误")})}},created(){},mounted(){}},n=i,l=(a("a901"),a("2877")),c=Object(l["a"])(n,o,r,!1,null,null,null);e["default"]=c.exports},"0b27":function(t,e,a){"use strict";a.r(e);var o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"components-box"},[a("Divider",{staticClass:"title",attrs:{orientation:"left"}},[t._v("字段库（向右拖拽）")]),a("div",{staticClass:"box"},[a("draggable",{attrs:{tag:"ul",list:t.baselist,group:{name:"content",pull:"clone"},pull:"clone",sort:!1,disabled:t.draggableStatus},on:{start:function(e){return t.startdrag("base",e)},end:function(e){return t.enddrag("base",e)}}},t._l(t.baselist,(function(e,o){return a("li",{key:o},[a("Button",{attrs:{type:"dashed"}},[t._v(t._s(e.name))])],1)})),0)],1)],1)},r=[],s=a("b76a"),i=a.n(s),n=a("cf45"),l={components:{draggable:i.a},data(){return{baselist:this.$store.state.baselist}},computed:{draggableStatus(){return this.$store.state.draggableStatus}},watch:{},methods:{startdrag(t,e){this.baselist[e.oldIndex]["showInfoType"]=!0},enddrag(t,e){console.log(t,e),"configbox"==e.to.id&&"base"==t&&(this.baselist=JSON.parse(JSON.stringify(this.baselist)),this.baselist[e.oldIndex]["id"]="main_"+n["a"].getRangeCode(6))}},created(){},mounted(){}},c=l,d=(a("3649"),a("2877")),u=Object(d["a"])(c,o,r,!1,null,"559b1831",null);e["default"]=u.exports},"0c2e":function(t,e,a){var o=a("24fb");e=o(!1),e.push([t.i,".item[data-v-d76c4b2c]{padding-left:10px;background:#efefef;margin-top:5px}.item span[data-v-d76c4b2c]{float:right}.label[data-v-d76c4b2c]{color:var(--labelColor);display:inline-block}",""]),t.exports=e},"0d79":function(t,e,a){"use strict";a.r(e);var o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("i-form",{attrs:{"label-position":"top"}},[a("FormItem",{attrs:{label:"标识名"}},[a("Input",{attrs:{type:"text",placeholder:"请输入标识名"},model:{value:t.label,callback:function(e){t.label=e},expression:"label"}})],1),a("FormItem",{attrs:{label:"提示文字"}},[a("Input",{attrs:{type:"text",placeholder:"请输入提示文字"},model:{value:t.placeholder,callback:function(e){t.placeholder=e},expression:"placeholder"}})],1),a("FormItem",{attrs:{label:"选项设置"}},t._l(t.content,(function(e,o){return a("Row",{key:o,staticStyle:{"margin-bottom":"10px"}},[a("i-col",{attrs:{span:9}},[a("Input",{attrs:{type:"text"},on:{input:t.input},model:{value:t.content[o]["value"],callback:function(e){t.$set(t.content[o],"value",e)},expression:"content[index]['value']"}})],1),a("i-col",{attrs:{span:13,offset:1}},[a("Button",{attrs:{icon:"md-close",size:"small",ghost:"",type:"error"},on:{click:function(e){return t.close(o)}}}),t._v(" "),a("Button",{attrs:{icon:"md-add",ghost:"",size:"small",type:"primary"},on:{click:function(e){return t.add(o)}}})],1)],1)})),1),a("FormItem",{attrs:{label:"默认值"}},[a("Checkbox-group",{model:{value:t.value,callback:function(e){t.value=e},expression:"value"}},t._l(t.content,(function(t,e){return a("Checkbox",{key:e,attrs:{label:t.value}})})),1)],1),t.showInfoType&&t.checkFormType(t.baseData.type)?a("FormItem",{attrs:{label:"信息类型"}},[a("Radio-group",{attrs:{vertical:""},model:{value:t.infoType,callback:function(e){t.infoType=e},expression:"infoType"}},[a("Radio",{attrs:{label:"main"}},[t._v("主要信息 (可用于搜索)")]),a("Radio",{attrs:{label:"additional"}},[t._v("附加信息 (减轻系统搜索压力)")])],1)],1):t._e(),a("FormItem",[a("Checkbox",{model:{value:t.addShow,callback:function(e){t.addShow=e},expression:"addShow"}},[t._v(" 隐藏字段 "),a("Tooltip",{attrs:{placement:"top-start"}},[a("Icon",{staticStyle:{background:"#2d8cf0","border-radius":"50%"},attrs:{type:"ios-help",size:"20",color:"#fff"}}),a("div",{staticStyle:{"white-space":"normal"},attrs:{slot:"content"},slot:"content"},[t._v(" 对于系统中已有数据的字段信息，若不想再次使用且保存该字段已有数据时，可选择将该字段从表单中隐藏，设置后， 原有字段信息仍可在已收集数据的表单中显示，而在新表单中隐藏。 ")])],1)],1)],1),a("FormItem",[a("Checkbox",{model:{value:t.required,callback:function(e){t.required=e},expression:"required"}},[t._v(" 是否必填 "),a("Tooltip",{attrs:{placement:"top-start"}},[a("Icon",{staticStyle:{background:"#2d8cf0","border-radius":"50%"},attrs:{type:"ios-help",size:"20",color:"#fff"}}),a("div",{staticStyle:{"white-space":"normal"},attrs:{slot:"content"},slot:"content"},[t._v(" 字段信息设置为必填后，用户在填写表单时若未填写该信息，则无法保存表单 ")])],1)],1)],1),"main"==t.infoType&&t.checkFormType(t.baseData.type)?a("FormItem",[a("Checkbox",{model:{value:t.listShow,callback:function(e){t.listShow=e},expression:"listShow"}},[t._v(" 列表是否显示 "),a("Tooltip",{attrs:{placement:"top-start"}},[a("Icon",{staticStyle:{background:"#2d8cf0","border-radius":"50%"},attrs:{type:"ios-help",size:"20",color:"#fff"}}),a("div",{staticStyle:{"white-space":"normal"},attrs:{slot:"content"},slot:"content"},[t._v(" 后台数据列表是否默认显示 ")])],1)],1)],1):t._e()],1)},r=[],s=a("cf45"),i={components:{},data(){return{content:this.$store.state.curformdata.config.content,showInfoType:this.$store.state.curformdata.showInfoType}},computed:{baseData(){return this.$store.state.baseData},label:{get(){return this.$store.state.curformdata.config.label},set(t){this.$store.state.curformdata.config.label=t,this.$store.dispatch("UpdateDataConfig")}},vertical:{get(){return this.$store.state.curformdata.config.vertical},set(t){this.$store.state.curformdata.config.vertical=t,this.$store.dispatch("UpdateDataConfig")}},name:{get(){return this.$store.state.curformdata.id},set(t){this.$store.state.curformdata.id=t,this.$store.dispatch("UpdateDataConfig")}},placeholder:{get(){return this.$store.state.curformdata.config.placeholder},set(t){this.$store.state.curformdata.config.placeholder=t,this.$store.dispatch("UpdateDataConfig")}},value:{get(){return this.$store.state.curformdata.config.value},set(t){console.log(t,"======="),this.$store.state.curformdata.config.value=t;let e=t.join(",");this.$store.state.curformdata.value=e,this.$store.dispatch("UpdateDataConfig")}},infoType:{get(){return this.$store.state.curformdata.config.infoType},set(t){this.$store.state.curformdata.config.infoType=t,this.$store.dispatch("UpdateDataConfig")}},listShow:{get(){return this.$store.state.curformdata.config.listShow},set(t){this.$store.state.curformdata.config.listShow=t,this.$store.dispatch("UpdateDataConfig")}},addShow:{get(){return this.$store.state.curformdata.config.addShow},set(t){this.$store.state.curformdata.config.addShow=t,this.$store.dispatch("UpdateDataConfig")}},required:{get(){return this.$store.state.curformdata.config.required},set(t){this.$store.state.curformdata.config.required=t,this.$store.dispatch("UpdateDataConfig")}}},watch:{content(t){this.$store.state.curformdata.config.content=t,this.$store.dispatch("UpdateDataConfig")},infoType(t,e){this.$store.state.curformdata.id="main"==t?"main_"+s["a"].getRangeCode():"other_"+s["a"].getRangeCode(),this.$store.dispatch("UpdateDataConfig")}},methods:{add(t){this.content.push({key:this.content[t].key,value:this.content[t].value})},close(t){if(1==this.content.length)return"";this.content.splice(t,1)},input(){this.$store.state.curformdata.config.content=this.content,this.$store.dispatch("UpdateDataConfig")}},created(){},mounted(){}},n=i,l=(a("0195"),a("2877")),c=Object(l["a"])(n,o,r,!1,null,"db3560de",null);e["default"]=c.exports},"0f42":function(t,e,a){"use strict";a("a41f")},"110b":function(t,e,a){"use strict";a.r(e);var o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{staticClass:"box-left-content"},[a(t.componentName,{tag:"component"})],1)])},r=[],s={components:{},data(){return{menuact:0,componentName:"Componentlist"}},computed:{},watch:{},methods:{changemenu(t,e){this.menuact=e,this.componentName=t}},created(){},mounted(){}},i=s,n=(a("5bd7"),a("2877")),l=Object(n["a"])(i,o,r,!1,null,"272345bd",null);e["default"]=l.exports},"188b":function(t,e,a){"use strict";a.r(e);var o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("FormItem",{directives:[{name:"show",rawName:"v-show",value:!t.data.config.hidden,expression:"!data.config.hidden"}],attrs:{label:t.data.config.label,id:t.data.id,required:t.data.config.required,"label-width":t.getwidth(t.data.config.labelWidth),error:t.error}},[a("Cascader",{attrs:{data:t.datalist,disabled:t.data.config.readonly,placeholder:t.data.config.placeholder,size:t.formconfig.size},model:{value:t.value,callback:function(e){t.value=e},expression:"value"}})],1)},r=[],s=a("bc0a"),i=s["a"],n=a("2877"),l=Object(n["a"])(i,o,r,!1,null,"5f4384f9",null);e["default"]=l.exports},"19b1":function(t,e,a){"use strict";a.r(e);var o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("i-form",{attrs:{"label-position":"top"}},[a("FormItem",{attrs:{label:"标识名"}},[a("Input",{attrs:{type:"text",placeholder:"请输入标识名"},model:{value:t.label,callback:function(e){t.label=e},expression:"label"}})],1),a("FormItem",{attrs:{label:"校验类型"}},[a("i-select",{model:{value:t.inputType,callback:function(e){t.inputType=e},expression:"inputType"}},t._l(t.regularlist,(function(e,o){return a("i-option",{key:o,attrs:{value:e.type}},[t._v(t._s(e.name))])})),1)],1),a("FormItem",{attrs:{label:"提示文字"}},[a("Input",{attrs:{type:"text",placeholder:"请输入提示文字"},model:{value:t.placeholder,callback:function(e){t.placeholder=e},expression:"placeholder"}})],1),a("FormItem",{attrs:{label:"默认值"}},[a("Input",{attrs:{type:"text",placeholder:"请输入默认值"},model:{value:t.value,callback:function(e){t.value=e},expression:"value"}})],1),t.showInfoType&&t.checkFormType(t.baseData.type)?a("FormItem",{attrs:{label:"信息类型"}},[a("Radio-group",{attrs:{vertical:""},model:{value:t.infoType,callback:function(e){t.infoType=e},expression:"infoType"}},[a("Radio",{attrs:{label:"main"}},[t._v("主要信息 (可用于搜索)")]),a("Radio",{attrs:{label:"additional"}},[t._v("附加信息 (减轻系统搜索压力)")])],1)],1):t._e(),"main"==t.infoType&&t.checkFormType(t.baseData.type)?a("FormItem",[a("Checkbox",{model:{value:t.only,callback:function(e){t.only=e},expression:"only"}},[t._v(" 是否唯一 "),a("Tooltip",{attrs:{placement:"top-start"}},[a("Icon",{staticStyle:{background:"#2d8cf0","border-radius":"50%"},attrs:{type:"ios-help",size:"20",color:"#fff"}}),a("div",{staticStyle:{"white-space":"normal"},attrs:{slot:"content"},slot:"content"},[t._v(" 字段设为唯一后在新建信息时则无法填写与系统内部已有信息重复的信息，否则无法保存。如将【新建客户】时“手机号”设为唯一， 则再次新建客户时，重复的手机号输入后，用户将无法保存【新建客户】表单。 ")])],1)],1)],1):t._e(),a("FormItem",[a("Checkbox",{model:{value:t.addShow,callback:function(e){t.addShow=e},expression:"addShow"}},[t._v(" 隐藏字段 "),a("Tooltip",{attrs:{placement:"top-start"}},[a("Icon",{staticStyle:{background:"#2d8cf0","border-radius":"50%"},attrs:{type:"ios-help",size:"20",color:"#fff"}}),a("div",{staticStyle:{"white-space":"normal"},attrs:{slot:"content"},slot:"content"},[t._v(" 对于系统中已有数据的字段信息，若不想再次使用且保存该字段已有数据时，可选择将该字段从表单中隐藏，设置后， 原有字段信息仍可在已收集数据的表单中显示，而在新表单中隐藏。 ")])],1)],1)],1),a("FormItem",[a("Checkbox",{model:{value:t.required,callback:function(e){t.required=e},expression:"required"}},[t._v(" 是否必填 "),a("Tooltip",{attrs:{placement:"top-start"}},[a("Icon",{staticStyle:{background:"#2d8cf0","border-radius":"50%"},attrs:{type:"ios-help",size:"20",color:"#fff"}}),a("div",{staticStyle:{"white-space":"normal"},attrs:{slot:"content"},slot:"content"},[t._v(" 字段信息设置为必填后，用户在填写表单时若未填写该信息，则无法保存表单 ")])],1)],1)],1),"main"==t.infoType&&t.checkFormType(t.baseData.type)?a("FormItem",[a("Checkbox",{model:{value:t.listShow,callback:function(e){t.listShow=e},expression:"listShow"}},[t._v(" 列表是否显示 "),a("Tooltip",{attrs:{placement:"top-start"}},[a("Icon",{staticStyle:{background:"#2d8cf0","border-radius":"50%"},attrs:{type:"ios-help",size:"20",color:"#fff"}}),a("div",{staticStyle:{"white-space":"normal"},attrs:{slot:"content"},slot:"content"},[t._v(" 后台数据列表是否默认显示 ")])],1)],1)],1):t._e()],1)},r=[],s=a("cf45"),i={components:{},data(){const t=(t,e,a)=>{reg.test(e)?a():a(new Error("请填写正确的格式"))};return{showInfoType:this.$store.state.curformdata.showInfoType,regularlist:[{type:"text",name:"文本输入"},{type:"number",name:"数字输入"},{type:"idcard",name:"身份证输入"},{type:"digit",name:"带小数点的数字输入"}],reg:"/^[\\u4e00-\\u9fa5]+$/",ruleValidate:{valuea:[{validator:t,trigger:"blur"}]},formParams:{valuea:""}}},computed:{baseData(){return this.$store.state.baseData},label:{get(){return this.$store.state.curformdata.config.label},set(t){this.$store.state.curformdata.config.label=t,this.$store.dispatch("UpdateDataConfig")}},name:{get(){return this.$store.state.curformdata.id},set(t){this.$store.state.curformdata.id=t,this.$store.dispatch("UpdateDataConfig")}},placeholder:{get(){return this.$store.state.curformdata.config.placeholder},set(t){this.$store.state.curformdata.config.placeholder=t,this.$store.dispatch("UpdateDataConfig")}},value:{get(){return this.$store.state.curformdata.value},set(t){this.$store.state.curformdata.value=t,this.$store.state.curformdata.config.value=t,this.$store.dispatch("UpdateDataConfig")}},infoType:{get(){return this.$store.state.curformdata.config.infoType},set(t){this.$store.state.curformdata.config.infoType=t,this.$store.dispatch("UpdateDataConfig")}},only:{get(){return this.$store.state.curformdata.config.only},set(t){this.$store.state.curformdata.config.only=t,this.$store.dispatch("UpdateDataConfig")}},listShow:{get(){return this.$store.state.curformdata.config.listShow},set(t){this.$store.state.curformdata.config.listShow=t,this.$store.dispatch("UpdateDataConfig")}},addShow:{get(){return this.$store.state.curformdata.config.addShow},set(t){this.$store.state.curformdata.config.addShow=t,this.$store.dispatch("UpdateDataConfig")}},required:{get(){return this.$store.state.curformdata.config.required},set(t){this.$store.state.curformdata.config.required=t,this.$store.dispatch("UpdateDataConfig")}},inputType:{get(){return this.$store.state.curformdata.config.inputType},set(t){this.regularlist.forEach(e=>{e.type==t&&(this.reg=e.value)}),this.$store.state.curformdata.config.inputType=t,this.$store.dispatch("UpdateDataConfig")}}},watch:{infoType(t,e){this.$store.state.curformdata.id="main"==t?"main_"+s["a"].getRangeCode():"other_"+s["a"].getRangeCode(),this.$store.dispatch("UpdateDataConfig")}},methods:{},created(){},mounted(){}},n=i,l=(a("20b8"),a("2877")),c=Object(l["a"])(n,o,r,!1,null,"68c96d1e",null);e["default"]=c.exports},"1d0c":function(t,e,a){var o=a("24fb");e=o(!1),e.push([t.i,".ivu-select-dropdown{left:0!important}",""]),t.exports=e},"1d79":function(module,__webpack_exports__,__webpack_require__){"use strict";__webpack_exports__["a"]={props:{data:Object,formconfig:Object},components:{},data(){return{error:"",defaultList:JSON.parse(JSON.stringify(this.data.config.value)),value:JSON.parse(JSON.stringify(this.data.config.value)),viewimg:"",visible:!1,url:window.basefile+"/ajax/upload"}},computed:{},watch:{value(value){this.data.config.required&&(""===value||""!=this.data.config.regular&&!eval(this.data.config.regular).test(value))?this.error=this.data.config.message:(this.error="",document.getElementById(this.data.id).classList.remove("ivu-form-item-error")),this.$emit("backdata",this.data.id,value)}},methods:{getwidth(t){return 0==t||"top"==this.formconfig.position?null:t},exceededSize(){this.$Message.error("文件超出指定大小限制时的钩子")},beforeUpload(){return this.data.config.maxnum<this.value.length+this.defaultList.length||0==this.data.config.maxnum?(this.$Message.error("文件数量已超出最大数"),!1):void 0},success(t){if(!t.code)return this.$Message.error(t.msg),!1;this.value.push({url:t.data.url,name:"文件"+Math.random()})},accepterror(){this.$Message.error("文件格式不正确")},handleView(t){this.visible=!0,this.viewimg=t},handleRemove(t){var e=JSON.parse(JSON.stringify(this.value));e.splice(t,1),this.value=e,this.defaultList=e},uploaderror(t){var e=t.toString();e.search(/401/i)?this.$Message.error("请登陆后操作"):this.$Message.error("网络错误")}},created(){},mounted(){}}},2035:function(module,__webpack_exports__,__webpack_require__){"use strict";__webpack_exports__["a"]={props:{data:Object,formconfig:Object},components:{},data(){return{error:""}},computed:{value:{get(){return this.data.config.value},set(value){this.data.config.required&&(""==value||""!=this.data.config.regular&&!eval(this.data.config.regular).test(value))?this.error=this.data.config.message:(this.error="",document.getElementById(this.data.id).classList.remove("ivu-form-item-error")),this.$emit("backdata",this.data.id,value)}}},watch:{},methods:{getwidth(t){return 0==t||"top"==this.formconfig.position?null:t}},created(){},mounted(){this.data.config.value&&(this.value=this.data.config.value)}}},"20b8":function(t,e,a){"use strict";a("9f8b")},"20da":function(t,e,a){var o=a("24fb");e=o(!1),e.push([t.i,".ivu-form-item[data-v-21dcac8d]{margin-bottom:15px}.circle[data-v-21dcac8d]{position:absolute;top:-30px;left:50px;z-index:1000}",""]),t.exports=e},"2cfd":function(t,e,a){"use strict";a("b50f")},"2ff1":function(t,e,a){"use strict";a.r(e);var o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("FormItem",{directives:[{name:"show",rawName:"v-show",value:!t.data.config.hidden,expression:"!data.config.hidden"}],attrs:{label:t.data.config.label,id:t.data.id,required:t.data.config.required,"label-width":t.getwidth(t.data.config.labelWidth),error:t.error}},[a("Input",{attrs:{type:"number",readonly:t.data.config.readonly,placeholder:t.data.config.placeholder,size:t.formconfig.size},model:{value:t.value,callback:function(e){t.value=e},expression:"value"}})],1)},r=[],s=a("74d3"),i=s["a"],n=a("2877"),l=Object(n["a"])(i,o,r,!1,null,"1d35c06a",null);e["default"]=l.exports},"33af":function(t,e,a){var o=a("24fb");e=o(!1),e.push([t.i,"#header{width:100%;height:64px;line-height:64px;font-size:16px;display:block;box-shadow:0 2px 4px rgba(0,0,0,.12),0 0 6px rgba(0,0,0,.04)}.title{font-weight:700}.action{text-align:right}.action button{margin-left:15px}",""]),t.exports=e},3567:function(t,e,a){"use strict";a.r(e);var o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("FormItem",{directives:[{name:"show",rawName:"v-show",value:!t.data.config.hidden,expression:"!data.config.hidden"}],attrs:{label:t.data.config.label,id:t.data.id,required:t.data.config.required,"label-width":t.getwidth(t.data.config.labelWidth),error:t.error}},[a("RadioGroup",{attrs:{size:t.formconfig.size,type:t.data.config.type},model:{value:t.value,callback:function(e){t.value=e},expression:"value"}},t._l(t.data.config.content,(function(e,o){return a("Radio",{key:o,attrs:{label:e.value}},[t._v(" "+t._s(e.value))])})),1)],1)},r=[],s=a("2035"),i=s["a"],n=a("2877"),l=Object(n["a"])(i,o,r,!1,null,"6485f628",null);e["default"]=l.exports},3591:function(t,e,a){var o=a("97b3");"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var r=a("499e").default;r("16357f67",o,!0,{sourceMap:!1,shadowMode:!1})},"35f6":function(t,e,a){"use strict";a.r(e);var o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("FormItem",{directives:[{name:"show",rawName:"v-show",value:!t.data.config.hidden,expression:"!data.config.hidden"}],attrs:{label:t.data.config.label,id:t.data.id,required:t.data.config.required,"label-width":t.getwidth(t.data.config.labelWidth),error:t.error}},[a("CheckboxGroup",{attrs:{size:t.formconfig.size},model:{value:t.value,callback:function(e){t.value=e},expression:"value"}},t._l(t.data.config.content,(function(e,o){return a("Checkbox",{key:o,attrs:{label:e.value}},[a("span",{staticStyle:{"padding-left":"10px"}},[t._v(t._s(e.value))])])})),1)],1)},r=[],s=a("42d4"),i=s["a"],n=a("2877"),l=Object(n["a"])(i,o,r,!1,null,"485d71ec",null);e["default"]=l.exports},3649:function(t,e,a){"use strict";a("3591")},3769:function(t,e,a){var o=a("ed8a");"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var r=a("499e").default;r("8a531e4e",o,!0,{sourceMap:!1,shadowMode:!1})},"37d9":function(t,e,a){"use strict";a("3829")},3829:function(t,e,a){var o=a("eed5");"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var r=a("499e").default;r("77f496b7",o,!0,{sourceMap:!1,shadowMode:!1})},"3a03":function(t,e,a){"use strict";a.r(e);var o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("i-form",{attrs:{"label-position":"top"}},[a("FormItem",{attrs:{label:"标识名"}},[a("Input",{attrs:{type:"text",placeholder:"请输入标识名"},model:{value:t.label,callback:function(e){t.label=e},expression:"label"}})],1),a("FormItem",{attrs:{label:"文件大小限制(kb)"}},[a("InputNumber",{staticStyle:{width:"100%"},model:{value:t.maxSize,callback:function(e){t.maxSize=e},expression:"maxSize"}})],1),a("FormItem",{attrs:{label:"文件数量"}},[a("InputNumber",{staticStyle:{width:"100%"},model:{value:t.maxnum,callback:function(e){t.maxnum=e},expression:"maxnum"}})],1),a("FormItem",{attrs:{label:"文件格式"}},[a("Input",{attrs:{type:"text",placeholder:"请输入文件格式"},model:{value:t.format,callback:function(e){t.format=e},expression:"format"}})],1),t.required?a("FormItem",{attrs:{label:"提示文字"}},[a("Input",{attrs:{type:"text",placeholder:"请输入提示文字"},model:{value:t.placeholder,callback:function(e){t.placeholder=e},expression:"placeholder"}})],1):t._e(),t.showInfoType&&t.checkFormType(t.baseData.type)?a("FormItem",{attrs:{label:"信息类型"}},[a("Radio-group",{attrs:{vertical:""},model:{value:t.infoType,callback:function(e){t.infoType=e},expression:"infoType"}},[a("Radio",{attrs:{label:"main"}},[t._v("主要信息 (可用于搜索)")]),a("Radio",{attrs:{label:"additional"}},[t._v("附加信息 (减轻系统搜索压力)")])],1)],1):t._e(),a("FormItem",[a("Checkbox",{model:{value:t.addShow,callback:function(e){t.addShow=e},expression:"addShow"}},[t._v(" 隐藏字段 "),a("Tooltip",{attrs:{placement:"top-start"}},[a("Icon",{staticStyle:{background:"#2d8cf0","border-radius":"50%"},attrs:{type:"ios-help",size:"20",color:"#fff"}}),a("div",{staticStyle:{"white-space":"normal"},attrs:{slot:"content"},slot:"content"},[t._v(" 对于系统中已有数据的字段信息，若不想再次使用且保存该字段已有数据时，可选择将该字段从表单中隐藏，设置后， 原有字段信息仍可在已收集数据的表单中显示，而在新表单中隐藏。 ")])],1)],1)],1),a("FormItem",[a("Checkbox",{model:{value:t.required,callback:function(e){t.required=e},expression:"required"}},[t._v(" 是否必填 "),a("Tooltip",{attrs:{placement:"top-start"}},[a("Icon",{staticStyle:{background:"#2d8cf0","border-radius":"50%"},attrs:{type:"ios-help",size:"20",color:"#fff"}}),a("div",{staticStyle:{"white-space":"normal"},attrs:{slot:"content"},slot:"content"},[t._v(" 字段信息设置为必填后，用户在填写表单时若未填写该信息，则无法保存表单 ")])],1)],1)],1),"main"==t.infoType&&t.checkFormType(t.baseData.type)?a("FormItem",[a("Checkbox",{model:{value:t.listShow,callback:function(e){t.listShow=e},expression:"listShow"}},[t._v(" 列表是否显示 "),a("Tooltip",{attrs:{placement:"top-start"}},[a("Icon",{staticStyle:{background:"#2d8cf0","border-radius":"50%"},attrs:{type:"ios-help",size:"20",color:"#fff"}}),a("div",{staticStyle:{"white-space":"normal"},attrs:{slot:"content"},slot:"content"},[t._v(" 后台数据列表是否默认显示 ")])],1)],1)],1):t._e()],1)},r=[],s=a("cf45"),i={components:{},data(){return{showInfoType:this.$store.state.curformdata.showInfoType}},computed:{baseData(){return this.$store.state.baseData},label:{get(){return this.$store.state.curformdata.config.label},set(t){this.$store.state.curformdata.config.label=t,this.$store.dispatch("UpdateDataConfig")}},name:{get(){return this.$store.state.curformdata.id},set(t){this.$store.state.curformdata.id=t,this.$store.dispatch("UpdateDataConfig")}},maxnum:{get(){return this.$store.state.curformdata.config.maxnum},set(t){this.$store.state.curformdata.config.maxnum=t,this.$store.dispatch("UpdateDataConfig")}},maxSize:{get(){return this.$store.state.curformdata.config.maxSize},set(t){this.$store.state.curformdata.config.maxSize=t,this.$store.dispatch("UpdateDataConfig")}},format:{get(){return this.$store.state.curformdata.config.format.join(",")},set(t){this.$store.state.curformdata.config.format=t.split(","),this.$store.dispatch("UpdateDataConfig")}},required:{get(){return this.$store.state.curformdata.config.required},set(t){this.$store.state.curformdata.config.required=t,this.$store.dispatch("UpdateDataConfig")}},infoType:{get(){return this.$store.state.curformdata.config.infoType},set(t){this.$store.state.curformdata.config.infoType=t,this.$store.dispatch("UpdateDataConfig")}},listShow:{get(){return this.$store.state.curformdata.config.listShow},set(t){this.$store.state.curformdata.config.listShow=t,this.$store.dispatch("UpdateDataConfig")}},addShow:{get(){return this.$store.state.curformdata.config.addShow},set(t){this.$store.state.curformdata.config.addShow=t,this.$store.dispatch("UpdateDataConfig")}},placeholder:{get(){return this.$store.state.curformdata.config.placeholder},set(t){this.$store.state.curformdata.config.placeholder=t,this.$store.dispatch("UpdateDataConfig")}}},watch:{infoType(t,e){this.$store.state.curformdata.id="main"==t?"main_"+s["a"].getRangeCode():"other_"+s["a"].getRangeCode(),this.$store.dispatch("UpdateDataConfig")}},methods:{},created(){},mounted(){}},n=i,l=(a("89eb"),a("2877")),c=Object(l["a"])(n,o,r,!1,null,"d99239d8",null);e["default"]=c.exports},"3b04":function(t,e,a){var o=a("af5f");"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var r=a("499e").default;r("967de672",o,!0,{sourceMap:!1,shadowMode:!1})},"3e28":function(t,e,a){var o=a("0c2e");"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var r=a("499e").default;r("04e0624e",o,!0,{sourceMap:!1,shadowMode:!1})},4075:function(t,e,a){var o=a("24fb");e=o(!1),e.push([t.i,".ivu-form-item[data-v-b5c3b830]{margin-bottom:15px}",""]),t.exports=e},"42d4":function(module,__webpack_exports__,__webpack_require__){"use strict";__webpack_exports__["a"]={props:{data:Object,formconfig:Object},components:{},data(){return{error:""}},computed:{value:{get(){return this.data.config.value},set(value){this.data.config.required&&(""==value||""!=this.data.config.regular&&!eval(this.data.config.regular).test(value))?this.error=this.data.config.message:(this.error="",document.getElementById(this.data.id).classList.remove("ivu-form-item-error")),this.$emit("backdata",this.data.id,value)}}},watch:{},methods:{getwidth(t){return 0==t||"top"==this.formconfig.position?null:t}},created(){},mounted(){this.data.config.value&&(this.value=this.data.config.value)}}},4548:function(t,e,a){var o={"./Componentlist.vue":"0b27","./FormSet.vue":"f00d","./Header.vue":"0418","./LeftMenu.vue":"110b","./MainConfig.vue":"dd1f","./edit/qdsd-Cascader-edit.vue":"cae5","./edit/qdsd-DatePicker-edit.vue":"faa4","./edit/qdsd-Rate-edit.vue":"701a","./edit/qdsd-TimePicker-edit.vue":"dbec","./edit/qdsd-checkbox-edit.vue":"0d79","./edit/qdsd-input-edit.vue":"19b1","./edit/qdsd-input-number-edit.vue":"7da5","./edit/qdsd-radio-edit.vue":"bbc0","./edit/qdsd-select-edit.vue":"6627","./edit/qdsd-textarea-edit.vue":"7a66","./edit/qdsd-uploadFile-edit.vue":"a9af","./edit/qdsd-uploadImage-edit.vue":"3a03","./form/qdsd-Cascader.vue":"188b","./form/qdsd-DatePicker.vue":"9344","./form/qdsd-Rate.vue":"abb8","./form/qdsd-TimePicker.vue":"495c","./form/qdsd-checkbox.vue":"35f6","./form/qdsd-input-number.vue":"2ff1","./form/qdsd-input.vue":"5638","./form/qdsd-radio.vue":"3567","./form/qdsd-select.vue":"7650","./form/qdsd-textarea.vue":"6f07","./form/qdsd-uploadFile.vue":"4cb8","./form/qdsd-uploadImage.vue":"58a6"};function r(t){var e=s(t);return a(e)}function s(t){if(!a.o(o,t)){var e=new Error("Cannot find module '"+t+"'");throw e.code="MODULE_NOT_FOUND",e}return o[t]}r.keys=function(){return Object.keys(o)},r.resolve=s,t.exports=r,r.id="4548"},"45be":function(t,e,a){"use strict";a("3e28")},"495c":function(t,e,a){"use strict";a.r(e);var o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("FormItem",{directives:[{name:"show",rawName:"v-show",value:!t.data.config.hidden,expression:"!data.config.hidden"}],attrs:{label:t.data.config.label,id:t.data.id,required:t.data.config.required,"label-width":t.getwidth(t.data.config.labelWidth),error:t.error}},[a("TimePicker",{staticStyle:{width:"100%"},attrs:{format:t.data.config.format,type:t.data.config.type,placeholder:t.data.config.placeholder,readonly:t.data.config.readonly,size:t.formconfig.size,confirm:t.data.config.confirm},model:{value:t.value,callback:function(e){t.value=e},expression:"value"}})],1)},r=[],s=a("6271"),i=s["a"],n=a("2877"),l=Object(n["a"])(i,o,r,!1,null,"4b231228",null);e["default"]=l.exports},"4cb8":function(t,e,a){"use strict";a.r(e);var o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("FormItem",{directives:[{name:"show",rawName:"v-show",value:!t.data.config.hidden,expression:"!data.config.hidden"}],attrs:{id:t.data.id,required:t.data.config.required,"label-width":t.getwidth(t.data.config.labelWidth),error:t.error}},[a("template",{slot:"label"},[a("div",{staticClass:"label"},[t._v(t._s(t.data.config.label))])]),a("Upload",{ref:"upload",attrs:{action:t.url,name:"file",multiple:"","max-size":t.data.config.maxSize,"on-exceeded-size":t.exceededSize,"before-upload":t.beforeUpload,"default-file-list":t.defaultList,"on-success":t.success,format:t.data.config.format,"on-format-error":t.accepterror,"on-remove":t.remove,"on-error":t.uploaderror,"show-upload-list":!1}},[a("Button",{attrs:{icon:"ios-cloud-upload-outline",type:"primary"}},[t._v("文件上传")])],1),t._l(t.value,(function(e,o){return a("div",{key:o,staticClass:"item"},[t._v(t._s(o+1)+"、"),a("a",{attrs:{href:e.url,target:"_block"}},[t._v(t._s(e.url))]),a("span",[a("Button",{attrs:{type:"text"},on:{click:function(a){return t.handleRemove(e)}}},[a("Icon",{attrs:{type:"ios-close",size:"20",color:"red"}})],1)],1)])}))],2)},r=[],s=a("70d2"),i=s["a"],n=(a("45be"),a("2877")),l=Object(n["a"])(i,o,r,!1,null,"d76c4b2c",null);e["default"]=l.exports},"4f3a":function(t,e,a){var o=a("24fb");e=o(!1),e.push([t.i,".ivu-form-item[data-v-db3560de]{margin-bottom:15px}",""]),t.exports=e},"503f":function(t,e,a){"use strict";a("a8aa")},5086:function(t,e,a){var o=a("1d0c");"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var r=a("499e").default;r("3d63c7b0",o,!0,{sourceMap:!1,shadowMode:!1})},5434:function(t,e,a){var o=a("24fb");e=o(!1),e.push([t.i,".circle[data-v-7408d775]{position:absolute;top:-30px;left:50px;z-index:1000}.ivu-form-item[data-v-7408d775]{margin-bottom:15px}",""]),t.exports=e},5638:function(t,e,a){"use strict";a.r(e);var o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("FormItem",{directives:[{name:"show",rawName:"v-show",value:!t.data.config.hidden,expression:"!data.config.hidden"}],attrs:{label:t.data.config.label,id:t.data.id,required:t.data.config.required,"label-width":t.getwidth(t.data.config.labelWidth),error:t.error}},[a("Input",{attrs:{type:t.data.config.type,readonly:t.data.config.readonly,placeholder:t.data.config.placeholder,size:t.formconfig.size},model:{value:t.value,callback:function(e){t.value=e},expression:"value"}})],1)},r=[],s=a("b879"),i=s["a"],n=a("2877"),l=Object(n["a"])(i,o,r,!1,null,"807b4a2c",null);e["default"]=l.exports},"56d7":function(t,e,a){"use strict";a.r(e);var o=a("2b0e"),r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{attrs:{id:"app"}},[a("router-view")],1)},s=[],i={data(){return{}},computed:{},watch:{},methods:{},created(){},mounted(){window.basefile||(window.basefile="")}},n=i,l=a("2877"),c=Object(l["a"])(n,r,s,!1,null,null,null),d=c.exports,u=a("8c4f");o["a"].use(u["a"]);const f=[{path:"/",name:"home",component:()=>a.e("chunk-ea4c6e30").then(a.bind(null,"bb51"))}],p=new u["a"]({routes:f});var h=p,m=a("2f62"),g=a("cf45");o["a"].use(m["a"]);var b=new m["a"].Store({state:{id:"",baseData:{},draggableStatus:!1,formconfig:{title:"自定义表单",float:"center",position:"top",size:"large",hidden:!0,style:{background:"#ffffff",color:"#17233d",padding:"15px"},showfield:"",showfieldlist:[]},dataconfig:[],curformdata:{},formdata:{},formstatus:!0,baselist:[{id:"main_"+g["a"].getRangeCode(),name:"文本框",type:1,component:"textarea",value:"",config:{label:"文本框",row:2,placeholder:"请输入",required:!1,is_delete:!0,value:"",infoType:"main",only:!1,listShow:!1,addShow:!1}},{id:"main_"+g["a"].getRangeCode(),name:"数字框",type:1,value:"",component:"input-number",config:{label:"数字框",placeholder:"请输入",required:!1,is_delete:!0,value:"",infoType:"main",only:!1,listShow:!1,addShow:!1}},{id:"main_"+g["a"].getRangeCode(),name:"单选框",type:1,value:"",component:"radio",config:{label:"单选框",content:[{value:""}],placeholder:"请输入",required:!1,is_delete:!0,value:"",infoType:"main",listShow:!1,addShow:!1}},{id:"main_"+g["a"].getRangeCode(),name:"多选框",type:1,value:"",component:"checkbox",config:{label:"多选框",type:null,content:[{value:""}],placeholder:"请输入",required:!1,is_delete:!0,value:[],infoType:"main",listShow:!1,addShow:!1}},{id:"main_"+g["a"].getRangeCode(),name:"评分",type:1,value:0,component:"Rate",config:{label:"评分",placeholder:"占位符",type:"default",is_delete:!0,value:0,infoType:"main",listShow:!1,addShow:!1}},{id:"main_"+g["a"].getRangeCode(),name:"单行输入框",type:1,value:"",component:"input",config:{type:"text",label:"输入框",placeholder:"请输入",required:!1,is_delete:!0,value:"",infoType:"main",only:!1,listShow:!1,addShow:!1,inputType:"text"}},{id:"main_"+g["a"].getRangeCode(),name:"区域选择框",type:1,value:"",component:"Cascader",config:{type:"text",label:"区域选择框",placeholder:"请选择",required:!1,is_delete:!0,value:"",infoType:"main",listShow:!1,addShow:!1}},{id:"main_"+g["a"].getRangeCode(),name:"下拉选择框",type:1,value:"",component:"select",config:{label:"下拉选择框",type:null,content:[],placeholder:"请选择",required:!1,multiple:!1,is_delete:!0,value:[],infoType:"main",listShow:!1,addShow:!1}},{id:"main_"+g["a"].getRangeCode(),name:"时间选择框",type:1,value:"",component:"TimePicker",config:{label:"时间选择框",placeholder:"请输入",type:"time",confirm:!0,required:!1,is_delete:!0,value:"",infoType:"main",only:!1,listShow:!1,addShow:!1}},{id:"main_"+g["a"].getRangeCode(),name:"日期选择框",type:1,value:"",component:"DatePicker",config:{label:"日期选择框",placeholder:"请输入",type:"date",required:!1,is_delete:!0,value:"",infoType:"main",only:!1,listShow:!1,addShow:!1}},{id:"main_"+g["a"].getRangeCode(),name:"图片上传",type:1,component:"uploadImage",config:{label:"图片上传",value:[],required:!1,placeholder:"请上传图片",maxnum:1,is_delete:!0,format:["jpg","jpeg","png"],maxSize:5e3,infoType:"main",listShow:!1,addShow:!1}},{id:"main_"+g["a"].getRangeCode(),name:"文件上传",type:1,component:"uploadFile",config:{label:"文件上传",value:[],required:!1,placeholder:"不能为空",maxSize:5e3,maxnum:1,format:["doc"],is_delete:!0,infoType:"main",addShow:!1}}]},mutations:{},actions:{UpdateDataConfig(t){var e=g["a"].updatedata(t.state.dataconfig,t.state.curformdata);t.state.dataconfig=e}},modules:{}});const v=a("4548");console.log(v,"123456"),v.keys().forEach(t=>{const e=v(t),a=t.split("/").pop().replace(/\.\w+$/,"");o["a"].component(a,e.default||e)});var y=a("19ae"),$=a("d35d"),_=a("e9ce"),w=a("3116"),x=a("ae14"),k=a("dd4b"),S=a("10aa"),I=a("a49b"),C=a("2ca9"),T=a("ea31"),D=a("0347"),F=a("2d66"),q=a("f2d8"),U=a("d842"),R=a("0ce4"),z=a("60de"),O=a("f69c"),j=a("7d1f"),M=a("d3b2"),E=a("6066"),N=a("cf18"),P=a("1d12"),L=a("de10"),B=a("01f8"),J=a("6ead"),W=a("266d"),G=a("093f"),K=a("311a"),A=a("bbbe"),H=a("117e"),V=a("6be2"),Y=a("c57e");a("dcad");let Q=[Y["a"],V["a"],H["a"],A["a"],K["a"],G["a"],W["a"],J["a"],B["a"],L["a"],P["a"],N["a"],E["a"],M["a"],j["a"],O["a"],z["a"],R["a"],U["a"],q["a"],F["a"],D["a"],T["a"],C["a"],I["a"],S["a"],k["a"],x["a"],w["a"],_["a"]];Q.forEach(t=>[o["a"].component(t.name,t)]),o["a"].component("TimePicker",$["a"]),o["a"].component("DatePicker",y["a"]),o["a"].prototype.$Modal=V["a"],o["a"].prototype.$Message=H["a"];var X=a("bc3a"),Z=a.n(X);Z.a.defaults.timeout=5e3,Z.a.defaults.headers.common["Content-Type"]="application/json",Z.a.interceptors.request.use((function(t){const e=sessionStorage.getItem("token");return e&&(t.headers.common["token"]=e),t}),(function(t){return Promise.reject(t.response)})),Z.a.interceptors.response.use((function(t){return t}),(function(t){if(t.response)return Promise.reject(t.response)})),o["a"].prototype.$http=Z.a,o["a"].config.productionTip=!1,o["a"].prototype.checkFormType=g["a"].checkFormType,new o["a"]({router:h,store:b,render:t=>t(d)}).$mount("#app")},"58a6":function(t,e,a){"use strict";a.r(e);var o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("FormItem",{directives:[{name:"show",rawName:"v-show",value:!t.data.config.hidden,expression:"!data.config.hidden"}],attrs:{id:t.data.id,required:t.data.config.required,"label-width":t.getwidth(t.data.config.labelWidth),error:t.error}},[a("template",{slot:"label"},[a("div",{staticClass:"label"},[t._v(t._s(t.data.config.label))])]),t._l(t.value,(function(e,o){return a("div",{key:o,staticClass:"upload-list"},[e.url?[a("img",{attrs:{src:e.url}}),a("div",{staticClass:"upload-list-cover"},[a("Icon",{attrs:{type:"ios-eye-outline"},nativeOn:{click:function(a){return t.handleView(e.url)}}}),a("Icon",{attrs:{type:"ios-trash-outline"},nativeOn:{click:function(e){return t.handleRemove(o)}}})],1)]:t._e()],2)})),a("Upload",{ref:"upload",staticStyle:{display:"inline-block",width:"58px"},attrs:{"show-upload-list":!1,"default-file-list":t.defaultList,"on-success":t.success,format:t.data.config.format,"max-size":t.data.config.maxSize,"on-format-error":t.accepterror,"on-exceeded-size":t.exceededSize,"before-upload":t.beforeUpload,multiple:"",type:"drag",action:t.url,"on-error":t.uploaderror}},[a("div",{staticStyle:{width:"58px",height:"58px","line-height":"58px"}},[a("Icon",{attrs:{type:"ios-camera",size:"20",color:"#17233d"}})],1)]),a("Modal",{attrs:{title:"View Image"},model:{value:t.visible,callback:function(e){t.visible=e},expression:"visible"}},[t.visible?a("img",{staticStyle:{width:"100%"},attrs:{src:t.viewimg}}):t._e()])],2)},r=[],s=a("1d79"),i=s["a"],n=(a("8370"),a("2877")),l=Object(n["a"])(i,o,r,!1,null,"4670d05c",null);e["default"]=l.exports},"5bd7":function(t,e,a){"use strict";a("d1da")},6271:function(module,__webpack_exports__,__webpack_require__){"use strict";__webpack_exports__["a"]={props:{data:Object,formconfig:Object},components:{},data(){return{error:""}},computed:{value:{get(){return this.data.config.value},set(value){this.data.config.required&&(""==value||""!=this.data.config.regular&&!eval(this.data.config.regular).test(value))?this.error=this.data.config.message:(this.error="",document.getElementById(this.data.id).classList.remove("ivu-form-item-error")),this.$emit("backdata",this.data.id,value)}}},watch:{},methods:{getwidth(t){return 0==t||"top"==this.formconfig.position?null:t}},created(){},mounted(){this.data.config.value&&(this.value=this.data.config.value)}}},"642e":function(t,e,a){var o=a("24fb");e=o(!1),e.push([t.i,".upload-list[data-v-4670d05c]{display:inline-block;width:60px;height:60px;text-align:center;line-height:60px;border:1px solid transparent;border-radius:4px;overflow:hidden;background:#fff;position:relative;box-shadow:0 1px 1px rgba(0,0,0,.2);margin-right:4px}.upload-list img[data-v-4670d05c]{width:100%;height:100%}.upload-list-cover[data-v-4670d05c]{display:none;position:absolute;top:0;bottom:0;left:0;right:0;background:rgba(0,0,0,.6)}.upload-list:hover .upload-list-cover[data-v-4670d05c]{display:block}.upload-list-cover i[data-v-4670d05c]{color:#fff;font-size:20px;cursor:pointer;margin:0 2px}.label[data-v-4670d05c]{color:var(--labelColor);display:inline-block}",""]),t.exports=e},6627:function(t,e,a){"use strict";a.r(e);var o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("i-form",{attrs:{"label-position":"top"}},[a("FormItem",{attrs:{label:"标识名"}},[a("Input",{attrs:{type:"text",placeholder:"请输入标识名"},model:{value:t.label,callback:function(e){t.label=e},expression:"label"}})],1),a("FormItem",{attrs:{label:"提示文字"}},[a("Input",{attrs:{type:"text",placeholder:"请输入提示文字"},model:{value:t.placeholder,callback:function(e){t.placeholder=e},expression:"placeholder"}})],1),a("FormItem",{attrs:{label:"选项设置"}},[a("Row",[a("Tree",{staticClass:"demo-tree-render",attrs:{data:t.data5,render:t.renderContent}})],1)],1),a("FormItem",{attrs:{label:"默认值"}},[a("i-select",{attrs:{multiple:t.multiple},model:{value:t.value,callback:function(e){t.value=e},expression:"value"}},t._l(t.content,(function(e,o){return a("i-option",{key:o,attrs:{value:e.value}},[t._v(t._s(e.value))])})),1)],1),t.showInfoType&&t.checkFormType(t.baseData.type)?a("FormItem",{attrs:{label:"信息类型"}},[a("Radio-group",{attrs:{vertical:""},model:{value:t.infoType,callback:function(e){t.infoType=e},expression:"infoType"}},[a("Radio",{attrs:{label:"main"}},[t._v("主要信息 (可用于搜索)")]),a("Radio",{attrs:{label:"additional"}},[t._v("附加信息 (减轻系统搜索压力)")])],1)],1):t._e(),a("FormItem",[a("Checkbox",{model:{value:t.multiple,callback:function(e){t.multiple=e},expression:"multiple"}},[t._v(" 是否多选 ")])],1),a("FormItem",[a("Checkbox",{model:{value:t.addShow,callback:function(e){t.addShow=e},expression:"addShow"}},[t._v(" 隐藏字段 "),a("Tooltip",{attrs:{placement:"top-start"}},[a("Icon",{staticStyle:{background:"#2d8cf0","border-radius":"50%"},attrs:{type:"ios-help",size:"20",color:"#fff"}}),a("div",{staticStyle:{"white-space":"normal"},attrs:{slot:"content"},slot:"content"},[t._v(" 对于系统中已有数据的字段信息，若不想再次使用且保存该字段已有数据时，可选择将该字段从表单中隐藏，设置后， 原有字段信息仍可在已收集数据的表单中显示，而在新表单中隐藏。 ")])],1)],1)],1),a("FormItem",[a("Checkbox",{model:{value:t.required,callback:function(e){t.required=e},expression:"required"}},[t._v(" 是否必填 "),a("Tooltip",{attrs:{placement:"top-start"}},[a("Icon",{staticStyle:{background:"#2d8cf0","border-radius":"50%"},attrs:{type:"ios-help",size:"20",color:"#fff"}}),a("div",{staticStyle:{"white-space":"normal"},attrs:{slot:"content"},slot:"content"},[t._v(" 字段信息设置为必填后，用户在填写表单时若未填写该信息，则无法保存表单 ")])],1)],1)],1),"main"==t.infoType&&t.checkFormType(t.baseData.type)?a("FormItem",[a("Checkbox",{model:{value:t.listShow,callback:function(e){t.listShow=e},expression:"listShow"}},[t._v(" 列表是否显示 "),a("Tooltip",{attrs:{placement:"top-start"}},[a("Icon",{staticStyle:{background:"#2d8cf0","border-radius":"50%"},attrs:{type:"ios-help",size:"20",color:"#fff"}}),a("div",{staticStyle:{"white-space":"normal"},attrs:{slot:"content"},slot:"content"},[t._v(" 后台数据列表是否默认显示 ")])],1)],1)],1):t._e()],1)},r=[],s=a("cf45"),i={components:{},data(){return{content:this.$store.state.curformdata.config.content,showInfoType:this.$store.state.curformdata.showInfoType,data5:[{label:"键值",expand:!0,value:0,render:(t,{root:e,node:a,data:o})=>t("span",{style:{display:"inline-block",width:"100%"}},[t("span",[t("Icon",{props:{type:"ios-folder-outline"},style:{marginRight:"8px"}}),t("span",{style:{marginRight:"10px"}}),t(""+(o.isEdit?"":"Button"),{props:Object.assign({},this.buttonProps,{icon:"ios-add"}),style:{marginRight:"8px"},on:{click:()=>{console.log(o,"数据-------"),this.append(o)}}})])]),children:this.$store.state.curformdata.config.content}],inputContent:"",beforeContent:"",oldName:"",buttonProps:{size:"small"}}},computed:{baseData(){return this.$store.state.baseData},label:{get(){return this.$store.state.curformdata.config.label},set(t){this.$store.state.curformdata.config.label=t,this.$store.dispatch("UpdateDataConfig")}},name:{get(){return this.$store.state.curformdata.id},set(t){this.$store.state.curformdata.id=t,this.$store.dispatch("UpdateDataConfig")}},type:{get(){return null==this.$store.state.curformdata.config.type?"null":"button"},set(t){"null"==t&&(t=null),this.$store.state.curformdata.config.type=t,this.$store.dispatch("UpdateDataConfig")}},required:{get(){return this.$store.state.curformdata.config.required},set(t){this.$store.state.curformdata.config.required=t,this.$store.dispatch("UpdateDataConfig")}},multiple:{get(){return this.$store.state.curformdata.config.multiple},set(t){this.$store.state.curformdata.config.multiple=t,this.$store.dispatch("UpdateDataConfig")}},value:{get(){return this.$store.state.curformdata.config.value},set(t){if(this.multiple){this.$store.state.curformdata.config.value=t;let e=t.join(",");this.$store.state.curformdata.value=e}else this.$store.state.curformdata.value=t,this.$store.state.curformdata.config.value=t;this.$store.dispatch("UpdateDataConfig")}},infoType:{get(){return this.$store.state.curformdata.config.infoType},set(t){this.$store.state.curformdata.config.infoType=t,this.$store.dispatch("UpdateDataConfig")}},listShow:{get(){return this.$store.state.curformdata.config.listShow},set(t){this.$store.state.curformdata.config.listShow=t,this.$store.dispatch("UpdateDataConfig")}},addShow:{get(){return this.$store.state.curformdata.config.addShow},set(t){this.$store.state.curformdata.config.addShow=t,this.$store.dispatch("UpdateDataConfig")}},placeholder:{get(){return this.$store.state.curformdata.config.placeholder},set(t){console.log(t),this.$store.state.curformdata.config.placeholder=t,this.$store.dispatch("UpdateDataConfig")}}},watch:{infoType(t,e){this.$store.state.curformdata.id="main"==t?"main_"+s["a"].getRangeCode():"other_"+s["a"].getRangeCode(),this.$store.dispatch("UpdateDataConfig")}},methods:{renderContent(t,{root:e,node:a,data:o}){return t("span",{class:"hhhaha",style:{display:"inline-block",lineHeight:"1.6rem",width:"100%",cursor:"pointer"}},[t("span",[t("Icon",{props:{type:"ios-paper-outline"},style:{marginRight:"8px"}}),t(""+(o.isEdit?"":"span"),{style:{marginRight:"10px"},on:{click:t=>{t.stopPropagation(),this.oldName=o.title,this.$set(o,"isEdit",!0)}}},o.label),t(""+(o.isEdit?"input":""),{attrs:{value:""+(o.isEdit?o.label:""),autofocus:"autofocus"},style:{width:"12rem",cursor:"auto"},on:{focus:t=>{this.beforeContent=o.__label,this.inputContent=o.__label},change:t=>{this.inputContent=t.target.value,this.confirmTheChange(o)},blur:t=>{}}}),t(""+(o.isEdit?"":"Button"),{props:{type:"primary",size:"small"},style:{marginRight:"8px"},on:{click:t=>{t.stopPropagation(),this.oldName=o.label,this.$set(o,"isEdit",!0)}}},"编辑"),t(""+(o.isEdit?"":"Button"),{props:Object.assign({},this.buttonProps,{icon:"ios-remove"}),style:{marginRight:"8px"},on:{click:()=>{this.remove(e,a,o)}}}),t(""+(o.isEdit?"span":""),{style:{marginLeft:".5rem"}},[t("Button",{props:Object.assign({},this.buttonProps,{icon:"md-checkmark"}),style:{border:0,background:"rgba(0,0,0,0)",fontSize:"1.3rem",outline:"none",lineHeight:1},on:{click:t=>{this.inputContent=o.__label,this.confirmTheChange(o)}}})])])])},confirmTheChange(t){this.inputContent?(t.label=this.inputContent,t.value=this.inputContent):this.inputContent=t.label,this.$set(t,"isEdit",!1),this.input()},CancelChange(t){this.beforeContent&&(t.label=this.beforeContent),this.$set(t,"isEdit",!1),this.input()},append(t){const e=t.children||[];e.push({label:"键值",expand:!0,value:"键值",isEdit:!1}),this.$set(t,"children",e),this.input()},remove(t,e,a){const o=t.find(t=>t===e).parent,r=t.find(t=>t.nodeKey===o).node,s=r.children.indexOf(a);r.children.splice(s,1),this.input()},toUp(t,e,a){const o=t.find(t=>t===e).parent,r=t.find(t=>t.nodeKey===o).node,s=r.children.indexOf(a),i=r.children;0!==s&&i.splice(s-1,1,...i.splice(s,1,i[s-1]))},toDown(t,e,a){const o=t.find(t=>t===e).parent,r=t.find(t=>t.nodeKey===o).node,s=r.children.indexOf(a),i=r.children;s+1!==i.length&&i.splice(s+1,1,...i.splice(s,1,i[s+1]))},add(t){this.content.push({key:this.content[t].key,value:this.content[t].value})},close(t){if(1==this.content.length)return"";this.content.splice(t,1)},input(){this.$store.state.curformdata.config.content=this.data5[0].children,this.$store.dispatch("UpdateDataConfig")}},created(){},mounted(){}},n=i,l=(a("37d9"),a("2877")),c=Object(l["a"])(n,o,r,!1,null,"f3345a24",null);e["default"]=c.exports},"683d":function(t,e,a){"use strict";a("9ad1")},"68b3":function(t,e,a){"use strict";a("e9c9")},"6d4e":function(t,e,a){var o=a("b816");"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var r=a("499e").default;r("0d7cedeb",o,!0,{sourceMap:!1,shadowMode:!1})},"6f07":function(t,e,a){"use strict";a.r(e);var o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("FormItem",{directives:[{name:"show",rawName:"v-show",value:!t.data.config.hidden,expression:"!data.config.hidden"}],attrs:{label:t.data.config.label,id:t.data.id,required:t.data.config.required,"label-width":t.getwidth(t.data.config.labelWidth),error:t.error}},[a("Input",{attrs:{type:"textarea",readonly:t.data.config.readonly,autosize:{minRows:t.data.config.row},placeholder:t.data.config.placeholder,size:t.formconfig.size},model:{value:t.value,callback:function(e){t.value=e},expression:"value"}})],1)},r=[],s=a("d46d"),i=s["a"],n=a("2877"),l=Object(n["a"])(i,o,r,!1,null,"3e38801e",null);e["default"]=l.exports},"701a":function(t,e,a){"use strict";a.r(e);var o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("i-form",{attrs:{"label-position":"top"}},[a("FormItem",{attrs:{label:"标识名"}},[a("Input",{attrs:{type:"text",placeholder:"请输入标识名"},model:{value:t.label,callback:function(e){t.label=e},expression:"label"}})],1),a("FormItem",{attrs:{label:"提示文字"}},[a("Input",{attrs:{type:"text",placeholder:"请输入提示文字"},model:{value:t.placeholder,callback:function(e){t.placeholder=e},expression:"placeholder"}})],1),a("FormItem",{attrs:{label:"默认值"}},[a("Rate",{attrs:{"show-text":""},model:{value:t.value,callback:function(e){t.value=e},expression:"value"}},[a("span",{staticStyle:{color:"#f5a623"}},[t._v(t._s(t.value)+"分")])])],1),t.showInfoType&&t.checkFormType(t.baseData.type)?a("FormItem",{attrs:{label:"信息类型"}},[a("Radio-group",{attrs:{vertical:""},model:{value:t.infoType,callback:function(e){t.infoType=e},expression:"infoType"}},[a("Radio",{attrs:{label:"main"}},[t._v("主要信息 (可用于搜索)")]),a("Radio",{attrs:{label:"additional"}},[t._v("附加信息 (减轻系统搜索压力)")])],1)],1):t._e(),a("FormItem",[a("Checkbox",{model:{value:t.addShow,callback:function(e){t.addShow=e},expression:"addShow"}},[t._v(" 隐藏字段 "),a("Tooltip",{attrs:{placement:"top-start"}},[a("Icon",{staticStyle:{background:"#2d8cf0","border-radius":"50%"},attrs:{type:"ios-help",size:"20",color:"#fff"}}),a("div",{staticStyle:{"white-space":"normal"},attrs:{slot:"content"},slot:"content"},[t._v(" 对于系统中已有数据的字段信息，若不想再次使用且保存该字段已有数据时，可选择将该字段从表单中隐藏，设置后， 原有字段信息仍可在已收集数据的表单中显示，而在新表单中隐藏。 ")])],1)],1)],1),"main"==t.infoType&&t.checkFormType(t.baseData.type)?a("FormItem",[a("Checkbox",{model:{value:t.listShow,callback:function(e){t.listShow=e},expression:"listShow"}},[t._v(" 列表是否显示 "),a("Tooltip",{attrs:{placement:"top-start"}},[a("Icon",{staticStyle:{background:"#2d8cf0","border-radius":"50%"},attrs:{type:"ios-help",size:"20",color:"#fff"}}),a("div",{staticStyle:{"white-space":"normal"},attrs:{slot:"content"},slot:"content"},[t._v(" 后台数据列表是否默认显示 ")])],1)],1)],1):t._e()],1)},r=[],s=a("cf45"),i={components:{},data(){return{showInfoType:this.$store.state.curformdata.showInfoType}},computed:{baseData(){return this.$store.state.baseData},type:{get(){return this.$store.state.curformdata.config.type},set(t){"default"==t&&(this.$store.state.curformdata.config.icon="",this.$store.state.curformdata.config.str=""),this.$store.state.curformdata.config.type=t,this.$store.dispatch("UpdateDataConfig")}},label:{get(){return this.$store.state.curformdata.config.label},set(t){this.$store.state.curformdata.config.label=t,this.$store.dispatch("UpdateDataConfig")}},name:{get(){return this.$store.state.curformdata.id},set(t){this.$store.state.curformdata.id=t,this.$store.dispatch("UpdateDataConfig")}},value:{get(){return this.$store.state.curformdata.value},set(t){this.$store.state.curformdata.value=t,this.$store.state.curformdata.config.value=t,this.$store.dispatch("UpdateDataConfig")}},infoType:{get(){return this.$store.state.curformdata.config.infoType},set(t){this.$store.state.curformdata.config.infoType=t,this.$store.dispatch("UpdateDataConfig")}},listShow:{get(){return this.$store.state.curformdata.config.listShow},set(t){this.$store.state.curformdata.config.listShow=t,this.$store.dispatch("UpdateDataConfig")}},addShow:{get(){return this.$store.state.curformdata.config.addShow},set(t){this.$store.state.curformdata.config.addShow=t,this.$store.dispatch("UpdateDataConfig")}},placeholder:{get(){return this.$store.state.curformdata.config.placeholder},set(t){this.$store.state.curformdata.config.placeholder=t,this.$store.dispatch("UpdateDataConfig")}}},watch:{infoType(t,e){this.$store.state.curformdata.id="main"==t?"main_"+s["a"].getRangeCode():"other_"+s["a"].getRangeCode(),this.$store.dispatch("UpdateDataConfig")}},methods:{},created(){},mounted(){}},n=i,l=(a("ca0d"),a("2877")),c=Object(l["a"])(n,o,r,!1,null,"39b57a72",null);e["default"]=c.exports},"70d2":function(module,__webpack_exports__,__webpack_require__){"use strict";__webpack_exports__["a"]={props:{data:Object,formconfig:Object},components:{},data(){return{error:"",defaultList:JSON.parse(JSON.stringify(this.data.config.value)),value:[],url:window.basefile+"/ajax/upload"}},computed:{},watch:{value(value){this.data.config.required&&(""===value||""!=this.data.config.regular&&!eval(this.data.config.regular).test(value))?this.error=this.data.config.message:(this.error="",document.getElementById(this.data.id).classList.remove("ivu-form-item-error")),this.$emit("backdata",this.data.id,value)}},methods:{getwidth(t){return 0==t||"top"==this.formconfig.position?null:t},exceededSize(){this.$Message.error("文件超出指定大小限制时的钩子")},beforeUpload(){return this.data.config.maxnum<this.value.length+this.defaultList.length||0==this.data.config.maxnum?(this.$Message.error("文件数量已超出最大数"),!1):void 0},success(t){if(!t.code)return this.$Message.error(t.msg),!1;this.value.push({url:t.data.url,name:"文件"+Math.random()})},accepterror(){this.$Message.error("文件格式不正确")},remove(t,e){for(var a=JSON.parse(JSON.stringify([])),o=0;o<e.length;o++)a.push({url:e[o]["url"],name:e[o]["name"]});this.value=a,this.defaultList=a},uploaderror(t){var e=t.toString();e.search(/401/i)?this.$Message.error("请登陆后操作"):this.$Message.error("网络错误")},handleRemove(t){this.$refs.upload.fileList.splice(this.$refs.upload.fileList.indexOf(t),1);const e=this.$refs.upload.fileList;for(var a=JSON.parse(JSON.stringify([])),o=0;o<e.length;o++)a.push({url:e[o]["url"],name:e[o]["name"]});this.value=a}},created(){},mounted(){this.data.config.value.length>0&&(this.value=this.$refs.upload.fileList)}}},"74d3":function(module,__webpack_exports__,__webpack_require__){"use strict";__webpack_exports__["a"]={props:{data:Object,formconfig:Object},components:{},data(){return{error:""}},computed:{value:{get(){return this.data.config.value},set(value){this.data.config.required&&(""==value||""!=this.data.config.regular&&!eval(this.data.config.regular).test(value))?this.error=this.data.config.message:(this.error="",document.getElementById(this.data.id).classList.remove("ivu-form-item-error")),this.$emit("backdata",this.data.id,value)}}},watch:{},methods:{getwidth(t){return 0==t||"top"==this.formconfig.position?null:t}},created(){},mounted(){this.data.config.value&&(this.value=parseInt(this.data.config.value))}}},7650:function(t,e,a){"use strict";a.r(e);var o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("FormItem",{directives:[{name:"show",rawName:"v-show",value:!t.data.config.hidden,expression:"!data.config.hidden"}],attrs:{label:t.data.config.label,id:t.data.id,required:t.data.config.required,"label-width":t.getwidth(t.data.config.labelWidth),error:t.error}},[a("i-select",{staticStyle:{width:"100%"},attrs:{size:t.formconfig.size,multiple:t.data.config.multiple},model:{value:t.value,callback:function(e){t.value=e},expression:"value"}},t._l(t.data.config.content,(function(e,o){return a("i-option",{key:o,attrs:{value:e.value}},[t._v(t._s(e.value))])})),1)],1)},r=[],s=a("d227"),i=s["a"],n=a("2877"),l=Object(n["a"])(i,o,r,!1,null,"484d027a",null);e["default"]=l.exports},"7a66":function(t,e,a){"use strict";a.r(e);var o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("i-form",{attrs:{"label-position":"top"}},[a("FormItem",{attrs:{label:"标识名"}},[a("Input",{attrs:{type:"text",placeholder:"请输入标识名"},model:{value:t.label,callback:function(e){t.label=e},expression:"label"}})],1),a("FormItem",{attrs:{label:"提示文字"}},[a("Input",{attrs:{type:"text",placeholder:"请输入提示文字"},model:{value:t.placeholder,callback:function(e){t.placeholder=e},expression:"placeholder"}})],1),a("FormItem",{attrs:{label:"默认值"}},[a("Input",{attrs:{type:"text",placeholder:"请输入默认值"},model:{value:t.value,callback:function(e){t.value=e},expression:"value"}})],1),a("FormItem",{attrs:{label:"行数"}},[a("Input",{attrs:{type:"number"},model:{value:t.row,callback:function(e){t.row=e},expression:"row"}})],1),t.showInfoType&&t.checkFormType(t.baseData.type)?a("FormItem",{attrs:{label:"信息类型"}},[a("Radio-group",{attrs:{vertical:""},model:{value:t.infoType,callback:function(e){t.infoType=e},expression:"infoType"}},[a("Radio",{attrs:{label:"main"}},[t._v("主要信息 (可用于搜索)")]),a("Radio",{attrs:{label:"additional"}},[t._v("附加信息 (减轻系统搜索压力)")])],1)],1):t._e(),"main"==t.infoType&&t.checkFormType(t.baseData.type)?a("FormItem",[a("Checkbox",{model:{value:t.only,callback:function(e){t.only=e},expression:"only"}},[t._v(" 是否唯一 "),a("Tooltip",{attrs:{placement:"top-start"}},[a("Icon",{staticStyle:{background:"#2d8cf0","border-radius":"50%"},attrs:{type:"ios-help",size:"20",color:"#fff"}}),a("div",{staticStyle:{"white-space":"normal"},attrs:{slot:"content"},slot:"content"},[t._v(" 字段设为唯一后在新建信息时则无法填写与系统内部已有信息重复的信息，否则无法保存。如将【新建客户】时“手机号”设为唯一， 则再次新建客户时，重复的手机号输入后，用户将无法保存【新建客户】表单。 ")])],1)],1)],1):t._e(),a("FormItem",[a("Checkbox",{model:{value:t.addShow,callback:function(e){t.addShow=e},expression:"addShow"}},[t._v(" 隐藏字段 "),a("Tooltip",{attrs:{placement:"top-start"}},[a("Icon",{staticStyle:{background:"#2d8cf0","border-radius":"50%"},attrs:{type:"ios-help",size:"20",color:"#fff"}}),a("div",{staticStyle:{"white-space":"normal"},attrs:{slot:"content"},slot:"content"},[t._v(" 对于系统中已有数据的字段信息，若不想再次使用且保存该字段已有数据时，可选择将该字段从表单中隐藏，设置后， 原有字段信息仍可在已收集数据的表单中显示，而在新表单中隐藏。 ")])],1)],1)],1),a("FormItem",[a("Checkbox",{model:{value:t.required,callback:function(e){t.required=e},expression:"required"}},[t._v(" 是否必填 "),a("Tooltip",{attrs:{placement:"top-start"}},[a("Icon",{staticStyle:{background:"#2d8cf0","border-radius":"50%"},attrs:{type:"ios-help",size:"20",color:"#fff"}}),a("div",{staticStyle:{"white-space":"normal"},attrs:{slot:"content"},slot:"content"},[t._v(" 字段信息设置为必填后，用户在填写表单时若未填写该信息，则无法保存表单 ")])],1)],1)],1),"main"==t.infoType&&t.checkFormType(t.baseData.type)?a("FormItem",[a("Checkbox",{model:{value:t.listShow,callback:function(e){t.listShow=e},expression:"listShow"}},[t._v(" 列表是否显示 "),a("Tooltip",{attrs:{placement:"top-start"}},[a("Icon",{staticStyle:{background:"#2d8cf0","border-radius":"50%"},attrs:{type:"ios-help",size:"20",color:"#fff"}}),a("div",{staticStyle:{"white-space":"normal"},attrs:{slot:"content"},slot:"content"},[t._v(" 后台数据列表是否默认显示 ")])],1)],1)],1):t._e()],1)},r=[],s=a("cf45"),i={components:{},data(){return{showInfoType:this.$store.state.curformdata.showInfoType}},computed:{baseData(){return this.$store.state.baseData},label:{get(){return this.$store.state.curformdata.config.label},set(t){this.$store.state.curformdata.config.label=t,this.$store.dispatch("UpdateDataConfig")}},name:{get(){return this.$store.state.curformdata.id},set(t){this.$store.state.curformdata.id=t,this.$store.dispatch("UpdateDataConfig")}},placeholder:{get(){return this.$store.state.curformdata.config.placeholder},set(t){this.$store.state.curformdata.config.placeholder=t,this.$store.dispatch("UpdateDataConfig")}},value:{get(){return this.$store.state.curformdata.value},set(t){this.$store.state.curformdata.value=t,this.$store.state.curformdata.config.value=t,this.$store.dispatch("UpdateDataConfig")}},infoType:{get(){return this.$store.state.curformdata.config.infoType},set(t){this.$store.state.curformdata.config.infoType=t,this.$store.dispatch("UpdateDataConfig")}},only:{get(){return this.$store.state.curformdata.config.only},set(t){this.$store.state.curformdata.config.only=t,this.$store.dispatch("UpdateDataConfig")}},listShow:{get(){return this.$store.state.curformdata.config.listShow},set(t){this.$store.state.curformdata.config.listShow=t,this.$store.dispatch("UpdateDataConfig")}},addShow:{get(){return this.$store.state.curformdata.config.addShow},set(t){this.$store.state.curformdata.config.addShow=t,this.$store.dispatch("UpdateDataConfig")}},row:{get(){return this.$store.state.curformdata.config.row},set(t){this.$store.state.curformdata.config.row=t,this.$store.dispatch("UpdateDataConfig")}},required:{get(){return this.$store.state.curformdata.config.required},set(t){this.$store.state.curformdata.config.required=t,this.$store.dispatch("UpdateDataConfig")}}},watch:{infoType(t,e){this.$store.state.curformdata.id="main"==t?"main_"+s["a"].getRangeCode():"other_"+s["a"].getRangeCode(),this.$store.dispatch("UpdateDataConfig")}},methods:{},created(){},mounted(){}},n=i,l=(a("68b3"),a("2877")),c=Object(l["a"])(n,o,r,!1,null,"20cd3a97",null);e["default"]=c.exports},"7da5":function(t,e,a){"use strict";a.r(e);var o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("i-form",{attrs:{"label-position":"top"}},[a("FormItem",{attrs:{label:"标识名"}},[a("Input",{attrs:{type:"text",placeholder:"请输入标识名"},model:{value:t.label,callback:function(e){t.label=e},expression:"label"}})],1),a("FormItem",{attrs:{label:"提示文字"}},[a("Input",{attrs:{type:"text",placeholder:"请输入提示文字"},model:{value:t.placeholder,callback:function(e){t.placeholder=e},expression:"placeholder"}})],1),a("FormItem",{attrs:{label:"默认值"}},[a("Input",{attrs:{type:"text",placeholder:"请输入默认值"},model:{value:t.value,callback:function(e){t.value=e},expression:"value"}})],1),t.showInfoType&&t.checkFormType(t.baseData.type)?a("FormItem",{attrs:{label:"信息类型"}},[a("Radio-group",{attrs:{vertical:""},model:{value:t.infoType,callback:function(e){t.infoType=e},expression:"infoType"}},[a("Radio",{attrs:{label:"main"}},[t._v("主要信息 (可用于搜索)")]),a("Radio",{attrs:{label:"additional"}},[t._v("附加信息 (减轻系统搜索压力)")])],1)],1):t._e(),"main"==t.infoType&&t.checkFormType(t.baseData.type)?a("FormItem",[a("Checkbox",{model:{value:t.only,callback:function(e){t.only=e},expression:"only"}},[t._v(" 是否唯一 "),a("Tooltip",{attrs:{placement:"top-start"}},[a("Icon",{staticStyle:{background:"#2d8cf0","border-radius":"50%"},attrs:{type:"ios-help",size:"20",color:"#fff"}}),a("div",{staticStyle:{"white-space":"normal"},attrs:{slot:"content"},slot:"content"},[t._v(" 字段设为唯一后在新建信息时则无法填写与系统内部已有信息重复的信息，否则无法保存。如将【新建客户】时“手机号”设为唯一， 则再次新建客户时，重复的手机号输入后，用户将无法保存【新建客户】表单。 ")])],1)],1)],1):t._e(),a("FormItem",[a("Checkbox",{model:{value:t.addShow,callback:function(e){t.addShow=e},expression:"addShow"}},[t._v(" 隐藏字段 "),a("Tooltip",{attrs:{placement:"top-start"}},[a("Icon",{staticStyle:{background:"#2d8cf0","border-radius":"50%"},attrs:{type:"ios-help",size:"20",color:"#fff"}}),a("div",{staticStyle:{"white-space":"normal"},attrs:{slot:"content"},slot:"content"},[t._v(" 对于系统中已有数据的字段信息，若不想再次使用且保存该字段已有数据时，可选择将该字段从表单中隐藏，设置后， 原有字段信息仍可在已收集数据的表单中显示，而在新表单中隐藏。 ")])],1)],1)],1),a("FormItem",[a("Checkbox",{model:{value:t.required,callback:function(e){t.required=e},expression:"required"}},[t._v(" 是否必填 "),a("Tooltip",{attrs:{placement:"top-start"}},[a("Icon",{staticStyle:{background:"#2d8cf0","border-radius":"50%"},attrs:{type:"ios-help",size:"20",color:"#fff"}}),a("div",{staticStyle:{"white-space":"normal"},attrs:{slot:"content"},slot:"content"},[t._v(" 字段信息设置为必填后，用户在填写表单时若未填写该信息，则无法保存表单 ")])],1)],1)],1),"main"==t.infoType&&t.checkFormType(t.baseData.type)?a("FormItem",[a("Checkbox",{model:{value:t.listShow,callback:function(e){t.listShow=e},expression:"listShow"}},[t._v(" 列表是否显示 "),a("Tooltip",{attrs:{placement:"top-start"}},[a("Icon",{staticStyle:{background:"#2d8cf0","border-radius":"50%"},attrs:{type:"ios-help",size:"20",color:"#fff"}}),a("div",{staticStyle:{"white-space":"normal"},attrs:{slot:"content"},slot:"content"},[t._v(" 后台数据列表是否默认显示 ")])],1)],1)],1):t._e()],1)},r=[],s=a("cf45"),i={components:{},data(){return{showInfoType:this.$store.state.curformdata.showInfoType}},computed:{baseData(){return this.$store.state.baseData},label:{get(){return this.$store.state.curformdata.config.label},set(t){this.$store.state.curformdata.config.label=t,this.$store.dispatch("UpdateDataConfig")}},name:{get(){return this.$store.state.curformdata.id},set(t){this.$store.state.curformdata.id=t,this.$store.dispatch("UpdateDataConfig")}},type:{get(){return this.$store.state.curformdata.config.type},set(t){this.$store.state.curformdata.config.type=t,this.$store.dispatch("UpdateDataConfig")}},placeholder:{get(){return this.$store.state.curformdata.config.placeholder},set(t){this.$store.state.curformdata.config.placeholder=t,this.$store.dispatch("UpdateDataConfig")}},value:{get(){return this.$store.state.curformdata.value},set(t){this.$store.state.curformdata.value=t,this.$store.state.curformdata.config.value=t,this.$store.dispatch("UpdateDataConfig")}},infoType:{get(){return this.$store.state.curformdata.config.infoType},set(t){this.$store.state.curformdata.config.infoType=t,this.$store.dispatch("UpdateDataConfig")}},only:{get(){return this.$store.state.curformdata.config.only},set(t){this.$store.state.curformdata.config.only=t,this.$store.dispatch("UpdateDataConfig")}},listShow:{get(){return this.$store.state.curformdata.config.listShow},set(t){this.$store.state.curformdata.config.listShow=t,this.$store.dispatch("UpdateDataConfig")}},addShow:{get(){return this.$store.state.curformdata.config.addShow},set(t){this.$store.state.curformdata.config.addShow=t,this.$store.dispatch("UpdateDataConfig")}},required:{get(){return this.$store.state.curformdata.config.required},set(t){this.$store.state.curformdata.config.required=t,this.$store.dispatch("UpdateDataConfig")}}},watch:{infoType(t,e){this.$store.state.curformdata.id="main"==t?"main_"+s["a"].getRangeCode():"other_"+s["a"].getRangeCode(),this.$store.dispatch("UpdateDataConfig")}},methods:{},created(){},mounted(){}},n=i,l=(a("00ba"),a("2877")),c=Object(l["a"])(n,o,r,!1,null,"b5c3b830",null);e["default"]=c.exports},"7fda":function(t,e,a){var o=a("24fb");e=o(!1),e.push([t.i,".circle[data-v-20cd3a97]{position:absolute;top:-30px;left:50px;z-index:1000}.ivu-form-item[data-v-20cd3a97]{margin-bottom:15px}",""]),t.exports=e},8370:function(t,e,a){"use strict";a("fc4d")},"846a":function(t,e,a){"use strict";a("f64c")},8493:function(t,e,a){var o=a("24fb");e=o(!1),e.push([t.i,".ivu-form-item[data-v-7f30cba8]{margin-bottom:15px}",""]),t.exports=e},"89eb":function(t,e,a){"use strict";a("6d4e")},"8ef8":function(t,e,a){"use strict";a("5086")},9344:function(t,e,a){"use strict";a.r(e);var o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("FormItem",{directives:[{name:"show",rawName:"v-show",value:!t.data.config.hidden,expression:"!data.config.hidden"}],attrs:{label:t.data.config.label,id:t.data.id,required:t.data.config.required,"label-width":t.getwidth(t.data.config.labelWidth),error:t.error}},[a("DatePicker",{staticStyle:{width:"100%"},attrs:{format:t.data.config.format,size:t.formconfig.size,type:t.data.config.type,placeholder:t.data.config.placeholder,readonly:t.data.config.readonly,confirm:t.data.config.confirm},model:{value:t.value,callback:function(e){t.value=e},expression:"value"}})],1)},r=[],s={props:{data:Object,formconfig:Object},components:{},data(){return{error:""}},computed:{value:{get(){return this.data.config.value},set(t){this.data.config.required?this.error=this.data.config.message:(this.error="",document.getElementById(this.data.id).classList.remove("ivu-form-item-error")),t&&this.$emit("backdata",this.data.id,this.formatDate(t))}}},watch:{},methods:{getwidth(t){return 0==t||"top"==this.formconfig.position?null:t},formatDate(t){var e=t.getMonth()+1,a=t.getFullYear(),o=t.getDate();return a+"-"+e+"-"+o}},created(){},mounted(){this.data.config.value&&(this.value=this.data.config.value)}},i=s,n=a("2877"),l=Object(n["a"])(i,o,r,!1,null,"39d50970",null);e["default"]=l.exports},9573:function(t,e,a){var o=a("24fb");e=o(!1),e.push([t.i,".box-left-menus[data-v-272345bd]{background:#2c3b41;width:50px;display:block;float:left}.box-left-menus ul[data-v-272345bd]{margin:0;padding:0}.box-left-menus ul li[data-v-272345bd]{width:50px;padding:15px 0;display:inline-block;text-align:center;color:#8f8f8f}.box-left-menus ul .act[data-v-272345bd],.box-left-menus ul li[data-v-272345bd]:hover{color:#fff}.box-left-content[data-v-272345bd]{width:calc(100% - 30px);display:block;overflow:hidden}",""]),t.exports=e},"97b3":function(t,e,a){var o=a("24fb");e=o(!1),e.push([t.i,".title[data-v-559b1831]{font-size:14px!important}.components-box[data-v-559b1831]{overflow-y:scroll}.components-box[data-v-559b1831]::-webkit-scrollbar{display:none}.box[data-v-559b1831]{padding:10px}.box ul li[data-v-559b1831]{margin-right:10px;margin-bottom:10px;display:inline-block}",""]),t.exports=e},"9ad1":function(t,e,a){var o=a("af93");"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var r=a("499e").default;r("50216806",o,!0,{sourceMap:!1,shadowMode:!1})},"9af0":function(t,e,a){var o=a("4075");"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var r=a("499e").default;r("39c72971",o,!0,{sourceMap:!1,shadowMode:!1})},"9ea9":function(t,e,a){var o=a("24fb");e=o(!1),e.push([t.i,".circle[data-v-68c96d1e]{position:absolute;top:-30px;left:50px;z-index:1000}.ivu-form-item[data-v-68c96d1e]{margin-bottom:15px}",""]),t.exports=e},"9efd":function(t,e,a){"use strict";a.d(e,"a",(function(){return o}));var o={design_data:window.basefile+"/qingdong/general/form/getinfo",design_save:window.basefile+"/qingdong/general/form/edit",edit_data:window.basefile+"/qingdong/general/form/edit_data",save_data:window.basefile+"/qingdong/general/form/save_data",region:"/qingdong/general/form/region.json"}},"9f8b":function(t,e,a){var o=a("9ea9");"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var r=a("499e").default;r("0b14cf79",o,!0,{sourceMap:!1,shadowMode:!1})},a265:function(t,e,a){"use strict";a("3b04")},a41f:function(t,e,a){var o=a("20da");"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var r=a("499e").default;r("2ce84305",o,!0,{sourceMap:!1,shadowMode:!1})},a549:function(t,e,a){var o=a("33af");"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var r=a("499e").default;r("16dd6efa",o,!0,{sourceMap:!1,shadowMode:!1})},a8aa:function(t,e,a){var o=a("d992");"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var r=a("499e").default;r("2869f23a",o,!0,{sourceMap:!1,shadowMode:!1})},a901:function(t,e,a){"use strict";a("a549")},a9af:function(t,e,a){"use strict";a.r(e);var o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("i-form",{attrs:{"label-position":"top"}},[a("FormItem",{attrs:{label:"标识名"}},[a("Input",{attrs:{type:"text",placeholder:"请输入标识名"},model:{value:t.label,callback:function(e){t.label=e},expression:"label"}})],1),a("FormItem",{attrs:{label:"文件大小限制(kb)"}},[a("InputNumber",{staticStyle:{width:"100%"},model:{value:t.maxSize,callback:function(e){t.maxSize=e},expression:"maxSize"}})],1),a("FormItem",{attrs:{label:"文件数量"}},[a("InputNumber",{staticStyle:{width:"100%"},model:{value:t.maxnum,callback:function(e){t.maxnum=e},expression:"maxnum"}})],1),a("FormItem",{attrs:{label:"文件格式"}},[a("Input",{attrs:{type:"text",placeholder:"请输入文件格式"},model:{value:t.format,callback:function(e){t.format=e},expression:"format"}})],1),t.required?a("FormItem",{attrs:{label:"提示文字"}},[a("Input",{attrs:{type:"text",placeholder:"请输入提示文字"},model:{value:t.placeholder,callback:function(e){t.placeholder=e},expression:"placeholder"}})],1):t._e(),t.showInfoType&&t.checkFormType(t.baseData.type)?a("FormItem",{attrs:{label:"信息类型"}},[a("Radio-group",{attrs:{vertical:""},model:{value:t.infoType,callback:function(e){t.infoType=e},expression:"infoType"}},[a("Radio",{attrs:{label:"main"}},[t._v("主要信息 (可用于搜索)")]),a("Radio",{attrs:{label:"additional"}},[t._v("附加信息 (减轻系统搜索压力)")])],1)],1):t._e(),a("FormItem",[a("Checkbox",{model:{value:t.addShow,callback:function(e){t.addShow=e},expression:"addShow"}},[t._v(" 隐藏字段 "),a("Tooltip",{attrs:{placement:"top-start"}},[a("Icon",{staticStyle:{background:"#2d8cf0","border-radius":"50%"},attrs:{type:"ios-help",size:"20",color:"#fff"}}),a("div",{staticStyle:{"white-space":"normal"},attrs:{slot:"content"},slot:"content"},[t._v(" 对于系统中已有数据的字段信息，若不想再次使用且保存该字段已有数据时，可选择将该字段从表单中隐藏，设置后， 原有字段信息仍可在已收集数据的表单中显示，而在新表单中隐藏。 ")])],1)],1)],1),a("FormItem",[a("Checkbox",{model:{value:t.required,callback:function(e){t.required=e},expression:"required"}},[t._v(" 是否必填 "),a("Tooltip",{attrs:{placement:"top-start"}},[a("Icon",{staticStyle:{background:"#2d8cf0","border-radius":"50%"},attrs:{type:"ios-help",size:"20",color:"#fff"}}),a("div",{staticStyle:{"white-space":"normal"},attrs:{slot:"content"},slot:"content"},[t._v(" 字段信息设置为必填后，用户在填写表单时若未填写该信息，则无法保存表单 ")])],1)],1)],1)],1)},r=[],s=a("cf45"),i={components:{},data(){return{showInfoType:this.$store.state.curformdata.showInfoType}},computed:{baseData(){return this.$store.state.baseData},label:{get(){return this.$store.state.curformdata.config.label},set(t){this.$store.state.curformdata.config.label=t,this.$store.dispatch("UpdateDataConfig")}},name:{get(){return this.$store.state.curformdata.id},set(t){this.$store.state.curformdata.id=t,this.$store.dispatch("UpdateDataConfig")}},maxSize:{get(){return this.$store.state.curformdata.config.maxSize},set(t){this.$store.state.curformdata.config.maxSize=t,this.$store.dispatch("UpdateDataConfig")}},maxnum:{get(){return this.$store.state.curformdata.config.maxnum},set(t){this.$store.state.curformdata.config.maxnum=t,this.$store.dispatch("UpdateDataConfig")}},format:{get(){return this.$store.state.curformdata.config.format.join(",")},set(t){this.$store.state.curformdata.config.format=t.split(","),this.$store.dispatch("UpdateDataConfig")}},required:{get(){return this.$store.state.curformdata.config.required},set(t){this.$store.state.curformdata.config.required=t,this.$store.dispatch("UpdateDataConfig")}},placeholder:{get(){return this.$store.state.curformdata.config.placeholder},set(t){this.$store.state.curformdata.config.placeholder=t,this.$store.dispatch("UpdateDataConfig")}},infoType:{get(){return this.$store.state.curformdata.config.infoType},set(t){this.$store.state.curformdata.config.infoType=t,this.$store.dispatch("UpdateDataConfig")}},addShow:{get(){return this.$store.state.curformdata.config.addShow},set(t){this.$store.state.curformdata.config.addShow=t,this.$store.dispatch("UpdateDataConfig")}}},watch:{infoType(t,e){this.$store.state.curformdata.id="main"==t?"main_"+s["a"].getRangeCode():"other_"+s["a"].getRangeCode(),this.$store.dispatch("UpdateDataConfig")}},methods:{},created(){},mounted(){}},n=i,l=(a("2cfd"),a("2877")),c=Object(l["a"])(n,o,r,!1,null,"7f30cba8",null);e["default"]=c.exports},abb8:function(t,e,a){"use strict";a.r(e);var o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("FormItem",{directives:[{name:"show",rawName:"v-show",value:!t.data.config.hidden,expression:"!data.config.hidden"}],attrs:{label:t.data.config.label,id:t.data.id,"label-width":t.getwidth(t.data.config.labelWidth)}},[a("Rate",{attrs:{"show-text":"","allow-half":t.data.config.allowHalf,disabled:t.data.config.disabled,character:t.data.config.str,icon:t.data.config.icon},model:{value:t.value,callback:function(e){t.value=e},expression:"value"}},[a("span",{staticStyle:{color:"#f5a623"}},[t._v(t._s(t.value)+"分")])])],1)},r=[],s={props:{data:Object,formconfig:Object},components:{},data(){return{error:"",value:this.data.config.value}},computed:{},watch:{value(t){document.getElementById(this.data.id).classList.remove("ivu-form-item-error"),this.$emit("backdata",this.data.id,t)}},methods:{getwidth(t){return 0==t||"top"==this.formconfig.position?null:t}},created(){},mounted(){this.data.config.value&&(this.value=this.data.config.value)}},i=s,n=a("2877"),l=Object(n["a"])(i,o,r,!1,null,"54a8af1a",null);e["default"]=l.exports},ad8b:function(t,e,a){var o=a("4f3a");"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var r=a("499e").default;r("3d488414",o,!0,{sourceMap:!1,shadowMode:!1})},af5f:function(t,e,a){var o=a("24fb");e=o(!1),e.push([t.i,".ivu-form-item[data-v-cb20970a]{margin-bottom:15px}",""]),t.exports=e},af93:function(t,e,a){var o=a("24fb");e=o(!1),e.push([t.i,".ivu-form-item[data-v-02645018]{margin-bottom:15px}",""]),t.exports=e},b50f:function(t,e,a){var o=a("8493");"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var r=a("499e").default;r("e68307c6",o,!0,{sourceMap:!1,shadowMode:!1})},b816:function(t,e,a){var o=a("24fb");e=o(!1),e.push([t.i,".ivu-form-item[data-v-d99239d8]{margin-bottom:15px}",""]),t.exports=e},b879:function(module,__webpack_exports__,__webpack_require__){"use strict";__webpack_exports__["a"]={props:{data:Object,formconfig:Object},components:{},data(){return{error:""}},computed:{value:{get(){return this.data.config.value},set(value){this.data.config.required&&(""==value||""!=this.data.config.regular&&!eval(this.data.config.regular).test(value))?this.error=this.data.config.message:(this.error="",document.getElementById(this.data.id).classList.remove("ivu-form-item-error")),this.$emit("backdata",this.data.id,value)}}},watch:{},methods:{getwidth(t){return 0==t||"top"==this.formconfig.position?null:t}},created(){},mounted(){this.data.config.value&&(this.value=this.data.config.value)}}},bbc0:function(t,e,a){"use strict";a.r(e);var o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("i-form",{attrs:{"label-position":"top"}},[a("FormItem",{attrs:{label:"标识名"}},[a("Input",{attrs:{type:"text",placeholder:"请输入标识名"},model:{value:t.label,callback:function(e){t.label=e},expression:"label"}})],1),a("FormItem",{attrs:{label:"提示文字"}},[a("Input",{attrs:{type:"text",placeholder:"请输入提示文字"},model:{value:t.placeholder,callback:function(e){t.placeholder=e},expression:"placeholder"}})],1),a("FormItem",{attrs:{label:"选项设置"}},t._l(t.content,(function(e,o){return a("Row",{key:o,staticStyle:{"margin-bottom":"10px"}},[a("i-col",{attrs:{span:9}},[a("Input",{attrs:{type:"text"},on:{input:t.input},model:{value:t.content[o]["value"],callback:function(e){t.$set(t.content[o],"value",e)},expression:"content[index]['value']"}})],1),a("i-col",{attrs:{span:6,offset:1}},[a("Button",{attrs:{icon:"md-close",size:"small",ghost:"",type:"error"},on:{click:function(e){return t.close(o)}}}),t._v(" "),a("Button",{attrs:{icon:"md-add",ghost:"",size:"small",type:"primary"},on:{click:function(e){return t.add(o)}}})],1)],1)})),1),a("FormItem",{attrs:{label:"默认值"}},[a("Radio-group",{model:{value:t.value,callback:function(e){t.value=e},expression:"value"}},t._l(t.content,(function(t,e){return a("Radio",{key:e,attrs:{label:t.value}})})),1)],1),t.showInfoType&&t.checkFormType(t.baseData.type)?a("FormItem",{attrs:{label:"信息类型"}},[a("Radio-group",{attrs:{vertical:""},model:{value:t.infoType,callback:function(e){t.infoType=e},expression:"infoType"}},[a("Radio",{attrs:{label:"main"}},[t._v("主要信息 (可用于搜索)")]),a("Radio",{attrs:{label:"additional"}},[t._v("附加信息 (减轻系统搜索压力)")])],1)],1):t._e(),a("FormItem",[a("Checkbox",{model:{value:t.addShow,callback:function(e){t.addShow=e},expression:"addShow"}},[t._v(" 隐藏字段 "),a("Tooltip",{attrs:{placement:"top-start"}},[a("Icon",{staticStyle:{background:"#2d8cf0","border-radius":"50%"},attrs:{type:"ios-help",size:"20",color:"#fff"}}),a("div",{staticStyle:{"white-space":"normal"},attrs:{slot:"content"},slot:"content"},[t._v(" 对于系统中已有数据的字段信息，若不想再次使用且保存该字段已有数据时，可选择将该字段从表单中隐藏，设置后， 原有字段信息仍可在已收集数据的表单中显示，而在新表单中隐藏。 ")])],1)],1)],1),a("FormItem",[a("Checkbox",{model:{value:t.required,callback:function(e){t.required=e},expression:"required"}},[t._v(" 是否必填 "),a("Tooltip",{attrs:{placement:"top-start"}},[a("Icon",{staticStyle:{background:"#2d8cf0","border-radius":"50%"},attrs:{type:"ios-help",size:"20",color:"#fff"}}),a("div",{staticStyle:{"white-space":"normal"},attrs:{slot:"content"},slot:"content"},[t._v(" 字段信息设置为必填后，用户在填写表单时若未填写该信息，则无法保存表单 ")])],1)],1)],1),"main"==t.infoType&&t.checkFormType(t.baseData.type)?a("FormItem",[a("Checkbox",{model:{value:t.listShow,callback:function(e){t.listShow=e},expression:"listShow"}},[t._v(" 列表是否显示 "),a("Tooltip",{attrs:{placement:"top-start"}},[a("Icon",{staticStyle:{background:"#2d8cf0","border-radius":"50%"},attrs:{type:"ios-help",size:"20",color:"#fff"}}),a("div",{staticStyle:{"white-space":"normal"},attrs:{slot:"content"},slot:"content"},[t._v(" 后台数据列表是否默认显示 ")])],1)],1)],1):t._e()],1)},r=[],s=a("cf45"),i={components:{},data(){return{content:this.$store.state.curformdata.config.content,showInfoType:this.$store.state.curformdata.showInfoType}},computed:{baseData(){return this.$store.state.baseData},label:{get(){return this.$store.state.curformdata.config.label},set(t){this.$store.state.curformdata.config.label=t,this.$store.dispatch("UpdateDataConfig")}},name:{get(){return this.$store.state.curformdata.id},set(t){this.$store.state.curformdata.id=t,this.$store.dispatch("UpdateDataConfig")}},type:{get(){return null==this.$store.state.curformdata.config.type?null:"button"},set(t){"null"==t&&(t=null),this.$store.state.curformdata.config.type=t,this.$store.dispatch("UpdateDataConfig")}},placeholder:{get(){return this.$store.state.curformdata.config.placeholder},set(t){this.$store.state.curformdata.config.placeholder=t,this.$store.dispatch("UpdateDataConfig")}},value:{get(){return this.$store.state.curformdata.value},set(t){this.$store.state.curformdata.value=t,this.$store.state.curformdata.config.value=t,this.$store.dispatch("UpdateDataConfig")}},infoType:{get(){return this.$store.state.curformdata.config.infoType},set(t){this.$store.state.curformdata.config.infoType=t,this.$store.dispatch("UpdateDataConfig")}},listShow:{get(){return this.$store.state.curformdata.config.listShow},set(t){this.$store.state.curformdata.config.listShow=t,this.$store.dispatch("UpdateDataConfig")}},addShow:{get(){return this.$store.state.curformdata.config.addShow},set(t){this.$store.state.curformdata.config.addShow=t,this.$store.dispatch("UpdateDataConfig")}},required:{get(){return this.$store.state.curformdata.config.required},set(t){this.$store.state.curformdata.config.required=t,this.$store.dispatch("UpdateDataConfig")}}},watch:{content(t){this.$store.state.curformdata.config.content=t,this.$store.dispatch("UpdateDataConfig")},infoType(t,e){this.$store.state.curformdata.id="main"==t?"main_"+s["a"].getRangeCode():"other_"+s["a"].getRangeCode(),this.$store.dispatch("UpdateDataConfig")}},methods:{add(t){this.content.push({key:this.content[t].key,value:this.content[t].value})},close(t){if(1==this.content.length)return"";this.content.splice(t,1)},input(){this.$store.state.curformdata.config.content=this.content,this.$store.dispatch("UpdateDataConfig")}},created(){},mounted(){}},n=i,l=(a("0f42"),a("2877")),c=Object(l["a"])(n,o,r,!1,null,"21dcac8d",null);e["default"]=c.exports},bc0a:function(module,__webpack_exports__,__webpack_require__){"use strict";var _utils_service_js__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("9efd");__webpack_exports__["a"]={props:{data:Object,formconfig:Object},components:{},data(){return{error:"",datalist:[],value:[]}},computed:{},watch:{value(value){this.data.config.required&&(""==value||""!=this.data.config.regular&&!eval(this.data.config.regular).test(value))?this.error=this.data.config.message:(this.error="",document.getElementById(this.data.id).classList.remove("ivu-form-item-error")),this.$emit("backdata",this.data.id,value)}},methods:{getwidth(t){return 0==t||"top"==this.formconfig.position?null:t},getregion(){var t=this;t.$http.post(_utils_service_js__WEBPACK_IMPORTED_MODULE_0__["a"].region).then((function(e){t.datalist=e.data})).catch(()=>{t.$Message.error("地区数据获取错误")})}},created(){},mounted(){if(this.data.config.value){for(var t=0;t<this.data.config.value.length;t++)this.data.config.value[t]=parseInt(this.data.config.value[t]);this.value=this.data.config.value}}}},ca0d:function(t,e,a){"use strict";a("3769")},cae5:function(t,e,a){"use strict";a.r(e);var o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("i-form",{attrs:{"label-position":"top"}},[a("FormItem",{attrs:{label:"标识名"}},[a("Input",{attrs:{type:"text",placeholder:"请输入标识名"},model:{value:t.label,callback:function(e){t.label=e},expression:"label"}})],1),a("FormItem",{attrs:{label:"提示文字"}},[a("Input",{attrs:{type:"text",placeholder:"请输入提示文字"},model:{value:t.placeholder,callback:function(e){t.placeholder=e},expression:"placeholder"}})],1),a("FormItem",{attrs:{label:"默认值"}},[a("Input",{attrs:{type:"text",placeholder:"请输入默认值"},model:{value:t.value,callback:function(e){t.value=e},expression:"value"}})],1),t.showInfoType&&t.checkFormType(t.baseData.type)?a("FormItem",{attrs:{label:"信息类型"}},[a("Radio-group",{attrs:{vertical:""},model:{value:t.infoType,callback:function(e){t.infoType=e},expression:"infoType"}},[a("Radio",{attrs:{label:"main"}},[t._v("主要信息 (可用于搜索)")]),a("Radio",{attrs:{label:"additional"}},[t._v("附加信息 (减轻系统搜索压力)")])],1)],1):t._e(),a("FormItem",[a("Checkbox",{model:{value:t.addShow,callback:function(e){t.addShow=e},expression:"addShow"}},[t._v(" 隐藏字段 "),a("Tooltip",{attrs:{placement:"top-start"}},[a("Icon",{staticStyle:{background:"#2d8cf0","border-radius":"50%"},attrs:{type:"ios-help",size:"20",color:"#fff"}}),a("div",{staticStyle:{"white-space":"normal"},attrs:{slot:"content"},slot:"content"},[t._v(" 对于系统中已有数据的字段信息，若不想再次使用且保存该字段已有数据时，可选择将该字段从表单中隐藏，设置后， 原有字段信息仍可在已收集数据的表单中显示，而在新表单中隐藏。 ")])],1)],1)],1),a("FormItem",[a("Checkbox",{model:{value:t.required,callback:function(e){t.required=e},expression:"required"}},[t._v(" 是否必填 "),a("Tooltip",{attrs:{placement:"top-start"}},[a("Icon",{staticStyle:{background:"#2d8cf0","border-radius":"50%"},attrs:{type:"ios-help",size:"20",color:"#fff"}}),a("div",{staticStyle:{"white-space":"normal"},attrs:{slot:"content"},slot:"content"},[t._v(" 字段信息设置为必填后，用户在填写表单时若未填写该信息，则无法保存表单 ")])],1)],1)],1),"main"==t.infoType&&t.checkFormType(t.baseData.type)?a("FormItem",[a("Checkbox",{model:{value:t.listShow,callback:function(e){t.listShow=e},expression:"listShow"}},[t._v(" 列表是否显示 "),a("Tooltip",{attrs:{placement:"top-start"}},[a("Icon",{staticStyle:{background:"#2d8cf0","border-radius":"50%"},attrs:{type:"ios-help",size:"20",color:"#fff"}}),a("div",{staticStyle:{"white-space":"normal"},attrs:{slot:"content"},slot:"content"},[t._v(" 后台数据列表是否默认显示 ")])],1)],1)],1):t._e()],1)},r=[],s=a("9efd"),i=a("cf45"),n={components:{},data(){return{datalist:[],showInfoType:this.$store.state.curformdata.showInfoType}},computed:{baseData(){return this.$store.state.baseData},label:{get(){return this.$store.state.curformdata.config.label},set(t){this.$store.state.curformdata.config.label=t,this.$store.dispatch("UpdateDataConfig")}},name:{get(){return this.$store.state.curformdata.id},set(t){this.$store.state.curformdata.id=t,this.$store.dispatch("UpdateDataConfig")}},placeholder:{get(){return this.$store.state.curformdata.config.placeholder},set(t){this.$store.state.curformdata.config.placeholder=t,this.$store.dispatch("UpdateDataConfig")}},value:{get(){return this.$store.state.curformdata.value},set(t){this.$store.state.curformdata.value=t,this.$store.state.curformdata.config.value=t,this.$store.dispatch("UpdateDataConfig")}},infoType:{get(){return this.$store.state.curformdata.config.infoType},set(t){this.$store.state.curformdata.config.infoType=t,this.$store.dispatch("UpdateDataConfig")}},listShow:{get(){return this.$store.state.curformdata.config.listShow},set(t){this.$store.state.curformdata.config.listShow=t,this.$store.dispatch("UpdateDataConfig")}},addShow:{get(){return this.$store.state.curformdata.config.addShow},set(t){this.$store.state.curformdata.config.addShow=t,this.$store.dispatch("UpdateDataConfig")}},required:{get(){return this.$store.state.curformdata.config.required},set(t){this.$store.state.curformdata.config.required=t,this.$store.dispatch("UpdateDataConfig")}}},watch:{infoType(t,e){this.$store.state.curformdata.id="main"==t?"main_"+i["a"].getRangeCode():"other_"+i["a"].getRangeCode(),this.$store.dispatch("UpdateDataConfig")}},methods:{getregion(){var t=this;t.$http.post(s["a"].region).then((function(e){t.datalist=e.data})).catch(()=>{t.$Message.error("地区数据获取错误")})}},created(){},mounted(){}},l=n,c=(a("683d"),a("2877")),d=Object(c["a"])(l,o,r,!1,null,"02645018",null);e["default"]=d.exports},cf45:function(t,e,a){"use strict";a.d(e,"a",(function(){return o}));var o={updatedata(t,e){for(var a=0;a<t.length;a++)t[a]["id"]==e["id"]?t[a]["config"]=e["config"]:"undefined"!==typeof t[a]["list"]&&(t[a]["list"]=o.updatedata(t[a]["list"],e));return t},getRangeCode(t=6){var e="abcdefghijklmnopqrstuvwxyz";let a="";for(var o=0;o<t;o++)a+=e.charAt(Math.floor(Math.random()*e.length));return a},checkFormType(t){let e=["leads","customer","contacts","contract","examine","business"];return!!e.includes(t)}}},d1da:function(t,e,a){var o=a("9573");"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var r=a("499e").default;r("f748cf1c",o,!0,{sourceMap:!1,shadowMode:!1})},d227:function(module,__webpack_exports__,__webpack_require__){"use strict";__webpack_exports__["a"]={props:{data:Object,formconfig:Object},components:{},data(){return{error:""}},computed:{value:{get(){return this.data.config.value},set(value){this.data.config.required&&(""==value||""!=this.data.config.regular&&!eval(this.data.config.regular).test(value))?this.error=this.data.config.message:(this.error="",document.getElementById(this.data.id).classList.remove("ivu-form-item-error")),this.$emit("backdata",this.data.id,value)}}},watch:{},methods:{getwidth(t){return 0==t||"top"==this.formconfig.position?null:t}},created(){},mounted(){this.data.config.value&&(this.value=this.data.config.value)}}},d46d:function(module,__webpack_exports__,__webpack_require__){"use strict";__webpack_exports__["a"]={props:{data:Object,formconfig:Object},components:{},data(){return{error:""}},computed:{value:{get(){return this.data.config.value},set(value){this.data.config.required&&(""==value||""!=this.data.config.regular&&!eval(this.data.config.regular).test(value))?this.error=this.data.config.message:(this.error="",document.getElementById(this.data.id).classList.remove("ivu-form-item-error")),this.$emit("backdata",this.data.id,value)}}},watch:{},methods:{getwidth(t){return 0==t||"top"==this.formconfig.position?null:t}},created(){},mounted(){this.data.config.value&&(this.value=this.data.config.value)}}},d992:function(t,e,a){var o=a("24fb");e=o(!1),e.push([t.i,".box[data-v-276c8db8]{height:calc(100vh - 166px);overflow:scroll;background:#fff;padding:20px}.box[data-v-276c8db8],.configbox[data-v-276c8db8]{width:100%;display:block}.configbox[data-v-276c8db8]{min-height:500px}.list[data-v-276c8db8]{border:1px dashed #ccc;box-sizing:border-box;padding:10px}.editact[data-v-276c8db8],.editact[data-v-276c8db8]:hover,.list[data-v-276c8db8]:hover{border:1px dashed #2d8cf0;box-sizing:border-box}.editact[data-v-276c8db8],.editact[data-v-276c8db8]:hover{position:relative}.del[data-v-276c8db8]{position:absolute;right:0;bottom:0}.box[data-v-276c8db8]::-webkit-scrollbar{display:none}.hidden[data-v-276c8db8]{line-height:50px;width:100%;text-align:center;color:#8f8f8f}",""]),t.exports=e},dbec:function(t,e,a){"use strict";a.r(e);var o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("i-form",{attrs:{"label-position":"top"}},[a("FormItem",{attrs:{label:"标识名"}},[a("Input",{attrs:{type:"text",placeholder:"请输入标识名"},model:{value:t.label,callback:function(e){t.label=e},expression:"label"}})],1),a("FormItem",{attrs:{label:"提示文字"}},[a("Input",{attrs:{type:"text",placeholder:"请输入提示文字"},model:{value:t.placeholder,callback:function(e){t.placeholder=e},expression:"placeholder"}})],1),a("FormItem",{attrs:{label:"默认值"}},[a("Input",{attrs:{type:"text",placeholder:"请输入默认值"},model:{value:t.value,callback:function(e){t.value=e},expression:"value"}})],1),t.showInfoType&&t.checkFormType(t.baseData.type)?a("FormItem",{attrs:{label:"信息类型"}},[a("Radio-group",{attrs:{vertical:""},model:{value:t.infoType,callback:function(e){t.infoType=e},expression:"infoType"}},[a("Radio",{attrs:{label:"main"}},[t._v("主要信息 (可用于搜索)")]),a("Radio",{attrs:{label:"additional"}},[t._v("附加信息 (减轻系统搜索压力)")])],1)],1):t._e(),"main"==t.infoType&&t.checkFormType(t.baseData.type)?a("FormItem",[a("Checkbox",{model:{value:t.only,callback:function(e){t.only=e},expression:"only"}},[t._v(" 是否唯一 "),a("Tooltip",{attrs:{placement:"top-start"}},[a("Icon",{staticStyle:{background:"#2d8cf0","border-radius":"50%"},attrs:{type:"ios-help",size:"20",color:"#fff"}}),a("div",{staticStyle:{"white-space":"normal"},attrs:{slot:"content"},slot:"content"},[t._v(" 字段设为唯一后在新建信息时则无法填写与系统内部已有信息重复的信息，否则无法保存。如将【新建客户】时“手机号”设为唯一， 则再次新建客户时，重复的手机号输入后，用户将无法保存【新建客户】表单。 ")])],1)],1)],1):t._e(),a("FormItem",[a("Checkbox",{model:{value:t.addShow,callback:function(e){t.addShow=e},expression:"addShow"}},[t._v(" 隐藏字段 "),a("Tooltip",{attrs:{placement:"top-start"}},[a("Icon",{staticStyle:{background:"#2d8cf0","border-radius":"50%"},attrs:{type:"ios-help",size:"20",color:"#fff"}}),a("div",{staticStyle:{"white-space":"normal"},attrs:{slot:"content"},slot:"content"},[t._v(" 对于系统中已有数据的字段信息，若不想再次使用且保存该字段已有数据时，可选择将该字段从表单中隐藏，设置后， 原有字段信息仍可在已收集数据的表单中显示，而在新表单中隐藏。 ")])],1)],1)],1),a("FormItem",[a("Checkbox",{model:{value:t.required,callback:function(e){t.required=e},expression:"required"}},[t._v(" 是否必填 "),a("Tooltip",{attrs:{placement:"top-start"}},[a("Icon",{staticStyle:{background:"#2d8cf0","border-radius":"50%"},attrs:{type:"ios-help",size:"20",color:"#fff"}}),a("div",{staticStyle:{"white-space":"normal"},attrs:{slot:"content"},slot:"content"},[t._v(" 字段信息设置为必填后，用户在填写表单时若未填写该信息，则无法保存表单 ")])],1)],1)],1),"main"==t.infoType&&t.checkFormType(t.baseData.type)?a("FormItem",[a("Checkbox",{model:{value:t.listShow,callback:function(e){t.listShow=e},expression:"listShow"}},[t._v(" 列表是否显示 "),a("Tooltip",{attrs:{placement:"top-start"}},[a("Icon",{staticStyle:{background:"#2d8cf0","border-radius":"50%"},attrs:{type:"ios-help",size:"20",color:"#fff"}}),a("div",{staticStyle:{"white-space":"normal"},attrs:{slot:"content"},slot:"content"},[t._v(" 后台数据列表是否默认显示 ")])],1)],1)],1):t._e()],1)},r=[],s=a("cf45"),i={components:{},data(){return{showInfoType:this.$store.state.curformdata.showInfoType}},computed:{baseData(){return this.$store.state.baseData},label:{get(){return this.$store.state.curformdata.config.label},set(t){this.$store.state.curformdata.config.label=t,this.$store.dispatch("UpdateDataConfig")}},name:{get(){return this.$store.state.curformdata.id},set(t){this.$store.state.curformdata.id=t,this.$store.dispatch("UpdateDataConfig")}},type:{get(){return this.$store.state.curformdata.config.type},set(t){this.$store.state.curformdata.config.type=t,this.$store.dispatch("UpdateDataConfig")}},format:{get(){return this.$store.state.curformdata.config.format},set(t){this.$store.state.curformdata.config.format=t,this.$store.dispatch("UpdateDataConfig")}},placeholder:{get(){return this.$store.state.curformdata.config.placeholder},set(t){this.$store.state.curformdata.config.placeholder=t,this.$store.dispatch("UpdateDataConfig")}},value:{get(){return this.$store.state.curformdata.value},set(t){this.$store.state.curformdata.value=t,this.$store.state.curformdata.config.value=t,this.$store.dispatch("UpdateDataConfig")}},infoType:{get(){return this.$store.state.curformdata.config.infoType},set(t){this.$store.state.curformdata.config.infoType=t,this.$store.dispatch("UpdateDataConfig")}},only:{get(){return this.$store.state.curformdata.config.only},set(t){this.$store.state.curformdata.config.only=t,this.$store.dispatch("UpdateDataConfig")}},listShow:{get(){return this.$store.state.curformdata.config.listShow},set(t){this.$store.state.curformdata.config.listShow=t,this.$store.dispatch("UpdateDataConfig")}},addShow:{get(){return this.$store.state.curformdata.config.addShow},set(t){this.$store.state.curformdata.config.addShow=t,this.$store.dispatch("UpdateDataConfig")}},required:{get(){return this.$store.state.curformdata.config.required},set(t){this.$store.state.curformdata.config.required=t,this.$store.dispatch("UpdateDataConfig")}}},watch:{infoType(t,e){this.$store.state.curformdata.id="main"==t?"main_"+s["a"].getRangeCode():"other_"+s["a"].getRangeCode(),this.$store.dispatch("UpdateDataConfig")}},methods:{},created(){},mounted(){}},n=i,l=(a("a265"),a("2877")),c=Object(l["a"])(n,o,r,!1,null,"cb20970a",null);e["default"]=c.exports},dd1f:function(t,e,a){"use strict";a.r(e);var o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"box"},[a("i-form",{ref:"formInline",attrs:{"label-position":t.formconfig.position}},[a("draggable",{staticClass:"configbox",attrs:{id:"configbox",tag:"div",group:"content",list:t.config},on:{start:t.startdrag,end:t.enddrag}},t._l(t.config,(function(e,o){return a("div",{key:o,staticClass:"list",class:t.editact==e.id?"editact":"",on:{click:function(a){return t.edititem(e.id)}}},[a("qdsd-"+e.component,{tag:"component",attrs:{data:e,formconfig:t.formconfig,ispreview:!0},on:{backdata:t.backdata}}),t.editact==e.id&&e.config.is_delete?a("div",{staticClass:"del"},[a("Button",{attrs:{type:"text",size:"small"},on:{click:function(a){return t.del(e.id)}}},[a("Icon",{attrs:{type:"ios-trash",size:"20",color:"#57a3f3"}})],1)],1):t._e()],1)})),0)],1)],1)},r=[],s=a("b76a"),i=a.n(s),n={components:{draggable:i.a},data(){return{editact:0,formdata:{}}},computed:{theme(){return"Theme"+this.$store.state.formconfig.themeSelected},config(){return this.$store.state.dataconfig},formconfig(){return this.$store.state.formconfig}},watch:{},methods:{startdrag(){this.$store.state.draggableStatus=!0},enddrag(){this.$store.state.draggableStatus=!1},edititem(t){this.editact=t,this.searchid(t,this.config)},searchid(t,e){for(var a=0;a<e.length;a++)e[a]["id"]==t?this.$store.state.curformdata=e[a]:"undefined"!==typeof e[a]["list"]&&this.searchid(t,e[a]["list"])},backdata(t,e){this.formdata[t]=e},del(t){this.searchid_del(t,this.config)},searchid_del(t,e){for(var a=0;a<e.length;a++)e[a]["id"]==t?e.splice(a,1):"undefined"!==typeof e[a]["list"]&&(e[a]["list"]=this.searchid_del(t,e[a]["list"]));return e}},created(){},mounted(){}},l=n,c=(a("503f"),a("2877")),d=Object(c["a"])(l,o,r,!1,null,"276c8db8",null);e["default"]=d.exports},e9c9:function(t,e,a){var o=a("7fda");"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var r=a("499e").default;r("64ad87f4",o,!0,{sourceMap:!1,shadowMode:!1})},ed8a:function(t,e,a){var o=a("24fb");e=o(!1),e.push([t.i,".ivu-form-item[data-v-39b57a72]{margin-bottom:15px}",""]),t.exports=e},eed5:function(t,e,a){var o=a("24fb");e=o(!1),e.push([t.i,".ivu-form-item[data-v-f3345a24]{margin-bottom:15px}",""]),t.exports=e},f00d:function(t,e,a){"use strict";a.r(e);var o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("i-form",{attrs:{"label-position":"top"}},[a("FormItem",{attrs:{label:"标题隐藏"}},[a("i-switch",{model:{value:t.hidden,callback:function(e){t.hidden=e},expression:"hidden"}})],1),a("FormItem",{attrs:{label:"表单标题"}},[a("Input",{attrs:{placeholder:"表单标题"},model:{value:t.title,callback:function(e){t.title=e},expression:"title"}})],1),a("FormItem",{attrs:{label:"标题对齐方式"}},[a("RadioGroup",{attrs:{type:"button"},model:{value:t.float,callback:function(e){t.float=e},expression:"float"}},[a("Radio",{attrs:{label:"left"}},[t._v("左对齐")]),a("Radio",{attrs:{label:"center"}},[t._v("居中")]),a("Radio",{attrs:{label:"right"}},[t._v("右对齐")])],1)],1),a("FormItem",{attrs:{label:"label对齐方式"}},[a("RadioGroup",{attrs:{type:"button"},model:{value:t.position,callback:function(e){t.position=e},expression:"position"}},[a("Radio",{attrs:{label:"left"}},[t._v("左对齐")]),a("Radio",{attrs:{label:"top"}},[t._v("顶部")]),a("Radio",{attrs:{label:"right"}},[t._v("右对齐")])],1)],1),a("FormItem",{attrs:{label:"输入框尺寸"}},[a("RadioGroup",{attrs:{type:"button"},model:{value:t.size,callback:function(e){t.size=e},expression:"size"}},[a("Radio",{attrs:{label:"large"}},[t._v("大")]),a("Radio",{attrs:{label:"default"}},[t._v("中")]),a("Radio",{attrs:{label:"small"}},[t._v("小")])],1)],1),a("FormItem",{attrs:{label:"表单缩进"}},[a("Input",{attrs:{placeholder:"表单缩进"},model:{value:t.padding,callback:function(e){t.padding=e},expression:"padding"}})],1),1==t.formconfig.themeSelected?a("FormItem",{attrs:{label:"标题颜色"}},[a("ColorPicker",{attrs:{recommend:""},model:{value:t.color,callback:function(e){t.color=e},expression:"color"}})],1):t._e(),a("FormItem",{attrs:{label:"背景颜色"}},[a("ColorPicker",{attrs:{recommend:""},model:{value:t.background,callback:function(e){t.background=e},expression:"background"}})],1),a("FormItem",{attrs:{label:"显示字段"}},[a("RadioGroup",{model:{value:t.showfield,callback:function(e){t.showfield=e},expression:"showfield"}},t._l(t.showfieldlist,(function(e,o){return a("Radio",{key:o,attrs:{label:e.id}},[t._v(t._s(e.name))])})),1)],1)],1)},r=[],s={components:{},data(){return{}},computed:{showfieldlist(){return this.getlist(JSON.parse(JSON.stringify(this.$store.state.dataconfig)))},showfield:{get(){return this.deletefield(this.$store.state.formconfig.showfield)},set(t){this.$store.state.formconfig.showfield=t}},formconfig(){return this.$store.state.formconfig},title:{get(){return this.$store.state.formconfig.title},set(t){this.$store.state.formconfig.title=t}},float:{get(){return this.$store.state.formconfig.float},set(t){this.$store.state.formconfig.float=t}},position:{get(){return this.$store.state.formconfig.position},set(t){this.$store.state.formconfig.position=t}},size:{get(){return this.$store.state.formconfig.size?this.$store.state.formconfig.size:""},set(t){this.$store.state.formconfig.size=t}},hidden:{get(){return this.$store.state.formconfig.hidden},set(t){this.$store.state.formconfig.hidden=t}},background:{get(){return this.$store.state.formconfig.style.background},set(t){this.$store.state.formconfig.style.background=t}},color:{get(){return this.$store.state.formconfig.style.color},set(t){this.$store.state.formconfig.style.color=t}},padding:{get(){return this.$store.state.formconfig.style.padding},set(t){this.$store.state.formconfig.style.padding=t}}},watch:{showfieldlist(t){this.$store.state.formconfig.showfieldlist=t}},methods:{getlist(t){for(var e=[],a=0;a<t.length;a++)t[a]["list"]?e=e.concat(this.getlist(t[a]["list"])):1==t[a]["type"]&&e.push({id:t[a]["id"],name:t[a]["config"]["label"]});return e},deletefield(t){for(var e=this.showfieldlist,a=!1,o=0;o<t.length;o++){for(var r=0;r<e.length;r++)t[o]==e[r]["id"]&&(a=!0);a||(t.splice(o,1),a=!1)}return this.$store.state.formconfig.showfield=t,t}},created(){},mounted(){}},i=s,n=(a("8ef8"),a("2877")),l=Object(n["a"])(i,o,r,!1,null,null,null);e["default"]=l.exports},f64c:function(t,e,a){var o=a("5434");"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var r=a("499e").default;r("4ffe4611",o,!0,{sourceMap:!1,shadowMode:!1})},faa4:function(t,e,a){"use strict";a.r(e);var o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("i-form",{attrs:{"label-position":"top"}},[a("FormItem",{attrs:{label:"类型"}},[a("RadioGroup",{model:{value:t.type,callback:function(e){t.type=e},expression:"type"}},[a("Radio",{attrs:{label:"date"}},[t._v("日期")])],1)],1),a("FormItem",{attrs:{label:"标识名"}},[a("Input",{attrs:{type:"text",placeholder:"请输入标识名"},model:{value:t.label,callback:function(e){t.label=e},expression:"label"}})],1),a("FormItem",{attrs:{label:"提示文字"}},[a("Input",{attrs:{type:"text",placeholder:"请输入提示文字"},model:{value:t.placeholder,callback:function(e){t.placeholder=e},expression:"placeholder"}})],1),a("FormItem",{attrs:{label:"默认值"}},[a("Input",{attrs:{type:"text",placeholder:"请输入默认值"},model:{value:t.value,callback:function(e){t.value=e},expression:"value"}})],1),t.showInfoType&&t.checkFormType(t.baseData.type)?a("FormItem",{attrs:{label:"信息类型"}},[a("Radio-group",{attrs:{vertical:""},model:{value:t.infoType,callback:function(e){t.infoType=e},expression:"infoType"}},[a("Radio",{attrs:{label:"main"}},[t._v("主要信息 (可用于搜索)")]),a("Radio",{attrs:{label:"additional"}},[t._v("附加信息 (减轻系统搜索压力)")])],1)],1):t._e(),"main"==t.infoType&&t.checkFormType(t.baseData.type)?a("FormItem",[a("Checkbox",{model:{value:t.only,callback:function(e){t.only=e},expression:"only"}},[t._v(" 是否唯一 "),a("Tooltip",{attrs:{placement:"top-start"}},[a("Icon",{staticStyle:{background:"#2d8cf0","border-radius":"50%"},attrs:{type:"ios-help",size:"20",color:"#fff"}}),a("div",{staticStyle:{"white-space":"normal"},attrs:{slot:"content"},slot:"content"},[t._v(" 字段设为唯一后在新建信息时则无法填写与系统内部已有信息重复的信息，否则无法保存。如将【新建客户】时“手机号”设为唯一， 则再次新建客户时，重复的手机号输入后，用户将无法保存【新建客户】表单。 ")])],1)],1)],1):t._e(),a("FormItem",[a("Checkbox",{model:{value:t.addShow,callback:function(e){t.addShow=e},expression:"addShow"}},[t._v(" 隐藏字段 "),a("Tooltip",{attrs:{placement:"top-start"}},[a("Icon",{staticStyle:{background:"#2d8cf0","border-radius":"50%"},attrs:{type:"ios-help",size:"20",color:"#fff"}}),a("div",{staticStyle:{"white-space":"normal"},attrs:{slot:"content"},slot:"content"},[t._v(" 对于系统中已有数据的字段信息，若不想再次使用且保存该字段已有数据时，可选择将该字段从表单中隐藏，设置后， 原有字段信息仍可在已收集数据的表单中显示，而在新表单中隐藏。 ")])],1)],1)],1),a("FormItem",[a("Checkbox",{model:{value:t.required,callback:function(e){t.required=e},expression:"required"}},[t._v(" 是否必填 "),a("Tooltip",{attrs:{placement:"top-start"}},[a("Icon",{staticStyle:{background:"#2d8cf0","border-radius":"50%"},attrs:{type:"ios-help",size:"20",color:"#fff"}}),a("div",{staticStyle:{"white-space":"normal"},attrs:{slot:"content"},slot:"content"},[t._v(" 字段信息设置为必填后，用户在填写表单时若未填写该信息，则无法保存表单 ")])],1)],1)],1),"main"==t.infoType&&t.checkFormType(t.baseData.type)?a("FormItem",[a("Checkbox",{model:{value:t.listShow,callback:function(e){t.listShow=e},expression:"listShow"}},[t._v(" 列表是否显示 "),a("Tooltip",{attrs:{placement:"top-start"}},[a("Icon",{staticStyle:{background:"#2d8cf0","border-radius":"50%"},attrs:{type:"ios-help",size:"20",color:"#fff"}}),a("div",{staticStyle:{"white-space":"normal"},attrs:{slot:"content"},slot:"content"},[t._v(" 后台数据列表是否默认显示 ")])],1)],1)],1):t._e()],1)},r=[],s=a("cf45"),i={components:{},data(){return{showInfoType:this.$store.state.curformdata.showInfoType}},computed:{baseData(){return this.$store.state.baseData},label:{get(){return this.$store.state.curformdata.config.label},set(t){this.$store.state.curformdata.config.label=t,this.$store.dispatch("UpdateDataConfig")}},name:{get(){return this.$store.state.curformdata.id},set(t){this.$store.state.curformdata.id=t,this.$store.dispatch("UpdateDataConfig")}},type:{get(){return this.$store.state.curformdata.config.type},set(t){this.$store.state.curformdata.config.type=t,this.$store.dispatch("UpdateDataConfig")}},placeholder:{get(){return this.$store.state.curformdata.config.placeholder},set(t){this.$store.state.curformdata.config.placeholder=t,this.$store.dispatch("UpdateDataConfig")}},value:{get(){return this.$store.state.curformdata.value},set(t){this.$store.state.curformdata.value=t,this.$store.state.curformdata.config.value=t,this.$store.dispatch("UpdateDataConfig")}},infoType:{get(){return this.$store.state.curformdata.config.infoType},set(t){this.$store.state.curformdata.config.infoType=t,this.$store.dispatch("UpdateDataConfig")}},only:{get(){return this.$store.state.curformdata.config.only},set(t){this.$store.state.curformdata.config.only=t,this.$store.dispatch("UpdateDataConfig")}},listShow:{get(){return this.$store.state.curformdata.config.listShow},set(t){this.$store.state.curformdata.config.listShow=t,this.$store.dispatch("UpdateDataConfig")}},addShow:{get(){return this.$store.state.curformdata.config.addShow},set(t){this.$store.state.curformdata.config.addShow=t,this.$store.dispatch("UpdateDataConfig")}},required:{get(){return this.$store.state.curformdata.config.required},set(t){this.$store.state.curformdata.config.required=t,this.$store.dispatch("UpdateDataConfig")}}},watch:{infoType(t,e){this.$store.state.curformdata.id="main"==t?"main_"+s["a"].getRangeCode():"other_"+s["a"].getRangeCode(),this.$store.dispatch("UpdateDataConfig")}},methods:{},created(){},mounted(){}},n=i,l=(a("846a"),a("2877")),c=Object(l["a"])(n,o,r,!1,null,"7408d775",null);e["default"]=c.exports},fc4d:function(t,e,a){var o=a("642e");"string"===typeof o&&(o=[[t.i,o,""]]),o.locals&&(t.exports=o.locals);var r=a("499e").default;r("585dd916",o,!0,{sourceMap:!1,shadowMode:!1})}});
//# sourceMappingURL=app.efec0597.js.map