{"version": 3, "sources": ["webpack:///./src/views/Home.vue?2c4c", "webpack:///./src/views/Home.vue?73df", "webpack:///./src/views/Home.vue?f585", "webpack:///./src/views/Home.vue?f66c", "webpack:///src/views/Home.vue", "webpack:///./src/views/Home.vue?2b8c", "webpack:///./src/views/Home.vue"], "names": ["content", "module", "i", "locals", "exports", "add", "default", "___CSS_LOADER_API_IMPORT___", "push", "render", "_vm", "this", "_h", "$createElement", "_c", "_self", "attrs", "staticClass", "_v", "curformdata", "component", "tag", "_e", "staticRenderFns", "deletelist", "id", "reload", "computed", "$store", "state", "watch", "$nextTick", "methods", "that", "$http", "to", "query", "getRow", "$route"], "mappings": "kHAAA,W,uBCGA,IAAIA,EAAU,EAAQ,QACA,kBAAZA,IAAsBA,EAAU,CAAC,CAACC,EAAOC,EAAIF,EAAS,MAC7DA,EAAQG,SAAQF,EAAOG,QAAUJ,EAAQG,QAE5C,IAAIE,EAAM,EAAQ,QAA+DC,QACpED,EAAI,WAAYL,GAAS,EAAM,CAAC,WAAY,EAAM,YAAa,K,qBCP5E,IAAIO,EAA8B,EAAQ,QAC1CH,EAAUG,GAA4B,GAEtCH,EAAQI,KAAK,CAACP,EAAOC,EAAI,gxBAAixB,KAE1yBD,EAAOG,QAAUA,G,yCCNjB,IAAIK,EAAS,WAAa,IAAIC,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,MAAM,CAACA,EAAG,UAAUA,EAAG,MAAM,CAACE,MAAM,CAAC,KAAO,OAAO,QAAU,kBAAkB,CAACF,EAAG,QAAQ,CAACE,MAAM,CAAC,GAAK,GAAG,GAAK,GAAG,GAAK,GAAG,GAAK,EAAE,aAAa,aAAa,CAACF,EAAG,YAAYA,EAAG,MAAM,CAACG,YAAY,UAAU,CAACH,EAAG,MAAM,CAACG,YAAY,YAAY,CAACP,EAAIQ,GAAG,UAAWR,EAAU,OAAEI,EAAGJ,EAAIS,YAAYC,UAAY,QAAUV,EAAIS,YAAYC,UAAY,QAAU,GAAG,CAACC,IAAI,cAAcX,EAAIY,KAAOZ,EAAIS,YAAYC,UAA2DV,EAAIY,KAApDR,EAAG,KAAK,CAACG,YAAY,QAAQ,CAACP,EAAIQ,GAAG,YAAqB,IAAI,GAAGJ,EAAG,QAAQ,CAACE,MAAM,CAAC,GAAK,GAAG,GAAK,GAAG,GAAK,GAAG,GAAK,GAAG,aAAa,eAAe,CAACF,EAAG,eAAe,IAAI,IAAI,IACnqBS,EAAkB,G,YC4BtB,GAEE,OAEE,MAAO,CACLC,WAAY,GACZC,GAAI,EACJC,QAAQ,IAIZC,SAAU,CACR,cACE,OAAOhB,KAAKiB,OAAOC,MAAMV,cAI7BW,MAAO,CACL,cACEnB,KAAKe,QAAS,EACdf,KAAKoB,WAAU,WACbpB,KAAKe,QAAS,OAKpBM,QAAS,CAEP,YACErB,KAAKiB,OAAOC,MAAMV,YAAc,IAElC,SACE,IAAIc,EAAOtB,KACXsB,EAAKC,MACX,yBAAQ,GAAR,KAAQ,MAAR,IACA,SACQ,EAAR,OACA,WACA,0BACY,EAAZ,sDAEU,EAAV,8BAEU,EAAV,6BAGA,WACQ,EAAR,2BAIE,kBAAkBC,GACZA,EAAGC,MAAMX,IAAMd,KAAKc,KACtBd,KAAKc,GAAKU,EAAGC,MAAMX,GACnBd,KAAKiB,OAAOC,MAAMJ,GAAKU,EAAGC,MAAMX,GAChCd,KAAK0B,WAIT,YAEA,UACE1B,KAAKc,GAAKd,KAAK2B,OAAOF,MAAMX,GAC5Bd,KAAKiB,OAAOC,MAAMJ,GAAKd,KAAK2B,OAAOF,MAAMX,GACzCd,KAAK0B,WC7FqU,I,wBCQ1UjB,EAAY,eACd,EACAX,EACAc,GACA,EACA,KACA,WACA,MAIa,aAAAH,E", "file": "assets/addons/qingdong/js/chunk-b7723af0.5cb4589f.js", "sourcesContent": ["export * from \"-!../../node_modules/vue-style-loader/index.js??ref--10-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-2!../../node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-3!../../node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Home.vue?vue&type=style&index=0&id=467f097f&lang=less&scoped=true&\"", "// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-2!../../node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-3!../../node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Home.vue?vue&type=style&index=0&id=467f097f&lang=less&scoped=true&\");\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add the styles to the DOM\nvar add = require(\"!../../node_modules/vue-style-loader/lib/addStylesClient.js\").default\nvar update = add(\"4033f784\", content, true, {\"sourceMap\":false,\"shadowMode\":false});", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../node_modules/css-loader/dist/runtime/api.js\");\nexports = ___CSS_LOADER_API_IMPORT___(false);\n// Module\nexports.push([module.id, \".box-center[data-v-467f097f],.box-left[data-v-467f097f],.box-right[data-v-467f097f]{height:calc(100vh - 64px);border:1px solid #f5f5f5;box-sizing:border-box}.box-left[data-v-467f097f]{border-left:0}.box-center[data-v-467f097f]{padding:50px;background:#efefef}.box-right[data-v-467f097f]{position:relative}.tips[data-v-467f097f]{color:#ccc}.deletebox[data-v-467f097f],.tips[data-v-467f097f]{width:100%;text-align:center}.deletebox[data-v-467f097f]{padding:30px 0;border-top:20px solid #efefef;color:#ed4014;position:absolute;bottom:0}.tabbox[data-v-467f097f]{padding:20px}.tabtitle[data-v-467f097f]{font-size:26px;color:#333;background:#f5f5f5;padding-left:20px;margin-bottom:20px}.ivu-tabs[data-v-467f097f]{overflow:visible}.tabbox[data-v-467f097f]::-webkit-scrollbar{display:none}\", \"\"]);\n// Exports\nmodule.exports = exports;\n", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',[_c('Header'),_c('Row',{attrs:{\"type\":\"flex\",\"justify\":\"space-between\"}},[_c('i-col',{attrs:{\"xs\":24,\"sm\":24,\"md\":12,\"lg\":8,\"class-name\":\"box-left\"}},[_c('LeftMenu'),_c('div',{staticClass:\"tabbox\"},[_c('div',{staticClass:\"tabtitle\"},[_vm._v(\"字段配置\")]),(_vm.reload)?_c(_vm.curformdata.component ? 'qdsd-' + _vm.curformdata.component + '-edit' : '',{tag:\"component\"}):_vm._e(),(!_vm.curformdata.component)?_c('h2',{staticClass:\"tips\"},[_vm._v(\"请选择控件\")]):_vm._e()],1)],1),_c('i-col',{attrs:{\"xs\":24,\"sm\":24,\"md\":12,\"lg\":16,\"class-name\":\"box-center\"}},[_c('MainConfig')],1)],1)],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n  <div>\r\n    <Header></Header>\r\n    <Row type=\"flex\" justify=\"space-between\">\r\n      <i-col :xs=\"24\" :sm=\"24\" :md=\"12\" :lg=\"8\" class-name=\"box-left\">\r\n        <LeftMenu></LeftMenu>\r\n        <div class=\"tabbox\">\r\n          <div class=\"tabtitle\">字段配置</div>\r\n          <component :is=\"curformdata.component ? 'qdsd-' + curformdata.component + '-edit' : ''\" v-if=\"reload\" ></component>\r\n          <h2 v-if=\"!curformdata.component\" class=\"tips\">请选择控件</h2>\r\n        </div>\r\n      </i-col>\r\n      <i-col :xs=\"24\" :sm=\"24\" :md=\"12\" :lg=\"16\" class-name=\"box-center\">\r\n        <MainConfig></MainConfig>\r\n      </i-col>\r\n\r\n      <!-- <i-col :xs=\"24\" :sm=\"24\" :md=\"7\" :lg=\"5\" class-name=\"box-right\">\r\n        <div class=\"tabbox\">\r\n          <div class=\"tabtitle\">字段配置</div>\r\n          <component :is=\"curformdata.component ? 'qdsd-' + curformdata.component + '-edit' : ''\" v-if=\"reload\" ></component>\r\n          <h2 v-if=\"!curformdata.component\" class=\"tips\">请选择控件</h2>\r\n        </div>\r\n      </i-col> -->\r\n    </Row>\r\n  </div>\r\n</template>\r\n<script>\r\nimport { service } from \"../utils/service.js\";\r\n// import draggable from 'vuedraggable'\r\nexport default {\r\n  // components: { draggable },\r\n  data() {\r\n    //这里存放数据\r\n    return {\r\n      deletelist: [],\r\n      id: 7,\r\n      reload: true,\r\n    };\r\n  },\r\n  //监听属性 类似于data概念\r\n  computed: {\r\n    curformdata() {\r\n      return this.$store.state.curformdata;\r\n    },\r\n  },\r\n  //监控data中的数据变化\r\n  watch: {\r\n    curformdata() {\r\n      this.reload = false;\r\n      this.$nextTick(function () {\r\n        this.reload = true;\r\n      });\r\n    },\r\n  },\r\n  //方法集合,\r\n  methods: {\r\n    //删除后回调\r\n    adddelete() {\r\n      this.$store.state.curformdata = {};\r\n    },\r\n    getRow() {\r\n      var that = this;\r\n      that.$http\r\n        .post(service.design_data, { id: that.id, _ajax: 1 })\r\n        .then((response) => {\r\n          response = response.data\r\n          if (response.code == 1) {\r\n            if(JSON.parse(response.data.data)) {\r\n              that.$store.state.dataconfig = JSON.parse(response.data.data).data\r\n            }\r\n            that.$store.state.baseData = response.data;\r\n          } else {\r\n            that.$Message.error(response.data.msg);\r\n          }\r\n        })\r\n        .catch(() => {\r\n          that.$Message.error(\"网络错误\");\r\n        });\r\n    },\r\n  },\r\n  beforeRouteUpdate(to) {\r\n    if (to.query.id != this.id) {\r\n      this.id = to.query.id;\r\n      this.$store.state.id = to.query.id;\r\n      this.getRow();\r\n    }\r\n  },\r\n  //生命周期 - 创建完成（可以访问当前this实例）\r\n  created() {},\r\n  //生命周期 - 挂载完成（可以访问DOM元素）\r\n  mounted() {\r\n    this.id = this.$route.query.id;\r\n    this.$store.state.id = this.$route.query.id;\r\n    this.getRow();\r\n  },\r\n};\r\n</script>\r\n<style lang=\"less\" scoped>\r\n.box-left,\r\n.box-center,\r\n.box-right {\r\n  height: calc(100vh - 64px);\r\n  border: solid 1px #f5f5f5;\r\n  box-sizing: border-box;\r\n}\r\n.box-left {\r\n  border-left: 0px;\r\n}\r\n.box-center {\r\n  padding: 50px;\r\n  background: #efefef;\r\n}\r\n.box-right {\r\n  position: relative;\r\n}\r\n.tips {\r\n  color: #cccccc;\r\n  width: 100%;\r\n  text-align: center;\r\n}\r\n.deletebox {\r\n  width: 100%;\r\n  padding: 30px 0;\r\n  border-top: 20px solid #efefef;\r\n  color: #ed4014;\r\n  text-align: center;\r\n  position: absolute;\r\n  bottom: 0;\r\n}\r\n.tabbox {\r\n  padding:20px;\r\n  // overflow-y: scroll;\r\n}\r\n.tabtitle{\r\n  font-size:26px;\r\n  color:#333;\r\n  background:#f5f5f5;\r\n  padding-left:20px;\r\n  margin-bottom:20px;\r\n}\r\n.ivu-tabs {\r\n  overflow: visible;\r\n}\r\n.tabbox::-webkit-scrollbar {\r\n  display: none;\r\n}\r\n</style>", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Home.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Home.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./Home.vue?vue&type=template&id=467f097f&scoped=true&\"\nimport script from \"./Home.vue?vue&type=script&lang=js&\"\nexport * from \"./Home.vue?vue&type=script&lang=js&\"\nimport style0 from \"./Home.vue?vue&type=style&index=0&id=467f097f&lang=less&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"467f097f\",\n  null\n  \n)\n\nexport default component.exports"], "sourceRoot": ""}