(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-148f843c"],{"0ecb":function(t,a,e){var s=e("c4d3");"string"===typeof s&&(s=[[t.i,s,""]]),s.locals&&(t.exports=s.locals);var o=e("499e").default;o("539e7f62",s,!0,{sourceMap:!1,shadowMode:!1})},2830:function(t,a,e){"use strict";e("0ecb")},bb51:function(t,a,e){"use strict";e.r(a);var s=function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",[e("Header"),e("Row",{attrs:{type:"flex",justify:"space-between"}},[e("i-col",{attrs:{xs:24,sm:24,md:7,lg:5,"class-name":"box-left"}},[e("LeftMenu")],1),e("i-col",{attrs:{xs:24,sm:24,md:10,lg:14,"class-name":"box-center"}},[e("MainConfig")],1),e("i-col",{attrs:{xs:24,sm:24,md:7,lg:5,"class-name":"box-right"}},[e("Tabs",[e("TabPane",{attrs:{label:"字段属性"}},[e("div",{staticClass:"tabbox"},[t.reload?e(t.curformdata.component?"qdsd-"+t.curformdata.component+"-edit":"",{tag:"component"}):t._e(),t.curformdata.component?t._e():e("h2",{staticClass:"tips"},[t._v(" 请选择控件 ")])],1)]),"customer"==t.baseData.type?e("TabPane",{attrs:{label:"基础设置"}},[e("div",{staticClass:"tabbox"},[e("i-form",{ref:"formInline",attrs:{inline:""}},[e("Form-item",{attrs:{label:"是否添加联系人"}},[e("i-switch",{model:{value:t.isAddContact,callback:function(a){t.isAddContact=a},expression:"isAddContact"}},[e("span",{attrs:{slot:"open"},slot:"open"},[t._v("是")]),e("span",{attrs:{slot:"close"},slot:"close"},[t._v("否")])])],1)],1)],1)]):t._e()],1)],1)],1)],1)},o=[],r=e("9efd"),d={data(){return{deletelist:[],id:7,reload:!0}},computed:{curformdata(){return this.$store.state.curformdata},baseData(){return this.$store.state.baseData},isAddContact:{get(){return this.$store.state.isAddContact},set(t){this.$store.state.isAddContact=t}}},watch:{curformdata(){this.reload=!1,this.$nextTick((function(){this.reload=!0}))}},methods:{adddelete(){this.$store.state.curformdata={}},getRow(){var t=this;t.$http.post(r["a"].design_data,{id:t.id,_ajax:1}).then(a=>{a=a.data,1==a.code?(JSON.parse(a.data.data)&&(t.$store.state.dataconfig=JSON.parse(a.data.data).data),t.$store.state.baseData=a.data):t.$Message.error(a.data.msg)}).catch(()=>{t.$Message.error("网络错误")})}},beforeRouteUpdate(t){t.query.id!=this.id&&(this.id=t.query.id,this.$store.state.id=t.query.id,this.getRow())},created(){},mounted(){this.id=this.$route.query.id,this.$store.state.id=this.$route.query.id,this.getRow()}},i=d,n=(e("2830"),e("2877")),c=Object(n["a"])(i,s,o,!1,null,"13347935",null);a["default"]=c.exports},c4d3:function(t,a,e){var s=e("24fb");a=s(!1),a.push([t.i,".box-center[data-v-13347935],.box-left[data-v-13347935],.box-right[data-v-13347935]{height:calc(100vh - 64px);border:1px solid #f5f5f5;box-sizing:border-box}.box-left[data-v-13347935]{border-left:0}.box-center[data-v-13347935]{padding:50px;background:#efefef}.box-right[data-v-13347935]{position:relative}.tips[data-v-13347935]{color:#ccc;text-align:center}.tabbox[data-v-13347935]{padding:10px}.tabbox[data-v-13347935]::-webkit-scrollbar{display:none}",""]),t.exports=a}}]);
//# sourceMappingURL=chunk-148f843c.a6c01166.js.map