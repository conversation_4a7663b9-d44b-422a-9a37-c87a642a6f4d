(function(t){function e(e){for(var r,s,n=e[0],l=e[1],d=e[2],c=0,f=[];c<n.length;c++)s=n[c],Object.prototype.hasOwnProperty.call(o,s)&&o[s]&&f.push(o[s][0]),o[s]=0;for(r in l)Object.prototype.hasOwnProperty.call(l,r)&&(t[r]=l[r]);u&&u(e);while(f.length)f.shift()();return i.push.apply(i,d||[]),a()}function a(){for(var t,e=0;e<i.length;e++){for(var a=i[e],r=!0,s=1;s<a.length;s++){var l=a[s];0!==o[l]&&(r=!1)}r&&(i.splice(e--,1),t=n(n.s=a[0]))}return t}var r={},o={app:0},i=[];function s(t){return n.p+"assets/addons/qingdong/js/"+({}[t]||t)+"."+{"chunk-780a07a6":"7a1f700f"}[t]+".js"}function n(e){if(r[e])return r[e].exports;var a=r[e]={i:e,l:!1,exports:{}};return t[e].call(a.exports,a,a.exports,n),a.l=!0,a.exports}n.e=function(t){var e=[],a=o[t];if(0!==a)if(a)e.push(a[2]);else{var r=new Promise((function(e,r){a=o[t]=[e,r]}));e.push(a[2]=r);var i,l=document.createElement("script");l.charset="utf-8",l.timeout=120,n.nc&&l.setAttribute("nonce",n.nc),l.src=s(t);var d=new Error;i=function(e){l.onerror=l.onload=null,clearTimeout(c);var a=o[t];if(0!==a){if(a){var r=e&&("load"===e.type?"missing":e.type),i=e&&e.target&&e.target.src;d.message="Loading chunk "+t+" failed.\n("+r+": "+i+")",d.name="ChunkLoadError",d.type=r,d.request=i,a[1](d)}o[t]=void 0}};var c=setTimeout((function(){i({type:"timeout",target:l})}),12e4);l.onerror=l.onload=i,document.head.appendChild(l)}return Promise.all(e)},n.m=t,n.c=r,n.d=function(t,e,a){n.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:a})},n.r=function(t){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},n.t=function(t,e){if(1&e&&(t=n(t)),8&e)return t;if(4&e&&"object"===typeof t&&t&&t.__esModule)return t;var a=Object.create(null);if(n.r(a),Object.defineProperty(a,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var r in t)n.d(a,r,function(e){return t[e]}.bind(null,r));return a},n.n=function(t){var e=t&&t.__esModule?function(){return t["default"]}:function(){return t};return n.d(e,"a",e),e},n.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},n.p="/",n.oe=function(t){throw console.error(t),t};var l=window["webpackJsonp"]=window["webpackJsonp"]||[],d=l.push.bind(l);l.push=e,l=l.slice();for(var c=0;c<l.length;c++)e(l[c]);var u=d;i.push([0,"chunk-vendors"]),a()})({0:function(t,e,a){t.exports=a("56d7")},"0418":function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{attrs:{id:"header"}},[a("Row",[a("i-col",{attrs:{span:"8",offset:"1","class-name":"title"}},[t._v("标题"+t._s(t.baseData.name))]),a("i-col",{attrs:{span:"13","class-name":"action"}},[a("Button",{attrs:{type:"primary"},on:{click:t.save}},[t._v("保存")])],1)],1)],1)},o=[],i=a("9efd"),s={components:{},data(){return{showbox:!1}},computed:{theme(){return"Theme"+this.$store.state.formconfig.themeSelected},baseData(){return this.$store.state.baseData},formconfig(){return this.$store.state.formconfig},config(){return this.$store.state.dataconfig}},watch:{},methods:{save(){var t=this;t.$http.post(i["a"].design_save,{id:t.$store.state.id,data:JSON.stringify({data:t.$store.state.dataconfig}),_ajax:1}).then((function(e){1==e.data.code?t.$Message.success(e.data.msg):t.$Message.error(e.data.msg)})).catch(()=>{t.$Message.error("网络错误")})}},created(){},mounted(){}},n=s,l=(a("a901"),a("2877")),d=Object(l["a"])(n,r,o,!1,null,null,null);e["default"]=d.exports},"0b27":function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"components-box"},[a("Divider",{staticClass:"title",attrs:{orientation:"left"}},[t._v("字段库（向右拖拽）")]),a("div",{staticClass:"box"},[a("draggable",{attrs:{tag:"ul",list:t.baselist,group:{name:"content",pull:"clone"},pull:"clone",sort:!1,disabled:t.draggableStatus},on:{end:function(e){return t.enddrag("base",e)}}},t._l(t.baselist,(function(e,r){return a("li",{key:r},[a("Button",{attrs:{type:"dashed"}},[t._v(t._s(e.name))])],1)})),0)],1)],1)},o=[],i=a("b76a"),s=a.n(i),n={components:{draggable:s.a},data(){return{baselist:this.$store.state.baselist}},computed:{draggableStatus(){return this.$store.state.draggableStatus}},watch:{},methods:{enddrag(t,e){console.log(t,e,this.baselist),"configbox"==e.to.id&&"base"==t&&(this.baselist=JSON.parse(JSON.stringify(this.baselist)),this.baselist[e.oldIndex]["id"]="other_"+parseInt(1e5*Math.random()))}},created(){},mounted(){}},l=n,d=(a("73c4"),a("2877")),c=Object(d["a"])(l,r,o,!1,null,"37912280",null);e["default"]=c.exports},"0c2e":function(t,e,a){var r=a("24fb");e=r(!1),e.push([t.i,".item[data-v-d76c4b2c]{padding-left:10px;background:#efefef;margin-top:5px}.item span[data-v-d76c4b2c]{float:right}.label[data-v-d76c4b2c]{color:var(--labelColor);display:inline-block}",""]),t.exports=e},"0d79":function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("i-form",{attrs:{"label-position":"top"}},[a("FormItem",{attrs:{label:"标识名"}},[a("Input",{attrs:{type:"text",placeholder:"请输入标识名"},model:{value:t.label,callback:function(e){t.label=e},expression:"label"}})],1),a("FormItem",{attrs:{label:"选项设置"}},t._l(t.content,(function(e,r){return a("Row",{key:r,staticStyle:{"margin-bottom":"10px"}},[a("i-col",{attrs:{span:9}},[a("Input",{attrs:{type:"text"},on:{input:t.input},model:{value:t.content[r]["value"],callback:function(e){t.$set(t.content[r],"value",e)},expression:"content[index]['value']"}})],1),a("i-col",{attrs:{span:6,offset:1}},[a("Button",{attrs:{icon:"md-close",size:"small",ghost:"",type:"error"},on:{click:function(e){return t.close(r)}}}),t._v(" "),a("Button",{attrs:{icon:"md-add",ghost:"",size:"small",type:"primary"},on:{click:function(e){return t.add(r)}}})],1)],1)})),1),a("FormItem",{attrs:{label:"提示文字"}},[a("Input",{attrs:{type:"text",placeholder:"请输入提示文字"},model:{value:t.placeholder,callback:function(e){t.placeholder=e},expression:"placeholder"}})],1),a("FormItem",{attrs:{label:"是否必填"}},[a("i-switch",{model:{value:t.required,callback:function(e){t.required=e},expression:"required"}})],1)],1)},o=[],i={components:{},data(){return{content:this.$store.state.curformdata.config.content}},computed:{label:{get(){return this.$store.state.curformdata.config.label},set(t){this.$store.state.curformdata.config.label=t,this.$store.dispatch("UpdateDataConfig")}},vertical:{get(){return this.$store.state.curformdata.config.vertical},set(t){this.$store.state.curformdata.config.vertical=t,this.$store.dispatch("UpdateDataConfig")}},name:{get(){return this.$store.state.curformdata.id},set(t){this.$store.state.curformdata.id=t,this.$store.dispatch("UpdateDataConfig")}},placeholder:{get(){return this.$store.state.curformdata.config.placeholder},set(t){this.$store.state.curformdata.config.placeholder=t,this.$store.dispatch("UpdateDataConfig")}},required:{get(){return this.$store.state.curformdata.config.required},set(t){this.$store.state.curformdata.config.required=t,this.$store.dispatch("UpdateDataConfig")}}},watch:{content(t){this.$store.state.curformdata.config.content=t,this.$store.dispatch("UpdateDataConfig")}},methods:{add(t){this.content.push({key:this.content[t].key,value:this.content[t].value})},close(t){if(1==this.content.length)return"";this.content.splice(t,1)},input(){this.$store.state.curformdata.config.content=this.content,this.$store.dispatch("UpdateDataConfig")}},created(){},mounted(){}},s=i,n=a("2877"),l=Object(n["a"])(s,r,o,!1,null,"30fdd65c",null);e["default"]=l.exports},"110b":function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{staticClass:"box-left-content"},[a(t.componentName,{tag:"component"})],1)])},o=[],i={components:{},data(){return{menuact:0,componentName:"Componentlist"}},computed:{},watch:{},methods:{changemenu(t,e){this.menuact=e,this.componentName=t}},created(){},mounted(){}},s=i,n=(a("5bd7"),a("2877")),l=Object(n["a"])(s,r,o,!1,null,"272345bd",null);e["default"]=l.exports},1335:function(t,e,a){var r=a("24fb");e=r(!1),e.push([t.i,".title[data-v-37912280]{font-size:14px!important}.components-box[data-v-37912280]{overflow-y:scroll}.components-box[data-v-37912280]::-webkit-scrollbar{display:none}.box[data-v-37912280]{padding:10px}.box ul li[data-v-37912280]{margin-right:10px;margin-bottom:10px;display:inline-block}",""]),t.exports=e},"188b":function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("FormItem",{directives:[{name:"show",rawName:"v-show",value:!t.data.config.hidden,expression:"!data.config.hidden"}],attrs:{label:t.data.config.label,id:t.data.id,required:t.data.config.required,"label-width":t.getwidth(t.data.config.labelWidth),error:t.error}},[a("Cascader",{attrs:{data:t.datalist,disabled:t.data.config.readonly,placeholder:t.data.config.placeholder,size:t.formconfig.size},model:{value:t.value,callback:function(e){t.value=e},expression:"value"}})],1)},o=[],i=a("bc0a"),s=i["a"],n=a("2877"),l=Object(n["a"])(s,r,o,!1,null,"5f4384f9",null);e["default"]=l.exports},1980:function(t,e,a){"use strict";a("48bc")},"19b1":function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("i-form",{attrs:{"label-position":"top"}},[a("FormItem",{attrs:{label:"类型"}},[a("RadioGroup",{attrs:{type:"button"},model:{value:t.type,callback:function(e){t.type=e},expression:"type"}},[a("Radio",{attrs:{label:"text"}},[t._v("文本")])],1)],1),a("FormItem",{attrs:{label:"标识名"}},[a("Input",{attrs:{type:"text",placeholder:"请输入标识名"},model:{value:t.label,callback:function(e){t.label=e},expression:"label"}})],1),a("FormItem",{attrs:{label:"提示文字"}},[a("Input",{attrs:{type:"text",placeholder:"请输入提示文字"},model:{value:t.placeholder,callback:function(e){t.placeholder=e},expression:"placeholder"}})],1),a("FormItem",{attrs:{label:"是否必填"}},[a("i-switch",{model:{value:t.required,callback:function(e){t.required=e},expression:"required"}})],1)],1)},o=[],i={components:{},data(){return{regularlist:[{name:"验证汉字",value:"/^[\\u4e00-\\u9fa5]+$/"},{name:"验证身份证号",value:"/(^\\d{15}$)|(^\\d{18}$)|(^\\d{17}(\\d|X|x)$)/"},{name:"验证手机号",value:"/^(1[3584]\\d{9})$/"},{name:"验证电话号码",value:"/^((0\\d{2,3}-\\d{7,8})|(1[3584]\\d{9}))$/"},{name:"验证邮箱",value:"/^\\w+@[a-zA-Z0-9]{2,10}(?:\\.[a-z]{2,4}){1,3}$/"},{name:"只能输入字母",value:"/^[a-zA-Z]+$/"},{name:"只能输入数字",value:"/^\\d*$/"},{name:"只能输入字母",value:"/^[a-zA-Z]+$/"},{name:"是否为数字、字母、下划线",value:"/^\\w+$/"}]}},computed:{label:{get(){return this.$store.state.curformdata.config.label},set(t){this.$store.state.curformdata.config.label=t,this.$store.dispatch("UpdateDataConfig")}},name:{get(){return this.$store.state.curformdata.id},set(t){this.$store.state.curformdata.id=t,this.$store.dispatch("UpdateDataConfig")}},type:{get(){return this.$store.state.curformdata.config.type},set(t){this.$store.state.curformdata.config.type=t,this.$store.dispatch("UpdateDataConfig")}},placeholder:{get(){return this.$store.state.curformdata.config.placeholder},set(t){this.$store.state.curformdata.config.placeholder=t,this.$store.dispatch("UpdateDataConfig")}},required:{get(){return this.$store.state.curformdata.config.required},set(t){this.$store.state.curformdata.config.required=t,this.$store.dispatch("UpdateDataConfig")}}},watch:{},methods:{},created(){},mounted(){}},s=i,n=(a("cb98"),a("2877")),l=Object(n["a"])(s,r,o,!1,null,"391839a0",null);e["default"]=l.exports},"1d0c":function(t,e,a){var r=a("24fb");e=r(!1),e.push([t.i,".ivu-select-dropdown{left:0!important}",""]),t.exports=e},"1d79":function(module,__webpack_exports__,__webpack_require__){"use strict";__webpack_exports__["a"]={props:{data:Object,formconfig:Object},components:{},data(){return{error:"",defaultList:JSON.parse(JSON.stringify(this.data.config.value)),value:JSON.parse(JSON.stringify(this.data.config.value)),viewimg:"",visible:!1,url:window.basefile+"/ajax/upload"}},computed:{},watch:{value(value){this.data.config.required&&(""===value||""!=this.data.config.regular&&!eval(this.data.config.regular).test(value))?this.error=this.data.config.message:(this.error="",document.getElementById(this.data.id).classList.remove("ivu-form-item-error")),this.$emit("backdata",this.data.id,value)}},methods:{getwidth(t){return 0==t||"top"==this.formconfig.position?null:t},exceededSize(){this.$Message.error("文件超出指定大小限制时的钩子")},beforeUpload(){return this.data.config.maxnum<this.value.length+this.defaultList.length||0==this.data.config.maxnum?(this.$Message.error("文件数量已超出最大数"),!1):void 0},success(t){if(!t.code)return this.$Message.error(t.msg),!1;this.value.push({url:t.data.url,name:"文件"+Math.random()})},accepterror(){this.$Message.error("文件格式不正确")},handleView(t){this.visible=!0,this.viewimg=t},handleRemove(t){var e=JSON.parse(JSON.stringify(this.value));e.splice(t,1),this.value=e,this.defaultList=e},uploaderror(t){var e=t.toString();e.search(/401/i)?this.$Message.error("请登陆后操作"):this.$Message.error("网络错误")}},created(){},mounted(){}}},2035:function(module,__webpack_exports__,__webpack_require__){"use strict";__webpack_exports__["a"]={props:{data:Object,formconfig:Object},components:{},data(){return{error:""}},computed:{value:{get(){return this.data.config.value},set(value){this.data.config.required&&(""==value||""!=this.data.config.regular&&!eval(this.data.config.regular).test(value))?this.error=this.data.config.message:(this.error="",document.getElementById(this.data.id).classList.remove("ivu-form-item-error")),this.$emit("backdata",this.data.id,value)}}},watch:{},methods:{getwidth(t){return 0==t||"top"==this.formconfig.position?null:t}},created(){},mounted(){this.data.config.value&&(this.value=this.data.config.value)}}},"2ff1":function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("FormItem",{directives:[{name:"show",rawName:"v-show",value:!t.data.config.hidden,expression:"!data.config.hidden"}],attrs:{label:t.data.config.label,id:t.data.id,required:t.data.config.required,"label-width":t.getwidth(t.data.config.labelWidth),error:t.error}},[a("Input",{attrs:{type:"number",readonly:t.data.config.readonly,placeholder:t.data.config.placeholder,size:t.formconfig.size},model:{value:t.value,callback:function(e){t.value=e},expression:"value"}})],1)},o=[],i=a("74d3"),s=i["a"],n=a("2877"),l=Object(n["a"])(s,r,o,!1,null,"1d35c06a",null);e["default"]=l.exports},"33af":function(t,e,a){var r=a("24fb");e=r(!1),e.push([t.i,"#header{width:100%;height:64px;line-height:64px;font-size:16px;display:block;box-shadow:0 2px 4px rgba(0,0,0,.12),0 0 6px rgba(0,0,0,.04)}.title{font-weight:700}.action{text-align:right}.action button{margin-left:15px}",""]),t.exports=e},3567:function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("FormItem",{directives:[{name:"show",rawName:"v-show",value:!t.data.config.hidden,expression:"!data.config.hidden"}],attrs:{label:t.data.config.label,id:t.data.id,required:t.data.config.required,"label-width":t.getwidth(t.data.config.labelWidth),error:t.error}},[a("RadioGroup",{attrs:{size:t.formconfig.size,type:t.data.config.type,vertical:t.data.config.vertical},model:{value:t.value,callback:function(e){t.value=e},expression:"value"}},t._l(t.data.config.content,(function(e,r){return a("Radio",{key:r,attrs:{label:e.key}},[t._v(" "+t._s(e.value))])})),1)],1)},o=[],i=a("2035"),s=i["a"],n=a("2877"),l=Object(n["a"])(s,r,o,!1,null,"d8b02e8a",null);e["default"]=l.exports},"35f6":function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("FormItem",{directives:[{name:"show",rawName:"v-show",value:!t.data.config.hidden,expression:"!data.config.hidden"}],attrs:{label:t.data.config.label,id:t.data.id,required:t.data.config.required,"label-width":t.getwidth(t.data.config.labelWidth),error:t.error}},[a("CheckboxGroup",{attrs:{size:t.formconfig.size},model:{value:t.value,callback:function(e){t.value=e},expression:"value"}},t._l(t.data.config.content,(function(e,r){return a("Checkbox",{key:r,attrs:{label:e.key}},[a("span",{staticStyle:{"padding-left":"10px"}},[t._v(t._s(e.value))])])})),1)],1)},o=[],i=a("42d4"),s=i["a"],n=a("2877"),l=Object(n["a"])(s,r,o,!1,null,"f30e50cc",null);e["default"]=l.exports},"3a03":function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("i-form",{attrs:{"label-position":"top"}},[a("FormItem",{attrs:{label:"标识名"}},[a("Input",{attrs:{type:"text",placeholder:"请输入标识名"},model:{value:t.label,callback:function(e){t.label=e},expression:"label"}})],1),a("FormItem",{attrs:{label:"文件大小限制(kb)"}},[a("InputNumber",{staticStyle:{width:"100%"},model:{value:t.maxSize,callback:function(e){t.maxSize=e},expression:"maxSize"}})],1),a("FormItem",{attrs:{label:"文件数量"}},[a("InputNumber",{staticStyle:{width:"100%"},model:{value:t.maxnum,callback:function(e){t.maxnum=e},expression:"maxnum"}})],1),a("FormItem",{attrs:{label:"文件格式"}},[a("Input",{attrs:{type:"text",placeholder:"请输入文件格式"},model:{value:t.format,callback:function(e){t.format=e},expression:"format"}})],1),a("FormItem",{attrs:{label:"是否必填"}},[a("i-switch",{model:{value:t.required,callback:function(e){t.required=e},expression:"required"}})],1),t.required?a("FormItem",{attrs:{label:"提示文字"}},[a("Input",{attrs:{type:"text",placeholder:"请输入提示文字"},model:{value:t.placeholder,callback:function(e){t.placeholder=e},expression:"placeholder"}})],1):t._e()],1)},o=[],i={components:{},data(){return{}},computed:{label:{get(){return this.$store.state.curformdata.config.label},set(t){this.$store.state.curformdata.config.label=t,this.$store.dispatch("UpdateDataConfig")}},name:{get(){return this.$store.state.curformdata.id},set(t){this.$store.state.curformdata.id=t,this.$store.dispatch("UpdateDataConfig")}},maxnum:{get(){return this.$store.state.curformdata.config.maxnum},set(t){this.$store.state.curformdata.config.maxnum=t,this.$store.dispatch("UpdateDataConfig")}},maxSize:{get(){return this.$store.state.curformdata.config.maxSize},set(t){this.$store.state.curformdata.config.maxSize=t,this.$store.dispatch("UpdateDataConfig")}},format:{get(){return this.$store.state.curformdata.config.format.join(",")},set(t){this.$store.state.curformdata.config.format=t.split(","),this.$store.dispatch("UpdateDataConfig")}},required:{get(){return this.$store.state.curformdata.config.required},set(t){this.$store.state.curformdata.config.required=t,this.$store.dispatch("UpdateDataConfig")}},placeholder:{get(){return this.$store.state.curformdata.config.placeholder},set(t){this.$store.state.curformdata.config.placeholder=t,this.$store.dispatch("UpdateDataConfig")}}},watch:{},methods:{},created(){},mounted(){}},s=i,n=a("2877"),l=Object(n["a"])(s,r,o,!1,null,"6b5c8d8c",null);e["default"]=l.exports},"3e28":function(t,e,a){var r=a("0c2e");"string"===typeof r&&(r=[[t.i,r,""]]),r.locals&&(t.exports=r.locals);var o=a("499e").default;o("04e0624e",r,!0,{sourceMap:!1,shadowMode:!1})},4279:function(t,e,a){"use strict";a("8ec8")},"42d4":function(module,__webpack_exports__,__webpack_require__){"use strict";__webpack_exports__["a"]={props:{data:Object,formconfig:Object},components:{},data(){return{error:""}},computed:{value:{get(){return this.data.config.value},set(value){this.data.config.required&&(""==value||""!=this.data.config.regular&&!eval(this.data.config.regular).test(value))?this.error=this.data.config.message:(this.error="",document.getElementById(this.data.id).classList.remove("ivu-form-item-error")),this.$emit("backdata",this.data.id,value)}}},watch:{},methods:{getwidth(t){return 0==t||"top"==this.formconfig.position?null:t}},created(){},mounted(){this.data.config.value&&(this.value=this.data.config.value)}}},4548:function(t,e,a){var r={"./Componentlist.vue":"0b27","./FormSet.vue":"f00d","./Header.vue":"0418","./LeftMenu.vue":"110b","./MainConfig.vue":"dd1f","./edit/qdsd-Cascader-edit.vue":"cae5","./edit/qdsd-DatePicker-edit.vue":"faa4","./edit/qdsd-Rate-edit.vue":"701a","./edit/qdsd-TimePicker-edit.vue":"dbec","./edit/qdsd-checkbox-edit.vue":"0d79","./edit/qdsd-input-edit.vue":"19b1","./edit/qdsd-input-number-edit.vue":"7da5","./edit/qdsd-radio-edit.vue":"bbc0","./edit/qdsd-select-edit.vue":"6627","./edit/qdsd-textarea-edit.vue":"7a66","./edit/qdsd-uploadFile-edit.vue":"a9af","./edit/qdsd-uploadImage-edit.vue":"3a03","./form/qdsd-Cascader.vue":"188b","./form/qdsd-DatePicker.vue":"9344","./form/qdsd-Rate.vue":"abb8","./form/qdsd-TimePicker.vue":"495c","./form/qdsd-checkbox.vue":"35f6","./form/qdsd-input-number.vue":"2ff1","./form/qdsd-input.vue":"5638","./form/qdsd-radio.vue":"3567","./form/qdsd-select.vue":"7650","./form/qdsd-textarea.vue":"6f07","./form/qdsd-uploadFile.vue":"4cb8","./form/qdsd-uploadImage.vue":"58a6"};function o(t){var e=i(t);return a(e)}function i(t){if(!a.o(r,t)){var e=new Error("Cannot find module '"+t+"'");throw e.code="MODULE_NOT_FOUND",e}return r[t]}o.keys=function(){return Object.keys(r)},o.resolve=i,t.exports=o,o.id="4548"},"45be":function(t,e,a){"use strict";a("3e28")},"45da":function(t,e,a){"use strict";a("d06b")},"48bc":function(t,e,a){var r=a("c95b");"string"===typeof r&&(r=[[t.i,r,""]]),r.locals&&(t.exports=r.locals);var o=a("499e").default;o("0bad1b04",r,!0,{sourceMap:!1,shadowMode:!1})},"495c":function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("FormItem",{directives:[{name:"show",rawName:"v-show",value:!t.data.config.hidden,expression:"!data.config.hidden"}],attrs:{label:t.data.config.label,id:t.data.id,required:t.data.config.required,"label-width":t.getwidth(t.data.config.labelWidth),error:t.error}},[a("TimePicker",{staticStyle:{width:"100%"},attrs:{format:t.data.config.format,type:t.data.config.type,placeholder:t.data.config.placeholder,readonly:t.data.config.readonly,size:t.formconfig.size,confirm:t.data.config.confirm},model:{value:t.value,callback:function(e){t.value=e},expression:"value"}})],1)},o=[],i=a("6271"),s=i["a"],n=a("2877"),l=Object(n["a"])(s,r,o,!1,null,"4b231228",null);e["default"]=l.exports},"4cb8":function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("FormItem",{directives:[{name:"show",rawName:"v-show",value:!t.data.config.hidden,expression:"!data.config.hidden"}],attrs:{id:t.data.id,required:t.data.config.required,"label-width":t.getwidth(t.data.config.labelWidth),error:t.error}},[a("template",{slot:"label"},[a("div",{staticClass:"label"},[t._v(t._s(t.data.config.label))])]),a("Upload",{ref:"upload",attrs:{action:t.url,name:"file",multiple:"","max-size":t.data.config.maxSize,"on-exceeded-size":t.exceededSize,"before-upload":t.beforeUpload,"default-file-list":t.defaultList,"on-success":t.success,format:t.data.config.format,"on-format-error":t.accepterror,"on-remove":t.remove,"on-error":t.uploaderror,"show-upload-list":!1}},[a("Button",{attrs:{icon:"ios-cloud-upload-outline",type:"primary"}},[t._v("文件上传")])],1),t._l(t.value,(function(e,r){return a("div",{key:r,staticClass:"item"},[t._v(t._s(r+1)+"、"),a("a",{attrs:{href:e.url,target:"_block"}},[t._v(t._s(e.url))]),a("span",[a("Button",{attrs:{type:"text"},on:{click:function(a){return t.handleRemove(e)}}},[a("Icon",{attrs:{type:"ios-close",size:"20",color:"red"}})],1)],1)])}))],2)},o=[],i=a("70d2"),s=i["a"],n=(a("45be"),a("2877")),l=Object(n["a"])(s,r,o,!1,null,"d76c4b2c",null);e["default"]=l.exports},5086:function(t,e,a){var r=a("1d0c");"string"===typeof r&&(r=[[t.i,r,""]]),r.locals&&(t.exports=r.locals);var o=a("499e").default;o("3d63c7b0",r,!0,{sourceMap:!1,shadowMode:!1})},"50d6":function(t,e,a){var r=a("24fb");e=r(!1),e.push([t.i,".circle[data-v-5f28f3b6]{position:absolute;top:-30px;left:50px;z-index:1000}",""]),t.exports=e},5638:function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("FormItem",{directives:[{name:"show",rawName:"v-show",value:!t.data.config.hidden,expression:"!data.config.hidden"}],attrs:{label:t.data.config.label,id:t.data.id,required:t.data.config.required,"label-width":t.getwidth(t.data.config.labelWidth),error:t.error}},[a("Input",{attrs:{type:t.data.config.type,readonly:t.data.config.readonly,placeholder:t.data.config.placeholder,size:t.formconfig.size},model:{value:t.value,callback:function(e){t.value=e},expression:"value"}})],1)},o=[],i=a("b879"),s=i["a"],n=a("2877"),l=Object(n["a"])(s,r,o,!1,null,"807b4a2c",null);e["default"]=l.exports},"56d7":function(t,e,a){"use strict";a.r(e);var r=a("2b0e"),o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{attrs:{id:"app"}},[a("router-view")],1)},i=[],s={data(){return{}},computed:{},watch:{},methods:{},created(){},mounted(){window.basefile||(window.basefile="")}},n=s,l=a("2877"),d=Object(l["a"])(n,o,i,!1,null,null,null),c=d.exports,u=a("8c4f");r["a"].use(u["a"]);const f=[{path:"/",name:"home",component:()=>a.e("chunk-780a07a6").then(a.bind(null,"bb51"))}],p=new u["a"]({routes:f});var h=p,m=a("2f62"),g={updatedata(t,e){for(var a=0;a<t.length;a++)t[a]["id"]==e["id"]?t[a]["config"]=e["config"]:"undefined"!==typeof t[a]["list"]&&(t[a]["list"]=g.updatedata(t[a]["list"],e));return t}};r["a"].use(m["a"]);var b=new m["a"].Store({state:{id:"",baseData:{},draggableStatus:!1,formconfig:{title:"自定义表单",float:"center",position:"top",size:"large",hidden:!0,style:{background:"#ffffff",color:"#17233d",padding:"15px"},showfield:"",showfieldlist:[]},dataconfig:[],curformdata:{},formdata:{},formstatus:!0,baselist:[{id:"other_"+parseInt(1e5*Math.random()),name:"文本框",type:1,component:"textarea",config:{label:"文本框",row:2,placeholder:"请输入",required:!1,is_delete:!0}},{id:"other_"+parseInt(1e5*Math.random()),name:"数字框",type:1,component:"input-number",config:{label:"数字框",placeholder:"请输入",required:!1,is_delete:!0}},{id:"other_"+parseInt(1e5*Math.random()),name:"单选框",type:1,component:"radio",config:{label:"单选框",content:[{key:"",value:""}],placeholder:"请输入",required:!1,is_delete:!0}},{id:"other_"+parseInt(1e5*Math.random()),name:"多选框",type:1,component:"checkbox",config:{label:"多选框",type:null,content:[{key:"",value:""}],placeholder:"请输入",required:!1,is_delete:!0}},{id:"other_"+parseInt(1e5*Math.random()),name:"评分",type:1,component:"Rate",config:{label:"评分",placeholder:"占位符",type:"default",is_delete:!0}},{id:"other_"+parseInt(1e5*Math.random()),name:"单行输入框",type:1,component:"input",config:{type:"text",label:"输入框",placeholder:"请输入",required:!1,is_delete:!0}},{id:"other_"+parseInt(1e5*Math.random()),name:"区域选择框",type:1,component:"Cascader",config:{type:"text",label:"区域选择框",placeholder:"请选择",required:!1,is_delete:!0}},{id:"other_"+parseInt(1e5*Math.random()),name:"地图控件",type:1,component:"map",config:{type:"text",label:"地图控件",placeholder:"请选择",required:!1,is_delete:!0}},{id:"other_"+parseInt(1e5*Math.random()),name:"下拉选择框",type:1,component:"select",config:{label:"下拉选择框",type:null,content:[],placeholder:"请选择",required:!1,multiple:!1,is_delete:!0}},{id:"other_"+parseInt(1e5*Math.random()),name:"时间选择框",type:1,component:"TimePicker",config:{label:"时间选择框",placeholder:"请输入",type:"time",confirm:!0,required:!1,is_delete:!0}},{id:"other_"+parseInt(1e5*Math.random()),name:"日期选择框",type:1,component:"DatePicker",config:{label:"日期选择框",placeholder:"请输入",type:"date",value:"",required:!1,is_delete:!0}},{id:"other_"+parseInt(1e5*Math.random()),name:"图片上传",type:1,component:"uploadImage",config:{label:"图片上传",value:[],required:!1,placeholder:"请上传图片",maxnum:1,is_delete:!0,format:["jpg","jpeg","png"],maxSize:5e3}},{id:"other_"+parseInt(1e5*Math.random()),name:"文件上传",type:1,component:"uploadFile",config:{label:"文件上传",value:[],required:!1,placeholder:"不能为空",maxSize:5e3,maxnum:1,format:["doc"],is_delete:!0}}]},mutations:{},actions:{UpdateDataConfig(t){var e=g.updatedata(t.state.dataconfig,t.state.curformdata);t.state.dataconfig=e}},modules:{}});const v=a("4548");console.log(v,"123456"),v.keys().forEach(t=>{const e=v(t),a=t.split("/").pop().replace(/\.\w+$/,"");r["a"].component(a,e.default||e)});var x=a("19ae"),_=a("d35d"),$=a("e9ce"),w=a("3116"),y=a("ae14"),k=a("dd4b"),q=a("10aa"),I=a("a49b"),C=a("2ca9"),O=a("ea31"),D=a("0347"),j=a("2d66"),U=a("f2d8"),F=a("d842"),z=a("0ce4"),S=a("60de"),M=a("f69c"),E=a("7d1f"),R=a("d3b2"),N=a("6066"),P=a("cf18"),L=a("1d12"),B=a("de10"),T=a("01f8"),J=a("6ead"),W=a("266d"),G=a("093f"),A=a("311a"),K=a("bbbe"),H=a("117e"),V=a("6be2"),Z=a("c57e");a("dcad");let X=[Z["a"],V["a"],H["a"],K["a"],A["a"],G["a"],W["a"],J["a"],T["a"],B["a"],L["a"],P["a"],N["a"],R["a"],E["a"],M["a"],S["a"],z["a"],F["a"],U["a"],j["a"],D["a"],O["a"],C["a"],I["a"],q["a"],k["a"],y["a"],w["a"],$["a"]];X.forEach(t=>[r["a"].component(t.name,t)]),r["a"].component("TimePicker",_["a"]),r["a"].component("DatePicker",x["a"]),r["a"].prototype.$Modal=V["a"],r["a"].prototype.$Message=H["a"];var Y=a("bc3a"),Q=a.n(Y);Q.a.defaults.timeout=5e3,Q.a.defaults.headers.common["Content-Type"]="application/json",Q.a.interceptors.request.use((function(t){const e=sessionStorage.getItem("token");return e&&(t.headers.common["token"]=e),t}),(function(t){return Promise.reject(t.response)})),Q.a.interceptors.response.use((function(t){return t}),(function(t){if(t.response)return Promise.reject(t.response)})),r["a"].prototype.$http=Q.a,r["a"].config.productionTip=!1,new r["a"]({router:h,store:b,render:t=>t(c)}).$mount("#app")},"57ec":function(t,e,a){var r=a("89c4");"string"===typeof r&&(r=[[t.i,r,""]]),r.locals&&(t.exports=r.locals);var o=a("499e").default;o("546fc426",r,!0,{sourceMap:!1,shadowMode:!1})},"58a6":function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("FormItem",{directives:[{name:"show",rawName:"v-show",value:!t.data.config.hidden,expression:"!data.config.hidden"}],attrs:{id:t.data.id,required:t.data.config.required,"label-width":t.getwidth(t.data.config.labelWidth),error:t.error}},[a("template",{slot:"label"},[a("div",{staticClass:"label"},[t._v(t._s(t.data.config.label))])]),t._l(t.value,(function(e,r){return a("div",{key:r,staticClass:"upload-list"},[e.url?[a("img",{attrs:{src:e.url}}),a("div",{staticClass:"upload-list-cover"},[a("Icon",{attrs:{type:"ios-eye-outline"},nativeOn:{click:function(a){return t.handleView(e.url)}}}),a("Icon",{attrs:{type:"ios-trash-outline"},nativeOn:{click:function(e){return t.handleRemove(r)}}})],1)]:t._e()],2)})),a("Upload",{ref:"upload",staticStyle:{display:"inline-block",width:"58px"},attrs:{"show-upload-list":!1,"default-file-list":t.defaultList,"on-success":t.success,format:t.data.config.format,"max-size":t.data.config.maxSize,"on-format-error":t.accepterror,"on-exceeded-size":t.exceededSize,"before-upload":t.beforeUpload,multiple:"",type:"drag",action:t.url,"on-error":t.uploaderror}},[a("div",{staticStyle:{width:"58px",height:"58px","line-height":"58px"}},[a("Icon",{attrs:{type:"ios-camera",size:"20",color:"#17233d"}})],1)]),a("Modal",{attrs:{title:"View Image"},model:{value:t.visible,callback:function(e){t.visible=e},expression:"visible"}},[t.visible?a("img",{staticStyle:{width:"100%"},attrs:{src:t.viewimg}}):t._e()])],2)},o=[],i=a("1d79"),s=i["a"],n=(a("8370"),a("2877")),l=Object(n["a"])(s,r,o,!1,null,"4670d05c",null);e["default"]=l.exports},"5bd7":function(t,e,a){"use strict";a("d1da")},6271:function(module,__webpack_exports__,__webpack_require__){"use strict";__webpack_exports__["a"]={props:{data:Object,formconfig:Object},components:{},data(){return{error:""}},computed:{value:{get(){return this.data.config.value},set(value){this.data.config.required&&(""==value||""!=this.data.config.regular&&!eval(this.data.config.regular).test(value))?this.error=this.data.config.message:(this.error="",document.getElementById(this.data.id).classList.remove("ivu-form-item-error")),this.$emit("backdata",this.data.id,value)}}},watch:{},methods:{getwidth(t){return 0==t||"top"==this.formconfig.position?null:t}},created(){},mounted(){this.data.config.value&&(this.value=this.data.config.value)}}},"642e":function(t,e,a){var r=a("24fb");e=r(!1),e.push([t.i,".upload-list[data-v-4670d05c]{display:inline-block;width:60px;height:60px;text-align:center;line-height:60px;border:1px solid transparent;border-radius:4px;overflow:hidden;background:#fff;position:relative;box-shadow:0 1px 1px rgba(0,0,0,.2);margin-right:4px}.upload-list img[data-v-4670d05c]{width:100%;height:100%}.upload-list-cover[data-v-4670d05c]{display:none;position:absolute;top:0;bottom:0;left:0;right:0;background:rgba(0,0,0,.6)}.upload-list:hover .upload-list-cover[data-v-4670d05c]{display:block}.upload-list-cover i[data-v-4670d05c]{color:#fff;font-size:20px;cursor:pointer;margin:0 2px}.label[data-v-4670d05c]{color:var(--labelColor);display:inline-block}",""]),t.exports=e},6627:function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("i-form",{attrs:{"label-position":"top"}},[a("FormItem",{attrs:{label:"标识名"}},[a("Input",{attrs:{type:"text",placeholder:"请输入标识名"},model:{value:t.label,callback:function(e){t.label=e},expression:"label"}})],1),a("FormItem",{attrs:{label:"选项设置"}},[a("Row",[a("Tree",{staticClass:"demo-tree-render",attrs:{data:t.data5,render:t.renderContent}})],1)],1),a("FormItem",{attrs:{label:"提示文字"}},[a("Input",{attrs:{type:"text",placeholder:"请输入提示文字"},model:{value:t.placeholder,callback:function(e){t.placeholder=e},expression:"placeholder"}})],1),a("FormItem",{attrs:{label:"是否多选"}},[a("i-switch",{model:{value:t.multiple,callback:function(e){t.multiple=e},expression:"multiple"}})],1),a("FormItem",{attrs:{label:"是否必填"}},[a("i-switch",{model:{value:t.required,callback:function(e){t.required=e},expression:"required"}})],1)],1)},o=[],i={components:{},data(){return{content:this.$store.state.curformdata.config.content,data5:[{label:"键值",expand:!0,value:0,render:(t,{root:e,node:a,data:r})=>t("span",{style:{display:"inline-block",width:"100%"}},[t("span",[t("Icon",{props:{type:"ios-folder-outline"},style:{marginRight:"8px"}}),t("span",{style:{marginRight:"10px"}}),t(""+(r.isEdit?"":"Button"),{props:Object.assign({},this.buttonProps,{icon:"ios-add"}),style:{marginRight:"8px"},on:{click:()=>{console.log(r,"数据-------"),this.append(r)}}})])]),children:this.$store.state.curformdata.config.content}],inputContent:"",beforeContent:"",oldName:"",buttonProps:{size:"small"}}},computed:{label:{get(){return this.$store.state.curformdata.config.label},set(t){this.$store.state.curformdata.config.label=t,this.$store.dispatch("UpdateDataConfig")}},name:{get(){return this.$store.state.curformdata.id},set(t){this.$store.state.curformdata.id=t,this.$store.dispatch("UpdateDataConfig")}},type:{get(){return null==this.$store.state.curformdata.config.type?"null":"button"},set(t){"null"==t&&(t=null),this.$store.state.curformdata.config.type=t,this.$store.dispatch("UpdateDataConfig")}},required:{get(){return this.$store.state.curformdata.config.required},set(t){this.$store.state.curformdata.config.required=t,this.$store.dispatch("UpdateDataConfig")}},multiple:{get(){return this.$store.state.curformdata.config.multiple},set(t){this.$store.state.curformdata.config.multiple=t,this.$store.dispatch("UpdateDataConfig")}},placeholder:{get(){return this.$store.state.curformdata.config.placeholder},set(t){console.log(t),this.$store.state.curformdata.config.placeholder=t,this.$store.dispatch("UpdateDataConfig")}}},watch:{data5(t){console.log(t,"123456789")}},methods:{renderContent(t,{root:e,node:a,data:r}){return t("span",{class:"hhhaha",style:{display:"inline-block",lineHeight:"1.6rem",width:"100%",cursor:"pointer"}},[t("span",[t("Icon",{props:{type:"ios-paper-outline"},style:{marginRight:"8px"}}),t(""+(r.isEdit?"":"span"),{style:{marginRight:"10px"},on:{click:t=>{t.stopPropagation(),this.oldName=r.title,this.$set(r,"isEdit",!0)}}},r.label),t(""+(r.isEdit?"input":""),{attrs:{value:""+(r.isEdit?r.label:""),autofocus:"autofocus"},style:{width:"12rem",cursor:"auto"},on:{focus:t=>{this.beforeContent=r.__label,console.log(r,"9999999999999"),this.inputContent=r.__label},change:t=>{this.inputContent=t.target.value,this.confirmTheChange(r)},blur:t=>{}}}),t(""+(r.isEdit?"":"Button"),{props:{type:"primary",size:"small"},style:{marginRight:"8px"},on:{click:t=>{t.stopPropagation(),this.oldName=r.label,this.$set(r,"isEdit",!0)}}},"编辑"),t(""+(r.isEdit?"":"Button"),{props:Object.assign({},this.buttonProps,{icon:"ios-remove"}),style:{marginRight:"8px"},on:{click:()=>{this.remove(e,a,r)}}}),t(""+(r.isEdit?"span":""),{style:{marginLeft:".5rem"}},[t("Button",{props:Object.assign({},this.buttonProps,{icon:"md-checkmark"}),style:{border:0,background:"rgba(0,0,0,0)",fontSize:"1.3rem",outline:"none",lineHeight:1},on:{click:t=>{this.inputContent=r.__label,this.confirmTheChange(r)}}})])])])},confirmTheChange(t){this.inputContent?t.label=this.inputContent:this.inputContent=t.label,this.$set(t,"isEdit",!1),this.input()},CancelChange(t){console.log(this.beforeContent,"之前1"),this.beforeContent&&(console.log(this.beforeContent,"之前2"),t.label=this.beforeContent),this.$set(t,"isEdit",!1),this.input()},append(t){const e=t.children||[];e.push({label:"键值",expand:!0,value:t.value+"-"+t.nodeKey,isEdit:!1}),this.$set(t,"children",e),this.input()},remove(t,e,a){const r=t.find(t=>t===e).parent,o=t.find(t=>t.nodeKey===r).node,i=o.children.indexOf(a);o.children.splice(i,1),this.input()},toUp(t,e,a){const r=t.find(t=>t===e).parent,o=t.find(t=>t.nodeKey===r).node,i=o.children.indexOf(a),s=o.children;0!==i&&s.splice(i-1,1,...s.splice(i,1,s[i-1]))},toDown(t,e,a){const r=t.find(t=>t===e).parent,o=t.find(t=>t.nodeKey===r).node,i=o.children.indexOf(a),s=o.children;i+1!==s.length&&s.splice(i+1,1,...s.splice(i,1,s[i+1]))},add(t){this.content.push({key:this.content[t].key,value:this.content[t].value})},close(t){if(1==this.content.length)return"";this.content.splice(t,1)},input(){this.$store.state.curformdata.config.content=this.data5[0].children,this.$store.dispatch("UpdateDataConfig")}},created(){},mounted(){}},s=i,n=a("2877"),l=Object(n["a"])(s,r,o,!1,null,"3a253d2e",null);e["default"]=l.exports},"6f07":function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("FormItem",{directives:[{name:"show",rawName:"v-show",value:!t.data.config.hidden,expression:"!data.config.hidden"}],attrs:{label:t.data.config.label,id:t.data.id,required:t.data.config.required,"label-width":t.getwidth(t.data.config.labelWidth),error:t.error}},[a("Input",{attrs:{type:"textarea",readonly:t.data.config.readonly,autosize:{minRows:t.data.config.row},placeholder:t.data.config.placeholder,size:t.formconfig.size},model:{value:t.value,callback:function(e){t.value=e},expression:"value"}})],1)},o=[],i=a("d46d"),s=i["a"],n=a("2877"),l=Object(n["a"])(s,r,o,!1,null,"3e38801e",null);e["default"]=l.exports},"701a":function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("i-form",{attrs:{"label-position":"top"}},[a("FormItem",{attrs:{label:"标识名"}},[a("Input",{attrs:{type:"text",placeholder:"请输入标识名"},model:{value:t.label,callback:function(e){t.label=e},expression:"label"}})],1),a("FormItem",{attrs:{label:"提示文字"}},[a("Input",{attrs:{type:"text",placeholder:"请输入提示文字"},model:{value:t.placeholder,callback:function(e){t.placeholder=e},expression:"placeholder"}})],1)],1)},o=[],i={components:{},data(){return{}},computed:{type:{get(){return this.$store.state.curformdata.config.type},set(t){"default"==t&&(this.$store.state.curformdata.config.icon="",this.$store.state.curformdata.config.str=""),this.$store.state.curformdata.config.type=t,this.$store.dispatch("UpdateDataConfig")}},label:{get(){return this.$store.state.curformdata.config.label},set(t){this.$store.state.curformdata.config.label=t,this.$store.dispatch("UpdateDataConfig")}},name:{get(){return this.$store.state.curformdata.id},set(t){this.$store.state.curformdata.id=t,this.$store.dispatch("UpdateDataConfig")}},placeholder:{get(){return this.$store.state.curformdata.config.placeholder},set(t){this.$store.state.curformdata.config.placeholder=t,this.$store.dispatch("UpdateDataConfig")}}},watch:{},methods:{},created(){},mounted(){}},s=i,n=a("2877"),l=Object(n["a"])(s,r,o,!1,null,"1d946b97",null);e["default"]=l.exports},"70d2":function(module,__webpack_exports__,__webpack_require__){"use strict";__webpack_exports__["a"]={props:{data:Object,formconfig:Object},components:{},data(){return{error:"",defaultList:JSON.parse(JSON.stringify(this.data.config.value)),value:[],url:window.basefile+"/ajax/upload"}},computed:{},watch:{value(value){this.data.config.required&&(""===value||""!=this.data.config.regular&&!eval(this.data.config.regular).test(value))?this.error=this.data.config.message:(this.error="",document.getElementById(this.data.id).classList.remove("ivu-form-item-error")),this.$emit("backdata",this.data.id,value)}},methods:{getwidth(t){return 0==t||"top"==this.formconfig.position?null:t},exceededSize(){this.$Message.error("文件超出指定大小限制时的钩子")},beforeUpload(){return this.data.config.maxnum<this.value.length+this.defaultList.length||0==this.data.config.maxnum?(this.$Message.error("文件数量已超出最大数"),!1):void 0},success(t){if(!t.code)return this.$Message.error(t.msg),!1;this.value.push({url:t.data.url,name:"文件"+Math.random()})},accepterror(){this.$Message.error("文件格式不正确")},remove(t,e){for(var a=JSON.parse(JSON.stringify([])),r=0;r<e.length;r++)a.push({url:e[r]["url"],name:e[r]["name"]});this.value=a,this.defaultList=a},uploaderror(t){var e=t.toString();e.search(/401/i)?this.$Message.error("请登陆后操作"):this.$Message.error("网络错误")},handleRemove(t){this.$refs.upload.fileList.splice(this.$refs.upload.fileList.indexOf(t),1);const e=this.$refs.upload.fileList;for(var a=JSON.parse(JSON.stringify([])),r=0;r<e.length;r++)a.push({url:e[r]["url"],name:e[r]["name"]});this.value=a}},created(){},mounted(){this.data.config.value.length>0&&(this.value=this.$refs.upload.fileList)}}},"71dc":function(t,e,a){var r=a("24fb");e=r(!1),e.push([t.i,".circle[data-v-4598efa4]{position:absolute;top:-30px;left:50px;z-index:1000}",""]),t.exports=e},"73c4":function(t,e,a){"use strict";a("c52c")},"74d3":function(module,__webpack_exports__,__webpack_require__){"use strict";__webpack_exports__["a"]={props:{data:Object,formconfig:Object},components:{},data(){return{error:""}},computed:{value:{get(){return this.data.config.value},set(value){this.data.config.required&&(""==value||""!=this.data.config.regular&&!eval(this.data.config.regular).test(value))?this.error=this.data.config.message:(this.error="",document.getElementById(this.data.id).classList.remove("ivu-form-item-error")),this.$emit("backdata",this.data.id,value)}}},watch:{},methods:{getwidth(t){return 0==t||"top"==this.formconfig.position?null:t}},created(){},mounted(){this.data.config.value&&(this.value=parseInt(this.data.config.value))}}},7650:function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("FormItem",{directives:[{name:"show",rawName:"v-show",value:!t.data.config.hidden,expression:"!data.config.hidden"}],attrs:{label:t.data.config.label,id:t.data.id,required:t.data.config.required,"label-width":t.getwidth(t.data.config.labelWidth),error:t.error}},[a("Cascader",{staticStyle:{width:"100%"},attrs:{data:t.data.config.content,placeholder:t.data.config.placeholder}})],1)},o=[],i=a("d227"),s=i["a"],n=a("2877"),l=Object(n["a"])(s,r,o,!1,null,"1f9c4d9a",null);e["default"]=l.exports},"7a66":function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("i-form",{attrs:{"label-position":"top"}},[a("FormItem",{attrs:{label:"标识名"}},[a("Input",{attrs:{type:"text",placeholder:"请输入标识名"},model:{value:t.label,callback:function(e){t.label=e},expression:"label"}})],1),a("FormItem",{attrs:{label:"提示文字"}},[a("Input",{attrs:{type:"text",placeholder:"请输入提示文字"},model:{value:t.placeholder,callback:function(e){t.placeholder=e},expression:"placeholder"}})],1),a("FormItem",{attrs:{label:"行数"}},[a("Input",{attrs:{type:"number"},model:{value:t.row,callback:function(e){t.row=e},expression:"row"}})],1),a("FormItem",{attrs:{label:"是否必填"}},[a("i-switch",{model:{value:t.required,callback:function(e){t.required=e},expression:"required"}})],1)],1)},o=[],i={components:{},data(){return{}},computed:{label:{get(){return this.$store.state.curformdata.config.label},set(t){this.$store.state.curformdata.config.label=t,this.$store.dispatch("UpdateDataConfig")}},name:{get(){return this.$store.state.curformdata.id},set(t){this.$store.state.curformdata.id=t,this.$store.dispatch("UpdateDataConfig")}},placeholder:{get(){return this.$store.state.curformdata.config.placeholder},set(t){this.$store.state.curformdata.config.placeholder=t,this.$store.dispatch("UpdateDataConfig")}},row:{get(){return this.$store.state.curformdata.config.row},set(t){this.$store.state.curformdata.config.row=t,this.$store.dispatch("UpdateDataConfig")}},required:{get(){return this.$store.state.curformdata.config.required},set(t){this.$store.state.curformdata.config.required=t,this.$store.dispatch("UpdateDataConfig")}}},watch:{},methods:{},created(){},mounted(){}},s=i,n=(a("4279"),a("2877")),l=Object(n["a"])(s,r,o,!1,null,"4598efa4",null);e["default"]=l.exports},"7da5":function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("i-form",{attrs:{"label-position":"top"}},[a("FormItem",{attrs:{label:"标识名"}},[a("Input",{attrs:{type:"text",placeholder:"请输入标识名"},model:{value:t.label,callback:function(e){t.label=e},expression:"label"}})],1),a("FormItem",{attrs:{label:"提示文字"}},[a("Input",{attrs:{type:"text",placeholder:"请输入提示文字"},model:{value:t.placeholder,callback:function(e){t.placeholder=e},expression:"placeholder"}})],1),a("FormItem",{attrs:{label:"是否必填"}},[a("i-switch",{model:{value:t.required,callback:function(e){t.required=e},expression:"required"}})],1)],1)},o=[],i={components:{},data(){return{}},computed:{label:{get(){return this.$store.state.curformdata.config.label},set(t){this.$store.state.curformdata.config.label=t,this.$store.dispatch("UpdateDataConfig")}},name:{get(){return this.$store.state.curformdata.id},set(t){this.$store.state.curformdata.id=t,this.$store.dispatch("UpdateDataConfig")}},type:{get(){return this.$store.state.curformdata.config.type},set(t){this.$store.state.curformdata.config.type=t,this.$store.dispatch("UpdateDataConfig")}},placeholder:{get(){return this.$store.state.curformdata.config.placeholder},set(t){this.$store.state.curformdata.config.placeholder=t,this.$store.dispatch("UpdateDataConfig")}},required:{get(){return this.$store.state.curformdata.config.required},set(t){this.$store.state.curformdata.config.required=t,this.$store.dispatch("UpdateDataConfig")}}},watch:{},methods:{},created(){},mounted(){}},s=i,n=a("2877"),l=Object(n["a"])(s,r,o,!1,null,"fa208716",null);e["default"]=l.exports},8370:function(t,e,a){"use strict";a("fc4d")},"89c4":function(t,e,a){var r=a("24fb");e=r(!1),e.push([t.i,".circle[data-v-73896c71]{position:absolute;top:-30px;left:50px;z-index:1000}",""]),t.exports=e},"8ec8":function(t,e,a){var r=a("71dc");"string"===typeof r&&(r=[[t.i,r,""]]),r.locals&&(t.exports=r.locals);var o=a("499e").default;o("27183aef",r,!0,{sourceMap:!1,shadowMode:!1})},"8ef8":function(t,e,a){"use strict";a("5086")},9344:function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("FormItem",{directives:[{name:"show",rawName:"v-show",value:!t.data.config.hidden,expression:"!data.config.hidden"}],attrs:{label:t.data.config.label,id:t.data.id,required:t.data.config.required,"label-width":t.getwidth(t.data.config.labelWidth),error:t.error}},[a("DatePicker",{staticStyle:{width:"100%"},attrs:{format:t.data.config.format,size:t.formconfig.size,type:t.data.config.type,placeholder:t.data.config.placeholder,readonly:t.data.config.readonly,confirm:t.data.config.confirm},model:{value:t.value,callback:function(e){t.value=e},expression:"value"}})],1)},o=[],i={props:{data:Object,formconfig:Object},components:{},data(){return{error:""}},computed:{value:{get(){return this.data.config.value},set(t){this.data.config.required?this.error=this.data.config.message:(this.error="",document.getElementById(this.data.id).classList.remove("ivu-form-item-error")),t&&this.$emit("backdata",this.data.id,this.formatDate(t))}}},watch:{},methods:{getwidth(t){return 0==t||"top"==this.formconfig.position?null:t},formatDate(t){var e=t.getMonth()+1,a=t.getFullYear(),r=t.getDate();return a+"-"+e+"-"+r}},created(){},mounted(){this.data.config.value&&(this.value=this.data.config.value)}},s=i,n=a("2877"),l=Object(n["a"])(s,r,o,!1,null,"39d50970",null);e["default"]=l.exports},9561:function(t,e,a){var r=a("24fb");e=r(!1),e.push([t.i,".circle[data-v-391839a0]{position:absolute;top:-30px;left:50px;z-index:1000}",""]),t.exports=e},9573:function(t,e,a){var r=a("24fb");e=r(!1),e.push([t.i,".box-left-menus[data-v-272345bd]{background:#2c3b41;width:50px;display:block;float:left}.box-left-menus ul[data-v-272345bd]{margin:0;padding:0}.box-left-menus ul li[data-v-272345bd]{width:50px;padding:15px 0;display:inline-block;text-align:center;color:#8f8f8f}.box-left-menus ul .act[data-v-272345bd],.box-left-menus ul li[data-v-272345bd]:hover{color:#fff}.box-left-content[data-v-272345bd]{width:calc(100% - 30px);display:block;overflow:hidden}",""]),t.exports=e},"9efd":function(t,e,a){"use strict";a.d(e,"a",(function(){return r}));var r={design_data:window.basefile+"/qingdong/general/form/getinfo",design_save:window.basefile+"/qingdong/general/form/edit",edit_data:window.basefile+"/qingdong/general/form/edit_data",save_data:window.basefile+"/qingdong/general/form/save_data",region:"/qingdong/general/form/region.json"}},a549:function(t,e,a){var r=a("33af");"string"===typeof r&&(r=[[t.i,r,""]]),r.locals&&(t.exports=r.locals);var o=a("499e").default;o("16dd6efa",r,!0,{sourceMap:!1,shadowMode:!1})},a55d:function(t,e,a){var r=a("9561");"string"===typeof r&&(r=[[t.i,r,""]]),r.locals&&(t.exports=r.locals);var o=a("499e").default;o("9b4e995e",r,!0,{sourceMap:!1,shadowMode:!1})},a901:function(t,e,a){"use strict";a("a549")},a9af:function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("i-form",{attrs:{"label-position":"top"}},[a("FormItem",{attrs:{label:"标识名"}},[a("Input",{attrs:{type:"text",placeholder:"请输入标识名"},model:{value:t.label,callback:function(e){t.label=e},expression:"label"}})],1),a("FormItem",{attrs:{label:"文件大小限制(kb)"}},[a("InputNumber",{staticStyle:{width:"100%"},model:{value:t.maxSize,callback:function(e){t.maxSize=e},expression:"maxSize"}})],1),a("FormItem",{attrs:{label:"文件数量"}},[a("InputNumber",{staticStyle:{width:"100%"},model:{value:t.maxnum,callback:function(e){t.maxnum=e},expression:"maxnum"}})],1),a("FormItem",{attrs:{label:"文件格式"}},[a("Input",{attrs:{type:"text",placeholder:"请输入文件格式"},model:{value:t.format,callback:function(e){t.format=e},expression:"format"}})],1),a("FormItem",{attrs:{label:"是否必填"}},[a("i-switch",{model:{value:t.required,callback:function(e){t.required=e},expression:"required"}})],1),t.required?a("FormItem",{attrs:{label:"提示文字"}},[a("Input",{attrs:{type:"text",placeholder:"请输入提示文字"},model:{value:t.placeholder,callback:function(e){t.placeholder=e},expression:"placeholder"}})],1):t._e()],1)},o=[],i={components:{},data(){return{}},computed:{label:{get(){return this.$store.state.curformdata.config.label},set(t){this.$store.state.curformdata.config.label=t,this.$store.dispatch("UpdateDataConfig")}},name:{get(){return this.$store.state.curformdata.id},set(t){this.$store.state.curformdata.id=t,this.$store.dispatch("UpdateDataConfig")}},maxSize:{get(){return this.$store.state.curformdata.config.maxSize},set(t){this.$store.state.curformdata.config.maxSize=t,this.$store.dispatch("UpdateDataConfig")}},maxnum:{get(){return this.$store.state.curformdata.config.maxnum},set(t){this.$store.state.curformdata.config.maxnum=t,this.$store.dispatch("UpdateDataConfig")}},format:{get(){return this.$store.state.curformdata.config.format.join(",")},set(t){this.$store.state.curformdata.config.format=t.split(","),this.$store.dispatch("UpdateDataConfig")}},required:{get(){return this.$store.state.curformdata.config.required},set(t){this.$store.state.curformdata.config.required=t,this.$store.dispatch("UpdateDataConfig")}},placeholder:{get(){return this.$store.state.curformdata.config.placeholder},set(t){this.$store.state.curformdata.config.placeholder=t,this.$store.dispatch("UpdateDataConfig")}}},watch:{},methods:{},created(){},mounted(){}},s=i,n=a("2877"),l=Object(n["a"])(s,r,o,!1,null,"6a5987b3",null);e["default"]=l.exports},abb8:function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("FormItem",{directives:[{name:"show",rawName:"v-show",value:!t.data.config.hidden,expression:"!data.config.hidden"}],attrs:{label:t.data.config.label,id:t.data.id,"label-width":t.getwidth(t.data.config.labelWidth)}},[a("Rate",{attrs:{"show-text":"","allow-half":t.data.config.allowHalf,disabled:t.data.config.disabled,character:t.data.config.str,icon:t.data.config.icon},model:{value:t.value,callback:function(e){t.value=e},expression:"value"}},[a("span",{staticStyle:{color:"#f5a623"}},[t._v(t._s(t.value)+"分")])])],1)},o=[],i={props:{data:Object,formconfig:Object},components:{},data(){return{error:"",value:this.data.config.value}},computed:{},watch:{value(t){document.getElementById(this.data.id).classList.remove("ivu-form-item-error"),this.$emit("backdata",this.data.id,t)}},methods:{getwidth(t){return 0==t||"top"==this.formconfig.position?null:t}},created(){},mounted(){this.data.config.value&&(this.value=this.data.config.value)}},s=i,n=a("2877"),l=Object(n["a"])(s,r,o,!1,null,"54a8af1a",null);e["default"]=l.exports},b7f8:function(t,e,a){"use strict";a("57ec")},b879:function(module,__webpack_exports__,__webpack_require__){"use strict";__webpack_exports__["a"]={props:{data:Object,formconfig:Object},components:{},data(){return{error:""}},computed:{value:{get(){return this.data.config.value},set(value){this.data.config.required&&(""==value||""!=this.data.config.regular&&!eval(this.data.config.regular).test(value))?this.error=this.data.config.message:(this.error="",document.getElementById(this.data.id).classList.remove("ivu-form-item-error")),this.$emit("backdata",this.data.id,value)}}},watch:{},methods:{getwidth(t){return 0==t||"top"==this.formconfig.position?null:t}},created(){},mounted(){this.data.config.value&&(this.value=this.data.config.value)}}},bbc0:function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("i-form",{attrs:{"label-position":"top"}},[a("FormItem",{attrs:{label:"标识名"}},[a("Input",{attrs:{type:"text",placeholder:"请输入标识名"},model:{value:t.label,callback:function(e){t.label=e},expression:"label"}})],1),a("FormItem",{attrs:{label:"选项设置"}},t._l(t.content,(function(e,r){return a("Row",{key:r,staticStyle:{"margin-bottom":"10px"}},[a("i-col",{attrs:{span:9}},[a("Input",{attrs:{type:"text"},on:{input:t.input},model:{value:t.content[r]["value"],callback:function(e){t.$set(t.content[r],"value",e)},expression:"content[index]['value']"}})],1),a("i-col",{attrs:{span:6,offset:1}},[a("Button",{attrs:{icon:"md-close",size:"small",ghost:"",type:"error"},on:{click:function(e){return t.close(r)}}}),t._v(" "),a("Button",{attrs:{icon:"md-add",ghost:"",size:"small",type:"primary"},on:{click:function(e){return t.add(r)}}})],1)],1)})),1),a("FormItem",{attrs:{label:"提示文字"}},[a("Input",{attrs:{type:"text",placeholder:"请输入提示文字"},model:{value:t.placeholder,callback:function(e){t.placeholder=e},expression:"placeholder"}})],1),a("FormItem",{attrs:{label:"是否必选"}},[a("i-switch",{model:{value:t.required,callback:function(e){t.required=e},expression:"required"}})],1)],1)},o=[],i={components:{},data(){return{content:this.$store.state.curformdata.config.content}},computed:{label:{get(){return this.$store.state.curformdata.config.label},set(t){this.$store.state.curformdata.config.label=t,this.$store.dispatch("UpdateDataConfig")}},name:{get(){return this.$store.state.curformdata.id},set(t){this.$store.state.curformdata.id=t,this.$store.dispatch("UpdateDataConfig")}},type:{get(){return null==this.$store.state.curformdata.config.type?null:"button"},set(t){"null"==t&&(t=null),this.$store.state.curformdata.config.type=t,this.$store.dispatch("UpdateDataConfig")}},placeholder:{get(){return this.$store.state.curformdata.config.placeholder},set(t){this.$store.state.curformdata.config.placeholder=t,this.$store.dispatch("UpdateDataConfig")}},required:{get(){return this.$store.state.curformdata.config.required},set(t){this.$store.state.curformdata.config.required=t,this.$store.dispatch("UpdateDataConfig")}}},watch:{content(t){this.$store.state.curformdata.config.content=t,this.$store.dispatch("UpdateDataConfig")}},methods:{add(t){this.content.push({key:this.content[t].key,value:this.content[t].value})},close(t){if(1==this.content.length)return"";this.content.splice(t,1)},input(){this.$store.state.curformdata.config.content=this.content,this.$store.dispatch("UpdateDataConfig")}},created(){},mounted(){}},s=i,n=(a("b7f8"),a("2877")),l=Object(n["a"])(s,r,o,!1,null,"73896c71",null);e["default"]=l.exports},bc0a:function(module,__webpack_exports__,__webpack_require__){"use strict";var _utils_service_js__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("9efd");__webpack_exports__["a"]={props:{data:Object,formconfig:Object},components:{},data(){return{error:"",datalist:[],value:[]}},computed:{},watch:{value(value){this.data.config.required&&(""==value||""!=this.data.config.regular&&!eval(this.data.config.regular).test(value))?this.error=this.data.config.message:(this.error="",document.getElementById(this.data.id).classList.remove("ivu-form-item-error")),this.$emit("backdata",this.data.id,value)}},methods:{getwidth(t){return 0==t||"top"==this.formconfig.position?null:t},getregion(){var t=this;t.$http.post(_utils_service_js__WEBPACK_IMPORTED_MODULE_0__["a"].region).then((function(e){t.datalist=e.data})).catch(()=>{t.$Message.error("地区数据获取错误")})}},created(){},mounted(){if(this.data.config.value){for(var t=0;t<this.data.config.value.length;t++)this.data.config.value[t]=parseInt(this.data.config.value[t]);this.value=this.data.config.value}}}},c52c:function(t,e,a){var r=a("1335");"string"===typeof r&&(r=[[t.i,r,""]]),r.locals&&(t.exports=r.locals);var o=a("499e").default;o("d9d11726",r,!0,{sourceMap:!1,shadowMode:!1})},c95b:function(t,e,a){var r=a("24fb");e=r(!1),e.push([t.i,".box[data-v-29400a94]{height:calc(100vh - 166px);overflow:scroll;background:#fff;padding:20px}.box[data-v-29400a94],.configbox[data-v-29400a94]{width:100%;display:block}.configbox[data-v-29400a94]{min-height:500px}.list[data-v-29400a94]{border:1px dashed #ccc;box-sizing:border-box;padding:10px}.editact[data-v-29400a94],.editact[data-v-29400a94]:hover,.list[data-v-29400a94]:hover{border:1px dashed #2d8cf0;box-sizing:border-box}.editact[data-v-29400a94],.editact[data-v-29400a94]:hover{position:relative}.del[data-v-29400a94]{position:absolute;right:0;bottom:0}.box[data-v-29400a94]::-webkit-scrollbar{display:none}.hidden[data-v-29400a94]{line-height:50px;width:100%;text-align:center;color:#8f8f8f}",""]),t.exports=e},cae5:function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("i-form",{attrs:{"label-position":"top"}},[a("FormItem",{attrs:{label:"标识名"}},[a("Input",{attrs:{type:"text",placeholder:"请输入标识名"},model:{value:t.label,callback:function(e){t.label=e},expression:"label"}})],1),a("FormItem",{attrs:{label:"提示文字"}},[a("Input",{attrs:{type:"text",placeholder:"请输入提示文字"},model:{value:t.placeholder,callback:function(e){t.placeholder=e},expression:"placeholder"}})],1),a("FormItem",{attrs:{label:"是否必填"}},[a("i-switch",{model:{value:t.required,callback:function(e){t.required=e},expression:"required"}})],1)],1)},o=[],i=a("9efd"),s={components:{},data(){return{datalist:[]}},computed:{label:{get(){return this.$store.state.curformdata.config.label},set(t){this.$store.state.curformdata.config.label=t,this.$store.dispatch("UpdateDataConfig")}},name:{get(){return this.$store.state.curformdata.id},set(t){this.$store.state.curformdata.id=t,this.$store.dispatch("UpdateDataConfig")}},placeholder:{get(){return this.$store.state.curformdata.config.placeholder},set(t){this.$store.state.curformdata.config.placeholder=t,this.$store.dispatch("UpdateDataConfig")}},required:{get(){return this.$store.state.curformdata.config.required},set(t){this.$store.state.curformdata.config.required=t,this.$store.dispatch("UpdateDataConfig")}}},watch:{},methods:{getregion(){var t=this;t.$http.post(i["a"].region).then((function(e){t.datalist=e.data})).catch(()=>{t.$Message.error("地区数据获取错误")})}},created(){},mounted(){}},n=s,l=a("2877"),d=Object(l["a"])(n,r,o,!1,null,"1a7e5e44",null);e["default"]=d.exports},cb98:function(t,e,a){"use strict";a("a55d")},d06b:function(t,e,a){var r=a("50d6");"string"===typeof r&&(r=[[t.i,r,""]]),r.locals&&(t.exports=r.locals);var o=a("499e").default;o("02907cb3",r,!0,{sourceMap:!1,shadowMode:!1})},d1da:function(t,e,a){var r=a("9573");"string"===typeof r&&(r=[[t.i,r,""]]),r.locals&&(t.exports=r.locals);var o=a("499e").default;o("f748cf1c",r,!0,{sourceMap:!1,shadowMode:!1})},d227:function(module,__webpack_exports__,__webpack_require__){"use strict";__webpack_exports__["a"]={props:{data:Object,formconfig:Object},components:{},data(){return{error:""}},computed:{value:{get(){return this.data.config.value},set(value){this.data.config.required&&(""==value||""!=this.data.config.regular&&!eval(this.data.config.regular).test(value))?this.error=this.data.config.message:(this.error="",document.getElementById(this.data.id).classList.remove("ivu-form-item-error")),this.$emit("backdata",this.data.id,value)}}},watch:{},methods:{getwidth(t){return 0==t||"top"==this.formconfig.position?null:t}},created(){},mounted(){this.data.config.value&&(this.value=this.data.config.value)}}},d46d:function(module,__webpack_exports__,__webpack_require__){"use strict";__webpack_exports__["a"]={props:{data:Object,formconfig:Object},components:{},data(){return{error:""}},computed:{value:{get(){return this.data.config.value},set(value){this.data.config.required&&(""==value||""!=this.data.config.regular&&!eval(this.data.config.regular).test(value))?this.error=this.data.config.message:(this.error="",document.getElementById(this.data.id).classList.remove("ivu-form-item-error")),this.$emit("backdata",this.data.id,value)}}},watch:{},methods:{getwidth(t){return 0==t||"top"==this.formconfig.position?null:t}},created(){},mounted(){this.data.config.value&&(this.value=this.data.config.value)}}},dbec:function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("i-form",{attrs:{"label-position":"top"}},[a("FormItem",{attrs:{label:"标识名"}},[a("Input",{attrs:{type:"text",placeholder:"请输入标识名"},model:{value:t.label,callback:function(e){t.label=e},expression:"label"}})],1),a("FormItem",{attrs:{label:"提示文字"}},[a("Input",{attrs:{type:"text",placeholder:"请输入提示文字"},model:{value:t.placeholder,callback:function(e){t.placeholder=e},expression:"placeholder"}})],1),a("FormItem",{attrs:{label:"是否必填"}},[a("i-switch",{model:{value:t.required,callback:function(e){t.required=e},expression:"required"}})],1)],1)},o=[],i={components:{},data(){return{}},computed:{label:{get(){return this.$store.state.curformdata.config.label},set(t){this.$store.state.curformdata.config.label=t,this.$store.dispatch("UpdateDataConfig")}},name:{get(){return this.$store.state.curformdata.id},set(t){this.$store.state.curformdata.id=t,this.$store.dispatch("UpdateDataConfig")}},type:{get(){return this.$store.state.curformdata.config.type},set(t){this.$store.state.curformdata.config.type=t,this.$store.dispatch("UpdateDataConfig")}},format:{get(){return this.$store.state.curformdata.config.format},set(t){this.$store.state.curformdata.config.format=t,this.$store.dispatch("UpdateDataConfig")}},placeholder:{get(){return this.$store.state.curformdata.config.placeholder},set(t){this.$store.state.curformdata.config.placeholder=t,this.$store.dispatch("UpdateDataConfig")}},required:{get(){return this.$store.state.curformdata.config.required},set(t){this.$store.state.curformdata.config.required=t,this.$store.dispatch("UpdateDataConfig")}}},watch:{},methods:{},created(){},mounted(){}},s=i,n=a("2877"),l=Object(n["a"])(s,r,o,!1,null,"2f95015e",null);e["default"]=l.exports},dd1f:function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"box"},[a("i-form",{ref:"formInline",attrs:{"label-position":t.formconfig.position}},[a("draggable",{staticClass:"configbox",attrs:{id:"configbox",tag:"div",group:"content",list:t.config},on:{start:t.startdrag,end:t.enddrag}},t._l(t.config,(function(e,r){return a("div",{key:r,staticClass:"list",class:t.editact==e.id?"editact":"",on:{click:function(a){return t.edititem(e.id)}}},[a("qdsd-"+e.component,{tag:"component",attrs:{data:e,formconfig:t.formconfig,ispreview:!0},on:{backdata:t.backdata}}),t.editact==e.id&&e.config.is_delete?a("div",{staticClass:"del"},[a("Button",{attrs:{type:"text",size:"small"},on:{click:function(a){return t.del(e.id)}}},[a("Icon",{attrs:{type:"ios-trash",size:"20",color:"#57a3f3"}})],1)],1):t._e()],1)})),0)],1)],1)},o=[],i=a("b76a"),s=a.n(i),n={components:{draggable:s.a},data(){return{editact:0,formdata:{}}},computed:{theme(){return"Theme"+this.$store.state.formconfig.themeSelected},config(){return this.$store.state.dataconfig},formconfig(){return this.$store.state.formconfig}},watch:{},methods:{startdrag(){this.$store.state.draggableStatus=!0},enddrag(){this.$store.state.draggableStatus=!1},edititem(t){this.editact=t,this.searchid(t,this.config)},searchid(t,e){for(var a=0;a<e.length;a++)e[a]["id"]==t?this.$store.state.curformdata=e[a]:"undefined"!==typeof e[a]["list"]&&this.searchid(t,e[a]["list"])},backdata(t,e){this.formdata[t]=e},del(t){this.searchid_del(t,this.config)},searchid_del(t,e){for(var a=0;a<e.length;a++)e[a]["id"]==t?e.splice(a,1):"undefined"!==typeof e[a]["list"]&&(e[a]["list"]=this.searchid_del(t,e[a]["list"]));return e}},created(){},mounted(){}},l=n,d=(a("1980"),a("2877")),c=Object(d["a"])(l,r,o,!1,null,"29400a94",null);e["default"]=c.exports},f00d:function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("i-form",{attrs:{"label-position":"top"}},[a("FormItem",{attrs:{label:"标题隐藏"}},[a("i-switch",{model:{value:t.hidden,callback:function(e){t.hidden=e},expression:"hidden"}})],1),a("FormItem",{attrs:{label:"表单标题"}},[a("Input",{attrs:{placeholder:"表单标题"},model:{value:t.title,callback:function(e){t.title=e},expression:"title"}})],1),a("FormItem",{attrs:{label:"标题对齐方式"}},[a("RadioGroup",{attrs:{type:"button"},model:{value:t.float,callback:function(e){t.float=e},expression:"float"}},[a("Radio",{attrs:{label:"left"}},[t._v("左对齐")]),a("Radio",{attrs:{label:"center"}},[t._v("居中")]),a("Radio",{attrs:{label:"right"}},[t._v("右对齐")])],1)],1),a("FormItem",{attrs:{label:"label对齐方式"}},[a("RadioGroup",{attrs:{type:"button"},model:{value:t.position,callback:function(e){t.position=e},expression:"position"}},[a("Radio",{attrs:{label:"left"}},[t._v("左对齐")]),a("Radio",{attrs:{label:"top"}},[t._v("顶部")]),a("Radio",{attrs:{label:"right"}},[t._v("右对齐")])],1)],1),a("FormItem",{attrs:{label:"输入框尺寸"}},[a("RadioGroup",{attrs:{type:"button"},model:{value:t.size,callback:function(e){t.size=e},expression:"size"}},[a("Radio",{attrs:{label:"large"}},[t._v("大")]),a("Radio",{attrs:{label:"default"}},[t._v("中")]),a("Radio",{attrs:{label:"small"}},[t._v("小")])],1)],1),a("FormItem",{attrs:{label:"表单缩进"}},[a("Input",{attrs:{placeholder:"表单缩进"},model:{value:t.padding,callback:function(e){t.padding=e},expression:"padding"}})],1),1==t.formconfig.themeSelected?a("FormItem",{attrs:{label:"标题颜色"}},[a("ColorPicker",{attrs:{recommend:""},model:{value:t.color,callback:function(e){t.color=e},expression:"color"}})],1):t._e(),a("FormItem",{attrs:{label:"背景颜色"}},[a("ColorPicker",{attrs:{recommend:""},model:{value:t.background,callback:function(e){t.background=e},expression:"background"}})],1),a("FormItem",{attrs:{label:"显示字段"}},[a("RadioGroup",{model:{value:t.showfield,callback:function(e){t.showfield=e},expression:"showfield"}},t._l(t.showfieldlist,(function(e,r){return a("Radio",{key:r,attrs:{label:e.id}},[t._v(t._s(e.name))])})),1)],1)],1)},o=[],i={components:{},data(){return{}},computed:{showfieldlist(){return this.getlist(JSON.parse(JSON.stringify(this.$store.state.dataconfig)))},showfield:{get(){return this.deletefield(this.$store.state.formconfig.showfield)},set(t){this.$store.state.formconfig.showfield=t}},formconfig(){return this.$store.state.formconfig},title:{get(){return this.$store.state.formconfig.title},set(t){this.$store.state.formconfig.title=t}},float:{get(){return this.$store.state.formconfig.float},set(t){this.$store.state.formconfig.float=t}},position:{get(){return this.$store.state.formconfig.position},set(t){this.$store.state.formconfig.position=t}},size:{get(){return this.$store.state.formconfig.size?this.$store.state.formconfig.size:""},set(t){this.$store.state.formconfig.size=t}},hidden:{get(){return this.$store.state.formconfig.hidden},set(t){this.$store.state.formconfig.hidden=t}},background:{get(){return this.$store.state.formconfig.style.background},set(t){this.$store.state.formconfig.style.background=t}},color:{get(){return this.$store.state.formconfig.style.color},set(t){this.$store.state.formconfig.style.color=t}},padding:{get(){return this.$store.state.formconfig.style.padding},set(t){this.$store.state.formconfig.style.padding=t}}},watch:{showfieldlist(t){this.$store.state.formconfig.showfieldlist=t}},methods:{getlist(t){for(var e=[],a=0;a<t.length;a++)t[a]["list"]?e=e.concat(this.getlist(t[a]["list"])):1==t[a]["type"]&&e.push({id:t[a]["id"],name:t[a]["config"]["label"]});return e},deletefield(t){for(var e=this.showfieldlist,a=!1,r=0;r<t.length;r++){for(var o=0;o<e.length;o++)t[r]==e[o]["id"]&&(a=!0);a||(t.splice(r,1),a=!1)}return this.$store.state.formconfig.showfield=t,t}},created(){},mounted(){}},s=i,n=(a("8ef8"),a("2877")),l=Object(n["a"])(s,r,o,!1,null,null,null);e["default"]=l.exports},faa4:function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("i-form",{attrs:{"label-position":"top"}},[a("FormItem",{attrs:{label:"类型"}},[a("RadioGroup",{model:{value:t.type,callback:function(e){t.type=e},expression:"type"}},[a("Radio",{attrs:{label:"date"}},[t._v("日期")])],1)],1),a("FormItem",{attrs:{label:"标识名"}},[a("Input",{attrs:{type:"text",placeholder:"请输入标识名"},model:{value:t.label,callback:function(e){t.label=e},expression:"label"}})],1),a("FormItem",{attrs:{label:"提示文字"}},[a("Input",{attrs:{type:"text",placeholder:"请输入提示文字"},model:{value:t.placeholder,callback:function(e){t.placeholder=e},expression:"placeholder"}})],1),a("FormItem",{attrs:{label:"是否必填"}},[a("i-switch",{model:{value:t.required,callback:function(e){t.required=e},expression:"required"}})],1)],1)},o=[],i={components:{},data(){return{}},computed:{label:{get(){return this.$store.state.curformdata.config.label},set(t){this.$store.state.curformdata.config.label=t,this.$store.dispatch("UpdateDataConfig")}},name:{get(){return this.$store.state.curformdata.id},set(t){this.$store.state.curformdata.id=t,this.$store.dispatch("UpdateDataConfig")}},type:{get(){return this.$store.state.curformdata.config.type},set(t){this.$store.state.curformdata.config.type=t,this.$store.dispatch("UpdateDataConfig")}},placeholder:{get(){return this.$store.state.curformdata.config.placeholder},set(t){this.$store.state.curformdata.config.placeholder=t,this.$store.dispatch("UpdateDataConfig")}},required:{get(){return this.$store.state.curformdata.config.required},set(t){this.$store.state.curformdata.config.required=t,this.$store.dispatch("UpdateDataConfig")}}},watch:{},methods:{},created(){},mounted(){}},s=i,n=(a("45da"),a("2877")),l=Object(n["a"])(s,r,o,!1,null,"5f28f3b6",null);e["default"]=l.exports},fc4d:function(t,e,a){var r=a("642e");"string"===typeof r&&(r=[[t.i,r,""]]),r.locals&&(t.exports=r.locals);var o=a("499e").default;o("585dd916",r,!0,{sourceMap:!1,shadowMode:!1})}});
//# sourceMappingURL=app.9f9469bd.js.map