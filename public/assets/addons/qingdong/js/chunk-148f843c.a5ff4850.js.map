{"version": 3, "sources": ["webpack:///./src/views/Home.vue?43e2", "webpack:///./src/views/Home.vue?5a23", "webpack:///./src/views/Home.vue?567d", "webpack:///src/views/Home.vue", "webpack:///./src/views/Home.vue?2b8c", "webpack:///./src/views/Home.vue", "webpack:///./src/views/Home.vue?c911"], "names": ["content", "module", "i", "locals", "exports", "add", "default", "render", "_vm", "this", "_h", "$createElement", "_c", "_self", "attrs", "staticClass", "curformdata", "component", "tag", "_e", "_v", "baseData", "type", "ref", "model", "value", "callback", "$$v", "isAddContact", "expression", "slot", "staticRenderFns", "___CSS_LOADER_API_IMPORT___", "push"], "mappings": "qGAGA,IAAIA,EAAU,EAAQ,QACA,kBAAZA,IAAsBA,EAAU,CAAC,CAACC,EAAOC,EAAIF,EAAS,MAC7DA,EAAQG,SAAQF,EAAOG,QAAUJ,EAAQG,QAE5C,IAAIE,EAAM,EAAQ,QAA+DC,QACpED,EAAI,WAAYL,GAAS,EAAM,CAAC,WAAY,EAAM,YAAa,K,kCCR5E,W,yCCAA,IAAIO,EAAS,WAAa,IAAIC,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,MAAM,CAACA,EAAG,UAAUA,EAAG,MAAM,CAACE,MAAM,CAAC,KAAO,OAAO,QAAU,kBAAkB,CAACF,EAAG,QAAQ,CAACE,MAAM,CAAC,GAAK,GAAG,GAAK,GAAG,GAAK,EAAE,GAAK,EAAE,aAAa,aAAa,CAACF,EAAG,aAAa,GAAGA,EAAG,QAAQ,CAACE,MAAM,CAAC,GAAK,GAAG,GAAK,GAAG,GAAK,GAAG,GAAK,GAAG,aAAa,eAAe,CAACF,EAAG,eAAe,GAAGA,EAAG,QAAQ,CAACE,MAAM,CAAC,GAAK,GAAG,GAAK,GAAG,GAAK,EAAE,GAAK,EAAE,aAAa,cAAc,CAACF,EAAG,OAAO,CAACA,EAAG,UAAU,CAACE,MAAM,CAAC,MAAQ,SAAS,CAACF,EAAG,MAAM,CAACG,YAAY,UAAU,CAAEP,EAAU,OAAEI,EAAGJ,EAAIQ,YAAYC,UACjhB,QACAT,EAAIQ,YAAYC,UAChB,QACA,GAAG,CAACC,IAAI,cAAcV,EAAIW,KAAOX,EAAIQ,YAAYC,UAA6DT,EAAIW,KAAtDP,EAAG,KAAK,CAACG,YAAY,QAAQ,CAACP,EAAIY,GAAG,cAAuB,KAA2B,YAArBZ,EAAIa,SAASC,KAAoBV,EAAG,UAAU,CAACE,MAAM,CAAC,MAAQ,SAAS,CAACF,EAAG,MAAM,CAACG,YAAY,UAAU,CAACH,EAAG,SAAS,CAACW,IAAI,aAAaT,MAAM,CAAC,OAAS,KAAK,CAACF,EAAG,YAAY,CAACE,MAAM,CAAC,MAAQ,YAAY,CAACF,EAAG,WAAW,CAACY,MAAM,CAACC,MAAOjB,EAAgB,aAAEkB,SAAS,SAAUC,GAAMnB,EAAIoB,aAAaD,GAAKE,WAAW,iBAAiB,CAACjB,EAAG,OAAO,CAACE,MAAM,CAAC,KAAO,QAAQgB,KAAK,QAAQ,CAACtB,EAAIY,GAAG,OAAOR,EAAG,OAAO,CAACE,MAAM,CAAC,KAAO,SAASgB,KAAK,SAAS,CAACtB,EAAIY,GAAG,UAAU,IAAI,IAAI,KAAKZ,EAAIW,MAAM,IAAI,IAAI,IAAI,IACtoBY,EAAkB,G,YC2CtB,GACE,OAEE,MAAJ,CACM,WAAN,GACM,GAAN,EACM,QAAN,IAIE,SAAF,CACI,cACE,OAAN,+BAEI,WACE,OAAN,4BAEI,aAAJ,CACM,MACE,OAAR,gCAEM,IAAN,GACQ,KAAR,+BAKE,MAAF,CACI,cACE,KAAN,UACM,KAAN,sBACQ,KAAR,eAKE,QAAF,CAEI,YACE,KAAN,6BAEI,SACE,IAAN,OACM,EAAN,MACA,yBAAQ,GAAR,KAAQ,MAAR,IACA,SACQ,EAAR,OACA,WACA,0BACY,EAAZ,mCACA,aACA,MAEU,EAAV,8BAEU,EAAV,6BAGA,WACQ,EAAR,2BAIE,kBAAF,GACA,sBACM,KAAN,cACM,KAAN,2BACM,KAAN,WAIE,YAEA,UACE,KAAJ,wBACI,KAAJ,qCACI,KAAJ,WC5H8U,I,wBCQ1Ud,EAAY,eACd,EACAV,EACAwB,GACA,EACA,KACA,WACA,MAIa,aAAAd,E,8BClBf,IAAIe,EAA8B,EAAQ,QAC1C5B,EAAU4B,GAA4B,GAEtC5B,EAAQ6B,KAAK,CAAChC,EAAOC,EAAI,ucAAwc,KAEjeD,EAAOG,QAAUA", "file": "assets/addons/qingdong/js/chunk-148f843c.a5ff4850.js", "sourcesContent": ["// style-loader: Adds some css to the DOM by adding a <style> tag\n\n// load the styles\nvar content = require(\"!!../../node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-2!../../node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-3!../../node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Home.vue?vue&type=style&index=0&id=13347935&lang=less&scoped=true&\");\nif(typeof content === 'string') content = [[module.id, content, '']];\nif(content.locals) module.exports = content.locals;\n// add the styles to the DOM\nvar add = require(\"!../../node_modules/vue-style-loader/lib/addStylesClient.js\").default\nvar update = add(\"539e7f62\", content, true, {\"sourceMap\":false,\"shadowMode\":false});", "export * from \"-!../../node_modules/vue-style-loader/index.js??ref--10-oneOf-1-0!../../node_modules/css-loader/dist/cjs.js??ref--10-oneOf-1-1!../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-2!../../node_modules/postcss-loader/src/index.js??ref--10-oneOf-1-3!../../node_modules/less-loader/dist/cjs.js??ref--10-oneOf-1-4!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Home.vue?vue&type=style&index=0&id=13347935&lang=less&scoped=true&\"", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',[_c('Header'),_c('Row',{attrs:{\"type\":\"flex\",\"justify\":\"space-between\"}},[_c('i-col',{attrs:{\"xs\":24,\"sm\":24,\"md\":7,\"lg\":5,\"class-name\":\"box-left\"}},[_c('LeftMenu')],1),_c('i-col',{attrs:{\"xs\":24,\"sm\":24,\"md\":10,\"lg\":14,\"class-name\":\"box-center\"}},[_c('MainConfig')],1),_c('i-col',{attrs:{\"xs\":24,\"sm\":24,\"md\":7,\"lg\":5,\"class-name\":\"box-right\"}},[_c('Tabs',[_c('TabPane',{attrs:{\"label\":\"字段属性\"}},[_c('div',{staticClass:\"tabbox\"},[(_vm.reload)?_c(_vm.curformdata.component\n                                    ? 'qdsd-' +\n                                      _vm.curformdata.component +\n                                      '-edit'\n                                    : '',{tag:\"component\"}):_vm._e(),(!_vm.curformdata.component)?_c('h2',{staticClass:\"tips\"},[_vm._v(\" 请选择控件 \")]):_vm._e()],1)]),(_vm.baseData.type == 'customer')?_c('TabPane',{attrs:{\"label\":\"基础设置\"}},[_c('div',{staticClass:\"tabbox\"},[_c('i-form',{ref:\"formInline\",attrs:{\"inline\":\"\"}},[_c('Form-item',{attrs:{\"label\":\"是否添加联系人\"}},[_c('i-switch',{model:{value:(_vm.isAddContact),callback:function ($$v) {_vm.isAddContact=$$v},expression:\"isAddContact\"}},[_c('span',{attrs:{\"slot\":\"open\"},slot:\"open\"},[_vm._v(\"是\")]),_c('span',{attrs:{\"slot\":\"close\"},slot:\"close\"},[_vm._v(\"否\")])])],1)],1)],1)]):_vm._e()],1)],1)],1)],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n    <div>\r\n        <Header></Header>\r\n        <Row type=\"flex\" justify=\"space-between\">\r\n            <i-col :xs=\"24\" :sm=\"24\" :md=\"7\" :lg=\"5\" class-name=\"box-left\">\r\n                <LeftMenu></LeftMenu>\r\n            </i-col>\r\n            <i-col :xs=\"24\" :sm=\"24\" :md=\"10\" :lg=\"14\" class-name=\"box-center\">\r\n                <MainConfig></MainConfig>\r\n            </i-col>\r\n            <i-col :xs=\"24\" :sm=\"24\" :md=\"7\" :lg=\"5\" class-name=\"box-right\">\r\n                <Tabs>\r\n                    <TabPane label=\"字段属性\">\r\n                        <div class=\"tabbox\">\r\n                            <component\r\n                                :is=\"\r\n                                    curformdata.component\r\n                                        ? 'qdsd-' +\r\n                                          curformdata.component +\r\n                                          '-edit'\r\n                                        : ''\r\n                                \"\r\n                                v-if=\"reload\"\r\n                            ></component>\r\n                            <h2 v-if=\"!curformdata.component\" class=\"tips\">\r\n                                请选择控件\r\n                            </h2>\r\n                        </div>\r\n                    </TabPane>\r\n                    <TabPane label=\"基础设置\" v-if=\"baseData.type == 'customer'\">\r\n                        <div class=\"tabbox\">\r\n                            <i-form ref=\"formInline\" inline>\r\n                                <Form-item label=\"是否添加联系人\">\r\n                                    <i-switch v-model=\"isAddContact\">\r\n                                        <span slot=\"open\">是</span>\r\n                                        <span slot=\"close\">否</span>\r\n                                    </i-switch>\r\n                                </Form-item>\r\n                            </i-form>\r\n                        </div>\r\n                    </TabPane>\r\n                </Tabs>\r\n            </i-col>\r\n        </Row>\r\n    </div>\r\n</template>\r\n<script>\r\nimport { service } from \"../utils/service.js\";\r\nexport default {\r\n    data() {\r\n        //这里存放数据\r\n        return {\r\n            deletelist: [],\r\n            id: 7,\r\n            reload: true,\r\n        };\r\n    },\r\n    //监听属性 类似于data概念\r\n    computed: {\r\n        curformdata() {\r\n            return this.$store.state.curformdata;\r\n        },\r\n        baseData() {\r\n            return this.$store.state.baseData;\r\n        },\r\n        isAddContact:{\r\n            get() {\r\n                return this.$store.state.isAddContact\r\n            },\r\n            set(value) {\r\n                this.$store.state.isAddContact = value\r\n            }\r\n        }\r\n    },\r\n    //监控data中的数据变化\r\n    watch: {\r\n        curformdata() {\r\n            this.reload = false;\r\n            this.$nextTick(function () {\r\n                this.reload = true;\r\n            });\r\n        }\r\n    },\r\n    //方法集合,\r\n    methods: {\r\n        //删除后回调\r\n        adddelete() {\r\n            this.$store.state.curformdata = {};\r\n        },\r\n        getRow() {\r\n            var that = this;\r\n            that.$http\r\n                .post(service.design_data, { id: that.id, _ajax: 1 })\r\n                .then((response) => {\r\n                    response = response.data;\r\n                    if (response.code == 1) {\r\n                        if (JSON.parse(response.data.data)) {\r\n                            that.$store.state.dataconfig = JSON.parse(\r\n                                response.data.data\r\n                            ).data;\r\n                        }\r\n                        that.$store.state.baseData = response.data;\r\n                    } else {\r\n                        that.$Message.error(response.data.msg);\r\n                    }\r\n                })\r\n                .catch(() => {\r\n                    that.$Message.error(\"网络错误\");\r\n                });\r\n        },\r\n    },\r\n    beforeRouteUpdate(to) {\r\n        if (to.query.id != this.id) {\r\n            this.id = to.query.id;\r\n            this.$store.state.id = to.query.id;\r\n            this.getRow();\r\n        }\r\n    },\r\n    //生命周期 - 创建完成（可以访问当前this实例）\r\n    created() {},\r\n    //生命周期 - 挂载完成（可以访问DOM元素）\r\n    mounted() {\r\n        this.id = this.$route.query.id;\r\n        this.$store.state.id = this.$route.query.id;\r\n        this.getRow();\r\n    },\r\n};\r\n</script>\r\n<style lang=\"less\" scoped>\r\n.box-left,\r\n.box-center,\r\n.box-right {\r\n    height: calc(100vh - 64px);\r\n    border: solid 1px #f5f5f5;\r\n    box-sizing: border-box;\r\n}\r\n.box-left {\r\n    border-left: 0px;\r\n}\r\n.box-center {\r\n    padding: 50px;\r\n    background: #efefef;\r\n}\r\n.box-right {\r\n    position: relative;\r\n}\r\n.tips {\r\n    color: #cccccc;\r\n    text-align: center;\r\n}\r\n.tabbox {\r\n    padding: 10px;\r\n}\r\n.tabbox::-webkit-scrollbar {\r\n    display: none;\r\n}\r\n</style>", "import mod from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Home.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../node_modules/thread-loader/dist/cjs.js!../../node_modules/babel-loader/lib/index.js!../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../node_modules/vue-loader/lib/index.js??vue-loader-options!./Home.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./Home.vue?vue&type=template&id=13347935&scoped=true&\"\nimport script from \"./Home.vue?vue&type=script&lang=js&\"\nexport * from \"./Home.vue?vue&type=script&lang=js&\"\nimport style0 from \"./Home.vue?vue&type=style&index=0&id=13347935&lang=less&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"13347935\",\n  null\n  \n)\n\nexport default component.exports", "// Imports\nvar ___CSS_LOADER_API_IMPORT___ = require(\"../../node_modules/css-loader/dist/runtime/api.js\");\nexports = ___CSS_LOADER_API_IMPORT___(false);\n// Module\nexports.push([module.id, \".box-center[data-v-13347935],.box-left[data-v-13347935],.box-right[data-v-13347935]{height:calc(100vh - 64px);border:1px solid #f5f5f5;box-sizing:border-box}.box-left[data-v-13347935]{border-left:0}.box-center[data-v-13347935]{padding:50px;background:#efefef}.box-right[data-v-13347935]{position:relative}.tips[data-v-13347935]{color:#ccc;text-align:center}.tabbox[data-v-13347935]{padding:10px}.tabbox[data-v-13347935]::-webkit-scrollbar{display:none}\", \"\"]);\n// Exports\nmodule.exports = exports;\n"], "sourceRoot": ""}